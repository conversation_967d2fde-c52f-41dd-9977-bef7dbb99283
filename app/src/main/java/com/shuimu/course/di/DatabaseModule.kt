package com.shuimu.course.di

import android.content.Context
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.shuimu.course.data.local.database.AppDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    private val MIGRATION_1_2 = object : Migration(1, 2) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 添加 isPackage 字段到 series 表
            database.execSQL("ALTER TABLE series ADD COLUMN is_package INTEGER NOT NULL DEFAULT 0")
        }
    }

    private val MIGRATION_2_3 = object : Migration(2, 3) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 添加 defaultExpanded 字段到 series 和 categories 表
            database.execSQL("ALTER TABLE series ADD COLUMN default_expanded INTEGER NOT NULL DEFAULT 0")
            database.execSQL("ALTER TABLE categories ADD COLUMN default_expanded INTEGER NOT NULL DEFAULT 0")
        }
    }

    private val MIGRATION_3_4 = object : Migration(3, 4) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 安全地添加 icon 字段到 series 表
            // 首先检查字段是否已存在
            val cursor = database.query("PRAGMA table_info(series)")
            var hasIconColumn = false
            
            while (cursor.moveToNext()) {
                val columnName = cursor.getString(cursor.getColumnIndex("name"))
                if (columnName == "icon") {
                    hasIconColumn = true
                    break
                }
            }
            cursor.close()
            
            // 只有当字段不存在时才添加
            if (!hasIconColumn) {
                database.execSQL("ALTER TABLE series ADD COLUMN icon TEXT")
            }
        }
    }

    private val MIGRATION_4_5 = object : Migration(4, 5) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 当前版本5与版本4无结构差异需要处理，保留占位符确保版本一致
        }
    }

    private val MIGRATION_5_6 = object : Migration(5, 6) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 添加所有缺失的字段到 videos 表
            database.execSQL("ALTER TABLE videos ADD COLUMN download_percent INTEGER")
            database.execSQL("ALTER TABLE videos ADD COLUMN watch_count INTEGER")
            database.execSQL("ALTER TABLE videos ADD COLUMN watch_progress REAL NOT NULL DEFAULT 0.0")
            database.execSQL("ALTER TABLE videos ADD COLUMN local_path TEXT")
            database.execSQL("ALTER TABLE videos ADD COLUMN cache_status TEXT NOT NULL DEFAULT 'NOT_CACHED'")
            database.execSQL("ALTER TABLE videos ADD COLUMN progress_percent INTEGER")
            database.execSQL("ALTER TABLE videos ADD COLUMN progress_float REAL")
        }
    }

    private val MIGRATION_6_7 = object : Migration(6, 7) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 1. 处理videos表的字段变更
            // 删除progress_percent和progress_float，添加progress字段
            database.execSQL("""
                CREATE TABLE videos_new (
                    id TEXT PRIMARY KEY NOT NULL,
                    title TEXT NOT NULL,
                    duration TEXT NOT NULL,
                    description TEXT NOT NULL,
                    category_id TEXT NOT NULL,
                    watch_count INTEGER,
                    watch_progress REAL NOT NULL,
                    cloud_url TEXT NOT NULL,
                    local_path TEXT,
                    cache_status TEXT NOT NULL,
                    download_percent INTEGER,
                    progress REAL NOT NULL
                )
            """.trimIndent())
            
            // 复制数据，progress使用progress_float的值，如果为空则使用0.0
            database.execSQL("""
                INSERT INTO videos_new (
                    id, title, duration, description, category_id, watch_count, 
                    watch_progress, cloud_url, local_path, cache_status, 
                    download_percent, progress
                )
                SELECT 
                    id, title, duration, description, category_id, watch_count,
                    watch_progress, cloud_url, local_path, cache_status,
                    download_percent, COALESCE(progress_float, 0.0)
                FROM videos
            """.trimIndent())
            
            database.execSQL("DROP TABLE videos")
            database.execSQL("ALTER TABLE videos_new RENAME TO videos")
            
            // 2. 处理categories表的字段变更
            // 修改price和isPurchased为可空类型，保持外键约束
            database.execSQL("""
                CREATE TABLE categories_new (
                    id TEXT PRIMARY KEY NOT NULL,
                    title TEXT NOT NULL,
                    series_id TEXT NOT NULL,
                    price TEXT,
                    is_free INTEGER NOT NULL,
                    is_purchased INTEGER,
                    default_expanded INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE
                )
            """.trimIndent())
            
            // 创建索引
            database.execSQL("CREATE INDEX index_categories_new_series_id ON categories_new(series_id)")
            
            // 复制数据
            database.execSQL("""
                INSERT INTO categories_new (
                    id, title, series_id, price, is_free, is_purchased, default_expanded
                )
                SELECT 
                    id, title, series_id, price, is_free, is_purchased, default_expanded
                FROM categories
            """.trimIndent())
            
            database.execSQL("DROP TABLE categories")
            database.execSQL("ALTER TABLE categories_new RENAME TO categories")
        }
    }

    private val MIGRATION_7_8 = object : Migration(7, 8) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 🔥 清理videos表：删除不再使用的字段
            database.execSQL("""
                CREATE TABLE videos_new (
                    id TEXT PRIMARY KEY NOT NULL,
                    title TEXT NOT NULL,
                    duration TEXT NOT NULL,
                    description TEXT NOT NULL,
                    category_id TEXT NOT NULL,
                    watch_count INTEGER,
                    cloud_url TEXT NOT NULL,
                    local_path TEXT,
                    cache_status TEXT NOT NULL DEFAULT 'NOT_CACHED',
                    progress REAL NOT NULL DEFAULT 0.0
                )
            """.trimIndent())
            
            // 复制数据，只保留需要的字段
            database.execSQL("""
                INSERT INTO videos_new (
                    id, title, duration, description, category_id, watch_count,
                    cloud_url, local_path, cache_status, progress
                )
                SELECT 
                    id, title, duration, description, category_id, watch_count,
                    cloud_url, local_path, cache_status, progress
                FROM videos
            """.trimIndent())
            
            database.execSQL("DROP TABLE videos")
            database.execSQL("ALTER TABLE videos_new RENAME TO videos")
            
            // 🔥 清理categories表：修改price类型为INTEGER
            database.execSQL("""
                CREATE TABLE categories_new (
                    id TEXT PRIMARY KEY NOT NULL,
                    title TEXT NOT NULL,
                    series_id TEXT NOT NULL,
                    price INTEGER,
                    is_free INTEGER NOT NULL,
                    is_purchased INTEGER,
                    default_expanded INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE
                )
            """.trimIndent())
            
            // 创建索引
            database.execSQL("CREATE INDEX index_categories_new_series_id ON categories_new(series_id)")
            
            // 复制数据，将price从TEXT转换为INTEGER（假设原来是数字字符串）
            database.execSQL("""
                INSERT INTO categories_new (
                    id, title, series_id, price, is_free, is_purchased, default_expanded
                )
                SELECT 
                    id, title, series_id, 
                    CASE 
                        WHEN price IS NULL OR price = '' THEN NULL
                        ELSE CAST(price AS INTEGER) * 100
                    END as price,
                    is_free, is_purchased, default_expanded
                FROM categories
            """.trimIndent())
            
            database.execSQL("DROP TABLE categories")
            database.execSQL("ALTER TABLE categories_new RENAME TO categories")
            
            // 🔥 清理series表：修改price类型为INTEGER
            database.execSQL("""
                CREATE TABLE series_new (
                    id TEXT PRIMARY KEY NOT NULL,
                    title TEXT NOT NULL,
                    icon TEXT,
                    price INTEGER NOT NULL DEFAULT 0,
                    is_free INTEGER NOT NULL,
                    is_purchased INTEGER NOT NULL DEFAULT 0,
                    is_package INTEGER NOT NULL DEFAULT 0,
                    default_expanded INTEGER NOT NULL DEFAULT 0
                )
            """.trimIndent())
            
            // 复制数据，将price从TEXT转换为INTEGER
            database.execSQL("""
                INSERT INTO series_new (
                    id, title, icon, price, is_free, is_purchased, is_package, default_expanded
                )
                SELECT 
                    id, title, icon,
                    CASE 
                        WHEN price IS NULL OR price = '' THEN 0
                        ELSE CAST(price AS INTEGER) * 100
                    END as price,
                    is_free, is_purchased, is_package, default_expanded
                FROM series
            """.trimIndent())
            
            database.execSQL("DROP TABLE series")
            database.execSQL("ALTER TABLE series_new RENAME TO series")
        }
    }

    private val MIGRATION_8_9 = object : Migration(8, 9) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 修复series表的is_purchased字段为NOT NULL
            database.execSQL("""
                CREATE TABLE series_new (
                    id TEXT PRIMARY KEY NOT NULL,
                    title TEXT NOT NULL,
                    icon TEXT,
                    price INTEGER NOT NULL DEFAULT 0,
                    is_free INTEGER NOT NULL,
                    is_purchased INTEGER NOT NULL DEFAULT 0,
                    is_package INTEGER NOT NULL DEFAULT 0,
                    default_expanded INTEGER NOT NULL DEFAULT 0
                )
            """.trimIndent())
            
            // 复制数据，确保is_purchased不为空
            database.execSQL("""
                INSERT INTO series_new (
                    id, title, icon, price, is_free, is_purchased, is_package, default_expanded
                )
                SELECT 
                    id, title, icon, price, is_free, 
                    COALESCE(is_purchased, 0) as is_purchased,
                    is_package, default_expanded
                FROM series
            """.trimIndent())
            
            database.execSQL("DROP TABLE series")
            database.execSQL("ALTER TABLE series_new RENAME TO series")
            
            // 🔥 迁移完成后检查外键完整性，清理孤儿数据
            val cursor = database.query("PRAGMA foreign_key_check")
            if (cursor.moveToFirst()) {
                android.util.Log.w("DatabaseMigration", "Found foreign key violations, cleaning up...")
                // 删除没有对应series的categories
                database.execSQL("""
                    DELETE FROM categories 
                    WHERE series_id NOT IN (SELECT id FROM series)
                """)
                // 删除没有对应category的videos  
                database.execSQL("""
                    DELETE FROM videos 
                    WHERE category_id NOT IN (SELECT id FROM categories)
                """)
            }
            cursor.close()
        }
    }

    private val MIGRATION_9_10 = object : Migration(9, 10) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 🔥 彻底清理所有数据，解决外键约束问题
            android.util.Log.i("DatabaseMigration", "Cleaning all tables to fix foreign key constraints")
            
            // 临时禁用外键约束
            database.execSQL("PRAGMA foreign_keys = OFF")
            
            // 清理所有表
            database.execSQL("DELETE FROM videos")
            database.execSQL("DELETE FROM categories") 
            database.execSQL("DELETE FROM series")
            
            // 重新启用外键约束
            database.execSQL("PRAGMA foreign_keys = ON")
            
            android.util.Log.i("DatabaseMigration", "All tables cleaned, foreign keys re-enabled")
        }
    }

    private val MIGRATION_10_11 = object : Migration(10, 11) {
        override fun migrate(database: SupportSQLiteDatabase) {
            // 🔥 清理可能包含旧分类标题的本地缓存数据
            android.util.Log.i("DatabaseMigration", "Clearing cached data to refresh category titles")
            
            // 临时禁用外键约束
            database.execSQL("PRAGMA foreign_keys = OFF")
            
            // 清理所有核心业务表，强制从服务器重新获取最新数据
            database.execSQL("DELETE FROM videos")
            database.execSQL("DELETE FROM categories") 
            database.execSQL("DELETE FROM series")
            
            // 重新启用外键约束
            database.execSQL("PRAGMA foreign_keys = ON")
            
            android.util.Log.i("DatabaseMigration", "Cached data cleared, will refresh from server")
        }
    }

    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            "shuimu_course.db"
        )
        .addMigrations(MIGRATION_1_2, MIGRATION_2_3, MIGRATION_3_4, MIGRATION_4_5, MIGRATION_5_6, MIGRATION_6_7, MIGRATION_7_8, MIGRATION_8_9, MIGRATION_9_10, MIGRATION_10_11)
        // 🔥 移除SQL查询日志，减少日志噪音
        // .setQueryCallback({ sqlQuery, bindArgs ->
        //     android.util.Log.d("RoomSQL", "Query: $sqlQuery, Args: $bindArgs")
        // }, java.util.concurrent.Executors.newSingleThreadExecutor())
        .fallbackToDestructiveMigration()
        .addCallback(object : RoomDatabase.Callback() {
            override fun onOpen(db: androidx.sqlite.db.SupportSQLiteDatabase) {
                super.onOpen(db)
                // 🔥 临时禁用外键约束以便清理脏数据
                db.execSQL("PRAGMA foreign_keys = ON")
            }
        })
        .build()
    }

    @Provides
    @Singleton
    fun provideUserDao(db: AppDatabase) = db.userDao()

    @Provides
    @Singleton
    fun provideSeriesDao(db: AppDatabase) = db.seriesDao()

    @Provides
    @Singleton
    fun provideCategoryDao(db: AppDatabase) = db.categoryDao()

    @Provides
    @Singleton
    fun provideVideoDao(db: AppDatabase) = db.videoDao()

    @Provides
    @Singleton
    fun providePurchaseDao(db: AppDatabase) = db.purchaseDao()

    @Provides
    @Singleton
    fun providePlayProgressDao(db: AppDatabase) = db.playProgressDao()

    @Provides
    @Singleton
    fun provideCacheInfoDao(db: AppDatabase) = db.cacheInfoDao()
} 