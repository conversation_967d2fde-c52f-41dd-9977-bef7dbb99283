package com.shuimu.course.domain.repository

import com.shuimu.course.domain.model.Video
import kotlinx.coroutines.flow.Flow

interface VideoRepository {
    suspend fun getVideoById(videoId: String): Video?
    suspend fun updateVideoProgress(videoId: String, progress: Float)
    suspend fun incrementWatchCount(videoId: String)
    fun getVideosForCategory(categoryId: String): Flow<List<Video>>
    
    // 删除缓存功能
    suspend fun deleteCachedVideo(videoId: String): Result<Unit>
} 