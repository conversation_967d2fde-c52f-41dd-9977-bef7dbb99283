package com.shuimu.course.domain.model

/**
 * 缓存视频 Domain 模型
 */
data class CachedVideo(
    val videoId: String,
    val title: String,
    val localPath: String?,
    val downloadedBytes: Long,
    val totalBytes: Long,
    val status: CacheStatus,
    val progress: Int, // 0-100
    val cloudUrl: String?,
    val updatedAt: Long,
    val errorMessage: String? = null,
    val priority: Int = 0, // 下载优先级，数值越大优先级越高
    val retryCount: Int = 0, // 重试次数
    val lastRetryTime: Long = 0L // 最后重试时间
) {
    /**
     * 获取下载进度百分比
     */
    fun getProgressPercentage(): Int {
        return if (totalBytes > 0) {
            ((downloadedBytes * 100) / totalBytes).toInt()
        } else {
            progress
        }
    }

    /**
     * 是否已缓存完成
     */
    fun isCached(): Boolean = status == CacheStatus.DOWNLOADED

    /**
     * 是否正在下载
     */
    fun isDownloading(): Boolean = status == CacheStatus.DOWNLOADING

    /**
     * 是否可以播放
     */
    fun canPlay(): Boolean = isCached() && !localPath.isNullOrEmpty()

    /**
     * 是否需要重试
     */
    fun needsRetry(): Boolean = status == CacheStatus.FAILED && retryCount < MAX_RETRY_COUNT

    /**
     * 获取下一次重试的延迟时间（指数退避）
     */
    fun getNextRetryDelay(): Long {
        return INITIAL_RETRY_DELAY * (1L shl retryCount.coerceAtMost(MAX_RETRY_COUNT))
    }

    /**
     * 是否可以开始重试
     */
    fun canRetryNow(): Boolean {
        return needsRetry() && (System.currentTimeMillis() - lastRetryTime) >= getNextRetryDelay()
    }

    /**
     * 获取显示文本（用于UI显示）
     */
    fun getDisplayText(): String = when (status) {
        CacheStatus.DOWNLOADED -> "（已缓存）"
        CacheStatus.DOWNLOADING -> "（${getProgressPercentage()}%）"
        CacheStatus.WAITING_NETWORK -> "（等待网络）"
        CacheStatus.PENDING -> "（等待下载）"
        CacheStatus.PAUSED -> "（已暂停）"
        CacheStatus.FAILED -> "（下载失败）"
        CacheStatus.NOT_CACHED -> ""
    }

    /**
     * 获取显示颜色（用于UI显示）
     */
    fun getDisplayColor(): String = status.getDisplayColor()

    companion object {
        const val MAX_RETRY_COUNT = 3
        const val INITIAL_RETRY_DELAY = 5000L // 5秒
    }
}