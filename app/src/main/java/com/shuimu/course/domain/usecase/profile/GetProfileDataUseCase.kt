package com.shuimu.course.domain.usecase.profile

import com.shuimu.course.domain.model.profile.UserProfile
import com.shuimu.course.domain.repository.UserRepository
import com.shuimu.course.domain.util.Resource
import javax.inject.Inject

class GetProfileDataUseCase @Inject constructor(
    private val userRepository: UserRepository
) {
    suspend operator fun invoke(): Resource<UserProfile> {
        return userRepository.getProfileData()
    }
} 