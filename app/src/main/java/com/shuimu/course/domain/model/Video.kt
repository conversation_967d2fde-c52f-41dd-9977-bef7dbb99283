package com.shuimu.course.domain.model

data class Video(
    val id: String,
    val title: String,
    val duration: Int, // 视频总时长，单位：秒
    val description: String,
    val categoryId: String, // 所属分类ID
    val watchCount: Int?, // 个人观看次数 (0-10次，用于徽章等级计算)
    val playCount: Long?, // 全局播放量（所有用户观看总次数）
    val cloudUrl: String,
    val localPath: String?,
    val cacheStatus: CacheStatus,
    
    // 🔥 统一进度参数：观看完成度 (0.0-1.0，用于UI显示)
    val progress: Float,
    val isPurchasable: Boolean = false // 是否可购买（由分类权限决定）
) 