package com.shuimu.course.domain.model

/**
 * 统一的缓存状态枚举
 * 整合了原有的 CacheStatus 和 CacheDownloadStatus
 */
enum class CacheStatus(val value: String, val displayName: String) {
    NOT_CACHED("NOT_CACHED", "未缓存"),
    WAITING_NETWORK("WAITING_NETWORK", "等待网络"),
    PENDING("PENDING", "等待下载"),
    DOWNLOADING("DOWNLOADING", "下载中"),
    PAUSED("PAUSED", "已暂停"),
    DOWNLOADED("DOWNLOADED", "已完成"),
    FAILED("FAILED", "下载失败");

    companion object {
        fun fromString(value: String?): CacheStatus {
            if (value.isNullOrBlank()) return NOT_CACHED
            return values().find { it.value.equals(value, ignoreCase = true) } ?: NOT_CACHED
        }
    }

    /**
     * 状态机：定义允许的状态转换
     */
    fun canTransitionTo(newStatus: CacheStatus): Bo<PERSON>an {
        return when (this) {
            NOT_CACHED -> newStatus in setOf(WAITING_NETWORK, PENDING, DOWNLOADING)
            WAITING_NETWORK -> newStatus in setOf(NOT_CACHED, PENDING, DOWNLOADING)
            PENDING -> newStatus in setOf(NOT_CACHED, DOWNLOADING, PAUSED)
            DOWNLOADING -> newStatus in setOf(NOT_CACHED, PAUSED, DOWNLOADED, FAILED)
            PAUSED -> newStatus in setOf(NOT_CACHED, DOWNLOADING, FAILED)
            DOWNLOADED -> newStatus in setOf(NOT_CACHED) // 只能删除
            FAILED -> newStatus in setOf(NOT_CACHED, PENDING, DOWNLOADING)
        }
    }

    /**
     * 获取状态显示颜色
     */
    fun getDisplayColor(): String = when (this) {
        DOWNLOADED -> "#4CAF50" // 绿色
        DOWNLOADING -> "#2196F3" // 蓝色
        WAITING_NETWORK -> "#FF5722" // 深橙色
        PENDING -> "#FF9800" // 橙色
        PAUSED -> "#9E9E9E" // 灰色
        FAILED -> "#F44336" // 红色
        NOT_CACHED -> "#000000" // 黑色
    }

    /**
     * 是否为活跃状态（需要处理的状态）
     */
    fun isActive(): Boolean = this in setOf(WAITING_NETWORK, PENDING, DOWNLOADING)

    /**
     * 是否为终止状态
     */
    fun isTerminal(): Boolean = this in setOf(DOWNLOADED, FAILED, NOT_CACHED)

    /**
     * 是否可以播放
     */
    fun canPlay(): Boolean = this == DOWNLOADED
}