package com.shuimu.course.domain.repository

import com.shuimu.course.domain.model.Series
import com.shuimu.course.domain.util.Resource
import com.shuimu.course.data.repository.update.DataChangeEvent
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow

interface SeriesRepository {
    fun getSeries(): Flow<Resource<List<Series>>>

    // 🔥 新增：预加载数据管理方法
    fun hasPreloadedData(): Boolean
    fun getPreloadedData(): Pair<List<Series>, String>?
    fun markPreloadCompleted(dataSource: String)

    // 🔥 新增：数据变化监听方法
    fun observeDataChanges(): SharedFlow<DataChangeEvent>
}