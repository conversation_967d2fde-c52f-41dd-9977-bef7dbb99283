package com.shuimu.course.domain.repository

import kotlinx.coroutines.flow.Flow

interface SearchRepository {
    suspend fun getSearchHistory(): List<String>
    suspend fun addSearchHistory(query: String)
    suspend fun clearSearchHistory()
    suspend fun searchContent(query: String): List<SearchResult>
}

data class SearchResult(
    val id: String,
    val title: String, 
    val description: String,
    val type: String,
    val matchScore: Float,
    val thumbnailUrl: String? = null
) 