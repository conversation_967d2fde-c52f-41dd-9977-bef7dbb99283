package com.shuimu.course.domain.usecase.cache

import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.CacheRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * 检查缓存状态 UseCase
 * 用于视频点击逻辑判断
 */
class CheckCacheStatusUseCase @Inject constructor(
    private val cacheRepository: CacheRepository
) {
    
    /**
     * 获取视频的缓存信息
     */
    fun getCacheInfo(videoId: String): Flow<CachedVideo?> {
        return cacheRepository.getCacheInfo(videoId)
    }
    
    /**
     * 同步获取视频的缓存信息
     */
    suspend fun getCacheInfoSync(videoId: String): CachedVideo? {
        return cacheRepository.getCacheInfoSync(videoId)
    }
    
    /**
     * 检查视频点击后应该执行的动作
     */
    suspend fun getVideoClickAction(videoId: String): VideoClickAction {
        val cacheInfo = getCacheInfoSync(videoId)
        
        return when (cacheInfo?.status) {
            CacheStatus.DOWNLOADED -> {
                // 检查本地文件是否还存在
                if (cacheInfo.localPath != null && cacheRepository.checkLocalFileExists(cacheInfo.localPath)) {
                    VideoClickAction.PlayVideo(cacheInfo.localPath)
                } else {
                    // 文件丢失，更新状态为未缓存
                    cacheRepository.updateDownloadStatus(videoId, CacheStatus.NOT_CACHED)
                    cacheRepository.syncCacheStateToServer(videoId, false)
                    VideoClickAction.ShowCacheDialog
                }
            }

            CacheStatus.WAITING_NETWORK -> {
                VideoClickAction.ShowDownloadingMessage("等待网络条件满足...")
            }

            CacheStatus.DOWNLOADING -> {
                VideoClickAction.ShowDownloadingMessage("正在下载中，进度：${cacheInfo.getProgressPercentage()}%")
            }

            CacheStatus.PENDING -> {
                VideoClickAction.ShowDownloadingMessage("已在下载队列中，请稍候...")
            }

            CacheStatus.PAUSED -> {
                VideoClickAction.ShowResumeDialog
            }

            CacheStatus.FAILED -> {
                VideoClickAction.ShowRetryDialog
            }

            CacheStatus.NOT_CACHED, null -> {
                VideoClickAction.ShowCacheDialog
            }
        }
    }
}

/**
 * 视频点击后的动作
 */
sealed class VideoClickAction {
    data class PlayVideo(val localPath: String) : VideoClickAction()
    object ShowCacheDialog : VideoClickAction()
    data class ShowDownloadingMessage(val message: String) : VideoClickAction()
    object ShowResumeDialog : VideoClickAction()
    object ShowRetryDialog : VideoClickAction()
} 