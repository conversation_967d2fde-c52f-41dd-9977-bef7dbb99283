package com.shuimu.course.domain.util

sealed class Resource<T>(
    val data: T? = null,
    val message: String? = null,
    val dataSource: String? = null // 🔥 新增：数据源标识
) {
    class Success<T>(data: T, dataSource: String? = null) : Resource<T>(data, null, dataSource)
    class Error<T>(message: String, data: T? = null, dataSource: String? = null) : Resource<T>(data, message, dataSource)
    class Loading<T>(val isLoading: Boolean = true, data: T? = null, dataSource: String? = null) : Resource<T>(data, null, dataSource)
}