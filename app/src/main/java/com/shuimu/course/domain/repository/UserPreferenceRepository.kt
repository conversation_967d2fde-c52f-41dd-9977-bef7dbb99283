package com.shuimu.course.domain.repository

import kotlinx.coroutines.flow.Flow

interface UserPreferenceRepository {
    suspend fun getExpandedState(id: String): Boolean?
    suspend fun setExpandedState(id: String, isExpanded: Boolean)
    suspend fun getAllExpandedStates(): Map<String, Boolean>
    fun getAllExpandedStatesFlow(): Flow<Map<String, Boolean>>
    suspend fun clearExpandedStates()
} 