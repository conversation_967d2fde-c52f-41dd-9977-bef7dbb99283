package com.shuimu.course.domain.usecase.cache

import com.shuimu.course.data.workers.DownloadManager
import com.shuimu.course.domain.repository.VideoRepository
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * 启动视频下载 UseCase
 */
class StartDownloadUseCase @Inject constructor(
    private val downloadManager: DownloadManager,
    private val videoRepository: VideoRepository
) {
    
    suspend operator fun invoke(videoId: String): Flow<Resource<String>> = flow {
        try {
            android.util.Log.d("StartDownloadUseCase", "=== 开始执行StartDownloadUseCase ===")
            android.util.Log.d("StartDownloadUseCase", "输入参数 videoId: $videoId")
            
            emit(Resource.Loading())
            android.util.Log.d("StartDownloadUseCase", "已发送 Resource.Loading")
            
            // 1. 检查视频是否存在
            android.util.Log.d("StartDownloadUseCase", "开始获取视频信息...")
            val video = videoRepository.getVideoById(videoId)
            android.util.Log.d("StartDownloadUseCase", "视频信息获取结果: ${video?.title ?: "null"}")
            
            if (video == null) {
                android.util.Log.e("StartDownloadUseCase", "视频不存在，返回错误")
                emit(Resource.Error("视频不存在"))
                return@flow
            }
            
            // 2. 启动下载
            android.util.Log.d("StartDownloadUseCase", "开始调用downloadManager.startDownload...")
            android.util.Log.d("StartDownloadUseCase", "下载参数: videoId=$videoId, cloudUrl=${video.cloudUrl}, title=${video.title}")
            
            val result = downloadManager.startDownload(
                videoId = videoId,
                cloudUrl = video.cloudUrl ?: "",
                videoTitle = video.title
            )
            
            android.util.Log.d("StartDownloadUseCase", "downloadManager.startDownload完成")
            android.util.Log.d("StartDownloadUseCase", "结果: isSuccess=${result.isSuccess}")
            
            if (result.isSuccess) {
                val successMessage = result.getOrNull() ?: "下载已启动"
                android.util.Log.d("StartDownloadUseCase", "成功，返回消息: $successMessage")
                emit(Resource.Success(successMessage))
            } else {
                val errorMessage = result.exceptionOrNull()?.message ?: "启动下载失败"
                android.util.Log.e("StartDownloadUseCase", "失败，错误消息: $errorMessage")
                emit(Resource.Error(errorMessage))
            }
            
            android.util.Log.d("StartDownloadUseCase", "=== StartDownloadUseCase执行完成 ===")
            
        } catch (e: Exception) {
            android.util.Log.e("StartDownloadUseCase", "UseCase执行异常", e)
            emit(Resource.Error("启动下载失败：${e.message}"))
        }
    }
} 