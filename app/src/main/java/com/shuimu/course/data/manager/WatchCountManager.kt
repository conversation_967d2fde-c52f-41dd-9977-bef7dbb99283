package com.shuimu.course.data.manager

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 观看次数统计管理器
 * 负责精确统计全局观看次数和用户个人观看次数
 */
@Singleton
class WatchCountManager @Inject constructor(
    private val dataSyncManager: DataSyncManager,
    private val smartBatchUpdateManager: SmartBatchUpdateManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 本地观看记录缓存
    private val watchRecords = ConcurrentHashMap<String, WatchRecord>()
    
    // 待同步的观看次数更新
    private val pendingWatchCountUpdates = ConcurrentHashMap<String, WatchCountUpdate>()
    
    companion object {
        private const val COMPLETION_THRESHOLD = 0.95f // 95%以上认为完成
        private const val VALID_WATCH_THRESHOLD = 0.1f // 10%以上认为有效观看
    }

    /**
     * 记录视频观看开始
     */
    fun startWatching(videoId: String, userId: String) {
        android.util.Log.d("WatchCountManager", "▶️ 开始观看视频: $videoId")
        
        val record = WatchRecord(
            videoId = videoId,
            userId = userId,
            startTime = System.currentTimeMillis(),
            lastUpdateTime = System.currentTimeMillis()
        )
        
        watchRecords[videoId] = record
    }

    /**
     * 更新观看进度
     */
    fun updateWatchProgress(
        videoId: String, 
        currentPosition: Long, 
        totalDuration: Long,
        isCompleted: Boolean = false
    ) {
        val record = watchRecords[videoId] ?: return
        val completionRate = if (totalDuration > 0) {
            (currentPosition.toFloat() / totalDuration.toFloat())
        } else 0f
        
        android.util.Log.d("WatchCountManager", "📊 更新观看进度: $videoId, 完成度: ${(completionRate * 100).toInt()}%")
        
        // 更新观看记录
        val updatedRecord = record.copy(
            currentPosition = currentPosition,
            totalDuration = totalDuration,
            completionRate = completionRate,
            lastUpdateTime = System.currentTimeMillis(),
            watchDuration = System.currentTimeMillis() - record.startTime
        )
        
        watchRecords[videoId] = updatedRecord
        
        // 检查是否需要增加观看次数
        checkAndUpdateWatchCount(updatedRecord, isCompleted)
    }

    /**
     * 视频播放完成
     */
    fun onVideoCompleted(videoId: String, currentPosition: Long, totalDuration: Long) {
        android.util.Log.d("WatchCountManager", "🎯 视频播放完成: $videoId")
        
        updateWatchProgress(videoId, currentPosition, totalDuration, true)
        
        // 播放完成时立即同步观看次数
        val record = watchRecords[videoId]
        if (record != null && record.completionRate >= COMPLETION_THRESHOLD) {
            syncWatchCountImmediately(record, WatchCountType.COMPLETED)
        }
    }

    /**
     * 停止观看（离开播放页、暂停等）
     */
    fun stopWatching(videoId: String, currentPosition: Long, totalDuration: Long) {
        android.util.Log.d("WatchCountManager", "⏹️ 停止观看视频: $videoId")
        
        updateWatchProgress(videoId, currentPosition, totalDuration)
        
        val record = watchRecords[videoId]
        if (record != null) {
            // 添加到批量更新队列
            addToBatchUpdate(record, WatchCountType.PROGRESS_UPDATE)
        }
    }

    /**
     * 检查并更新观看次数
     */
    private fun checkAndUpdateWatchCount(record: WatchRecord, forceComplete: Boolean = false) {
        val videoId = record.videoId
        val completionRate = record.completionRate
        
        when {
            // 播放完成：增加完整观看次数
            forceComplete || completionRate >= COMPLETION_THRESHOLD -> {
                if (!record.hasCompletedCount) {
                    android.util.Log.d("WatchCountManager", "🎯 增加完整观看次数: $videoId")
                    
                    // 标记已计算完整观看次数
                    val updatedRecord = record.copy(hasCompletedCount = true)
                    watchRecords[videoId] = updatedRecord
                    
                    // 立即同步完整观看次数
                    syncWatchCountImmediately(updatedRecord, WatchCountType.COMPLETED)
                }
            }
            
            // 有效观看：记录但不增加完整观看次数
            completionRate >= VALID_WATCH_THRESHOLD -> {
                if (!record.hasValidWatch) {
                    android.util.Log.d("WatchCountManager", "👁️ 记录有效观看: $videoId (${(completionRate * 100).toInt()}%)")
                    
                    // 标记已记录有效观看
                    val updatedRecord = record.copy(hasValidWatch = true)
                    watchRecords[videoId] = updatedRecord
                    
                    // 添加到批量更新
                    addToBatchUpdate(updatedRecord, WatchCountType.VALID_WATCH)
                }
            }
        }
    }

    /**
     * 立即同步观看次数
     */
    private fun syncWatchCountImmediately(record: WatchRecord, type: WatchCountType) {
        scope.launch {
            try {
                android.util.Log.d("WatchCountManager", "🚀 立即同步观看次数: ${record.videoId}, 类型: $type")
                
                val updateData = createWatchCountUpdate(record, type)
                
                // 立即同步到服务器
                dataSyncManager.syncWatchCountImmediately(updateData.toMap())
                
                android.util.Log.d("WatchCountManager", "✅ 观看次数同步成功")
                
            } catch (e: Exception) {
                android.util.Log.e("WatchCountManager", "❌ 观看次数同步失败", e)
                
                // 同步失败，添加到批量更新队列
                addToBatchUpdate(record, type)
            }
        }
    }

    /**
     * 添加到批量更新队列
     */
    private fun addToBatchUpdate(record: WatchRecord, type: WatchCountType) {
        val updateData = createWatchCountUpdate(record, type)
        
        // 添加到智能批量更新管理器
        smartBatchUpdateManager.addBatchUpdate(
            key = "watch_count_${record.videoId}_${System.currentTimeMillis()}",
            data = BatchUpdateData(
                type = BatchUpdateType.WATCH_COUNT,
                data = updateData.toMap(),
                priority = if (type == WatchCountType.COMPLETED) BatchPriority.CRITICAL else BatchPriority.NORMAL
            )
        )
        
        android.util.Log.d("WatchCountManager", "📦 观看次数添加到批量更新: ${record.videoId}, 类型: $type")
    }

    /**
     * 创建观看次数更新数据
     */
    private fun createWatchCountUpdate(record: WatchRecord, type: WatchCountType): WatchCountUpdate {
        return WatchCountUpdate(
            videoId = record.videoId,
            userId = record.userId,
            type = type,
            completionRate = record.completionRate,
            watchDuration = record.watchDuration,
            timestamp = System.currentTimeMillis(),
            globalWatchCountIncrement = if (type == WatchCountType.COMPLETED) 1 else 0,
            userWatchCountIncrement = if (type == WatchCountType.COMPLETED) 1 else 0
        )
    }

    /**
     * 获取视频的观看统计
     */
    fun getWatchStats(videoId: String): WatchStats? {
        val record = watchRecords[videoId] ?: return null
        
        return WatchStats(
            videoId = videoId,
            completionRate = record.completionRate,
            watchDuration = record.watchDuration,
            hasValidWatch = record.hasValidWatch,
            hasCompletedCount = record.hasCompletedCount
        )
    }

    /**
     * 清理观看记录
     */
    fun clearWatchRecord(videoId: String) {
        watchRecords.remove(videoId)
        android.util.Log.d("WatchCountManager", "🗑️ 清理观看记录: $videoId")
    }

    /**
     * 批量清理过期的观看记录
     */
    fun cleanupExpiredRecords() {
        val now = System.currentTimeMillis()
        val expiredThreshold = 24 * 60 * 60 * 1000L // 24小时
        
        val expiredKeys = watchRecords.entries
            .filter { now - it.value.lastUpdateTime > expiredThreshold }
            .map { it.key }
        
        expiredKeys.forEach { key ->
            watchRecords.remove(key)
        }
        
        if (expiredKeys.isNotEmpty()) {
            android.util.Log.d("WatchCountManager", "🗑️ 清理过期观看记录: ${expiredKeys.size}条")
        }
    }
}

/**
 * 观看记录
 */
data class WatchRecord(
    val videoId: String,
    val userId: String,
    val startTime: Long,
    val currentPosition: Long = 0L,
    val totalDuration: Long = 0L,
    val completionRate: Float = 0f,
    val watchDuration: Long = 0L,
    val lastUpdateTime: Long,
    val hasValidWatch: Boolean = false,      // 是否已记录有效观看
    val hasCompletedCount: Boolean = false   // 是否已计算完整观看次数
)

/**
 * 观看次数更新
 */
data class WatchCountUpdate(
    val videoId: String,
    val userId: String,
    val type: WatchCountType,
    val completionRate: Float,
    val watchDuration: Long,
    val timestamp: Long,
    val globalWatchCountIncrement: Int,  // 全局观看次数增量
    val userWatchCountIncrement: Int     // 用户观看次数增量
) {
    fun toMap(): Map<String, Any> {
        return mapOf(
            "videoId" to videoId,
            "userId" to userId,
            "type" to type.toString(),
            "completionRate" to completionRate,
            "watchDuration" to watchDuration,
            "timestamp" to timestamp,
            "globalWatchCountIncrement" to globalWatchCountIncrement,
            "userWatchCountIncrement" to userWatchCountIncrement
        )
    }
}

/**
 * 观看次数类型
 */
enum class WatchCountType {
    COMPLETED,      // 完整观看
    VALID_WATCH,    // 有效观看
    PROGRESS_UPDATE // 进度更新
}

/**
 * 观看统计
 */
data class WatchStats(
    val videoId: String,
    val completionRate: Float,
    val watchDuration: Long,
    val hasValidWatch: Boolean,
    val hasCompletedCount: Boolean
)
