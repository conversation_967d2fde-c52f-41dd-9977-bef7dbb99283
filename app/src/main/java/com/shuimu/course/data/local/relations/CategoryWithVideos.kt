package com.shuimu.course.data.local.relations

import androidx.room.Embedded
import androidx.room.Relation
import com.shuimu.course.data.local.entities.CategoryEntity
import com.shuimu.course.data.local.entities.VideoEntity

data class CategoryWithVideos(
    @Embedded val category: CategoryEntity,
    @Relation(
        parentColumn = "id",
        entityColumn = "category_id"
    )
    val videos: List<VideoEntity>
) 