package com.shuimu.course.data.remote.api

import com.shuimu.course.config.ApiConfig
import com.shuimu.course.data.remote.dto.user.*
import retrofit2.Response
import retrofit2.http.*

/**
 * 用户个人数据API接口
 * 包括观看进度、缓存状态、收藏等个人数据
 * 遵循RESTful设计原则
 */
interface UserDataApi {

    // ========== 用户观看进度 ==========
    
    /**
     * 获取用户所有观看进度
     */
    @GET(ApiConfig.Endpoints.USER_PROGRESS)
    suspend fun getUserAllProgress(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserProgressListResponseDto>

    /**
     * 获取用户特定视频的观看进度
     */
    @GET(ApiConfig.Endpoints.USER_PROGRESS_VIDEO)
    suspend fun getUserVideoProgress(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserProgressResponseDto>

    /**
     * 更新用户视频观看进度
     */
    @PUT(ApiConfig.Endpoints.USER_PROGRESS_VIDEO)
    suspend fun updateUserVideoProgress(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false",
        @Body progressRequest: UserProgressUpdateRequestDto
    ): Response<UserProgressResponseDto>

    /**
     * 删除用户视频观看进度
     */
    @DELETE(ApiConfig.Endpoints.USER_PROGRESS_VIDEO)
    suspend fun deleteUserVideoProgress(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<DeleteResponseDto>

    // ========== 用户缓存管理 ==========
    
    /**
     * 获取用户缓存列表
     */
    @GET(ApiConfig.Endpoints.USER_CACHE)
    suspend fun getUserCacheList(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserCacheListResponseDto>

    /**
     * 更新用户视频缓存状态
     */
    @PUT(ApiConfig.Endpoints.USER_CACHE_VIDEO)
    suspend fun updateUserVideoCache(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false",
        @Body cacheRequest: UserCacheUpdateRequestDto
    ): Response<UserCacheResponseDto>

    /**
     * 删除用户视频缓存记录
     */
    @DELETE(ApiConfig.Endpoints.USER_CACHE_VIDEO)
    suspend fun deleteUserVideoCache(
        @Path("user_id") userId: String,
        @Path("video_id") videoId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<DeleteResponseDto>

    // ========== 用户收藏管理 ==========
    
    /**
     * 获取用户收藏列表
     */
    @GET(ApiConfig.Endpoints.USER_FAVORITES)
    suspend fun getUserFavorites(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserFavoritesResponseDto>

    /**
     * 更新用户收藏
     */
    @PUT(ApiConfig.Endpoints.USER_FAVORITES)
    suspend fun updateUserFavorites(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false",
        @Body favoritesRequest: UserFavoritesUpdateRequestDto
    ): Response<UserFavoritesResponseDto>

    // ========== 废弃的端点（向后兼容） ==========
    
    /**
     * @deprecated 使用 updateUserVideoProgress 替代
     */
    @Deprecated("使用 updateUserVideoProgress 替代")
    @PUT("/api/videos/{video_id}/progress")
    suspend fun updateVideoProgressDeprecated(
        @Path("video_id") videoId: String,
        @Body progressRequest: UserProgressUpdateRequestDto
    ): Response<UserProgressResponseDto>

    /**
     * @deprecated 使用 updateUserVideoCache 替代
     */
    @Deprecated("使用 updateUserVideoCache 替代")
    @PUT("/api/videos/{video_id}/cache-status")
    suspend fun updateVideoCacheStatusDeprecated(
        @Path("video_id") videoId: String,
        @Body cacheRequest: UserCacheUpdateRequestDto
    ): Response<UserCacheResponseDto>
}
