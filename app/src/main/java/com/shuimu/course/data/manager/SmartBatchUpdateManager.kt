package com.shuimu.course.data.manager

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.BatteryManager
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 智能批量更新管理器
 * 实现网络状态感知、电池状态考虑的智能批量更新策略
 */
@Singleton
class SmartBatchUpdateManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val dataSyncManager: DataSyncManager
) : DefaultLifecycleObserver {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 待批量更新的数据
    private val pendingUpdates = ConcurrentHashMap<String, BatchUpdateData>()
    
    // 批量更新任务
    private var batchUpdateJob: Job? = null
    
    // 网络状态
    private val _networkState = MutableStateFlow(SmartNetworkState.UNKNOWN)
    val networkState: StateFlow<SmartNetworkState> = _networkState.asStateFlow()
    
    // 电池状态
    private val _batteryLevel = MutableStateFlow(100)
    val batteryLevel: StateFlow<Int> = _batteryLevel.asStateFlow()
    
    companion object {
        private const val BASE_UPDATE_INTERVAL = 60_000L // 1分钟基础间隔
        private const val LOW_BATTERY_THRESHOLD = 20 // 低电量阈值
        private const val WEAK_NETWORK_THRESHOLD = 1000L // 弱网络延迟阈值(ms)
    }

    init {
        // 注册生命周期监听
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
        
        // 开始监控网络和电池状态
        startStatusMonitoring()
        
        // 启动批量更新任务
        startBatchUpdates()
    }

    /**
     * 添加待批量更新的数据
     */
    fun addBatchUpdate(key: String, data: BatchUpdateData) {
        android.util.Log.d("SmartBatchUpdateManager", "📦 添加批量更新: $key")
        pendingUpdates[key] = data
    }

    /**
     * 立即执行批量更新
     */
    suspend fun executeBatchUpdate() {
        if (pendingUpdates.isEmpty()) {
            android.util.Log.d("SmartBatchUpdateManager", "📦 无待更新数据，跳过批量更新")
            return
        }

        android.util.Log.d("SmartBatchUpdateManager", "🚀 执行批量更新: ${pendingUpdates.size}项")
        
        try {
            val updates = pendingUpdates.values.toList()
            pendingUpdates.clear()
            
            // 按优先级分组处理
            val criticalUpdates = updates.filter { it.priority == BatchPriority.CRITICAL }
            val normalUpdates = updates.filter { it.priority == BatchPriority.NORMAL }
            val lowUpdates = updates.filter { it.priority == BatchPriority.LOW }
            
            // 优先处理关键更新
            if (criticalUpdates.isNotEmpty()) {
                processBatchUpdates(criticalUpdates, "关键")
            }
            
            // 根据网络状态处理普通更新
            if (shouldProcessNormalUpdates()) {
                if (normalUpdates.isNotEmpty()) {
                    processBatchUpdates(normalUpdates, "普通")
                }
                if (lowUpdates.isNotEmpty()) {
                    processBatchUpdates(lowUpdates, "低优先级")
                }
            } else {
                // 网络条件不好，重新加入队列
                normalUpdates.forEach { addBatchUpdate("retry_${System.currentTimeMillis()}", it) }
                lowUpdates.forEach { addBatchUpdate("retry_${System.currentTimeMillis()}", it) }
            }
            
        } catch (e: Exception) {
            android.util.Log.e("SmartBatchUpdateManager", "❌ 批量更新失败", e)
        }
    }

    /**
     * 获取智能更新间隔
     */
    fun getSmartUpdateInterval(): Long {
        val network = _networkState.value
        val battery = _batteryLevel.value
        
        return when {
            battery <= LOW_BATTERY_THRESHOLD -> {
                android.util.Log.d("SmartBatchUpdateManager", "🔋 低电量模式，延长更新间隔")
                BASE_UPDATE_INTERVAL * 5 // 5分钟
            }
            network == SmartNetworkState.WIFI && battery > 30 -> {
                android.util.Log.d("SmartBatchUpdateManager", "📶 WiFi环境，正常间隔")
                BASE_UPDATE_INTERVAL // 1分钟
            }
            network == SmartNetworkState.MOBILE && battery > 30 -> {
                android.util.Log.d("SmartBatchUpdateManager", "📱 移动网络，适度延长")
                BASE_UPDATE_INTERVAL * 2 // 2分钟
            }
            network == SmartNetworkState.WEAK -> {
                android.util.Log.d("SmartBatchUpdateManager", "🐌 弱网络，大幅延长")
                BASE_UPDATE_INTERVAL * 3 // 3分钟
            }
            else -> {
                android.util.Log.d("SmartBatchUpdateManager", "❓ 未知状态，使用默认间隔")
                BASE_UPDATE_INTERVAL // 1分钟
            }
        }
    }

    /**
     * 启动批量更新任务
     */
    private fun startBatchUpdates() {
        batchUpdateJob?.cancel()
        batchUpdateJob = scope.launch {
            while (isActive) {
                val interval = getSmartUpdateInterval()
                delay(interval)
                
                try {
                    executeBatchUpdate()
                } catch (e: Exception) {
                    android.util.Log.e("SmartBatchUpdateManager", "批量更新任务异常", e)
                }
            }
        }
    }

    /**
     * 开始状态监控
     */
    private fun startStatusMonitoring() {
        scope.launch {
            while (isActive) {
                updateNetworkState()
                updateBatteryLevel()
                delay(10_000L) // 每10秒检查一次状态
            }
        }
    }

    /**
     * 更新网络状态
     */
    private fun updateNetworkState() {
        try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            
            val newState = when {
                capabilities == null -> SmartNetworkState.OFFLINE
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> SmartNetworkState.WIFI
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                    // 简单的网络质量检测
                    if (capabilities.linkDownstreamBandwidthKbps < 1000) {
                        SmartNetworkState.WEAK
                    } else {
                        SmartNetworkState.MOBILE
                    }
                }
                else -> SmartNetworkState.UNKNOWN
            }
            
            if (_networkState.value != newState) {
                android.util.Log.d("SmartBatchUpdateManager", "📡 网络状态变化: ${_networkState.value} → $newState")
                _networkState.value = newState
                
                // 网络状态变化时重新调整更新间隔
                if (newState != SmartNetworkState.OFFLINE) {
                    restartBatchUpdates()
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("SmartBatchUpdateManager", "网络状态检测失败", e)
        }
    }

    /**
     * 更新电池状态
     */
    private fun updateBatteryLevel() {
        try {
            val batteryIntent = context.registerReceiver(null, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
            val level = batteryIntent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
            val scale = batteryIntent?.getIntExtra(BatteryManager.EXTRA_SCALE, -1) ?: -1
            
            if (level >= 0 && scale > 0) {
                val batteryPct = (level * 100 / scale)
                if (_batteryLevel.value != batteryPct) {
                    android.util.Log.d("SmartBatchUpdateManager", "🔋 电池电量: ${_batteryLevel.value}% → $batteryPct%")
                    _batteryLevel.value = batteryPct
                    
                    // 电池状态变化时重新调整更新间隔
                    restartBatchUpdates()
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("SmartBatchUpdateManager", "电池状态检测失败", e)
        }
    }

    /**
     * 重新启动批量更新任务
     */
    private fun restartBatchUpdates() {
        android.util.Log.d("SmartBatchUpdateManager", "🔄 重新调整批量更新间隔")
        startBatchUpdates()
    }

    /**
     * 判断是否应该处理普通更新
     */
    private fun shouldProcessNormalUpdates(): Boolean {
        return when (_networkState.value) {
            SmartNetworkState.WIFI -> true
            SmartNetworkState.MOBILE -> _batteryLevel.value > 30
            SmartNetworkState.WEAK -> _batteryLevel.value > 50
            SmartNetworkState.OFFLINE -> false
            SmartNetworkState.UNKNOWN -> _batteryLevel.value > 50
        }
    }

    /**
     * 处理批量更新
     */
    private suspend fun processBatchUpdates(updates: List<BatchUpdateData>, type: String) {
        android.util.Log.d("SmartBatchUpdateManager", "📤 处理${type}更新: ${updates.size}项")
        
        updates.forEach { update ->
            try {
                when (update.type) {
                    BatchUpdateType.PLAY_PROGRESS -> {
                        dataSyncManager.syncPlayProgress(update.data)
                    }
                    BatchUpdateType.WATCH_COUNT -> {
                        dataSyncManager.syncWatchCount(update.data)
                    }
                    BatchUpdateType.USER_BEHAVIOR -> {
                        dataSyncManager.syncUserBehavior(update.data)
                    }
                    BatchUpdateType.CACHE_STATUS -> {
                        dataSyncManager.syncCacheStatus(update.data)
                    }
                }
                android.util.Log.d("SmartBatchUpdateManager", "✅ ${type}更新成功: ${update.type}")
            } catch (e: Exception) {
                android.util.Log.e("SmartBatchUpdateManager", "❌ ${type}更新失败: ${update.type}", e)
                // 失败的更新重新加入队列
                addBatchUpdate("retry_${System.currentTimeMillis()}", update)
            }
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        // App进入后台时立即执行一次批量更新
        android.util.Log.d("SmartBatchUpdateManager", "📱 App进入后台，立即执行批量更新")
        scope.launch {
            executeBatchUpdate()
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        batchUpdateJob?.cancel()
        scope.cancel()
    }
}

/**
 * 批量更新数据
 */
data class BatchUpdateData(
    val type: BatchUpdateType,
    val data: Map<String, Any>,
    val priority: BatchPriority = BatchPriority.NORMAL,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 批量更新类型
 */
enum class BatchUpdateType {
    PLAY_PROGRESS,    // 播放进度
    WATCH_COUNT,      // 观看次数
    USER_BEHAVIOR,    // 用户行为
    CACHE_STATUS      // 缓存状态
}

/**
 * 批量更新优先级
 */
enum class BatchPriority {
    CRITICAL,  // 关键：立即处理
    NORMAL,    // 普通：正常批量处理
    LOW        // 低：WiFi环境下处理
}

/**
 * 智能网络状态（避免与DataLayerManager中的NetworkState冲突）
 */
enum class SmartNetworkState {
    WIFI,      // WiFi网络
    MOBILE,    // 移动网络
    WEAK,      // 弱网络
    OFFLINE,   // 离线
    UNKNOWN    // 未知
}
