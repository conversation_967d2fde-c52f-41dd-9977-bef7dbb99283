package com.shuimu.course.data.repository.update

import com.shuimu.course.data.local.dao.SeriesDao
import com.shuimu.course.data.local.dao.CategoryDao
import com.shuimu.course.data.local.dao.VideoDao
import com.shuimu.course.data.local.database.AppDatabase
import com.shuimu.course.domain.model.Series
import com.shuimu.course.data.toEntity
import androidx.room.withTransaction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 增量更新策略
 * 负责执行增量数据更新，保留用户数据，更新商业数据
 */
@Singleton
class IncrementalUpdateStrategy @Inject constructor(
    private val database: AppDatabase,
    private val seriesDao: SeriesDao,
    private val categoryDao: CategoryDao,
    private val videoDao: VideoDao
) {
    
    /**
     * 执行增量更新
     * @param diffResult 数据差异结果
     * @param serverData 服务器数据
     */
    suspend fun performIncrementalUpdate(
        diffResult: DataDiffResult,
        serverData: List<Series>
    ) = withContext(Dispatchers.IO) {
        
        android.util.Log.d("IncrementalUpdate", "🔄 开始执行增量更新")
        android.util.Log.d("IncrementalUpdate", "总变化数量: ${diffResult.getTotalChanges()}")
        
        database.withTransaction {
            // 1. 删除不存在的数据（按外键约束顺序）
            deleteRemovedData(diffResult)
            
            // 2. 更新变化的数据
            updateChangedData(diffResult, serverData)
            
            // 3. 插入新增的数据
            insertNewData(diffResult, serverData)
        }
        
        android.util.Log.d("IncrementalUpdate", "✅ 增量更新完成")
    }
    
    /**
     * 删除不存在的数据
     */
    private suspend fun deleteRemovedData(diffResult: DataDiffResult) {
        // 按外键约束顺序删除：视频 → 分类 → 系列
        
        // 1. 删除视频
        if (diffResult.deletedVideos.isNotEmpty()) {
            android.util.Log.d("IncrementalUpdate", "删除视频: ${diffResult.deletedVideos.size}个")
            diffResult.deletedVideos.forEach { videoId ->
                videoDao.deleteById(videoId)
            }
        }
        
        // 2. 删除分类
        if (diffResult.deletedCategories.isNotEmpty()) {
            android.util.Log.d("IncrementalUpdate", "删除分类: ${diffResult.deletedCategories.size}个")
            diffResult.deletedCategories.forEach { categoryId ->
                categoryDao.deleteById(categoryId)
            }
        }
        
        // 3. 删除系列
        if (diffResult.deletedSeries.isNotEmpty()) {
            android.util.Log.d("IncrementalUpdate", "删除系列: ${diffResult.deletedSeries.size}个")
            diffResult.deletedSeries.forEach { seriesId ->
                seriesDao.deleteById(seriesId)
            }
        }
    }
    
    /**
     * 更新变化的数据
     * 🔥 重要：购买状态以服务器为准，保留用户数据
     */
    private suspend fun updateChangedData(
        diffResult: DataDiffResult,
        serverData: List<Series>
    ) {
        // 1. 更新变化的系列
        updateChangedSeries(diffResult.changedSeries, serverData)
        
        // 2. 更新变化的分类
        updateChangedCategories(diffResult.changedCategories, serverData)
        
        // 3. 更新变化的视频
        updateChangedVideos(diffResult.changedVideos, serverData)
    }
    
    /**
     * 更新变化的系列
     * 🔥 购买状态以服务器为准
     */
    private suspend fun updateChangedSeries(
        changedSeriesIds: List<String>,
        serverData: List<Series>
    ) {
        if (changedSeriesIds.isEmpty()) return
        
        android.util.Log.d("IncrementalUpdate", "更新系列: ${changedSeriesIds.size}个")
        
        changedSeriesIds.forEach { seriesId ->
            val serverSeries = serverData.find { it.id == seriesId }
            serverSeries?.let { series ->
                // 🔥 关键：只更新服务器相关字段，保留用户数据
                seriesDao.updateSeriesInfo(
                    id = series.id,
                    title = series.title,
                    icon = series.icon,
                    price = series.price,
                    isFree = series.isFree,
                    isPurchased = series.isPurchased,  // 🔥 购买状态以服务器为准
                    isPackage = series.isPackage
                )
                android.util.Log.d("IncrementalUpdate", "更新系列: ${series.title}")
            }
        }
    }

    /**
     * 更新变化的分类
     * 🔥 购买状态以服务器为准
     */
    private suspend fun updateChangedCategories(
        changedCategoryIds: List<String>,
        serverData: List<Series>
    ) {
        if (changedCategoryIds.isEmpty()) return

        android.util.Log.d("IncrementalUpdate", "更新分类: ${changedCategoryIds.size}个")

        val allServerCategories = serverData.flatMap { it.categories }

        changedCategoryIds.forEach { categoryId ->
            val serverCategory = allServerCategories.find { it.id == categoryId }
            serverCategory?.let { category ->
                // 🔥 关键：只更新服务器相关字段
                categoryDao.updateCategoryInfo(
                    id = category.id,
                    title = category.title,
                    price = category.price,
                    isFree = category.isFree,
                    isPurchased = category.isPurchased  // 🔥 购买状态以服务器为准
                )
                android.util.Log.d("IncrementalUpdate", "更新分类: ${category.title}")
            }
        }
    }

    /**
     * 更新变化的视频
     * 🔥 保留用户的播放进度和观看次数
     */
    private suspend fun updateChangedVideos(
        changedVideoIds: List<String>,
        serverData: List<Series>
    ) {
        if (changedVideoIds.isEmpty()) return

        android.util.Log.d("IncrementalUpdate", "更新视频: ${changedVideoIds.size}个")

        val allServerVideos = serverData.flatMap { it.categories }.flatMap { it.videos }

        changedVideoIds.forEach { videoId ->
            val serverVideo = allServerVideos.find { it.id == videoId }
            serverVideo?.let { video ->
                // 🔥 关键：只更新服务器相关字段，保留用户数据
                videoDao.updateVideoInfo(
                    id = video.id,
                    title = video.title,
                    duration = video.duration,
                    description = video.description,
                    cloudUrl = video.cloudUrl
                    // 🔥 不更新：progress, watchCount, localPath, cacheStatus (用户数据)
                )
                android.util.Log.d("IncrementalUpdate", "更新视频: ${video.title}")
            }
        }
    }

    /**
     * 插入新增的数据
     */
    private suspend fun insertNewData(
        diffResult: DataDiffResult,
        serverData: List<Series>
    ) {
        // 按外键约束顺序插入：系列 → 分类 → 视频

        // 1. 插入新增的系列
        insertNewSeries(diffResult.newSeries, serverData)

        // 2. 插入新增的分类
        insertNewCategories(diffResult.newCategories, serverData)

        // 3. 插入新增的视频
        insertNewVideos(diffResult.newVideos, serverData)
    }

    /**
     * 插入新增的系列
     */
    private suspend fun insertNewSeries(
        newSeriesIds: List<String>,
        serverData: List<Series>
    ) {
        if (newSeriesIds.isEmpty()) return

        android.util.Log.d("IncrementalUpdate", "插入新系列: ${newSeriesIds.size}个")

        val newSeriesEntities = newSeriesIds.mapNotNull { seriesId ->
            serverData.find { it.id == seriesId }?.toEntity()
        }

        if (newSeriesEntities.isNotEmpty()) {
            seriesDao.insertSeries(newSeriesEntities)
        }
    }

    /**
     * 插入新增的分类
     */
    private suspend fun insertNewCategories(
        newCategoryIds: List<String>,
        serverData: List<Series>
    ) {
        if (newCategoryIds.isEmpty()) return

        android.util.Log.d("IncrementalUpdate", "插入新分类: ${newCategoryIds.size}个")

        val allServerCategories = serverData.flatMap { it.categories }
        val newCategoryEntities = newCategoryIds.mapNotNull { categoryId ->
            allServerCategories.find { it.id == categoryId }?.toEntity()
        }

        if (newCategoryEntities.isNotEmpty()) {
            categoryDao.insertCategories(newCategoryEntities)
        }
    }

    /**
     * 插入新增的视频
     */
    private suspend fun insertNewVideos(
        newVideoIds: List<String>,
        serverData: List<Series>
    ) {
        if (newVideoIds.isEmpty()) return

        android.util.Log.d("IncrementalUpdate", "插入新视频: ${newVideoIds.size}个")

        val allServerVideos = serverData.flatMap { it.categories }.flatMap { it.videos }
        val newVideoEntities = newVideoIds.mapNotNull { videoId ->
            allServerVideos.find { it.id == videoId }?.toEntity()
        }

        if (newVideoEntities.isNotEmpty()) {
            videoDao.insertVideos(newVideoEntities)
        }
    }
}
