package com.shuimu.course.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.shuimu.course.data.local.entities.PurchaseEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface PurchaseDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPurchases(purchases: List<PurchaseEntity>)

    @Query("SELECT * FROM purchases WHERE user_id = :userId")
    fun getPurchasesForUser(userId: String): Flow<List<PurchaseEntity>>

    @Query("DELETE FROM purchases")
    suspend fun clearAll()
} 