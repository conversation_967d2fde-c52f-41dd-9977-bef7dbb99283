package com.shuimu.course.data.repository

import com.shuimu.course.data.local.dao.VideoDao
import com.shuimu.course.data.manager.UserManager
import com.shuimu.course.data.remote.api.VideoApi
import com.shuimu.course.data.remote.api.UserDataApi

import com.shuimu.course.data.remote.dto.user.*
import com.shuimu.course.data.toEntity
import com.shuimu.course.data.toModel
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.VideoRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VideoRepositoryImpl @Inject constructor(
    private val videoApi: Video<PERSON>pi,
    private val userDataApi: UserData<PERSON><PERSON>,
    private val videoDao: VideoDao,
    private val userManager: UserManager
) : VideoRepository {

    override suspend fun getVideoById(videoId: String): Video? {
        return try {
            // 首先尝试从服务器获取
            val response = videoApi.getVideoDetails(videoId)
            if (response.isSuccessful && response.body() != null) {
                val videoDto = response.body()!!
                val video = videoDto.toModel()
                
                // 缓存到本地数据库
                videoDao.insertVideos(listOf(video.toEntity()))
                
                video
            } else {
                // 如果服务器返回错误，从本地数据库获取
                videoDao.getVideoById(videoId)?.toModel()
            }
        } catch (e: Exception) {
            // 如果网络失败，从本地数据库获取
            videoDao.getVideoById(videoId)?.toModel()
        }
    }

    override suspend fun updateVideoProgress(videoId: String, progress: Float) {
        try {
            // 获取当前用户ID
            val userId = userManager.getCurrentUserId()
            val currentUserId = userManager.getCurrentUserId()
            val isAdmin = userManager.isCurrentUserAdmin().toString()

            // 创建进度更新请求
            val progressRequest = UserProgressUpdateRequestDto(
                position = (progress * 100).toInt(), // 假设总长度为100
                progress = progress,
                lastWatchedAt = System.currentTimeMillis().toString()
            )

            // 使用新的RESTful用户数据API
            val response = userDataApi.updateUserVideoProgress(
                userId = userId,
                videoId = videoId,
                currentUserId = currentUserId,
                isAdmin = isAdmin,
                progressRequest = progressRequest
            )

            // 更新本地缓存
            val entity = videoDao.getVideoById(videoId)
            entity?.let {
                val updatedEntity = it.copy(
                    progress = progress
                )
                videoDao.insertVideos(listOf(updatedEntity))
            }
        } catch (e: Exception) {
            // 网络失败时只更新本地
            val entity = videoDao.getVideoById(videoId)
            entity?.let {
                val updatedEntity = it.copy(
                    progress = progress
                )
                videoDao.insertVideos(listOf(updatedEntity))
            }
        }
    }

    override suspend fun incrementWatchCount(videoId: String) {
        try {
            // 获取当前用户ID
            val userId = userManager.getCurrentUserId()
            val currentUserId = userManager.getCurrentUserId()
            val isAdmin = userManager.isCurrentUserAdmin().toString()

            // 获取当前观看次数
            val currentProgressResponse = userDataApi.getUserVideoProgress(
                userId = userId,
                videoId = videoId,
                currentUserId = currentUserId,
                isAdmin = isAdmin
            )

            val currentWatchCount = if (currentProgressResponse.isSuccessful) {
                currentProgressResponse.body()?.data?.watchCount ?: 0
            } else {
                0
            }

            // 创建观看次数更新请求
            val progressRequest = UserProgressUpdateRequestDto(
                watchCount = currentWatchCount + 1
            )

            // 使用新的RESTful用户数据API更新观看次数
            userDataApi.updateUserVideoProgress(
                userId = userId,
                videoId = videoId,
                currentUserId = currentUserId,
                isAdmin = isAdmin,
                progressRequest = progressRequest
            )
        } catch (e: Exception) {
            // 忽略网络错误，观看次数不是关键数据
        }
    }

    override fun getVideosForCategory(categoryId: String): Flow<List<Video>> {
        return videoDao.getVideosForCategory(categoryId).map { entities ->
            entities.map { it.toModel() }
        }
    }
    
    override suspend fun deleteCachedVideo(videoId: String): Result<Unit> {
        return try {
            // 1. 立即更新本地数据库（本地优先原则）
            videoDao.updateVideoCacheStatus(
                videoId = videoId,
                cacheStatus = CacheStatus.NOT_CACHED.value,
                localPath = null
            )

            // 2. Fire & Forget: 异步更新服务器状态
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    // 获取当前用户ID
                    val userId = userManager.getCurrentUserId()
                    val currentUserId = userManager.getCurrentUserId()
                    val isAdmin = userManager.isCurrentUserAdmin().toString()

                    // 创建缓存更新请求
                    val cacheRequest = UserCacheUpdateRequestDto(
                        isCached = false,
                        localPath = null
                    )

                    // 使用新的RESTful用户数据API
                    userDataApi.updateUserVideoCache(
                        userId = userId,
                        videoId = videoId,
                        currentUserId = currentUserId,
                        isAdmin = isAdmin,
                        cacheRequest = cacheRequest
                    )
                } catch (e: Exception) {
                    // 服务器更新失败不影响本地结果
                    // 可以在这里添加日志或重试机制
                }
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}