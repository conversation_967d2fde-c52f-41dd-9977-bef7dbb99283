package com.shuimu.course.data.local.dao

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Upsert
import com.shuimu.course.data.local.entities.CacheInfoEntity
import com.shuimu.course.data.local.entities.CacheStatus
import kotlinx.coroutines.flow.Flow

@Dao
interface CacheInfoDao {
    @Upsert
    suspend fun upsertCacheInfo(info: CacheInfoEntity)

    @Query("SELECT * FROM cache_info WHERE video_id = :videoId")
    fun getCacheInfoForVideo(videoId: String): Flow<CacheInfoEntity?>

    @Query("SELECT * FROM cache_info WHERE video_id = :videoId")
    suspend fun getCacheInfoSync(videoId: String): CacheInfoEntity?

    @Query("SELECT * FROM cache_info WHERE status = :status")
    fun getCachesByStatus(status: CacheStatus): Flow<List<CacheInfoEntity>>

    @Query("SELECT * FROM cache_info WHERE status = 'DOWNLOADED'")
    fun getAllCompletedCaches(): Flow<List<CacheInfoEntity>>

    @Query("SELECT * FROM cache_info WHERE status = 'DOWNLOADING'")
    fun getAllDownloadingCaches(): Flow<List<CacheInfoEntity>>

    @Query("SELECT * FROM cache_info WHERE status = 'PENDING'")
    fun getAllPendingCaches(): Flow<List<CacheInfoEntity>>

    @Query("SELECT COUNT(*) FROM cache_info WHERE status = 'DOWNLOADING'")
    suspend fun getDownloadingCount(): Int

    @Query("SELECT * FROM cache_info")
    fun getAllCacheInfo(): Flow<List<CacheInfoEntity>>

    @Query("UPDATE cache_info SET status = :status, progress = :progress, updated_at = :updatedAt WHERE video_id = :videoId")
    suspend fun updateDownloadProgress(videoId: String, status: CacheStatus, progress: Int, updatedAt: Long = System.currentTimeMillis())

    @Query("UPDATE cache_info SET status = :status, error_message = :errorMessage, updated_at = :updatedAt WHERE video_id = :videoId")
    suspend fun updateDownloadStatus(videoId: String, status: CacheStatus, errorMessage: String? = null, updatedAt: Long = System.currentTimeMillis())

    @Query("DELETE FROM cache_info WHERE video_id = :videoId")
    suspend fun deleteCacheInfo(videoId: String)

    @Query("DELETE FROM cache_info WHERE status = 'FAILED'")
    suspend fun clearFailedCaches()

    @Query("UPDATE cache_info SET progress = :progress, downloaded_bytes = :downloadedBytes, total_bytes = :totalBytes, updated_at = :updatedAt WHERE video_id = :videoId")
    suspend fun updateDownloadProgress(
        videoId: String,
        progress: Int,
        downloadedBytes: Long,
        totalBytes: Long,
        updatedAt: Long = System.currentTimeMillis()
    )
} 