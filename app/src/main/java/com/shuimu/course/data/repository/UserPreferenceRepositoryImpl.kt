package com.shuimu.course.data.repository

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import com.shuimu.course.domain.repository.UserPreferenceRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "user_preferences")

@Singleton
class UserPreferenceRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : UserPreferenceRepository {

    override suspend fun getExpandedState(id: String): Boolean? {
        val key = booleanPreferencesKey("expanded_$id")
        val preferences = context.dataStore.data.first()
        return if (preferences.contains(key)) preferences[key] else null
    }

    override suspend fun setExpandedState(id: String, isExpanded: Boolean) {
        val key = booleanPreferencesKey("expanded_$id")
        context.dataStore.edit { preferences ->
            preferences[key] = isExpanded
        }
    }

    override suspend fun getAllExpandedStates(): Map<String, Boolean> {
        val preferences = context.dataStore.data.first()
        return preferences.asMap()
            .filterKeys { it.name.startsWith("expanded_") }
            .mapKeys { it.key.name.removePrefix("expanded_") }
            .mapValues { it.value as Boolean }
    }

    override fun getAllExpandedStatesFlow(): Flow<Map<String, Boolean>> {
        return context.dataStore.data.map { preferences ->
            preferences.asMap()
                .filterKeys { it.name.startsWith("expanded_") }
                .mapKeys { it.key.name.removePrefix("expanded_") }
                .mapValues { it.value as Boolean }
        }
    }

    override suspend fun clearExpandedStates() {
        context.dataStore.edit { preferences ->
            val keysToRemove = preferences.asMap().keys.filter { it.name.startsWith("expanded_") }
            keysToRemove.forEach { key ->
                preferences.remove(key)
            }
        }
    }
} 