package com.shuimu.course.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.shuimu.course.data.local.entities.CategoryEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface CategoryDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategories(categories: List<CategoryEntity>)

    @Query("SELECT * FROM categories WHERE series_id = :seriesId")
    fun getCategoriesForSeries(seriesId: String): Flow<List<CategoryEntity>>

    @Query("DELETE FROM categories")
    suspend fun clearAll()

    // 🔥 增量更新方法：删除指定分类
    @Query("DELETE FROM categories WHERE id = :categoryId")
    suspend fun deleteById(categoryId: String)

    // 🔥 增量更新方法：更新分类信息（购买状态以服务器为准）
    @Query("""
        UPDATE categories SET
            title = :title,
            price = :price,
            is_free = :isFree,
            is_purchased = :isPurchased
        WHERE id = :id
    """)
    suspend fun updateCategoryInfo(
        id: String,
        title: String,
        price: Int?,
        isFree: Boolean,
        isPurchased: Boolean?
    )
} 