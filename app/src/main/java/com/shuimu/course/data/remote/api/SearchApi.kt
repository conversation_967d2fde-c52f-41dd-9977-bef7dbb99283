package com.shuimu.course.data.remote.api

import com.shuimu.course.data.remote.dto.SearchResultDto
import retrofit2.Response
import retrofit2.http.*

interface SearchApi {
    @GET("search/history")
    suspend fun getSearchHistory(): Response<List<String>>
    
    @POST("search/history")
    suspend fun addSearchHistory(@Body query: String): Response<Unit>
    
    @DELETE("search/history")
    suspend fun clearSearchHistory(): Response<Unit>
    
    @GET("search")
    suspend fun searchContent(@Query("q") query: String): Response<List<SearchResultDto>>
} 