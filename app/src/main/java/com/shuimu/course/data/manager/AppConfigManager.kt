package com.shuimu.course.data.manager

import android.content.Context
import android.content.SharedPreferences
import com.shuimu.course.data.remote.api.CacheApi
import com.shuimu.course.data.remote.dto.cache.toDomain
import com.shuimu.course.domain.repository.CacheConfig
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * App配置管理器 - 启动时预加载，使用过程中从内存获取
 */
@Singleton
class AppConfigManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val cacheApi: CacheApi
) {
    private val prefs: SharedPreferences = context.getSharedPreferences("app_config", Context.MODE_PRIVATE)
    private val mutex = Mutex()
    
    // 内存缓存
    private var cachedCacheConfig: CacheConfig? = null
    private var configLoadTime: Long = 0
    
    companion object {
        private const val KEY_CACHE_CONFIG = "cache_config"
        private const val KEY_CONFIG_TIMESTAMP = "config_timestamp"
        private const val CONFIG_CACHE_DURATION = 24 * 60 * 60 * 1000L // 24小时
        
        // 默认配置
        private val DEFAULT_CACHE_CONFIG = CacheConfig(
            maxConcurrentDownloads = 3,
            cacheDirectory = "/storage/emulated/0/VideoCache/",
            supportedFormats = listOf("mp4", "m3u8")
        )
    }
    
    /**
     * App启动时预加载所有配置
     */
    suspend fun preloadConfigs() = mutex.withLock {
        try {
            android.util.Log.d("AppConfigManager", "开始预加载配置")
            
            // 检查本地缓存是否有效
            val localConfig = getLocalCacheConfig()
            val lastUpdateTime = prefs.getLong(KEY_CONFIG_TIMESTAMP, 0)
            val isLocalValid = localConfig != null && 
                (System.currentTimeMillis() - lastUpdateTime) < CONFIG_CACHE_DURATION
            
            if (isLocalValid) {
                android.util.Log.d("AppConfigManager", "使用本地缓存配置")
                cachedCacheConfig = localConfig
                configLoadTime = System.currentTimeMillis()
                return@withLock
            }
            
            // 从服务器获取最新配置
            android.util.Log.d("AppConfigManager", "从服务器获取配置")
            val response = cacheApi.getCacheConfig()
            if (!response.isSuccessful || response.body() == null) {
                throw Exception("获取配置失败: ${response.message()}")
            }
            val remoteConfig = response.body()!!.toDomain()
            
            // 保存到本地和内存
            saveCacheConfigToLocal(remoteConfig)
            cachedCacheConfig = remoteConfig
            configLoadTime = System.currentTimeMillis()
            
            android.util.Log.d("AppConfigManager", "配置预加载完成: $remoteConfig")
            
        } catch (e: Exception) {
            android.util.Log.e("AppConfigManager", "配置预加载失败，使用默认配置", e)
            // 使用默认配置
            cachedCacheConfig = DEFAULT_CACHE_CONFIG
            configLoadTime = System.currentTimeMillis()
        }
    }
    
    /**
     * 获取缓存配置（从内存中获取，极快）
     */
    fun getCacheConfig(): CacheConfig {
        return cachedCacheConfig ?: run {
            android.util.Log.w("AppConfigManager", "配置未预加载，使用默认配置")
            DEFAULT_CACHE_CONFIG
        }
    }
    
    /**
     * 检查配置是否已加载
     */
    fun isConfigLoaded(): Boolean {
        return cachedCacheConfig != null
    }
    
    /**
     * 强制刷新配置（仅在必要时使用）
     */
    suspend fun refreshConfig() = mutex.withLock {
        try {
            val response = cacheApi.getCacheConfig()
            if (!response.isSuccessful || response.body() == null) {
                throw Exception("刷新配置失败: ${response.message()}")
            }
            val remoteConfig = response.body()!!.toDomain()
            saveCacheConfigToLocal(remoteConfig)
            cachedCacheConfig = remoteConfig
            configLoadTime = System.currentTimeMillis()
            android.util.Log.d("AppConfigManager", "配置刷新完成")
        } catch (e: Exception) {
            android.util.Log.e("AppConfigManager", "配置刷新失败", e)
        }
    }
    
    /**
     * 从本地存储读取配置
     */
    private fun getLocalCacheConfig(): CacheConfig? {
        return try {
            val configJson = prefs.getString(KEY_CACHE_CONFIG, null) ?: return null
            // 这里应该使用JSON解析，简化起见直接返回默认配置
            // 实际项目中应该使用Gson或Moshi解析
            DEFAULT_CACHE_CONFIG
        } catch (e: Exception) {
            android.util.Log.e("AppConfigManager", "读取本地配置失败", e)
            null
        }
    }
    
    /**
     * 保存配置到本地存储
     */
    private fun saveCacheConfigToLocal(config: CacheConfig) {
        try {
            // 这里应该使用JSON序列化，简化起见直接保存标记
            // 实际项目中应该使用Gson或Moshi序列化
            prefs.edit()
                .putString(KEY_CACHE_CONFIG, "cached") // 实际应该是JSON字符串
                .putLong(KEY_CONFIG_TIMESTAMP, System.currentTimeMillis())
                .apply()
        } catch (e: Exception) {
            android.util.Log.e("AppConfigManager", "保存本地配置失败", e)
        }
    }
}
