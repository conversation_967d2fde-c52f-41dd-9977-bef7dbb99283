package com.shuimu.course.data.repository.update

/**
 * 数据差异检测结果
 */
data class DataDiffResult(
    val hasChanges: Boolean,
    val changedSeries: List<String>,      // 变化的系列ID
    val changedCategories: List<String>,   // 变化的分类ID
    val changedVideos: List<String>,       // 变化的视频ID
    val deletedSeries: List<String>,       // 删除的系列ID
    val deletedCategories: List<String>,   // 删除的分类ID
    val deletedVideos: List<String>,       // 删除的视频ID
    val newSeries: List<String>,           // 新增的系列ID
    val newCategories: List<String>,       // 新增的分类ID
    val newVideos: List<String>            // 新增的视频ID
) {
    
    /**
     * 合并两个差异结果
     */
    fun merge(other: DataDiffResult): DataDiffResult {
        return DataDiffResult(
            hasChanges = this.hasChanges || other.hasChanges,
            changedSeries = (this.changedSeries + other.changedSeries).distinct(),
            changedCategories = (this.changedCategories + other.changedCategories).distinct(),
            changedVideos = (this.changedVideos + other.changedVideos).distinct(),
            deletedSeries = (this.deletedSeries + other.deletedSeries).distinct(),
            deletedCategories = (this.deletedCategories + other.deletedCategories).distinct(),
            deletedVideos = (this.deletedVideos + other.deletedVideos).distinct(),
            newSeries = (this.newSeries + other.newSeries).distinct(),
            newCategories = (this.newCategories + other.newCategories).distinct(),
            newVideos = (this.newVideos + other.newVideos).distinct()
        )
    }
    
    /**
     * 获取总变化数量
     */
    fun getTotalChanges(): Int {
        return changedSeries.size + changedCategories.size + changedVideos.size +
               deletedSeries.size + deletedCategories.size + deletedVideos.size +
               newSeries.size + newCategories.size + newVideos.size
    }
    
    companion object {
        fun empty(): DataDiffResult {
            return DataDiffResult(
                hasChanges = false,
                changedSeries = emptyList(),
                changedCategories = emptyList(),
                changedVideos = emptyList(),
                deletedSeries = emptyList(),
                deletedCategories = emptyList(),
                deletedVideos = emptyList(),
                newSeries = emptyList(),
                newCategories = emptyList(),
                newVideos = emptyList()
            )
        }
    }
}
