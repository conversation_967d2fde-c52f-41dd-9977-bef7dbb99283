package com.shuimu.course.data.remote.dto.user

import com.google.gson.annotations.SerializedName

/**
 * 用户相关的数据传输对象
 */

/**
 * 用户创建请求
 */
data class UserCreateRequestDto(
    val username: String,
    val email: String,
    val password: String,
    @SerializedName("display_name")
    val displayName: String? = null,
    val phone: String? = null,
    @SerializedName("is_admin")
    val isAdmin: Boolean = false
)

/**
 * 用户更新请求
 */
data class UserUpdateRequestDto(
    val username: String? = null,
    val email: String? = null,
    @SerializedName("display_name")
    val displayName: String? = null,
    val phone: String? = null,
    @SerializedName("avatar_url")
    val avatarUrl: String? = null,
    @SerializedName("is_active")
    val isActive: Boolean? = null,
    @SerializedName("is_admin")
    val isAdmin: Boolean? = null
)

/**
 * 用户资料更新请求
 */
data class UserProfileUpdateRequestDto(
    @SerializedName("display_name")
    val displayName: String? = null,
    val phone: String? = null,
    @SerializedName("avatar_url")
    val avatarUrl: String? = null
)

/**
 * 用户响应
 */
data class UserResponseDto(
    val success: Boolean,
    val data: UserDto,
    val message: String? = null
)

/**
 * 用户列表响应
 */
data class UserListResponseDto(
    val success: Boolean,
    val data: List<UserDto>,
    val pagination: PaginationDto,
    val message: String? = null
)

/**
 * 用户资料响应
 */
data class UserProfileResponseDto(
    val success: Boolean,
    val data: UserProfileDto,
    val message: String? = null
)

/**
 * 用户数据
 */
data class UserDto(
    val id: String,
    @SerializedName("user_id")
    val userId: String,
    val username: String,
    val email: String,
    @SerializedName("display_name")
    val displayName: String?,
    @SerializedName("avatar_url")
    val avatarUrl: String?,
    val phone: String?,
    @SerializedName("is_active")
    val isActive: Boolean,
    @SerializedName("is_admin")
    val isAdmin: Boolean,
    @SerializedName("last_login_at")
    val lastLoginAt: String?,
    @SerializedName("created_at")
    val createdAt: String,
    @SerializedName("updated_at")
    val updatedAt: String?
)

/**
 * 用户资料数据
 */
data class UserProfileDto(
    val id: String,
    @SerializedName("user_id")
    val userId: String,
    val username: String,
    val email: String,
    @SerializedName("display_name")
    val displayName: String?,
    @SerializedName("avatar_url")
    val avatarUrl: String?,
    val phone: String?,
    @SerializedName("is_active")
    val isActive: Boolean,
    @SerializedName("last_login_at")
    val lastLoginAt: String?,
    @SerializedName("created_at")
    val createdAt: String,
    val stats: UserStatsDto?
)

/**
 * 用户统计数据
 */
data class UserStatsDto(
    @SerializedName("watched_videos")
    val watchedVideos: Int,
    @SerializedName("purchased_series")
    val purchasedSeries: Int,
    @SerializedName("total_watch_count")
    val totalWatchCount: Int
)

/**
 * 分页数据
 */
data class PaginationDto(
    val page: Int,
    @SerializedName("page_size")
    val pageSize: Int,
    val total: Int,
    val pages: Int
)

/**
 * 通用响应
 */
data class ApiResponseDto<T>(
    val success: Boolean,
    val data: T? = null,
    val message: String? = null,
    val error: String? = null
)

/**
 * 错误响应
 */
data class ErrorResponseDto(
    val success: Boolean = false,
    val message: String,
    val error: String? = null,
    val code: Int? = null
)

// ========== 用户数据相关DTO ==========

/**
 * 用户进度更新请求
 */
data class UserProgressUpdateRequestDto(
    val position: Int? = null,
    val progress: Float? = null,
    val watchCount: Int? = null,
    val isCompleted: Boolean? = null,
    val lastWatchedAt: String? = null
)

/**
 * 用户缓存更新请求
 */
data class UserCacheUpdateRequestDto(
    val isCached: Boolean,
    val localPath: String? = null,
    val fileSize: Long? = null,
    val cachedAt: String? = null
)

/**
 * 用户收藏更新请求
 */
data class UserFavoritesUpdateRequestDto(
    val favoriteVideos: List<String>? = null,
    val favoriteCategories: List<String>? = null,
    val favoriteSeries: List<String>? = null
)

/**
 * 用户进度响应
 */
data class UserProgressResponseDto(
    val success: Boolean,
    val data: UserProgressDto,
    val message: String? = null
)

/**
 * 用户进度列表响应
 */
data class UserProgressListResponseDto(
    val success: Boolean,
    val data: List<UserProgressDto>,
    val total: Int,
    val message: String? = null
)

/**
 * 用户进度数据
 */
data class UserProgressDto(
    val userId: String,
    val videoId: String,
    val position: Int,
    val progress: Float,
    val watchCount: Int,
    val isCompleted: Boolean,
    val lastWatchedAt: String?,
    val videoTitle: String?,
    val videoDuration: Int?,
    val categoryTitle: String?,
    val seriesTitle: String?
)

/**
 * 用户缓存响应
 */
data class UserCacheResponseDto(
    val success: Boolean,
    val data: UserCacheDto,
    val message: String? = null
)

/**
 * 用户缓存列表响应
 */
data class UserCacheListResponseDto(
    val success: Boolean,
    val data: List<UserCacheDto>,
    val total: Int,
    val message: String? = null
)

/**
 * 用户缓存数据
 */
data class UserCacheDto(
    val userId: String,
    val videoId: String,
    val isCached: Boolean,
    val localPath: String?,
    val fileSize: Long?,
    val cachedAt: String?
)

/**
 * 用户收藏响应
 */
data class UserFavoritesResponseDto(
    val success: Boolean,
    val data: UserFavoritesDto,
    val message: String? = null
)

/**
 * 用户收藏数据
 */
data class UserFavoritesDto(
    val userId: String,
    val favoriteVideos: List<String>,
    val favoriteCategories: List<String>,
    val favoriteSeries: List<String>,
    val updatedAt: String
)

/**
 * 删除操作响应
 */
data class DeleteResponseDto(
    val success: Boolean,
    val message: String
)
