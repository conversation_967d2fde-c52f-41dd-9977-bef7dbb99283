package com.shuimu.course.data.manager

import android.content.Context
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 智能本地持久化管理器
 * 实现"关键数据立即保存，普通数据智能批量保存"的策略
 */
@Singleton
class SmartPersistenceManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val dataSyncManager: DataSyncManager
) : DefaultLifecycleObserver {

    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private val mutex = Mutex()
    
    // 待持久化的数据
    private val pendingChanges = ConcurrentHashMap<String, PendingChange>()
    
    // 自动保存任务
    private var autoSaveJob: Job? = null
    
    companion object {
        private const val AUTO_SAVE_INTERVAL = 30_000L // 30秒
        private const val USER_IDLE_THRESHOLD = 5 * 60 * 1000L // 5分钟
    }

    init {
        // 注册生命周期监听
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
        startAutoSave()
    }

    /**
     * 记录数据变化
     */
    fun recordChange(key: String, value: Any, priority: PersistencePriority) {
        scope.launch {
            when (priority) {
                PersistencePriority.IMMEDIATE -> {
                    // 立即持久化：购买、支付等关键操作
                    android.util.Log.d("SmartPersistence", "🔥 立即持久化: $key")
                    persistImmediately(key, value)
                }
                PersistencePriority.BATCHED -> {
                    // 批量持久化：播放进度等
                    android.util.Log.d("SmartPersistence", "📦 加入批量队列: $key")
                    pendingChanges[key] = PendingChange(
                        key = key,
                        value = value,
                        timestamp = System.currentTimeMillis(),
                        priority = priority
                    )
                }
            }
        }
    }

    /**
     * 立即持久化关键数据
     */
    private suspend fun persistImmediately(key: String, value: Any) = mutex.withLock {
        try {
            when {
                key.startsWith("purchase_") -> {
                    // 处理购买数据
                    savePurchaseData(key, value)
                }
                key.startsWith("payment_") -> {
                    // 处理支付数据
                    savePaymentData(key, value)
                }
                key.startsWith("progress_") -> {
                    // 处理播放进度（完成时）
                    saveProgressData(key, value)
                }
                else -> {
                    // 通用数据保存
                    saveGenericData(key, value)
                }
            }
            android.util.Log.d("SmartPersistence", "✅ 立即持久化完成: $key")
        } catch (e: Exception) {
            android.util.Log.e("SmartPersistence", "❌ 立即持久化失败: $key", e)
        }
    }

    /**
     * 批量持久化待保存数据
     */
    private suspend fun persistPendingChanges() = mutex.withLock {
        if (pendingChanges.isEmpty()) return@withLock
        
        try {
            android.util.Log.d("SmartPersistence", "📦 开始批量持久化: ${pendingChanges.size}项")
            
            val changesToProcess = pendingChanges.values.toList()
            pendingChanges.clear()
            
            changesToProcess.forEach { change ->
                try {
                    when {
                        change.key.startsWith("progress_") -> {
                            saveProgressData(change.key, change.value)
                        }
                        change.key.startsWith("cache_") -> {
                            saveCacheData(change.key, change.value)
                        }
                        change.key.startsWith("preference_") -> {
                            savePreferenceData(change.key, change.value)
                        }
                        else -> {
                            saveGenericData(change.key, change.value)
                        }
                    }
                } catch (e: Exception) {
                    android.util.Log.e("SmartPersistence", "批量保存失败: ${change.key}", e)
                }
            }
            
            android.util.Log.d("SmartPersistence", "✅ 批量持久化完成")
            
        } catch (e: Exception) {
            android.util.Log.e("SmartPersistence", "❌ 批量持久化失败", e)
        }
    }

    /**
     * 启动自动保存
     */
    private fun startAutoSave() {
        autoSaveJob?.cancel()
        autoSaveJob = scope.launch {
            while (isActive) {
                delay(AUTO_SAVE_INTERVAL)
                
                if (pendingChanges.isNotEmpty()) {
                    android.util.Log.d("SmartPersistence", "⏰ 定时自动保存触发")
                    persistPendingChanges()
                }
            }
        }
    }

    /**
     * 停止自动保存
     */
    private fun stopAutoSave() {
        autoSaveJob?.cancel()
        autoSaveJob = null
    }

    // 生命周期回调
    override fun onStart(owner: LifecycleOwner) {
        android.util.Log.d("SmartPersistence", "🟢 App进入前台，启动自动保存")
        startAutoSave()
    }

    override fun onStop(owner: LifecycleOwner) {
        android.util.Log.d("SmartPersistence", "🔴 App进入后台，立即保存并停止自动保存")
        scope.launch {
            persistPendingChanges()
            stopAutoSave()
        }
    }

    /**
     * 用户操作触发的保存
     */
    fun onUserAction(action: UserAction) {
        scope.launch {
            when (action) {
                UserAction.VIDEO_PAUSED -> {
                    android.util.Log.d("SmartPersistence", "⏸️ 视频暂停，保存进度")
                    persistPendingChanges()
                }
                UserAction.SCREEN_CHANGED -> {
                    android.util.Log.d("SmartPersistence", "🔄 页面切换，保存状态")
                    persistPendingChanges()
                }
                UserAction.USER_IDLE -> {
                    android.util.Log.d("SmartPersistence", "😴 用户空闲，保存数据")
                    persistPendingChanges()
                }
            }
        }
    }

    // 具体的数据保存方法
    private suspend fun savePurchaseData(key: String, value: Any) {
        // 实现购买数据保存逻辑
        android.util.Log.d("SmartPersistence", "💳 保存购买数据: $key")
    }

    private suspend fun savePaymentData(key: String, value: Any) {
        // 实现支付数据保存逻辑
        android.util.Log.d("SmartPersistence", "💰 保存支付数据: $key")
    }

    private suspend fun saveProgressData(key: String, value: Any) {
        // 实现播放进度保存逻辑
        android.util.Log.d("SmartPersistence", "📊 保存播放进度: $key")
    }

    private suspend fun saveCacheData(key: String, value: Any) {
        // 实现缓存数据保存逻辑
        android.util.Log.d("SmartPersistence", "💾 保存缓存数据: $key")
    }

    private suspend fun savePreferenceData(key: String, value: Any) {
        // 实现偏好设置保存逻辑
        android.util.Log.d("SmartPersistence", "⚙️ 保存偏好设置: $key")
    }

    private suspend fun saveGenericData(key: String, value: Any) {
        // 实现通用数据保存逻辑
        android.util.Log.d("SmartPersistence", "📄 保存通用数据: $key")
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        scope.launch {
            persistPendingChanges()
            scope.cancel()
        }
    }
}

/**
 * 待持久化的数据项
 */
data class PendingChange(
    val key: String,
    val value: Any,
    val timestamp: Long,
    val priority: PersistencePriority
)

/**
 * 持久化优先级
 */
enum class PersistencePriority {
    IMMEDIATE,  // 立即持久化：购买、支付等关键操作
    BATCHED     // 批量持久化：播放进度、缓存状态等
}

/**
 * 用户操作类型
 */
enum class UserAction {
    VIDEO_PAUSED,    // 视频暂停
    SCREEN_CHANGED,  // 页面切换
    USER_IDLE        // 用户空闲
}
