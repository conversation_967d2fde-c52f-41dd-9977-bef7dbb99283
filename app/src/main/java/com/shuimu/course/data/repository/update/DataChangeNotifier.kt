package com.shuimu.course.data.repository.update

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据变化通知器
 * 负责管理和分发数据变化事件
 */
@Singleton
class DataChangeNotifier @Inject constructor() {
    
    // 数据变化事件流
    private val _dataChanges = MutableSharedFlow<DataChangeEvent>(
        replay = 0,  // 不重播历史事件
        extraBufferCapacity = 10  // 缓冲区大小
    )
    
    /**
     * 数据变化事件流（只读）
     */
    val dataChanges: SharedFlow<DataChangeEvent> = _dataChanges.asSharedFlow()
    
    /**
     * 通知数据变化
     * @param event 数据变化事件
     */
    suspend fun notifyDataChanged(event: DataChangeEvent) {
        android.util.Log.d("DataChangeNotifier", "📢 通知数据变化: ${event}")
        
        try {
            _dataChanges.emit(event)
            android.util.Log.d("DataChangeNotifier", "✅ 数据变化通知发送成功")
        } catch (e: Exception) {
            android.util.Log.e("DataChangeNotifier", "❌ 数据变化通知发送失败", e)
        }
    }
    
    /**
     * 批量通知数据变化
     * @param events 数据变化事件列表
     */
    suspend fun notifyDataChanges(events: List<DataChangeEvent>) {
        android.util.Log.d("DataChangeNotifier", "📢 批量通知数据变化: ${events.size}个事件")
        
        events.forEach { event ->
            try {
                _dataChanges.emit(event)
            } catch (e: Exception) {
                android.util.Log.e("DataChangeNotifier", "❌ 批量通知中的事件发送失败: $event", e)
            }
        }
        
        android.util.Log.d("DataChangeNotifier", "✅ 批量数据变化通知完成")
    }
    
    /**
     * 根据差异结果生成并通知相应的数据变化事件
     * @param diffResult 数据差异结果
     */
    suspend fun notifyFromDiffResult(diffResult: DataDiffResult) {
        android.util.Log.d("DataChangeNotifier", "🔍 根据差异结果生成通知")
        
        val events = mutableListOf<DataChangeEvent>()
        
        // 1. 处理修改的数据
        if (diffResult.changedSeries.isNotEmpty()) {
            events.add(DataChangeEvent.SeriesUpdated(
                seriesIds = diffResult.changedSeries,
                changeType = DataChangeEvent.ChangeType.MODIFIED
            ))
        }
        
        if (diffResult.changedCategories.isNotEmpty()) {
            events.add(DataChangeEvent.CategoriesUpdated(
                categoryIds = diffResult.changedCategories,
                changeType = DataChangeEvent.ChangeType.MODIFIED
            ))
        }
        
        if (diffResult.changedVideos.isNotEmpty()) {
            events.add(DataChangeEvent.VideosUpdated(
                videoIds = diffResult.changedVideos,
                changeType = DataChangeEvent.ChangeType.MODIFIED
            ))
        }
        
        // 2. 处理新增的数据
        if (diffResult.newSeries.isNotEmpty() || 
            diffResult.newCategories.isNotEmpty() || 
            diffResult.newVideos.isNotEmpty()) {
            events.add(DataChangeEvent.DataAdded(diffResult))
        }
        
        // 3. 处理删除的数据
        if (diffResult.deletedSeries.isNotEmpty() || 
            diffResult.deletedCategories.isNotEmpty() || 
            diffResult.deletedVideos.isNotEmpty()) {
            events.add(DataChangeEvent.DataDeleted(diffResult))
        }
        
        // 4. 特别处理购买状态更新
        // 🔥 购买状态变化需要特别通知，因为涉及商业逻辑
        val purchaseRelatedSeries = diffResult.changedSeries
        val purchaseRelatedCategories = diffResult.changedCategories
        
        if (purchaseRelatedSeries.isNotEmpty() || purchaseRelatedCategories.isNotEmpty()) {
            events.add(DataChangeEvent.PurchaseStatusUpdated(
                seriesIds = purchaseRelatedSeries,
                categoryIds = purchaseRelatedCategories
            ))
        }
        
        // 5. 发送所有事件
        if (events.isNotEmpty()) {
            notifyDataChanges(events)
        } else {
            android.util.Log.d("DataChangeNotifier", "📊 无数据变化，跳过通知")
        }
    }
    
    /**
     * 通知需要完全刷新
     * 当数据变化过大或出现异常时使用
     */
    suspend fun notifyFullRefreshNeeded() {
        android.util.Log.w("DataChangeNotifier", "🔄 通知需要完全刷新数据")
        notifyDataChanged(DataChangeEvent.FullRefreshNeeded)
    }
}
