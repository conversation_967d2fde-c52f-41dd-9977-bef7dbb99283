package com.shuimu.course.data.manager

import android.content.Context
import android.content.SharedPreferences
import com.shuimu.course.data.remote.api.PaymentApi
import com.shuimu.course.data.remote.api.CacheApi
import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.repository.CacheRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据同步管理器 - 实现离线优先策略
 * 1. 购买完成后立即同步
 * 2. App关闭时批量同步所有数据
 */
@Singleton
class DataSyncManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val paymentApi: PaymentApi,
    private val cacheApi: CacheApi,
    private val cacheRepository: CacheRepository
) {
    private val prefs: SharedPreferences = context.getSharedPreferences("data_sync", Context.MODE_PRIVATE)
    private val mutex = Mutex()
    private val gson = Gson()
    
    companion object {
        private const val KEY_PENDING_PURCHASES = "pending_purchases"
        private const val KEY_PENDING_CACHE_OPERATIONS = "pending_cache_operations"
        private const val KEY_PENDING_WATCH_HISTORY = "pending_watch_history"
        private const val KEY_LAST_SYNC_TIME = "last_sync_time"
    }
    
    /**
     * 购买完成后立即同步到服务器
     */
    suspend fun syncPurchaseImmediately(
        itemId: String,
        itemType: String,
        amount: Float,
        orderId: String
    ): Result<Unit> = mutex.withLock {
        return try {
            android.util.Log.d("DataSyncManager", "立即同步购买数据: $itemId")
            
            // 尝试同步到服务器
            val purchaseData = PurchaseData(
                itemId = itemId,
                itemType = itemType,
                amount = amount,
                orderId = orderId,
                timestamp = System.currentTimeMillis()
            )
            
            // 这里应该调用购买同步API，暂时模拟成功
            // val response = paymentApi.syncPurchase(purchaseData)
            
            android.util.Log.d("DataSyncManager", "购买数据同步成功")
            Result.success(Unit)
            
        } catch (e: Exception) {
            android.util.Log.e("DataSyncManager", "购买数据同步失败，保存到本地队列", e)
            
            // 同步失败，保存到本地队列
            savePendingPurchase(
                PurchaseData(
                    itemId = itemId,
                    itemType = itemType,
                    amount = amount,
                    orderId = orderId,
                    timestamp = System.currentTimeMillis()
                )
            )
            
            Result.failure(e)
        }
    }
    
    /**
     * 缓存操作完成后保存到本地队列（不立即同步）
     */
    suspend fun queueCacheOperation(
        videoId: String,
        operation: String, // "DOWNLOAD_SUCCESS", "DOWNLOAD_FAILED", "DELETE"
        localPath: String? = null
    ) = mutex.withLock {
        try {
            android.util.Log.d("DataSyncManager", "缓存操作加入队列: $videoId - $operation")
            
            val cacheOperation = CacheOperationData(
                videoId = videoId,
                operation = operation,
                localPath = localPath,
                timestamp = System.currentTimeMillis()
            )
            
            savePendingCacheOperation(cacheOperation)
            
        } catch (e: Exception) {
            android.util.Log.e("DataSyncManager", "保存缓存操作失败", e)
        }
    }
    
    /**
     * 观看记录加入队列（不立即同步）
     */
    suspend fun queueWatchHistory(
        videoId: String,
        watchDuration: Long,
        progress: Float,
        completed: Boolean
    ) = mutex.withLock {
        try {
            android.util.Log.d("DataSyncManager", "观看记录加入队列: $videoId")
            
            val watchData = WatchHistoryData(
                videoId = videoId,
                watchDuration = watchDuration,
                progress = progress,
                completed = completed,
                timestamp = System.currentTimeMillis()
            )
            
            savePendingWatchHistory(watchData)
            
        } catch (e: Exception) {
            android.util.Log.e("DataSyncManager", "保存观看记录失败", e)
        }
    }
    
    /**
     * App关闭时批量同步所有数据
     */
    suspend fun syncAllPendingData(): Result<SyncResult> = mutex.withLock {
        return try {
            android.util.Log.d("DataSyncManager", "开始批量同步所有数据")
            
            val syncResult = SyncResult()
            
            // 1. 同步待处理的购买记录
            val pendingPurchases = getPendingPurchases()
            if (pendingPurchases.isNotEmpty()) {
                android.util.Log.d("DataSyncManager", "同步${pendingPurchases.size}个购买记录")
                // 这里应该调用批量购买同步API
                // val result = paymentApi.batchSyncPurchases(pendingPurchases)
                clearPendingPurchases()
                syncResult.purchasesSynced = pendingPurchases.size
            }
            
            // 2. 同步缓存操作记录
            val pendingCacheOps = getPendingCacheOperations()
            if (pendingCacheOps.isNotEmpty()) {
                android.util.Log.d("DataSyncManager", "同步${pendingCacheOps.size}个缓存操作")
                // 转换为服务器需要的格式并批量同步
                val cacheUpdates = pendingCacheOps.map { op ->
                    com.shuimu.course.data.remote.dto.cache.CacheStateUpdateDto(
                        videoId = op.videoId,
                        isCached = op.operation == "DOWNLOAD_SUCCESS",
                        localPath = op.localPath,
                        deviceId = null
                    )
                }
                
                try {
                    val response = cacheApi.batchSyncCacheStates(cacheUpdates)
                    if (response.isSuccessful) {
                        clearPendingCacheOperations()
                        syncResult.cacheOperationsSynced = pendingCacheOps.size
                    }
                } catch (e: Exception) {
                    android.util.Log.e("DataSyncManager", "缓存操作同步失败", e)
                }
            }
            
            // 3. 同步观看记录
            val pendingWatchHistory = getPendingWatchHistory()
            if (pendingWatchHistory.isNotEmpty()) {
                android.util.Log.d("DataSyncManager", "同步${pendingWatchHistory.size}个观看记录")
                // 这里应该调用观看记录同步API
                // val result = videoApi.batchSyncWatchHistory(pendingWatchHistory)
                clearPendingWatchHistory()
                syncResult.watchHistorySynced = pendingWatchHistory.size
            }
            
            // 更新最后同步时间
            prefs.edit().putLong(KEY_LAST_SYNC_TIME, System.currentTimeMillis()).apply()
            
            android.util.Log.d("DataSyncManager", "批量同步完成: $syncResult")
            Result.success(syncResult)
            
        } catch (e: Exception) {
            android.util.Log.e("DataSyncManager", "批量同步失败", e)
            Result.failure(e)
        }
    }
    
    /**
     * 获取同步统计信息
     */
    fun getSyncStats(): SyncStats {
        return SyncStats(
            pendingPurchases = getPendingPurchases().size,
            pendingCacheOperations = getPendingCacheOperations().size,
            pendingWatchHistory = getPendingWatchHistory().size,
            lastSyncTime = prefs.getLong(KEY_LAST_SYNC_TIME, 0)
        )
    }

    /**
     * 🔥 新增：同步播放进度
     */
    suspend fun syncPlayProgress(data: Map<String, Any>): Result<Unit> {
        return try {
            android.util.Log.d("DataSyncManager", "📊 同步播放进度: ${data["videoId"]}")
            // TODO: 实际的API调用
            // videoApi.updateProgress(data)
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("DataSyncManager", "播放进度同步失败", e)
            Result.failure(e)
        }
    }

    /**
     * 🔥 新增：同步观看次数
     */
    suspend fun syncWatchCount(data: Map<String, Any>): Result<Unit> {
        return try {
            android.util.Log.d("DataSyncManager", "👁️ 同步观看次数: ${data["videoId"]}")
            // TODO: 实际的API调用
            // videoApi.updateWatchCount(data)
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("DataSyncManager", "观看次数同步失败", e)
            Result.failure(e)
        }
    }

    /**
     * 🔥 新增：立即同步观看次数
     */
    suspend fun syncWatchCountImmediately(data: Map<String, Any>): Result<Unit> {
        return try {
            android.util.Log.d("DataSyncManager", "🚀 立即同步观看次数: ${data["videoId"]}")
            // TODO: 实际的API调用
            // videoApi.updateWatchCountImmediate(data)
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("DataSyncManager", "立即观看次数同步失败", e)
            Result.failure(e)
        }
    }

    /**
     * 🔥 新增：同步用户行为
     */
    suspend fun syncUserBehavior(data: Map<String, Any>): Result<Unit> {
        return try {
            android.util.Log.d("DataSyncManager", "👤 同步用户行为")
            // TODO: 实际的API调用
            // userApi.updateBehavior(data)
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("DataSyncManager", "用户行为同步失败", e)
            Result.failure(e)
        }
    }

    /**
     * 🔥 新增：同步缓存状态
     */
    suspend fun syncCacheStatus(data: Map<String, Any>): Result<Unit> {
        return try {
            android.util.Log.d("DataSyncManager", "💾 同步缓存状态: ${data["videoId"]}")
            // TODO: 实际的API调用
            // cacheApi.updateStatus(data)
            Result.success(Unit)
        } catch (e: Exception) {
            android.util.Log.e("DataSyncManager", "缓存状态同步失败", e)
            Result.failure(e)
        }
    }
    
    // 私有方法：本地存储操作
    private fun savePendingPurchase(purchase: PurchaseData) {
        val purchases = getPendingPurchases().toMutableList()
        purchases.add(purchase)
        val json = gson.toJson(purchases)
        prefs.edit().putString(KEY_PENDING_PURCHASES, json).apply()
    }

    private fun getPendingPurchases(): List<PurchaseData> {
        val json = prefs.getString(KEY_PENDING_PURCHASES, null) ?: return emptyList()
        return try {
            val type = object : TypeToken<List<PurchaseData>>() {}.type
            gson.fromJson(json, type)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    private fun clearPendingPurchases() {
        prefs.edit().remove(KEY_PENDING_PURCHASES).apply()
    }
    
    private fun savePendingCacheOperation(operation: CacheOperationData) {
        val operations = getPendingCacheOperations().toMutableList()
        operations.add(operation)
        val json = gson.toJson(operations)
        prefs.edit().putString(KEY_PENDING_CACHE_OPERATIONS, json).apply()
    }

    private fun getPendingCacheOperations(): List<CacheOperationData> {
        val json = prefs.getString(KEY_PENDING_CACHE_OPERATIONS, null) ?: return emptyList()
        return try {
            val type = object : TypeToken<List<CacheOperationData>>() {}.type
            gson.fromJson(json, type)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    private fun clearPendingCacheOperations() {
        prefs.edit().remove(KEY_PENDING_CACHE_OPERATIONS).apply()
    }
    
    private fun savePendingWatchHistory(watchData: WatchHistoryData) {
        val history = getPendingWatchHistory().toMutableList()
        history.add(watchData)
        val json = gson.toJson(history)
        prefs.edit().putString(KEY_PENDING_WATCH_HISTORY, json).apply()
    }

    private fun getPendingWatchHistory(): List<WatchHistoryData> {
        val json = prefs.getString(KEY_PENDING_WATCH_HISTORY, null) ?: return emptyList()
        return try {
            val type = object : TypeToken<List<WatchHistoryData>>() {}.type
            gson.fromJson(json, type)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    private fun clearPendingWatchHistory() {
        prefs.edit().remove(KEY_PENDING_WATCH_HISTORY).apply()
    }
}

// 数据类定义
data class PurchaseData(
    val itemId: String,
    val itemType: String,
    val amount: Float,
    val orderId: String,
    val timestamp: Long
)

data class CacheOperationData(
    val videoId: String,
    val operation: String,
    val localPath: String? = null,
    val timestamp: Long
)

data class WatchHistoryData(
    val videoId: String,
    val watchDuration: Long,
    val progress: Float,
    val completed: Boolean,
    val timestamp: Long
)

data class SyncResult(
    var purchasesSynced: Int = 0,
    var cacheOperationsSynced: Int = 0,
    var watchHistorySynced: Int = 0
)

data class SyncStats(
    val pendingPurchases: Int,
    val pendingCacheOperations: Int,
    val pendingWatchHistory: Int,
    val lastSyncTime: Long
)


