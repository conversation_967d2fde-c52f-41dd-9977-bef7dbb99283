package com.shuimu.course.data.workers

import android.content.Context
import androidx.work.*
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.CacheRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.channels.awaitClose
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 视频下载管理器
 * 负责统一管理所有下载任务
 */
@Singleton
class DownloadManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val cacheRepository: CacheRepository,
    private val appConfigManager: com.shuimu.course.data.manager.AppConfigManager,
    private val dataLayerManager: com.shuimu.course.data.manager.DataLayerManager
    // 暂时注释掉以解决编译问题
    // private val smartDownloadScheduler: SmartDownloadScheduler,
    // private val storageManager: com.shuimu.course.data.storage.StorageManager,
    // private val downloadErrorHandler: com.shuimu.course.data.error.DownloadErrorHandler
) {

    private val workManager = WorkManager.getInstance(context)

    /**
     * 启动视频下载（简化版本）
     */
    suspend fun startDownload(videoId: String, cloudUrl: String, videoTitle: String = "", priority: Int = 50, estimatedSizeBytes: Long = 100 * 1024 * 1024): Result<String> {
        return try {
            android.util.Log.d("DownloadManager", "=== 开始下载 ===")
            android.util.Log.d("DownloadManager", "videoId: $videoId")
            android.util.Log.d("DownloadManager", "cloudUrl: $cloudUrl")
            android.util.Log.d("DownloadManager", "videoTitle: $videoTitle")

            // 检查当前状态
            val existingCache = cacheRepository.getCacheInfoSync(videoId)
            android.util.Log.d("DownloadManager", "现有缓存状态: ${existingCache?.status}")

            if (existingCache?.status == CacheStatus.DOWNLOADED) {
                android.util.Log.d("DownloadManager", "视频已缓存，跳过下载")
                return Result.failure(Exception("视频已缓存"))
            }

            if (existingCache?.status == CacheStatus.DOWNLOADING) {
                android.util.Log.d("DownloadManager", "视频正在下载中，跳过下载")
                return Result.failure(Exception("视频正在下载中"))
            }

            // 从预加载的配置中获取（极快，无网络请求）
            val config = appConfigManager.getCacheConfig()
            val maxConcurrentDownloads = config.maxConcurrentDownloads
            android.util.Log.d("DownloadManager", "最大并发下载数: $maxConcurrentDownloads（来自预加载配置）")

            // 检查当前下载数量
            val currentDownloadingCount = cacheRepository.getDownloadingCount()
            android.util.Log.d("DownloadManager", "当前下载数量: $currentDownloadingCount")

            if (currentDownloadingCount >= maxConcurrentDownloads) {
                android.util.Log.d("DownloadManager", "达到最大并发数，加入队列")
                // 达到最大并发数，加入待下载队列
                cacheRepository.updateDownloadStatus(videoId, CacheStatus.PENDING)

                // 创建缓存记录
                val pendingCache = com.shuimu.course.domain.model.CachedVideo(
                    videoId = videoId,
                    title = videoTitle,
                    localPath = null,
                    downloadedBytes = 0L,
                    totalBytes = 0L,
                    status = CacheStatus.PENDING,
                    progress = 0,
                    cloudUrl = cloudUrl,
                    updatedAt = System.currentTimeMillis(),
                    priority = 0,
                    retryCount = 0,
                    lastRetryTime = 0L
                )
                cacheRepository.upsertCacheInfo(pendingCache)

                Result.success("已加入下载队列")
            } else {
                android.util.Log.d("DownloadManager", "可以立即开始下载")
                // 可以立即开始下载
                val downloadRequest = VideoDownloadWorker.createDownloadRequest(
                    videoId = videoId,
                    cloudUrl = cloudUrl,
                    videoTitle = videoTitle
                )

                android.util.Log.d("DownloadManager", "创建WorkRequest: ${downloadRequest.id}")

                // 创建下载中的缓存记录
                val downloadingCache = com.shuimu.course.domain.model.CachedVideo(
                    videoId = videoId,
                    title = videoTitle,
                    localPath = null,
                    downloadedBytes = 0L,
                    totalBytes = 0L,
                    status = CacheStatus.DOWNLOADING,
                    progress = 0,
                    cloudUrl = cloudUrl,
                    updatedAt = System.currentTimeMillis(),
                    priority = 0,
                    retryCount = 0,
                    lastRetryTime = 0L
                )
                cacheRepository.upsertCacheInfo(downloadingCache)

                // 提交下载任务
                workManager.enqueue(downloadRequest)
                android.util.Log.d("DownloadManager", "WorkManager任务已提交")

                Result.success("开始下载")
            }
        } catch (e: Exception) {
            android.util.Log.e("DownloadManager", "下载启动失败", e)
            Result.failure(e)
        }
    }

    /**
     * 设置下载优先级（暂时不支持）
     */
    suspend fun setDownloadPriority(videoId: String, priority: Int): Result<Unit> {
        return Result.success(Unit) // 暂时不支持
    }

    /**
     * 取消下载
     */
    suspend fun cancelDownload(videoId: String): Result<Unit> {
        return try {
            // 取消 WorkManager 任务
            workManager.cancelAllWorkByTag("video_$videoId")

            // 更新状态
            cacheRepository.updateDownloadStatus(videoId, CacheStatus.NOT_CACHED)

            // 删除缓存记录
            cacheRepository.deleteCacheInfo(videoId)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 暂停下载
     */
    suspend fun pauseDownload(videoId: String): Result<Unit> {
        return try {
            // 取消 WorkManager 任务
            workManager.cancelAllWorkByTag("video_$videoId")

            // 更新状态为暂停
            cacheRepository.updateDownloadStatus(videoId, CacheStatus.PAUSED)

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 恢复下载
     */
    suspend fun resumeDownload(videoId: String): Result<Unit> {
        return try {
            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            if (cacheInfo == null) {
                return Result.failure(Exception("缓存信息不存在"))
            }

            // 重新启动下载
            startDownload(videoId, cacheInfo.cloudUrl ?: "", cacheInfo.title)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // 暂时注释掉这些方法，因为依赖的类还有编译问题
    /*
    suspend fun getStorageInfo(): com.shuimu.course.data.storage.StorageManager.StorageInfo {
        return storageManager.getStorageInfo()
    }

    suspend fun cleanupCache(): Result<com.shuimu.course.data.storage.StorageManager.CleanupResult> {
        return storageManager.autoCleanupIfNeeded()
    }

    suspend fun performLRUCleanup(targetSizeBytes: Long? = null): Result<com.shuimu.course.data.storage.StorageManager.CleanupResult> {
        return storageManager.performLRUCleanup(targetSizeBytes)
    }

    suspend fun cleanupInvalidCaches(): Result<com.shuimu.course.data.storage.StorageManager.CleanupResult> {
        return storageManager.cleanupInvalidCaches()
    }

    suspend fun batchRetryFailedDownloads(): Result<com.shuimu.course.data.error.DownloadErrorHandler.BatchRetryResult> {
        return downloadErrorHandler.batchRetryFailedDownloads()
    }

    suspend fun getErrorStatistics(): com.shuimu.course.data.error.DownloadErrorHandler.ErrorStatistics {
        return downloadErrorHandler.getErrorStatistics()
    }

    suspend fun retryFailedDownload(videoId: String): Result<Unit> {
        return downloadErrorHandler.executeRetry(videoId) {
            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            if (cacheInfo != null) {
                smartDownloadScheduler.addDownloadTask(
                    videoId = cacheInfo.videoId,
                    cloudUrl = cacheInfo.cloudUrl ?: "",
                    videoTitle = cacheInfo.title,
                    priority = cacheInfo.priority
                )
            } else {
                Result.failure(Exception("缓存信息不存在"))
            }
        }
    }
    */

    /**
     * 重试下载
     */
    suspend fun retryDownload(videoId: String): Result<String> {
        return try {
            val cacheInfo = cacheRepository.getCacheInfoSync(videoId)
            if (cacheInfo == null) {
                return Result.failure(Exception("缓存信息不存在"))
            }

            // 清理失败状态
            cacheRepository.updateDownloadStatus(videoId, CacheStatus.NOT_CACHED)
            
            // 重新启动下载
            startDownload(videoId, cacheInfo.cloudUrl ?: "", cacheInfo.title)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 获取下载进度
     */
    fun getDownloadProgress(videoId: String): Flow<WorkInfo?> {
        return workManager.getWorkInfosByTagLiveData("video_$videoId")
            .asFlow()
            .map { workInfoList ->
                workInfoList.firstOrNull { 
                    it.state == WorkInfo.State.RUNNING || it.state == WorkInfo.State.ENQUEUED 
                }
            }
    }

    /**
     * 获取所有下载任务状态
     */
    fun getAllDownloadStatus(): Flow<List<WorkInfo>> {
        return workManager.getWorkInfosByTagLiveData("video_download")
            .asFlow()
            .map { it }
    }

    /**
     * 清理所有失败的下载任务
     */
    suspend fun clearFailedDownloads(): Result<Unit> {
        return try {
            // 取消所有失败的 Work
            workManager.cancelAllWorkByTag("video_download")
            
            // 清理数据库中的失败记录
            cacheRepository.clearFailedCaches()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * 删除缓存文件和记录
     */
    suspend fun deleteCache(videoId: String): Result<Unit> {
        return try {
            // 取消下载任务（如果正在进行）
            workManager.cancelAllWorkByTag("video_$videoId")
            
            // 删除缓存记录和文件
            cacheRepository.deleteCacheInfo(videoId)
            
            // 同步到服务端
            cacheRepository.syncCacheStateToServer(videoId, false)
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

// 扩展函数：LiveData 转 Flow
fun <T> androidx.lifecycle.LiveData<T>.asFlow(): Flow<T> = kotlinx.coroutines.flow.callbackFlow {
    val observer = androidx.lifecycle.Observer<T> { value ->
        trySend(value)
    }
    observeForever(observer)
    awaitClose { removeObserver(observer) }
} 