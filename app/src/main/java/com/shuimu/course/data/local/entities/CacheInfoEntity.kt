package com.shuimu.course.data.local.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.PrimaryKey

/**
 * 数据库缓存状态枚举（与Domain层保持一致）
 */
enum class CacheStatus {
    NOT_CACHED,     // 未缓存
    WAITING_NETWORK, // 等待网络条件满足
    PENDING,        // 等待下载（队列中）
    DOWNLOADING,    // 正在下载
    PAUSED,         // 下载暂停
    DOWNLOADED,     // 已下载完成
    FAILED          // 下载失败
}

@Entity(
    tableName = "cache_info",
    foreignKeys = [ForeignKey(
        entity = VideoEntity::class,
        parentColumns = ["id"],
        childColumns = ["video_id"],
        onDelete = ForeignKey.CASCADE
    )]
)
data class CacheInfoEntity(
    @PrimaryKey @ColumnInfo(name = "video_id") val videoId: String,
    @ColumnInfo(name = "local_path") val localPath: String?,
    @ColumnInfo(name = "downloaded_bytes") val downloadedBytes: Long = 0L,
    @ColumnInfo(name = "total_bytes") val totalBytes: Long = 0L,
    @ColumnInfo(name = "status") val status: CacheStatus = CacheStatus.NOT_CACHED,
    @ColumnInfo(name = "progress") val progress: Int = 0, // 0-100
    @ColumnInfo(name = "cloud_url") val cloudUrl: String? = null,
    @ColumnInfo(name = "updated_at") val updatedAt: Long = System.currentTimeMillis(),
    @ColumnInfo(name = "error_message") val errorMessage: String? = null,
    @ColumnInfo(name = "priority") val priority: Int = 0, // 下载优先级
    @ColumnInfo(name = "retry_count") val retryCount: Int = 0, // 重试次数
    @ColumnInfo(name = "last_retry_time") val lastRetryTime: Long = 0L // 最后重试时间
)