package com.shuimu.course.data.repository

import com.shuimu.course.data.remote.api.PlaylistApi
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.repository.PlaylistRepository
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PlaylistRepositoryImpl @Inject constructor(
    private val playlistApi: PlaylistApi
) : PlaylistRepository {

    override suspend fun getPlaylist(): List<Video> {
        return try {
            val response = playlistApi.getPlaylist()
            if (response.isSuccessful) {
                response.body()?.map { it.toVideo("default-category") } ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }

    override suspend fun getPlaylistForVideo(videoId: String): List<Video> {
        return try {
            val response = playlistApi.getPlaylistForVideo(videoId)
            if (response.isSuccessful) {
                response.body()?.map { it.toVideo("default-category") } ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
} 