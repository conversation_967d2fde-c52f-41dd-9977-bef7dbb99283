package com.shuimu.course.data.remote.api

import com.shuimu.course.data.remote.dto.CategoryDto
import com.shuimu.course.data.remote.dto.SeriesDto
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path

interface SeriesApi {
    @GET("series")
    suspend fun getAllSeries(): Response<List<SeriesDto>>

    @GET("series/{id}/categories")
    suspend fun getSeriesCategories(@Path("id") seriesId: String): Response<List<CategoryDto>>
}
