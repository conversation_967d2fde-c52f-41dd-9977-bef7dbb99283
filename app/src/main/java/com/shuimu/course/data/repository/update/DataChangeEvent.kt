package com.shuimu.course.data.repository.update

/**
 * 数据变化事件
 * 用于通知UI层数据已更新
 */
sealed class DataChangeEvent {
    
    /**
     * 系列数据更新事件
     * @param seriesIds 更新的系列ID列表
     * @param changeType 变化类型
     */
    data class SeriesUpdated(
        val seriesIds: List<String>,
        val changeType: ChangeType = ChangeType.MODIFIED
    ) : DataChangeEvent()
    
    /**
     * 分类数据更新事件
     * @param categoryIds 更新的分类ID列表
     * @param changeType 变化类型
     */
    data class CategoriesUpdated(
        val categoryIds: List<String>,
        val changeType: ChangeType = ChangeType.MODIFIED
    ) : DataChangeEvent()
    
    /**
     * 视频数据更新事件
     * @param videoIds 更新的视频ID列表
     * @param changeType 变化类型
     */
    data class VideosUpdated(
        val videoIds: List<String>,
        val changeType: ChangeType = ChangeType.MODIFIED
    ) : DataChangeEvent()
    
    /**
     * 数据删除事件
     * @param deletedItems 删除的数据项
     */
    data class DataDeleted(
        val deletedItems: DataDiffResult
    ) : DataChangeEvent()
    
    /**
     * 新数据添加事件
     * @param newItems 新增的数据项
     */
    data class DataAdded(
        val newItems: DataDiffResult
    ) : DataChangeEvent()
    
    /**
     * 需要完全刷新事件
     * 当数据变化过大或出现异常时使用
     */
    object FullRefreshNeeded : DataChangeEvent()
    
    /**
     * 购买状态更新事件
     * 🔥 特别处理购买状态变化，因为这涉及商业逻辑
     */
    data class PurchaseStatusUpdated(
        val seriesIds: List<String>,
        val categoryIds: List<String>
    ) : DataChangeEvent()
    
    /**
     * 变化类型枚举
     */
    enum class ChangeType {
        ADDED,      // 新增
        MODIFIED,   // 修改
        DELETED     // 删除
    }
    
    /**
     * 获取事件的重要性级别
     */
    fun getImportanceLevel(): ImportanceLevel {
        return when (this) {
            is PurchaseStatusUpdated -> ImportanceLevel.HIGH
            is DataDeleted -> ImportanceLevel.HIGH
            is FullRefreshNeeded -> ImportanceLevel.CRITICAL
            is SeriesUpdated -> when (changeType) {
                ChangeType.ADDED -> ImportanceLevel.HIGH
                ChangeType.DELETED -> ImportanceLevel.HIGH
                ChangeType.MODIFIED -> ImportanceLevel.MEDIUM
            }
            is CategoriesUpdated -> ImportanceLevel.MEDIUM
            is VideosUpdated -> ImportanceLevel.LOW
            is DataAdded -> ImportanceLevel.HIGH
        }
    }
    
    /**
     * 重要性级别
     */
    enum class ImportanceLevel {
        LOW,        // 低重要性：视频元数据更新
        MEDIUM,     // 中等重要性：分类更新、系列修改
        HIGH,       // 高重要性：购买状态、新增/删除系列
        CRITICAL    // 关键重要性：需要完全刷新
    }
}
