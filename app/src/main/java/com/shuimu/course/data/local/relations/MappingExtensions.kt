package com.shuimu.course.data.local.relations

import com.shuimu.course.data.local.entities.PlayProgressEntity
import com.shuimu.course.data.local.entities.CacheInfoEntity
import com.shuimu.course.domain.model.Category
import com.shuimu.course.domain.model.Series
import com.shuimu.course.data.toDomainModel

// These thin extension wrappers keep the original API (`toDomain`) used throughout the
// repository layer while delegating the actual mapping logic to the `toDomainModel`
// implementations in the central mapper file.

fun CategoryWithVideos.toDomain(
    allProgress: List<PlayProgressEntity>,
    allCaches: List<CacheInfoEntity>
): Category = this.toDomainModel(allProgress, allCaches)

fun SeriesWithCategories.toDomain(
    allProgress: List<PlayProgressEntity>,
    allCaches: List<CacheInfoEntity>
): Series = this.toDomainModel(allProgress, allCaches)
