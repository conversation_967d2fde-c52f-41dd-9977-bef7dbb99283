package com.shuimu.course.data.remote.interceptors

import com.shuimu.course.BuildConfig
import okhttp3.Interceptor
import okhttp3.Response
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserIdInterceptor @Inject constructor() : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // 从 BuildConfig 读取配置化的用户ID，后续可从 UserRepository 获取当前登录用户ID
        val requestWithUserId = originalRequest.newBuilder()
            .addHeader("X-User-Id", BuildConfig.MOCK_USER_ID)
            .build()
        
        return chain.proceed(requestWithUserId)
    }
} 