package com.shuimu.course.data.local.entities

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "series")
data class SeriesEntity(
    @PrimaryKey val id: String,
    val title: String,
    val icon: String?,
    val price: Int, // 价格，单位：分
    @ColumnInfo(name = "is_free") val isFree: Boolean,
    @ColumnInfo(name = "is_purchased") val isPurchased: Boolean,
    @ColumnInfo(name = "is_package") val isPackage: Boolean = false,
    @ColumnInfo(name = "default_expanded") val defaultExpanded: Boolean = false
) 