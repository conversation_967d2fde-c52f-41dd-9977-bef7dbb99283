package com.shuimu.course.data.repository

import com.shuimu.course.data.local.dao.CacheInfoDao
import com.shuimu.course.data.local.dao.VideoDao
import com.shuimu.course.data.remote.api.CacheApi
import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.CacheConfig
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.data.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CacheRepositoryImpl @Inject constructor(
    private val cacheInfoDao: CacheInfoDao,
    private val videoDao: VideoDao,
    private val cacheApi: CacheApi
) : CacheRepository {

    override fun getCacheInfo(videoId: String): Flow<CachedVideo?> {
        return cacheInfoDao.getCacheInfoForVideo(videoId).map { entity ->
            entity?.let { 
                // 获取视频标题
                val video = videoDao.getVideoByIdSync(videoId)
                entity.toDomain(video?.title ?: "")
            }
        }
    }

    override suspend fun getCacheInfoSync(videoId: String): CachedVideo? {
        val entity = cacheInfoDao.getCacheInfoSync(videoId)
        return entity?.let {
            val video = videoDao.getVideoByIdSync(videoId)
            entity.toDomain(video?.title ?: "")
        }
    }

    override fun getAllCacheInfo(): Flow<List<CachedVideo>> {
        return cacheInfoDao.getAllCacheInfo().map { entities ->
            entities.map { entity ->
                val video = videoDao.getVideoByIdSync(entity.videoId)
                entity.toDomain(video?.title ?: "")
            }
        }
    }

    override fun getCachesByStatus(status: com.shuimu.course.domain.model.CacheStatus): Flow<List<CachedVideo>> {
        val entityStatus = when (status) {
            com.shuimu.course.domain.model.CacheStatus.NOT_CACHED -> com.shuimu.course.data.local.entities.CacheStatus.NOT_CACHED
            com.shuimu.course.domain.model.CacheStatus.WAITING_NETWORK -> com.shuimu.course.data.local.entities.CacheStatus.WAITING_NETWORK
            com.shuimu.course.domain.model.CacheStatus.PENDING -> com.shuimu.course.data.local.entities.CacheStatus.PENDING
            com.shuimu.course.domain.model.CacheStatus.DOWNLOADING -> com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADING
            com.shuimu.course.domain.model.CacheStatus.PAUSED -> com.shuimu.course.data.local.entities.CacheStatus.PAUSED
            com.shuimu.course.domain.model.CacheStatus.DOWNLOADED -> com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADED
            com.shuimu.course.domain.model.CacheStatus.FAILED -> com.shuimu.course.data.local.entities.CacheStatus.FAILED
        }
        
        return cacheInfoDao.getCachesByStatus(entityStatus).map { entities ->
            entities.map { entity ->
                val video = videoDao.getVideoByIdSync(entity.videoId)
                entity.toDomain(video?.title ?: "")
            }
        }
    }

    override fun getCompletedCaches(): Flow<List<CachedVideo>> {
        return cacheInfoDao.getAllCompletedCaches().map { entities ->
            entities.map { entity ->
                val video = videoDao.getVideoByIdSync(entity.videoId)
                entity.toDomain(video?.title ?: "")
            }
        }
    }

    override fun getDownloadingCaches(): Flow<List<CachedVideo>> {
        return cacheInfoDao.getAllDownloadingCaches().map { entities ->
            entities.map { entity ->
                val video = videoDao.getVideoByIdSync(entity.videoId)
                entity.toDomain(video?.title ?: "")
            }
        }
    }

    override fun getPendingCaches(): Flow<List<CachedVideo>> {
        return cacheInfoDao.getAllPendingCaches().map { entities ->
            entities.map { entity ->
                val video = videoDao.getVideoByIdSync(entity.videoId)
                entity.toDomain(video?.title ?: "")
            }
        }
    }

    override suspend fun getDownloadingCount(): Int {
        return cacheInfoDao.getDownloadingCount()
    }

    override suspend fun upsertCacheInfo(cachedVideo: CachedVideo) {
        cacheInfoDao.upsertCacheInfo(cachedVideo.toEntity())
    }

    override suspend fun updateDownloadProgress(
        videoId: String,
        progress: Int,
        downloadedBytes: Long,
        totalBytes: Long
    ) {
        // 首先更新进度
        cacheInfoDao.updateDownloadProgress(
            videoId = videoId,
            status = com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADING,
            progress = progress
        )
        
        // 然后更新字节数
        val existingCache = cacheInfoDao.getCacheInfoSync(videoId)
        if (existingCache != null) {
            val updatedCache = existingCache.copy(
                downloadedBytes = downloadedBytes,
                totalBytes = totalBytes,
                progress = progress,
                updatedAt = System.currentTimeMillis()
            )
            cacheInfoDao.upsertCacheInfo(updatedCache)
        }
    }

    override suspend fun updateDownloadStatus(
        videoId: String,
        status: com.shuimu.course.domain.model.CacheStatus,
        errorMessage: String?
    ) {
        val entityStatus = when (status) {
            com.shuimu.course.domain.model.CacheStatus.NOT_CACHED -> com.shuimu.course.data.local.entities.CacheStatus.NOT_CACHED
            com.shuimu.course.domain.model.CacheStatus.WAITING_NETWORK -> com.shuimu.course.data.local.entities.CacheStatus.WAITING_NETWORK
            com.shuimu.course.domain.model.CacheStatus.PENDING -> com.shuimu.course.data.local.entities.CacheStatus.PENDING
            com.shuimu.course.domain.model.CacheStatus.DOWNLOADING -> com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADING
            com.shuimu.course.domain.model.CacheStatus.PAUSED -> com.shuimu.course.data.local.entities.CacheStatus.PAUSED
            com.shuimu.course.domain.model.CacheStatus.DOWNLOADED -> com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADED
            com.shuimu.course.domain.model.CacheStatus.FAILED -> com.shuimu.course.data.local.entities.CacheStatus.FAILED
        }

        cacheInfoDao.updateDownloadStatus(
            videoId = videoId,
            status = entityStatus,
            errorMessage = errorMessage
        )
    }

    override suspend fun updateDownloadStatusSafely(
        videoId: String,
        newStatus: com.shuimu.course.domain.model.CacheStatus,
        errorMessage: String?
    ): Result<Unit> {
        return try {
            val currentCache = getCacheInfoSync(videoId)
            val currentStatus = currentCache?.status ?: com.shuimu.course.domain.model.CacheStatus.NOT_CACHED

            if (currentStatus.canTransitionTo(newStatus)) {
                updateDownloadStatus(videoId, newStatus, errorMessage)
                Result.success(Unit)
            } else {
                Result.failure(Exception("无效的状态转换: ${currentStatus.displayName} -> ${newStatus.displayName}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteCacheInfo(videoId: String) {
        // 先删除本地文件
        val cacheInfo = getCacheInfoSync(videoId)
        cacheInfo?.localPath?.let { path ->
            deleteLocalFile(path)
        }
        
        // 再删除数据库记录
        cacheInfoDao.deleteCacheInfo(videoId)
    }

    override suspend fun clearFailedCaches() {
        cacheInfoDao.clearFailedCaches()
    }

    override suspend fun checkLocalFileExists(localPath: String): Boolean {
        return try {
            File(localPath).exists()
        } catch (e: Exception) {
            false
        }
    }

    override suspend fun deleteLocalFile(localPath: String): Boolean {
        return try {
            File(localPath).delete()
        } catch (e: Exception) {
            false
        }
    }

    override suspend fun syncCacheStateToServer(
        videoId: String,
        isCached: Boolean,
        localPath: String?
    ): Result<Unit> {
        return try {
            val cacheUpdate = com.shuimu.course.data.remote.dto.cache.CacheStateUpdateDto(
                videoId = videoId,
                isCached = isCached,
                localPath = localPath,
                deviceId = null
            )
            
            val response = cacheApi.syncCacheState(cacheUpdate)
            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                Result.failure(Exception("同步失败: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getCacheConfig(): Result<CacheConfig> {
        // 注意：这个方法已经不应该被使用了
        // 应该使用AppConfigManager.getCacheConfig()来获取预加载的配置
        android.util.Log.w("CacheRepository", "getCacheConfig方法已废弃，请使用AppConfigManager")

        // 返回默认配置作为后备
        val defaultConfig = CacheConfig(
            maxConcurrentDownloads = 5,
            cacheDirectory = "/storage/emulated/0/VideoCache/",
            supportedFormats = listOf("mp4", "m3u8")
        )
        return Result.success(defaultConfig)
    }

    override suspend fun batchSyncCacheStates(cachedVideos: List<CachedVideo>): Result<Unit> {
        return try {
            val cacheUpdates = cachedVideos.map { it.toCacheStateUpdateDto() }
            val response = cacheApi.batchSyncCacheStates(cacheUpdates)
            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                Result.failure(Exception("批量同步失败: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun cleanupOrphanDownloads(): Result<Int> {
        return try {
            android.util.Log.d("CacheRepository", "开始清理孤儿下载状态")
            
            // 获取所有DOWNLOADING状态的记录
            val downloadingCaches = mutableListOf<com.shuimu.course.data.local.entities.CacheInfoEntity>()
            cacheInfoDao.getCachesByStatus(com.shuimu.course.data.local.entities.CacheStatus.DOWNLOADING).collect { entities ->
                downloadingCaches.addAll(entities)
            }
            
            android.util.Log.d("CacheRepository", "发现${downloadingCaches.size}个DOWNLOADING状态记录")
            
            // 将所有DOWNLOADING状态重置为FAILED
            var resetCount = 0
            downloadingCaches.forEach { entity ->
                cacheInfoDao.updateDownloadStatus(
                    videoId = entity.videoId,
                    status = com.shuimu.course.data.local.entities.CacheStatus.FAILED,
                    errorMessage = "系统重启后清理的孤儿任务"
                )
                resetCount++
            }
            
            android.util.Log.d("CacheRepository", "已重置${resetCount}个孤儿下载状态")
            Result.success(resetCount)
        } catch (e: Exception) {
            android.util.Log.e("CacheRepository", "清理孤儿下载状态失败", e)
            Result.failure(e)
        }
    }


}

 