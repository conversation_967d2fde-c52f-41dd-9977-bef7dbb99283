package com.shuimu.course.data.repository

import com.shuimu.course.data.remote.api.UserApi
import com.shuimu.course.data.remote.dto.EarningsResponseDto
import com.shuimu.course.domain.model.Earnings
import com.shuimu.course.domain.model.MonthlyEarning
import com.shuimu.course.domain.repository.ShareRepository
import com.shuimu.course.domain.util.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import retrofit2.HttpException
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ShareRepositoryImpl @Inject constructor(
    private val userApi: UserApi
): ShareRepository {
    override fun getEarnings(): Flow<Resource<Earnings>> = flow {
        emit(Resource.Loading())
        try {
            // Assuming a valid token is passed via an interceptor
            val response = userApi.getShareEarnings("Bearer FAKE_TOKEN")
            if (response.isSuccessful && response.body() != null) {
                emit(Resource.Success(response.body()!!.toDomainModel()))
            } else {
                emit(Resource.Error("Failed to fetch earnings"))
            }
        } catch (e: IOException) {
            emit(Resource.Error("Couldn't reach server."))
        } catch (e: HttpException) {
            emit(Resource.Error("An unexpected error occurred."))
        }
    }
}

// Mapper DTO -> Domain
fun EarningsResponseDto.toDomainModel(): Earnings = Earnings(
    totalEarnings = this.totalEarnings,
    availableForWithdrawal = this.availableForWithdrawal,
    withdrawn = this.withdrawn,
    sharedUsersCount = this.sharedUsersCount,
    monthlyEarnings = this.monthlyEarnings.map { MonthlyEarning(it.month, it.earnings) }
) 