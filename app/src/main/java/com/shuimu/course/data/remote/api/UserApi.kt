package com.shuimu.course.data.remote.api

import com.shuimu.course.config.ApiConfig
import com.shuimu.course.data.remote.dto.EarningsResponseDto
import com.shuimu.course.data.remote.dto.UserDto
import com.shuimu.course.data.remote.dto.UserDataDto
import com.shuimu.course.data.remote.dto.orders.OrderDto
import com.shuimu.course.data.remote.dto.profile.ProfileDataDto
import com.shuimu.course.data.remote.dto.user.*
import retrofit2.Response
import retrofit2.http.*

// Dummy response DTOs for other share endpoints
data class ShareLinkDto(val share_link: String)
data class RankingDto(val rank: Int, val username: String, val earnings: Double)
data class MaterialDto(val posters: List<Any>, val copywriting: List<Any>)


/**
 * 用户基本信息API接口
 * 遵循RESTful设计原则
 */
interface UserApi {

    // ========== 新的RESTful用户API ==========

    /**
     * 获取用户列表（管理端）
     */
    @GET(ApiConfig.Endpoints.USERS)
    suspend fun getUsers(
        @Header(ApiConfig.HEADER_USER_ID) userId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "true",
        @Query("page") page: Int = 1,
        @Query("page_size") pageSize: Int = 20,
        @Query("search") search: String? = null,
        @Query("is_active") isActive: Boolean? = null
    ): Response<UserListResponseDto>

    /**
     * 根据ID获取用户信息
     */
    @GET(ApiConfig.Endpoints.USER_BY_ID)
    suspend fun getUserById(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserResponseDto>

    /**
     * 创建用户（管理端）
     */
    @POST(ApiConfig.Endpoints.ADMIN_USERS)
    suspend fun createUser(
        @Header(ApiConfig.HEADER_USER_ID) adminUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "true",
        @Body userCreateRequest: UserCreateRequestDto
    ): Response<UserResponseDto>

    /**
     * 更新用户信息（管理端）
     */
    @PUT(ApiConfig.Endpoints.ADMIN_USER_BY_ID)
    suspend fun updateUser(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) adminUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "true",
        @Body userUpdateRequest: UserUpdateRequestDto
    ): Response<UserResponseDto>

    /**
     * 删除用户（管理端）
     */
    @DELETE(ApiConfig.Endpoints.ADMIN_USER_BY_ID)
    suspend fun deleteUser(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) adminUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "true"
    ): Response<DeleteResponseDto>

    /**
     * 获取用户资料
     */
    @GET(ApiConfig.Endpoints.USER_PROFILE)
    suspend fun getUserProfile(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false"
    ): Response<UserProfileResponseDto>

    /**
     * 更新用户资料
     */
    @PUT(ApiConfig.Endpoints.USER_PROFILE)
    suspend fun updateUserProfile(
        @Path("user_id") userId: String,
        @Header(ApiConfig.HEADER_USER_ID) currentUserId: String,
        @Header(ApiConfig.HEADER_IS_ADMIN) isAdmin: String = "false",
        @Body profileUpdateRequest: UserProfileUpdateRequestDto
    ): Response<UserProfileResponseDto>

    // ========== 旧的API（保持兼容性） ==========

    @GET("user/profile_data")
    suspend fun getProfileData(): ProfileDataDto

    @GET("user/data")
    suspend fun getUserData(): UserDataDto

    @GET("user/orders")
    suspend fun getOrders(): List<OrderDto>

    @GET("user/purchases")
    suspend fun getUserPurchases(@Header("Authorization") token: String): Response<List<Any>>

    @GET("share/earnings")
    suspend fun getShareEarnings(@Header("Authorization") token: String): Response<EarningsResponseDto>

    @POST("share/create")
    suspend fun createShareLink(@Header("Authorization") token: String): Response<ShareLinkDto>

    @GET("share/ranking")
    suspend fun getShareRanking(): Response<List<RankingDto>>

    @GET("share/materials")
    suspend fun getShareMaterials(): Response<MaterialDto>
}
