package com.shuimu.course.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.shuimu.course.data.local.entities.SeriesEntity
import com.shuimu.course.data.local.entities.CategoryEntity
import com.shuimu.course.data.local.entities.VideoEntity
import com.shuimu.course.data.local.relations.SeriesWithCategories
import kotlinx.coroutines.flow.Flow

@Dao
interface SeriesDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSeries(series: List<SeriesEntity>)

    @Transaction
    @Query("SELECT * FROM series")
    fun getSeriesWithCategories(): Flow<List<SeriesWithCategories>>

    @Query("DELETE FROM series")
    suspend fun clearAll()

    @Query("SELECT COUNT(*) FROM series")
    suspend fun getSeriesCount(): Int

    // 🔥 增量更新方法：删除指定系列
    @Query("DELETE FROM series WHERE id = :seriesId")
    suspend fun deleteById(seriesId: String)

    // 🔥 增量更新方法：更新系列信息（购买状态以服务器为准）
    @Query("""
        UPDATE series SET
            title = :title,
            icon = :icon,
            price = :price,
            is_free = :isFree,
            is_purchased = :isPurchased,
            is_package = :isPackage
        WHERE id = :id
    """)
    suspend fun updateSeriesInfo(
        id: String,
        title: String,
        icon: String?,
        price: Int,
        isFree: Boolean,
        isPurchased: Boolean,
        isPackage: Boolean
    )

    // 🔥 事务方法：原子性地插入所有层级数据
    @Transaction
    suspend fun insertAllData(
        seriesEntities: List<SeriesEntity>,
        categoryEntities: List<CategoryEntity>, 
        videoEntities: List<VideoEntity>,
        categoryDao: CategoryDao,
        videoDao: VideoDao
    ) {
        // 按顺序插入，确保外键约束
        insertSeries(seriesEntities)
        categoryDao.insertCategories(categoryEntities)
        videoDao.insertVideos(videoEntities)
    }
} 