package com.shuimu.course.data.remote.dto

import com.google.gson.annotations.SerializedName

data class UserDataDto(
    @SerializedName("schemaVersion") val schemaVersion: Int? = null,
    @SerializedName("userId") val userId: String,
    @SerializedName("purchases") val purchases: PurchasesDto,
    @SerializedName("favorites") val favorites: FavoritesDto? = null,
    @SerializedName("watchProgress") val watchProgress: Map<String, WatchProgressDto>? = null,
    @SerializedName("videoCaches") val videoCaches: Map<String, VideoCacheDto>? = null
)

data class PurchasesDto(
    @SerializedName("series") val series: List<String> = emptyList(),
    @SerializedName("categories") val categories: List<CategoryPurchaseDto> = emptyList()
)

data class CategoryPurchaseDto(
    @SerializedName("id") val id: String,
    @SerializedName("paidAt") val paidAt: String? = null,
    @SerializedName("orderId") val orderId: String? = null
)

data class FavoritesDto(
    @SerializedName("videos") val videos: List<String> = emptyList(),
    @SerializedName("categories") val categories: List<String> = emptyList()
)

data class WatchProgressDto(
    @SerializedName("position") val position: Int,
    @SerializedName("duration") val duration: Int,
    @SerializedName("watchCount") val watchCount: Int,
    @SerializedName("lastWatchedAt") val lastWatchedAt: String? = null
)

data class VideoCacheDto(
    @SerializedName("status") val status: String,
    @SerializedName("localPath") val localPath: String? = null,
    @SerializedName("size") val size: Long? = null,
    @SerializedName("updatedAt") val updatedAt: String? = null
) 