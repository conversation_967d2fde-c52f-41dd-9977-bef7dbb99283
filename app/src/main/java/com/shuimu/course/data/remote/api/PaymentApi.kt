package com.shuimu.course.data.remote.api

import com.shuimu.course.data.remote.dto.PaymentResponseDto
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST

data class PaymentCreateRequest(
    val itemId: String,
    val itemType: String,
    val amount: Float,
    val paymentMethod: String
)

interface PaymentApi {
    @POST("payments/create")
    suspend fun createPayment(@Body request: PaymentCreateRequest): Response<PaymentResponseDto>
}
