package com.shuimu.course.data.repository.update

import com.shuimu.course.domain.model.Series
import com.shuimu.course.domain.model.Category
import com.shuimu.course.domain.model.Video
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 数据差异检测器
 * 负责比较服务器数据和本地数据的差异
 */
@Singleton
class DataDiffDetector @Inject constructor() {
    
    /**
     * 检测数据变化
     * @param serverData 服务器数据
     * @param localData 本地数据
     * @return 数据差异结果
     */
    suspend fun detectChanges(
        serverData: List<Series>,
        localData: List<Series>
    ): DataDiffResult = withContext(Dispatchers.Default) {
        
        android.util.Log.d("DataDiffDetector", "🔍 开始检测数据差异")
        android.util.Log.d("DataDiffDetector", "服务器数据: ${serverData.size}个系列")
        android.util.Log.d("DataDiffDetector", "本地数据: ${localData.size}个系列")
        
        // 1. 构建本地数据映射表
        val localSeriesMap = localData.associateBy { it.id }
        val localCategoriesMap = localData.flatMap { it.categories }
            .associateBy { it.id }
        val localVideosMap = localData.flatMap { it.categories }
            .flatMap { it.videos }.associateBy { it.id }
        
        // 2. 检测系列级别变化
        val (changedSeries, deletedSeries, newSeries) = detectSeriesChanges(
            serverData, localSeriesMap
        )
        
        // 3. 检测分类级别变化
        val (changedCategories, deletedCategories, newCategories) = detectCategoryChanges(
            serverData, localCategoriesMap
        )
        
        // 4. 检测视频级别变化
        val (changedVideos, deletedVideos, newVideos) = detectVideoChanges(
            serverData, localVideosMap
        )
        
        val result = DataDiffResult(
            hasChanges = changedSeries.isNotEmpty() || 
                        changedCategories.isNotEmpty() || 
                        changedVideos.isNotEmpty() ||
                        deletedSeries.isNotEmpty() ||
                        deletedCategories.isNotEmpty() ||
                        deletedVideos.isNotEmpty() ||
                        newSeries.isNotEmpty() ||
                        newCategories.isNotEmpty() ||
                        newVideos.isNotEmpty(),
            changedSeries = changedSeries,
            changedCategories = changedCategories,
            changedVideos = changedVideos,
            deletedSeries = deletedSeries,
            deletedCategories = deletedCategories,
            deletedVideos = deletedVideos,
            newSeries = newSeries,
            newCategories = newCategories,
            newVideos = newVideos
        )
        
        android.util.Log.d("DataDiffDetector", "✅ 差异检测完成: ${result}")
        return@withContext result
    }
    
    /**
     * 检测系列变化
     */
    private fun detectSeriesChanges(
        serverData: List<Series>,
        localSeriesMap: Map<String, Series>
    ): Triple<List<String>, List<String>, List<String>> {
        
        val changedSeries = mutableListOf<String>()
        val newSeries = mutableListOf<String>()
        val deletedSeries = localSeriesMap.keys.toMutableSet()
        
        serverData.forEach { serverSeries ->
            deletedSeries.remove(serverSeries.id)
            val localSeries = localSeriesMap[serverSeries.id]
            
            if (localSeries == null) {
                // 新增系列
                newSeries.add(serverSeries.id)
                android.util.Log.d("DataDiffDetector", "新增系列: ${serverSeries.title}")
            } else if (hasSeriesChanged(serverSeries, localSeries)) {
                // 系列有变化
                changedSeries.add(serverSeries.id)
                android.util.Log.d("DataDiffDetector", "系列变化: ${serverSeries.title}")
            }
        }
        
        if (deletedSeries.isNotEmpty()) {
            android.util.Log.d("DataDiffDetector", "删除系列: ${deletedSeries.size}个")
        }
        
        return Triple(changedSeries, deletedSeries.toList(), newSeries)
    }
    
    /**
     * 检测系列是否有变化
     * 🔥 重要：购买状态以服务器为准
     */
    private fun hasSeriesChanged(server: Series, local: Series): Boolean {
        return server.title != local.title ||
               server.icon != local.icon ||
               server.price != local.price ||
               server.isFree != local.isFree ||
               server.isPurchased != local.isPurchased ||  // 🔥 购买状态以服务器为准
               server.isPackage != local.isPackage ||
               server.categories.size != local.categories.size
    }

    /**
     * 检测分类变化
     */
    private fun detectCategoryChanges(
        serverData: List<Series>,
        localCategoriesMap: Map<String, Category>
    ): Triple<List<String>, List<String>, List<String>> {

        val changedCategories = mutableListOf<String>()
        val newCategories = mutableListOf<String>()
        val deletedCategories = localCategoriesMap.keys.toMutableSet()

        serverData.flatMap { it.categories }.forEach { serverCategory ->
            deletedCategories.remove(serverCategory.id)
            val localCategory = localCategoriesMap[serverCategory.id]

            if (localCategory == null) {
                // 新增分类
                newCategories.add(serverCategory.id)
                android.util.Log.d("DataDiffDetector", "新增分类: ${serverCategory.title}")
            } else if (hasCategoryChanged(serverCategory, localCategory)) {
                // 分类有变化
                changedCategories.add(serverCategory.id)
                android.util.Log.d("DataDiffDetector", "分类变化: ${serverCategory.title}")
            }
        }

        if (deletedCategories.isNotEmpty()) {
            android.util.Log.d("DataDiffDetector", "删除分类: ${deletedCategories.size}个")
        }

        return Triple(changedCategories, deletedCategories.toList(), newCategories)
    }

    /**
     * 检测分类是否有变化
     * 🔥 重要：购买状态以服务器为准
     */
    private fun hasCategoryChanged(server: Category, local: Category): Boolean {
        return server.title != local.title ||
               server.price != local.price ||
               server.isFree != local.isFree ||
               server.isPurchased != local.isPurchased ||  // 🔥 购买状态以服务器为准
               server.videos.size != local.videos.size
    }

    /**
     * 检测视频变化
     */
    private fun detectVideoChanges(
        serverData: List<Series>,
        localVideosMap: Map<String, Video>
    ): Triple<List<String>, List<String>, List<String>> {

        val changedVideos = mutableListOf<String>()
        val newVideos = mutableListOf<String>()
        val deletedVideos = localVideosMap.keys.toMutableSet()

        serverData.flatMap { it.categories }.flatMap { it.videos }.forEach { serverVideo ->
            deletedVideos.remove(serverVideo.id)
            val localVideo = localVideosMap[serverVideo.id]

            if (localVideo == null) {
                // 新增视频
                newVideos.add(serverVideo.id)
                android.util.Log.d("DataDiffDetector", "新增视频: ${serverVideo.title}")
            } else if (hasVideoChanged(serverVideo, localVideo)) {
                // 视频有变化
                changedVideos.add(serverVideo.id)
                android.util.Log.d("DataDiffDetector", "视频变化: ${serverVideo.title}")
            }
        }

        if (deletedVideos.isNotEmpty()) {
            android.util.Log.d("DataDiffDetector", "删除视频: ${deletedVideos.size}个")
        }

        return Triple(changedVideos, deletedVideos.toList(), newVideos)
    }

    /**
     * 检测视频是否有变化
     * 🔥 重要：保留用户的播放进度和观看次数
     */
    private fun hasVideoChanged(server: Video, local: Video): Boolean {
        return server.title != local.title ||
               server.duration != local.duration ||
               server.description != local.description ||
               server.cloudUrl != local.cloudUrl
               // 🔥 不比较：progress, watchCount, localPath, cacheStatus, playCount, isPurchasable (用户数据或不存在字段)
    }
}
