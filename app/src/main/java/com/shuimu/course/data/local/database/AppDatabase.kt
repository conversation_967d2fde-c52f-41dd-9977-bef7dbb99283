package com.shuimu.course.data.local.database

import androidx.room.Database
import androidx.room.RoomDatabase
import com.shuimu.course.data.local.dao.*
import com.shuimu.course.data.local.entities.*

@Database(
    entities = [
        UserEntity::class,
        SeriesEntity::class,
        CategoryEntity::class,
        VideoEntity::class,
        PurchaseEntity::class,
        PlayProgressEntity::class,
        CacheInfoEntity::class
    ],
    version = 12,
    exportSchema = false
)
abstract class AppDatabase : RoomDatabase() {
    abstract fun userDao(): UserDao
    abstract fun seriesDao(): SeriesDao
    abstract fun categoryDao(): CategoryDao
    abstract fun videoDao(): VideoDao
    abstract fun purchaseDao(): PurchaseDao
    abstract fun playProgressDao(): PlayProgressDao
    abstract fun cacheInfoDao(): CacheInfoDao
} 