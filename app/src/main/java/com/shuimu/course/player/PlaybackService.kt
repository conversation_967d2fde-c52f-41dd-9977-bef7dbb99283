package com.shuimu.course.player

import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.session.MediaSession
import androidx.media3.session.MediaSessionService
import java.io.File
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.common.util.UnstableApi

@UnstableApi
class PlaybackService : MediaSessionService() {
    private var mediaSession: MediaSession? = null
    private lateinit var player: ExoPlayer
    private lateinit var cache: SimpleCache

    // Create a MediaSessionService.
    override fun onCreate() {
        super.onCreate()

        // 1. Create a cache instance
        val cacheFolder = File(cacheDir, "media_cache")
        val cacheEvictor = LeastRecentlyUsedCacheEvictor(100 * 1024 * 1024) // 100MB cache size
        val databaseProvider = StandaloneDatabaseProvider(this)
        cache = SimpleCache(cacheFolder, cacheEvictor, databaseProvider)

        // 2. Create a cache-aware DataSource factory
        val cacheDataSourceFactory = CacheDataSource.Factory()
            .setCache(cache)
            .setUpstreamDataSourceFactory(DefaultDataSource.Factory(this))
            .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)

        // 3. Create a MediaSource factory that uses the cache
        val mediaSourceFactory = DefaultMediaSourceFactory(cacheDataSourceFactory)

        // 4. Create an ExoPlayer instance with the caching MediaSource factory
        player = ExoPlayer.Builder(this)
            .setMediaSourceFactory(mediaSourceFactory)
            .build()
            
        mediaSession = MediaSession.Builder(this, player).build()
    }

    override fun onGetSession(controllerInfo: MediaSession.ControllerInfo): MediaSession? {
        return mediaSession
    }

    override fun onDestroy() {
        mediaSession?.run {
            player.release()
            release()
            mediaSession = null
        }
        cache.release()
        super.onDestroy()
    }
} 