package com.shuimu.course

import android.app.Application
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltAndroidApp
class CourseApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    @Inject
    lateinit var appConfigManager: com.shuimu.course.data.manager.AppConfigManager

    @Inject
    lateinit var dataSyncManager: com.shuimu.course.data.manager.DataSyncManager

    // Application级别的协程作用域
    private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    override fun onCreate() {
        super.onCreate()
        android.util.Log.d("CourseApplication", "Application启动开始")

        // 静默预加载配置（不阻塞UI）
        applicationScope.launch {
            try {
                appConfigManager.preloadConfigs()
                android.util.Log.d("CourseApplication", "配置预加载完成")
            } catch (e: Exception) {
                android.util.Log.e("CourseApplication", "配置预加载失败", e)
            }
        }

        android.util.Log.d("CourseApplication", "Application启动完成")
    }

    override fun onTerminate() {
        super.onTerminate()
        android.util.Log.d("CourseApplication", "Application即将关闭，开始同步数据")

        // App关闭时同步所有待处理的数据
        applicationScope.launch {
            try {
                val syncResult = dataSyncManager.syncAllPendingData()
                if (syncResult.isSuccess) {
                    val result = syncResult.getOrNull()
                    android.util.Log.d("CourseApplication", "数据同步完成: $result")
                } else {
                    android.util.Log.e("CourseApplication", "数据同步失败: ${syncResult.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                android.util.Log.e("CourseApplication", "数据同步异常", e)
            }
        }
    }
    
    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .setMinimumLoggingLevel(android.util.Log.DEBUG)
            .build()
} 