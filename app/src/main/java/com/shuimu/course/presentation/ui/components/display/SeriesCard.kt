package com.shuimu.course.presentation.ui.components.display

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.R
import com.shuimu.course.presentation.ui.components.base.SeriesShuimuCard
import com.shuimu.course.presentation.ui.components.base.SeriesIcon
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import androidx.compose.ui.graphics.graphicsLayer

@Composable
fun SeriesCard(
    modifier: Modifier = Modifier,
    seriesTitle: String,
    price: String? = null,
    isFree: Boolean = false,
    iconName: String?,
    isExpanded: Boolean,
    onHeaderClick: () -> Unit,
    showExpandIcon: Boolean = true,
    content: @Composable () -> Unit
) {
    val bg = if (isFree) Color(0xFFF0FDF4) else Color.White
    val arrowRotation by animateFloatAsState(
        targetValue = if (isExpanded) 180f else 0f,
        animationSpec = tween(durationMillis = 150),
        label = "arrow-rotation"
    )

    SeriesShuimuCard(
        modifier = modifier,
        containerColor = bg
    ) {
        Column(
            modifier = Modifier.graphicsLayer { clip = false }
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(12.dp))
                    .clickable(onClick = onHeaderClick)
                    .padding(horizontal = 10.dp, vertical = 25.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f)
                ) {
                    SeriesIcon(
                        iconKey = iconName,
                        size = 28.dp
                    )
                    Spacer(Modifier.width(16.dp))
                    Text(
                        text = seriesTitle,
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    if (price != null && !isFree) {
                        Text(
                            text = " ($price)",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                        )
                    }
                }
                if (showExpandIcon) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_chevron_down),
                        contentDescription = if (isExpanded) "Collapse" else "Expand",
                        modifier = Modifier
                            .size(18.dp)
                            .rotate(arrowRotation),
                        tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 1f)
                    )
                }
            }

            AnimatedVisibility(
                visible = isExpanded,
                modifier = Modifier.graphicsLayer { clip = false }
            ) {
                Column(
                    modifier = Modifier
                        .padding(start = 8.dp, end = 5.dp, top = 0.dp, bottom = 5.dp)
                        .heightIn(max = 400.dp)
                        .verticalScroll(rememberScrollState())
                        .graphicsLayer { clip = false }
                ) {
                    content()
                }
            }
        }
    }
}

@Preview(name = "Series Card - Paid and Collapsed")
@Composable
fun SeriesCardPaidPreview() {
    ShuimuCourseTheme {
        SeriesCard(
            seriesTitle = "道：恋爱宝典系列",
            price = "¥600",
            iconName = "heart",
            isExpanded = false,
            onHeaderClick = {}
        ) {
            // Content is hidden
        }
    }
}

@Preview(name = "Series Card - Free and Expanded")
@Composable
fun SeriesCardFreePreview() {
    ShuimuCourseTheme {
        SeriesCard(
            seriesTitle = "免费精品系列",
            isFree = true,
            iconName = "play",
            isExpanded = true,
            onHeaderClick = {}
        ) {
            Text("This is the expanded content with category cards inside.")
        }
    }
} 