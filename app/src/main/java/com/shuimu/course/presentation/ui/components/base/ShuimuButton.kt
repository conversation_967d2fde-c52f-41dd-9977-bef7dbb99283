package com.shuimu.course.presentation.ui.components.base

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.course.presentation.ui.theme.BrandGreen
import com.shuimu.course.presentation.ui.theme.GreenPrimary
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.ui.theme.White
import androidx.compose.material3.MaterialTheme

@Composable
fun ShuimuButton(
    onClick: () -> Unit,
    text: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    Button(
        onClick = onClick,
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp),
        enabled = enabled,
        shape = MaterialTheme.shapes.medium,
        colors = ButtonDefaults.buttonColors(
            containerColor = GreenPrimary
        )
    ) {
        Text(
            text = text,
            color = White
        )
    }
}

@Composable
fun PaymentPrimaryButton(
    onClick: () -> Unit,
    text: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    loading: Boolean = false
) {
    Button(
        onClick = if (loading) { {} } else onClick,
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp)
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(12.dp),
                ambientColor = BrandGreen.copy(alpha = 0.3f),
                spotColor = BrandGreen.copy(alpha = 0.3f)
            ),
        enabled = enabled && !loading,
        shape = RoundedCornerShape(12.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = BrandGreen,
            disabledContainerColor = Color(0xFF9CA3AF)
        )
    ) {
        if (loading) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                color = White,
                strokeWidth = 2.dp
            )
        } else {
            Text(
                text = text,
                color = White,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

@Preview(name = "Enabled Button")
@Composable
fun ShuimuButtonEnabledPreview() {
    ShuimuCourseTheme {
        ShuimuButton(
            onClick = {},
            text = "Click Me",
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(name = "Disabled Button")
@Composable
fun ShuimuButtonDisabledPreview() {
    ShuimuCourseTheme {
        ShuimuButton(
            onClick = {},
            text = "Cannot Click",
            enabled = false,
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(name = "Payment Button")
@Composable
fun PaymentPrimaryButtonPreview() {
    ShuimuCourseTheme {
        PaymentPrimaryButton(
            onClick = {},
            text = "立即支付",
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Preview(name = "Payment Button Loading")
@Composable
fun PaymentPrimaryButtonLoadingPreview() {
    ShuimuCourseTheme {
        PaymentPrimaryButton(
            onClick = {},
            text = "立即支付",
            loading = true,
            modifier = Modifier.padding(16.dp)
        )
    }
} 