package com.shuimu.course.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.compose.animation.*
import androidx.compose.animation.core.tween
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.shuimu.course.presentation.ui.screens.CacheManagerScreen
import com.shuimu.course.presentation.ui.screens.HomeScreen
import com.shuimu.course.presentation.ui.screens.LoginScreen
import com.shuimu.course.presentation.ui.screens.PaymentScreen
import com.shuimu.course.presentation.ui.screens.SplashScreen
import com.shuimu.course.presentation.ui.screens.VideoPlayerScreen
import com.shuimu.course.presentation.ui.screens.orders.OrdersScreen
import com.shuimu.course.presentation.ui.screens.profile.ProfileScreen
import com.shuimu.course.presentation.ui.screens.share.ShareEarningsScreen
import com.shuimu.course.presentation.ui.screens.share.ShareMaterialsScreen
import com.shuimu.course.presentation.ui.screens.share.ShareRankingScreen

// Placeholder screens
@Composable
fun SeriesDetailScreen(seriesId: String) { /* TODO */ }

@Composable
fun AppNavigation() {
    val navController = rememberNavController()
    NavHost(navController = navController, startDestination = Routes.SPLASH) {
        // 🔥 新增：启动页 - 从中心往外扩散的轻微动画
        composable(
            route = Routes.SPLASH,
            exitTransition = {
                // 🔥 启动页退出动画：淡出 + 轻微缩小（从中心往内收缩）
                fadeOut(animationSpec = tween(200)) +
                scaleOut(targetScale = 0.98f, animationSpec = tween(200))
            }
        ) {
            SplashScreen(
                onNavigateToHome = {
                    navController.navigate(Routes.HOME) {
                        popUpTo(Routes.SPLASH) { inclusive = true }
                    }
                }
            )
        }

        // 🔥 首页：从中心往外扩散的轻微动画
        composable(
            route = Routes.HOME,
            enterTransition = {
                // 🔥 首页进入动画：淡入 + 轻微放大（从中心往外扩散）
                fadeIn(animationSpec = tween(200)) +
                scaleIn(initialScale = 0.98f, animationSpec = tween(200))
            }
        ) {
            HomeScreen(navController = navController)
        }
        composable(
            route = "${Routes.SERIES_DETAIL}/{seriesId}",
            arguments = listOf(navArgument("seriesId") { type = NavType.StringType })
        ) { backStackEntry ->
            val seriesId = backStackEntry.arguments?.getString("seriesId") ?: ""
            SeriesDetailScreen(seriesId = seriesId)
        }
        composable(
            route = "${Routes.VIDEO_PLAYER}/{videoId}",
            arguments = listOf(navArgument("videoId") { type = NavType.StringType })
        ) {
            @OptIn(androidx.media3.common.util.UnstableApi::class)
            VideoPlayerScreen()
        }
        composable(Routes.LOGIN) {
            LoginScreen(
                onLoginSuccess = {
                    navController.navigate(Routes.HOME) {
                        popUpTo(Routes.LOGIN) { inclusive = true }
                    }
                }
            )
        }
        composable(Routes.PROFILE) {
            ProfileScreen(
                onNavigateTo = { route -> navController.navigate(route) }
            )
        }
        composable(Routes.ORDERS) {
            OrdersScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
        composable(Routes.CACHE_MANAGER) {
            CacheManagerScreen(navController = navController)
        }
        composable(Routes.SHARE_EARNINGS) {
            ShareEarningsScreen()
        }
        composable(Routes.SHARE_RANKING) {
            ShareRankingScreen()
        }
        composable(Routes.SHARE_MATERIALS) {
            ShareMaterialsScreen()
        }
        composable(
            route = Routes.PAYMENT,
            arguments = listOf(
                navArgument("itemId") { type = NavType.StringType },
                navArgument("itemType") { type = NavType.StringType },
                navArgument("title") { type = NavType.StringType },
                navArgument("price") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val title = backStackEntry.arguments?.getString("title") ?: ""
            val price = backStackEntry.arguments?.getString("price") ?: ""
            PaymentScreen(
                title = title,
                price = price,
                onPaymentSuccess = { navController.popBackStack() }
            )
        }
    }
} 