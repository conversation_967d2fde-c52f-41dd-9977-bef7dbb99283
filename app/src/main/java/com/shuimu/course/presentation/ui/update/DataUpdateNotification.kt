package com.shuimu.course.presentation.ui.update

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.shuimu.course.data.repository.update.DataChangeEvent

/**
 * 数据更新通知组件
 * 显示数据更新的状态和进度
 */
@OptIn(ExperimentalAnimationApi::class)
@Composable
fun DataUpdateNotification(
    uiUpdateState: UIUpdateState,
    modifier: Modifier = Modifier
) {
    // 动画状态
    val animatedVisibility = remember { MutableTransitionState(false) }
    
    // 根据状态控制显示
    LaunchedEffect(uiUpdateState.shouldShowNotification()) {
        animatedVisibility.targetState = uiUpdateState.shouldShowNotification()
    }
    
    AnimatedVisibility(
        visibleState = animatedVisibility,
        enter = slideInVertically(
            initialOffsetY = { -it },
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessLow
            )
        ) + fadeIn(),
        exit = slideOutVertically(
            targetOffsetY = { -it },
            animationSpec = tween(300)
        ) + fadeOut(),
        modifier = modifier
    ) {
        UpdateNotificationCard(
            message = uiUpdateState.getUpdateMessage(),
            importanceLevel = uiUpdateState.getImportanceLevel(),
            isUpdating = uiUpdateState.isUpdating,
            progress = uiUpdateState.updateProgress
        )
    }
}

/**
 * 更新通知卡片
 */
@Composable
private fun UpdateNotificationCard(
    message: String,
    importanceLevel: DataChangeEvent.ImportanceLevel,
    isUpdating: Boolean,
    progress: Float,
    modifier: Modifier = Modifier
) {
    val colors = getNotificationColors(importanceLevel)
    val icon = getNotificationIcon(importanceLevel, isUpdating)
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = colors.backgroundColor
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 图标
                if (isUpdating) {
                    // 旋转动画的刷新图标
                    RotatingIcon(
                        icon = icon,
                        tint = colors.iconColor
                    )
                } else {
                    Icon(
                        imageVector = icon,
                        contentDescription = null,
                        tint = colors.iconColor,
                        modifier = Modifier.size(24.dp)
                    )
                }
                
                // 消息文本
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium,
                    color = colors.textColor,
                    fontWeight = if (importanceLevel == DataChangeEvent.ImportanceLevel.HIGH) {
                        FontWeight.SemiBold
                    } else {
                        FontWeight.Normal
                    },
                    modifier = Modifier.weight(1f)
                )
            }
            
            // 进度条（仅在更新时显示）
            if (isUpdating && progress > 0f) {
                Spacer(modifier = Modifier.height(8.dp))
                LinearProgressIndicator(
                    progress = progress,
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(2.dp)
                        .clip(RoundedCornerShape(1.dp)),
                    color = colors.iconColor,
                    trackColor = colors.iconColor.copy(alpha = 0.3f)
                )
            }
        }
    }
}

/**
 * 旋转图标组件
 */
@Composable
private fun RotatingIcon(
    icon: ImageVector,
    tint: Color,
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition()
    val rotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        )
    )
    
    Icon(
        imageVector = icon,
        contentDescription = null,
        tint = tint,
        modifier = modifier
            .size(24.dp)
            .graphicsLayer(rotationZ = rotation)
    )
}

/**
 * 通知颜色配置
 */
private data class NotificationColors(
    val backgroundColor: Color,
    val iconColor: Color,
    val textColor: Color
)

/**
 * 根据重要性级别获取通知颜色
 */
@Composable
private fun getNotificationColors(importanceLevel: DataChangeEvent.ImportanceLevel): NotificationColors {
    return when (importanceLevel) {
        DataChangeEvent.ImportanceLevel.LOW -> NotificationColors(
            backgroundColor = MaterialTheme.colorScheme.surfaceVariant,
            iconColor = MaterialTheme.colorScheme.primary,
            textColor = MaterialTheme.colorScheme.onSurfaceVariant
        )
        DataChangeEvent.ImportanceLevel.MEDIUM -> NotificationColors(
            backgroundColor = MaterialTheme.colorScheme.primaryContainer,
            iconColor = MaterialTheme.colorScheme.primary,
            textColor = MaterialTheme.colorScheme.onPrimaryContainer
        )
        DataChangeEvent.ImportanceLevel.HIGH -> NotificationColors(
            backgroundColor = MaterialTheme.colorScheme.secondaryContainer,
            iconColor = MaterialTheme.colorScheme.secondary,
            textColor = MaterialTheme.colorScheme.onSecondaryContainer
        )
        DataChangeEvent.ImportanceLevel.CRITICAL -> NotificationColors(
            backgroundColor = MaterialTheme.colorScheme.errorContainer,
            iconColor = MaterialTheme.colorScheme.error,
            textColor = MaterialTheme.colorScheme.onErrorContainer
        )
    }
}

/**
 * 根据重要性级别和更新状态获取图标
 */
private fun getNotificationIcon(
    importanceLevel: DataChangeEvent.ImportanceLevel,
    isUpdating: Boolean
): ImageVector {
    return when {
        isUpdating -> Icons.Default.Refresh
        importanceLevel == DataChangeEvent.ImportanceLevel.CRITICAL -> Icons.Default.Warning
        else -> Icons.Default.CheckCircle
    }
}
