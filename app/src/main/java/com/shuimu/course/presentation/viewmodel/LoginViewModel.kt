package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.repository.UserRepository
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

data class LoginState(
    val isLoading: Boolean = false,
    val loginError: String? = null,
    val loginSuccess: Boolean = false,
    val countdownSeconds: Int = 0
)

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {

    private val _state = MutableStateFlow(LoginState())
    val state = _state.asStateFlow()

    private var countdownJob: Job? = null

    fun onLogin(username: String, password: String) {
        viewModelScope.launch {
            userRepository.login(username, password).onEach { result ->
                _state.value = when(result) {
                    is Resource.Loading -> _state.value.copy(isLoading = true)
                    is Resource.Success -> _state.value.copy(isLoading = false, loginSuccess = true)
                    is Resource.Error -> _state.value.copy(isLoading = false, loginError = result.message)
                }
            }.launchIn(this)
        }
    }

    fun startVerificationCountdown() {
        if (countdownJob?.isActive == true) return

        countdownJob = viewModelScope.launch {
            _state.value = _state.value.copy(countdownSeconds = 60)
            for (i in 59 downTo 0) {
                delay(1000)
                _state.value = _state.value.copy(countdownSeconds = i)
            }
        }
    }
} 