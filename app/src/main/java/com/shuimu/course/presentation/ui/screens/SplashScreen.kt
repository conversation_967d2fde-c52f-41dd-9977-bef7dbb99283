package com.shuimu.course.presentation.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.viewmodel.SplashViewModel
import com.shuimu.course.presentation.ui.components.base.ShuimuLogo
import kotlinx.coroutines.delay

/**
 * 启动页面 - 仿美团模式
 * 显示品牌信息的同时后台预加载数据
 */
@Composable
fun SplashScreen(
    onNavigateToHome: () -> Unit,
    viewModel: SplashViewModel = hiltViewModel()
) {
    val splashState by viewModel.state.collectAsState()

    // 🔥 启动时立即开始数据预加载
    LaunchedEffect(Unit) {
        android.util.Log.d("SplashScreen", "🚀 启动页显示，立即开始数据预加载")
        viewModel.preloadData()
    }

    // 🔥 数据准备完成立即导航
    LaunchedEffect(splashState.isDataReady) {
        if (splashState.isDataReady) {
            android.util.Log.d("SplashScreen", "✅ 数据准备完成，立即导航到首页")
            android.util.Log.d("SplashScreen", "数据源: ${splashState.dataSourceLabel}")
            onNavigateToHome()
        }
    }
    
    SplashContent()
}

@Composable
private fun SplashContent() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // 🔥 Logo+slogan偏上显示
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.TopCenter)
                .padding(top = 180.dp), // 🔥 从顶部向下180dp，偏上显示
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 🔥 启动页专用Logo：蓝色圆底 + 白色"水幕"文字（静态专业）
            ShuimuLogo(
                size = 120.dp, // 启动页使用更大的Logo
                animated = false, // 🔥 静态Logo，专业品牌形象
                useTextMode = true // 🔥 使用文字模式：蓝色圆底 + 白色"水幕"文字
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Slogan
            Text(
                text = "从此让情感自由不再难",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF333333),
                textAlign = TextAlign.Center
            )
        }
        
        // 底部：服务提供商信息
        Text(
            text = "水幕情感提供服务",
            fontSize = 14.sp,
            color = Color(0xFF999999),
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 48.dp)
        )
    }
}

@Preview(
    name = "启动页预览",
    showBackground = true,
    widthDp = 412,
    heightDp = 892,
    device = "spec:width=412dp,height=892dp"
)
@Composable
fun SplashScreenPreview() {
    ShuimuCourseTheme {
        SplashContent()
    }
}
