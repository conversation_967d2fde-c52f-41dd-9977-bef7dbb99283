package com.shuimu.course.presentation.viewmodel

import android.util.Log
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.model.PlayProgress
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.domain.repository.PlayProgressRepository
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.repository.VideoRepository
import com.shuimu.course.domain.repository.PlaylistRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

// This represents the state of the player itself, as requested in the instructions
enum class PlayerStatus {
    IDLE,
    BUFFERING,
    PLAYING,
    PAUSED,
    ENDED,
    ERROR
}

data class VideoPlayerState(
    val video: Video? = null,
    val playlist: List<Video> = emptyList(),
    val playerStatus: PlayerStatus = PlayerStatus.IDLE,
    val initialSeekPosition: Long? = null, // To signal initial seek
    val isLoading: Boolean = false,
    val error: String? = null
)

@HiltViewModel
class VideoPlayerViewModel @Inject constructor(
    private val seriesRepository: SeriesRepository,
    private val playProgressRepository: PlayProgressRepository,
    private val videoRepository: VideoRepository,
    private val playlistRepository: PlaylistRepository,
    private val dataLayerManager: com.shuimu.course.data.manager.DataLayerManager,
    private val smartPersistenceManager: com.shuimu.course.data.manager.SmartPersistenceManager,
    private val smartBatchUpdateManager: com.shuimu.course.data.manager.SmartBatchUpdateManager,
    private val watchCountManager: com.shuimu.course.data.manager.WatchCountManager,
    private val savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val _state = MutableStateFlow(VideoPlayerState())
    val state = _state.asStateFlow()

    private val videoId: String? = savedStateHandle.get<String>("videoId")

    // 🔥 新增：智能播放进度管理
    private var lastSavedProgress = 0f
    private var lastSaveTime = 0L
    private var lastSyncedProgress = 0

    init {
        if (videoId != null) {
            loadVideoAndPlaylist(videoId)
        } else {
            _state.value = VideoPlayerState(error = "Video ID not found.")
        }
    }

    private fun loadVideoAndPlaylist(videoId: String) {
        viewModelScope.launch {
            try {
                _state.value = _state.value.copy(isLoading = true)
                
                // 并行加载视频和播放列表数据
                val videoData = videoRepository.getVideoById(videoId)
                val playlistData = playlistRepository.getPlaylistForVideo(videoId)
                
                videoData?.let {
                    _state.value = _state.value.copy(
                        video = it,
                        playlist = playlistData,
                        initialSeekPosition = (it.progress * it.duration).toLong()
                    )
                }
            } catch (e: Exception) {
                Log.e("VideoPlayerViewModel", "Failed to load video: ${e.message}")
                _state.value = _state.value.copy(error = e.message)
            } finally {
                _state.value = _state.value.copy(isLoading = false)
            }
        }
    }

    fun onPlayerStatusChanged(status: PlayerStatus) {
        _state.value = _state.value.copy(playerStatus = status)
    }

    /**
     * 🔥 修改：简化的进度保存方法，主要用于兼容性
     */
    fun saveProgress(currentPosition: Long, totalDuration: Long) {
        if (videoId == null || currentPosition <= 0 || totalDuration <= 0) return

        viewModelScope.launch {
            // 只保存到本地，不频繁上传
            saveProgressLocally(currentPosition, totalDuration)

            // 更新观看统计
            watchCountManager.updateWatchProgress(videoId!!, currentPosition, totalDuration)
        }
    }

    /**
     * 视频播放器关闭时调用，立即同步当前进度
     */
    fun onPlayerClosed(currentPosition: Long, totalDuration: Long) {
        if (videoId == null || totalDuration <= 0) return

        viewModelScope.launch {
            val progressPercentage = if (totalDuration > 0) {
                (currentPosition.toFloat() / totalDuration.toFloat())
            } else 0f

            android.util.Log.d("VideoPlayerViewModel", "播放器关闭，立即同步进度: $progressPercentage")

            // 播放器关闭时立即同步进度（关键数据）
            dataLayerManager.syncCriticalData(
                videoId = videoId,
                progress = progressPercentage,
                completed = false
            )
        }
    }

    fun onInitialSeekDone() {
        _state.value = _state.value.copy(initialSeekPosition = null)
    }

    /**
     * 🔥 新增：智能播放进度同步策略
     */
    private suspend fun updateProgressWithSmartSync(
        videoId: String,
        progress: Float,
        completed: Boolean
    ) {
        val now = System.currentTimeMillis()
        val progressDiff = kotlin.math.abs(progress - lastSavedProgress)
        val timeDiff = now - lastSaveTime

        when {
            // 播放完成：立即同步（关键数据）
            completed -> {
                android.util.Log.d("VideoPlayerViewModel", "🎯 视频播放完成，立即同步进度")
                dataLayerManager.syncCriticalData(videoId, progress, true)

                // 🔥 使用智能持久化管理器
                smartPersistenceManager.recordChange(
                    "progress_completed_$videoId",
                    mapOf("progress" to progress, "completed" to true),
                    com.shuimu.course.data.manager.PersistencePriority.IMMEDIATE
                )
            }

            // 进度变化超过10%：批量同步
            progressDiff >= 0.1f -> {
                android.util.Log.d("VideoPlayerViewModel", "📊 进度变化${(progressDiff*100).toInt()}%，批量同步")

                smartPersistenceManager.recordChange(
                    "progress_$videoId",
                    mapOf("progress" to progress, "completed" to false),
                    com.shuimu.course.data.manager.PersistencePriority.BATCHED
                )

                lastSavedProgress = progress
                lastSyncedProgress = (progress * 10).toInt()
            }

            // 时间超过30秒：批量同步
            timeDiff >= 30_000L -> {
                android.util.Log.d("VideoPlayerViewModel", "⏰ 超过30秒，批量同步进度")

                smartPersistenceManager.recordChange(
                    "progress_$videoId",
                    mapOf("progress" to progress, "completed" to false),
                    com.shuimu.course.data.manager.PersistencePriority.BATCHED
                )

                lastSaveTime = now
            }
        }
    }

    /**
     * 🔥 新增：播放开始时的处理
     */
    fun onPlaybackStarted() {
        if (videoId == null) return

        android.util.Log.d("VideoPlayerViewModel", "▶️ 开始播放视频: $videoId")

        // 开始观看统计
        watchCountManager.startWatching(videoId!!, getCurrentUserId())
    }

    /**
     * 🔥 新增：视频播放完成时的处理
     */
    fun onVideoCompleted(currentPosition: Long, totalDuration: Long) {
        if (videoId == null || totalDuration <= 0) return

        android.util.Log.d("VideoPlayerViewModel", "🎯 视频播放完成: $videoId")

        viewModelScope.launch {
            // 1. 保存播放进度到本地
            saveProgressLocally(currentPosition, totalDuration)

            // 2. 立即更新观看次数统计
            watchCountManager.onVideoCompleted(videoId!!, currentPosition, totalDuration)

            // 3. 立即同步关键数据到服务器
            val progressPercentage = currentPosition.toFloat() / totalDuration.toFloat()
            dataLayerManager.syncCriticalData(
                videoId = videoId!!,
                progress = progressPercentage,
                completed = true
            )
        }
    }

    /**
     * 🔥 新增：离开播放页时的处理
     */
    fun onLeavePlayer(currentPosition: Long, totalDuration: Long) {
        if (videoId == null || totalDuration <= 0) return

        android.util.Log.d("VideoPlayerViewModel", "🚪 离开播放页: $videoId")

        viewModelScope.launch {
            // 1. 保存当前播放进度
            saveProgressLocally(currentPosition, totalDuration)

            // 2. 停止观看统计
            watchCountManager.stopWatching(videoId!!, currentPosition, totalDuration)

            // 3. 立即同步当前进度到服务器
            val progressPercentage = currentPosition.toFloat() / totalDuration.toFloat()
            dataLayerManager.syncCriticalData(
                videoId = videoId!!,
                progress = progressPercentage,
                completed = false
            )
        }
    }

    /**
     * 🔥 新增：视频暂停时保存进度
     */
    fun onVideoPaused(currentPosition: Long, totalDuration: Long) {
        android.util.Log.d("VideoPlayerViewModel", "⏸️ 视频暂停，保存当前进度")

        viewModelScope.launch {
            // 暂停时保存进度并更新观看统计
            saveProgressLocally(currentPosition, totalDuration)

            if (videoId != null && totalDuration > 0) {
                watchCountManager.updateWatchProgress(videoId!!, currentPosition, totalDuration)
            }

            // 通知智能持久化管理器
            smartPersistenceManager.onUserAction(
                com.shuimu.course.data.manager.UserAction.VIDEO_PAUSED
            )
        }
    }

    /**
     * 🔥 新增：只保存到本地的进度保存方法
     */
    private suspend fun saveProgressLocally(currentPosition: Long, totalDuration: Long) {
        if (videoId == null || currentPosition <= 0 || totalDuration <= 0) return

        val currentWatchCount = state.value.video?.watchCount ?: 0
        val progressPercentage = currentPosition.toFloat() / totalDuration.toFloat()

        val progress = PlayProgress(
            videoId = videoId!!,
            watchCount = currentWatchCount,
            lastPositionSeconds = currentPosition / 1000,
            totalDurationSeconds = totalDuration / 1000,
            updatedAt = System.currentTimeMillis()
        )

        // 只保存到本地，不立即上传
        playProgressRepository.saveProgress(progress)

        android.util.Log.d("VideoPlayerViewModel", "💾 本地保存播放进度: ${(progressPercentage * 100).toInt()}%")
    }

    /**
     * 🔥 获取当前用户ID（临时实现）
     */
    private fun getCurrentUserId(): String {
        // TODO: 从用户管理器获取真实的用户ID
        return "user_${System.currentTimeMillis() % 10000}"
    }
}