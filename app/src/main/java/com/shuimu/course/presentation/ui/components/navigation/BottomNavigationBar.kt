package com.shuimu.course.presentation.ui.components.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

data class BottomNavItem(
    val label: String,
    val icon: ImageVector,
    val route: String
)

@Composable
fun ShuimuBottomNavigationBar(
    currentRoute: String?,
    onNavigate: (String) -> Unit
) {
    val items = listOf(
        BottomNavItem("首页", Icons.Default.Home, "home"),
        BottomNavItem("学习", Icons.Default.Star, "learning"),
        BottomNavItem("分享", Icons.Default.Share, "share"),
        BottomNavItem("我的", Icons.Default.AccountCircle, "profile")
    )

    NavigationBar {
        items.forEach { item ->
            NavigationBarItem(
                icon = { Icon(item.icon, contentDescription = item.label) },
                label = { Text(item.label) },
                selected = currentRoute == item.route,
                onClick = { onNavigate(item.route) }
            )
        }
    }
}

@Preview
@Composable
fun ShuimuBottomNavigationBarPreview() {
    ShuimuCourseTheme {
        ShuimuBottomNavigationBar(currentRoute = "home", onNavigate = {})
    }
} 