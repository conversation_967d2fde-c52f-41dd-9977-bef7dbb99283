package com.shuimu.course.presentation.ui.components.user

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.BlueAccent
import com.shuimu.course.presentation.ui.theme.GreenPrimary
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.ui.theme.White

@Composable
fun UserProfileHeader(
    modifier: Modifier = Modifier,
    username: String,
    nickname: String,
    avatarUrl: String?
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .background(
                Brush.linearGradient(
                    colors = listOf(GreenPrimary, BlueAccent)
                )
            )
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        UserAvatar(
            avatarUrl = avatarUrl,
            size = 64.dp
        )
        Spacer(modifier = Modifier.width(16.dp))
        Column {
            Text(
                text = username,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                color = White
            )
            Text(
                text = nickname,
                style = MaterialTheme.typography.bodyMedium,
                color = White.copy(alpha = 0.8f)
            )
        }
    }
}

@Preview
@Composable
fun UserProfileHeaderPreview() {
    ShuimuCourseTheme {
        UserProfileHeader(
            username = "张三",
            nickname = "会员用户",
            avatarUrl = ""
        )
    }
} 