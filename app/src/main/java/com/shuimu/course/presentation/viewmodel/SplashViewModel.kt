package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.data.manager.DataLayerManager
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.data.local.dao.SeriesDao
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.coroutines.Dispatchers
import javax.inject.Inject

/**
 * 启动页ViewModel - 负责数据预加载
 */
@HiltViewModel
class SplashViewModel @Inject constructor(
    private val seriesRepository: SeriesRepository,
    private val cacheRepository: CacheRepository,
    private val dataLayerManager: DataLayerManager,
    private val seriesDao: SeriesDao,
    private val cacheInfoDao: com.shuimu.course.data.local.dao.CacheInfoDao
) : ViewModel() {
    
    private val _state = MutableStateFlow(SplashState())
    val state: StateFlow<SplashState> = _state.asStateFlow()
    
    /**
     * 🔥 优化：冷启动逻辑 - 优先使用本地缓存
     * 1. 快速检查本地缓存数据（50ms内）
     * 2. 有有效缓存：立即使用 + 后台更新
     * 3. 无缓存或过期：显示启动页 + 加载服务器数据
     */
    fun preloadData() {
        viewModelScope.launch {
            android.util.Log.d("SplashViewModel", "🚀 冷启动开始，优先检查本地缓存")

            try {
                // 🔥 第一步：快速检查本地缓存（50ms内完成）
                val startTime = System.currentTimeMillis()
                val hasValidLocalData = checkLocalDataAvailability()
                val checkTime = System.currentTimeMillis() - startTime
                android.util.Log.d("SplashViewModel", "本地缓存检查完成，耗时: ${checkTime}ms")

                if (hasValidLocalData) {
                    // 🔥 有有效本地缓存：直接通过Repository获取（Repository会优先返回本地数据）
                    android.util.Log.d("SplashViewModel", "✅ 发现有效本地缓存，通过Repository获取数据")
                    loadDataThroughRepository()

                } else {
                    // 🔥 无有效缓存：通过Repository获取（Repository会等待服务器数据）
                    android.util.Log.w("SplashViewModel", "无有效本地缓存，通过Repository获取服务器数据")
                    loadDataThroughRepository()
                }

            } catch (e: Exception) {
                android.util.Log.e("SplashViewModel", "启动数据检查异常", e)
                // 异常时降级到Repository获取数据
                loadDataThroughRepository()
            }
        }
    }

    /**
     * 🔥 新增：统一通过Repository获取数据（Repository内部处理并行逻辑）
     */
    private suspend fun loadDataThroughRepository() {
        try {
            android.util.Log.d("SplashViewModel", "通过Repository获取数据")

            seriesRepository.getSeries().collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        if (resource.data?.isNotEmpty() == true) {
                            android.util.Log.d("SplashViewModel", "✅ Repository数据获取成功: ${resource.data.size}个系列，数据源: ${resource.dataSource}")

                            // 标记预加载完成
                            seriesRepository.markPreloadCompleted(resource.dataSource ?: "Repository数据")

                            _state.value = _state.value.copy(
                                isDataReady = true,
                                dataSource = when (resource.dataSource) {
                                    "本地数据" -> DataSource.LOCAL
                                    "服务器数据" -> DataSource.SERVER
                                    else -> DataSource.PRESET
                                },
                                seriesData = resource.data,
                                dataSourceLabel = resource.dataSource ?: "Repository数据"
                            )
                            return@collect
                        }
                    }
                    is Resource.Error -> {
                        android.util.Log.w("SplashViewModel", "Repository数据获取失败: ${resource.message}")
                        throw Exception(resource.message ?: "Repository数据获取失败")
                    }
                    is Resource.Loading -> {
                        android.util.Log.d("SplashViewModel", "Repository正在获取数据...")
                    }
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("SplashViewModel", "Repository数据获取异常", e)
            // 最后的兜底：使用预置数据
            tryUsePresetData()
        }
    }

    /**
     * 🔥 新增：后台静默更新服务器数据
     */
    private fun updateDataInBackground() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                android.util.Log.d("SplashViewModel", "🔄 后台静默更新服务器数据")

                seriesRepository.getSeries().collect { resource ->
                    when (resource) {
                        is Resource.Success -> {
                            if (resource.data?.isNotEmpty() == true) {
                                android.util.Log.d("SplashViewModel", "✅ 后台更新成功: ${resource.data.size}个系列")
                                // 静默更新，不改变UI状态
                                return@collect
                            }
                        }
                        is Resource.Error -> {
                            android.util.Log.w("SplashViewModel", "后台更新失败: ${resource.message}")
                            // 静默失败，不影响用户体验
                            return@collect
                        }
                        is Resource.Loading -> {
                            // 静默加载，不显示给用户
                        }
                    }
                }
            } catch (e: Exception) {
                android.util.Log.w("SplashViewModel", "后台更新异常（静默处理）", e)
                // 静默处理异常，不影响用户体验
            }
        }
    }



    /**
     * 尝试使用本地缓存数据
     */
    private suspend fun tryUseLocalData() {
        try {
            android.util.Log.d("SplashViewModel", "尝试获取本地缓存数据...")

            // 检查本地是否有数据
            val hasLocalData = checkLocalDataAvailability()

            if (hasLocalData) {
                android.util.Log.d("SplashViewModel", "本地缓存数据可用")

                // 🔥 标记预加载完成
                seriesRepository.markPreloadCompleted("本地数据")

                _state.value = _state.value.copy(
                    isDataReady = true,
                    dataSource = DataSource.LOCAL,
                    dataSourceLabel = "本地数据"
                )
            } else {
                android.util.Log.w("SplashViewModel", "本地缓存数据不可用，使用预置数据")

                // 🔥 标记预加载完成
                seriesRepository.markPreloadCompleted("预置数据")

                _state.value = _state.value.copy(
                    isDataReady = true,
                    dataSource = DataSource.PRESET,
                    dataSourceLabel = "预置数据"
                )
            }

        } catch (e: Exception) {
            android.util.Log.e("SplashViewModel", "本地缓存数据获取异常", e)
            // 最后兜底：使用预置数据

            // 🔥 标记预加载完成
            seriesRepository.markPreloadCompleted("预置数据")

            _state.value = _state.value.copy(
                isDataReady = true,
                dataSource = DataSource.PRESET,
                dataSourceLabel = "预置数据"
            )
        }
    }

    /**
     * 🔥 优化：检查本地数据可用性和新鲜度
     */
    private suspend fun checkLocalDataAvailability(): Boolean {
        return try {
            // 检查是否有系列数据
            val seriesCount = seriesDao.getSeriesCount()
            android.util.Log.d("SplashViewModel", "本地系列数据数量: $seriesCount")

            if (seriesCount == 0) {
                android.util.Log.d("SplashViewModel", "本地无数据")
                return false
            }

            // 🔥 简化：如果有数据就认为有效，让Repository层处理缓存策略
            // 这里主要检查是否有基础数据，具体的缓存新鲜度由Repository管理

            android.util.Log.d("SplashViewModel", "✅ 本地数据有效且新鲜")
            return true

        } catch (e: Exception) {
            android.util.Log.e("SplashViewModel", "检查本地数据失败", e)
            false
        }
    }

    /**
     * 🔥 新增：最后兜底使用预置数据
     */
    private suspend fun tryUsePresetData() {
        try {
            android.util.Log.w("SplashViewModel", "使用预置数据作为最后兜底")

            // 通过Repository获取预置数据
            seriesRepository.getSeries().collect { resource ->
                when (resource) {
                    is Resource.Success -> {
                        if (resource.data?.isNotEmpty() == true) {
                            android.util.Log.d("SplashViewModel", "✅ 预置数据获取成功: ${resource.data.size}个系列")

                            seriesRepository.markPreloadCompleted("预置数据")

                            _state.value = _state.value.copy(
                                isDataReady = true,
                                dataSource = DataSource.PRESET,
                                seriesData = resource.data,
                                dataSourceLabel = "预置数据"
                            )
                            return@collect
                        }
                    }
                    is Resource.Error -> {
                        android.util.Log.e("SplashViewModel", "预置数据获取失败: ${resource.message}")
                    }
                    is Resource.Loading -> {
                        android.util.Log.d("SplashViewModel", "正在获取预置数据...")
                    }
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("SplashViewModel", "预置数据获取异常", e)
        }
    }
}

/**
 * 启动页状态
 */
data class SplashState(
    val isDataReady: Boolean = false,
    val shouldUseLocalData: Boolean = false,
    val shouldUsePresetData: Boolean = false,
    val dataSource: DataSource = DataSource.LOCAL,
    val seriesData: List<com.shuimu.course.domain.model.Series>? = null,
    val dataSourceLabel: String = "未知",
    val error: String? = null
)

/**
 * 数据来源
 */
enum class DataSource {
    SERVER,
    LOCAL,
    PRESET
}
