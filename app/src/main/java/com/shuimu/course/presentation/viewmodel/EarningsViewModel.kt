package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.model.Earnings
import com.shuimu.course.domain.repository.ShareRepository
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

data class EarningsState(
    val isLoading: Boolean = false,
    val earnings: Earnings? = null,
    val error: String? = null
)

@HiltViewModel
class EarningsViewModel @Inject constructor(
    private val shareRepository: ShareRepository
) : ViewModel() {

    private val _state = MutableStateFlow(EarningsState())
    val state = _state.asStateFlow()

    init {
        loadEarnings()
    }

    fun loadEarnings() {
        shareRepository.getEarnings().onEach { result ->
            _state.value = when (result) {
                is Resource.Loading -> EarningsState(isLoading = true)
                is Resource.Success -> EarningsState(earnings = result.data)
                is Resource.Error -> EarningsState(error = result.message)
            }
        }.launchIn(viewModelScope)
    }
} 