package com.shuimu.course.presentation.ui.components.base

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.graphics.Color

@Composable
fun ShuimuCard(
    modifier: Modifier = Modifier,
    containerColor: Color = MaterialTheme.colorScheme.surface,
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer { clip = false },
        shape = MaterialTheme.shapes.large,  // 16dp 圆角 (当前)
        // shape = MaterialTheme.shapes.small,     // 4dp 圆角 (小)
        // shape = MaterialTheme.shapes.medium,    // 8dp 圆角 (中)
        // shape = MaterialTheme.shapes.extraLarge, // 28dp 圆角 (超大)
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
        colors = CardDefaults.cardColors(
            containerColor = containerColor
        ),
        content = {
            Column(modifier = Modifier.padding(0.dp)) {
                content()
            }
        }
    )
}

@Preview(name = "Shuimu Card Preview")
@Composable
fun ShuimuCardPreview() {
    ShuimuCourseTheme {
        ShuimuCard(modifier = Modifier.padding(16.dp)) {
            Text(text = "This is some content inside a ShuimuCard.")
        }
    }
} 