package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.repository.SearchRepository
import com.shuimu.course.domain.repository.SearchResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class SearchState(
    val searchQuery: String = "",
    val searchHistory: List<String> = emptyList(),
    val searchResults: List<SearchResult> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)

@HiltViewModel
class SearchViewModel @Inject constructor(
    private val searchRepository: SearchRepository
) : ViewModel() {

    private val _state = MutableStateFlow(SearchState())
    val state: StateFlow<SearchState> = _state.asStateFlow()

    init {
        loadSearchHistory()
    }

    fun updateSearchQuery(query: String) {
        _state.value = _state.value.copy(searchQuery = query)
        if (query.isNotBlank()) {
            searchContent(query)
        } else {
            _state.value = _state.value.copy(searchResults = emptyList())
        }
    }

    private fun loadSearchHistory() {
        viewModelScope.launch {
            try {
                val history = searchRepository.getSearchHistory()
                _state.value = _state.value.copy(searchHistory = history)
            } catch (e: Exception) {
                _state.value = _state.value.copy(error = e.message)
            }
        }
    }

    private fun searchContent(query: String) {
        viewModelScope.launch {
            try {
                _state.value = _state.value.copy(isLoading = true)
                val results = searchRepository.searchContent(query)
                _state.value = _state.value.copy(
                    searchResults = results,
                    isLoading = false
                )
            } catch (e: Exception) {
                _state.value = _state.value.copy(
                    error = e.message,
                    isLoading = false
                )
            }
        }
    }

    fun addToHistory(query: String) {
        viewModelScope.launch {
            try {
                searchRepository.addSearchHistory(query)
                loadSearchHistory() // 重新加载历史记录
            } catch (e: Exception) {
                // 忽略错误
            }
        }
    }

    fun clearHistory() {
        viewModelScope.launch {
            try {
                searchRepository.clearSearchHistory()
                _state.value = _state.value.copy(searchHistory = emptyList())
            } catch (e: Exception) {
                _state.value = _state.value.copy(error = e.message)
            }
        }
    }
} 