package com.shuimu.course.presentation.viewmodel.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.model.profile.UserProfile
import com.shuimu.course.domain.usecase.profile.GetProfileDataUseCase
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

sealed class ProfileUiState {
    object Loading : ProfileUiState()
    data class Success(val userProfile: UserProfile) : ProfileUiState()
    data class Error(val message: String) : ProfileUiState()
}

@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val getProfileDataUseCase: GetProfileDataUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow<ProfileUiState>(ProfileUiState.Loading)
    val uiState: StateFlow<ProfileUiState> = _uiState.asStateFlow()

    init {
        fetchProfileData()
    }

    private fun fetchProfileData() {
        viewModelScope.launch {
            _uiState.value = ProfileUiState.Loading
            when (val result = getProfileDataUseCase()) {
                is Resource.Success -> {
                    _uiState.value = ProfileUiState.Success(result.data!!)
                }
                is Resource.Error -> {
                    _uiState.value = ProfileUiState.Error(result.message ?: "An unknown error occurred")
                }
                is Resource.Loading -> {
                    // State is already Loading, can be a no-op or log for debugging
                }
            }
        }
    }
} 