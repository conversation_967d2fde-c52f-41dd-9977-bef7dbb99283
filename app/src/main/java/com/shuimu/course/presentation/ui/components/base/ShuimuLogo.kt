package com.shuimu.course.presentation.ui.components.base

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.foundation.layout.offset
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.font.FontWeight
import com.shuimu.course.R
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun ShuimuLogo(
    modifier: Modifier = Modifier,
    size: Dp = 48.dp,
    animated: Boolean = false,
    useWaveIcon: Boolean = false, // 是否使用水波浪图标
    useTextMode: Boolean = false  // 🔥 新增：是否使用文字模式（显示"水幕"文字）
) {
    val scale = if (animated) {
        val infiniteTransition = rememberInfiniteTransition(label = "logo-animation")
        val animatedScale by infiniteTransition.animateFloat(
            initialValue = 1f,
            targetValue = 1.1f,
            animationSpec = infiniteRepeatable(
                animation = tween(1000, easing = LinearEasing),
                repeatMode = RepeatMode.Reverse
            ),
            label = "logo-scale"
        )
        animatedScale
    } else {
        1f
    }

    Surface(
        modifier = modifier
            .size(size)
            .scale(scale),
        shape = CircleShape,
        color = Color(0xFF3B82F6) // 蓝色背景
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            if (useTextMode) {
                // 🔥 文字模式：显示白色"水幕"文字
                Text(
                    text = "水幕",
                    color = Color.White,
                    fontSize = (size * 0.25f).value.sp, // 根据Logo尺寸调整文字大小
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
            } else {
                // 图标模式：显示水滴或水波浪图标
                Icon(
                    painter = painterResource(
                        id = if (useWaveIcon) R.drawable.ic_water_wave else R.drawable.ic_water_fa
                    ),
                    contentDescription = "水幕Logo",
                    tint = Color.White,
                    modifier = Modifier
                        .size(size * 0.6f) // 图标适合的尺寸
                        .offset(y = 0.dp) // 图标居中
                )
            }
        }
    }
}

// 🧹 清理多余预览，只保留启动页预览

@Preview(
    name = "启动页Logo",
    showBackground = true,
    widthDp = 200,
    heightDp = 200
)
@Composable
fun ShuimuLogoPreview() {
    ShuimuCourseTheme {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            ShuimuLogo(size = 120.dp, animated = false, useTextMode = true)
        }
    }
}