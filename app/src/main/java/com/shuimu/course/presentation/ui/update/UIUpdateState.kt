package com.shuimu.course.presentation.ui.update

import com.shuimu.course.data.repository.update.DataChangeEvent

/**
 * UI更新状态
 * 管理数据更新时的UI状态和动画
 */
data class UIUpdateState(
    val isUpdating: Boolean = false,
    val updateProgress: Float = 0f,
    val updatedItemsCount: Int = 0,
    val showUpdateNotification: Boolean = false,
    val notificationMessage: String = "",
    val lastUpdateEvent: DataChangeEvent? = null,
    val updateStartTime: Long = 0L,
    val animationInProgress: Boolean = false
) {
    
    /**
     * 获取更新通知消息
     */
    fun getUpdateMessage(): String {
        return when {
            notificationMessage.isNotEmpty() -> notificationMessage
            updatedItemsCount > 0 -> "已更新 $updatedItemsCount 项内容"
            else -> "数据已更新"
        }
    }
    
    /**
     * 检查是否应该显示更新通知
     */
    fun shouldShowNotification(): Boolean {
        return showUpdateNotification && updatedItemsCount > 0
    }
    
    /**
     * 获取更新重要性级别
     */
    fun getImportanceLevel(): DataChangeEvent.ImportanceLevel {
        return lastUpdateEvent?.getImportanceLevel() ?: DataChangeEvent.ImportanceLevel.LOW
    }
    
    /**
     * 检查是否是重要更新
     */
    fun isImportantUpdate(): Boolean {
        val level = getImportanceLevel()
        return level == DataChangeEvent.ImportanceLevel.HIGH || 
               level == DataChangeEvent.ImportanceLevel.CRITICAL
    }
    
    companion object {
        /**
         * 创建开始更新状态
         */
        fun startUpdate(event: DataChangeEvent, itemsCount: Int): UIUpdateState {
            return UIUpdateState(
                isUpdating = true,
                updateProgress = 0f,
                updatedItemsCount = itemsCount,
                showUpdateNotification = false,
                lastUpdateEvent = event,
                updateStartTime = System.currentTimeMillis(),
                animationInProgress = true
            )
        }
        
        /**
         * 创建更新完成状态
         */
        fun completeUpdate(previousState: UIUpdateState): UIUpdateState {
            return previousState.copy(
                isUpdating = false,
                updateProgress = 1f,
                showUpdateNotification = true,
                notificationMessage = generateUpdateMessage(previousState.lastUpdateEvent, previousState.updatedItemsCount),
                animationInProgress = false
            )
        }
        
        /**
         * 创建隐藏通知状态
         */
        fun hideNotification(previousState: UIUpdateState): UIUpdateState {
            return previousState.copy(
                showUpdateNotification = false,
                notificationMessage = "",
                lastUpdateEvent = null,
                updatedItemsCount = 0
            )
        }
        
        /**
         * 生成更新消息
         */
        private fun generateUpdateMessage(event: DataChangeEvent?, itemsCount: Int): String {
            return when (event) {
                is DataChangeEvent.PurchaseStatusUpdated -> "购买状态已更新"
                is DataChangeEvent.SeriesUpdated -> when (event.changeType) {
                    DataChangeEvent.ChangeType.ADDED -> "新增了 ${event.seriesIds.size} 个系列"
                    DataChangeEvent.ChangeType.MODIFIED -> "更新了 ${event.seriesIds.size} 个系列"
                    DataChangeEvent.ChangeType.DELETED -> "删除了 ${event.seriesIds.size} 个系列"
                }
                is DataChangeEvent.CategoriesUpdated -> "更新了 ${event.categoryIds.size} 个分类"
                is DataChangeEvent.VideosUpdated -> "更新了 ${event.videoIds.size} 个视频"
                is DataChangeEvent.DataAdded -> "新增了内容"
                is DataChangeEvent.DataDeleted -> "删除了部分内容"
                is DataChangeEvent.FullRefreshNeeded -> "数据已完全刷新"
                null -> if (itemsCount > 0) "已更新 $itemsCount 项内容" else "数据已更新"
            }
        }
    }
}
