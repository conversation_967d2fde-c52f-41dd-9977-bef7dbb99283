package com.shuimu.course.presentation.viewmodel.orders

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.model.orders.Order
import com.shuimu.course.domain.usecase.orders.GetOrdersUseCase
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import javax.inject.Inject

data class OrdersUiState(
    val isLoading: Boolean = false,
    val orders: List<Order> = emptyList(),
    val error: String? = null
)

@HiltViewModel
class OrdersViewModel @Inject constructor(
    private val getOrdersUseCase: GetOrdersUseCase
) : ViewModel() {

    private val _uiState = MutableStateFlow(OrdersUiState())
    val uiState: StateFlow<OrdersUiState> = _uiState.asStateFlow()

    init {
        fetchOrders()
    }

    private fun fetchOrders() {
        getOrdersUseCase().onEach { result ->
            _uiState.value = when (result) {
                is Resource.Loading -> OrdersUiState(isLoading = true)
                is Resource.Success -> OrdersUiState(orders = result.data ?: emptyList())
                is Resource.Error -> OrdersUiState(error = result.message ?: "An unknown error occurred")
            }
        }.launchIn(viewModelScope)
    }
} 