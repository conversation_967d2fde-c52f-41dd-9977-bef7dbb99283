package com.shuimu.course.presentation.ui.components.display

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.R
import com.shuimu.course.domain.model.Category
import com.shuimu.course.domain.model.Video
import com.shuimu.course.presentation.ui.components.base.CategoryShuimuCard
import com.shuimu.course.presentation.ui.components.base.VideoProgressBar
import com.shuimu.course.presentation.ui.components.base.WatchCountBadge
import com.shuimu.course.presentation.ui.components.base.BadgeStyle
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll

@Composable
fun CategoryCard(
    category: Category,
    isExpanded: Boolean = false,
    isPurchasable: Boolean = false,
    onClick: () -> Unit,
    onVideoPlay: (Video) -> Unit = {},
    onVideoShowCacheDialog: (Video) -> Unit = {},
    onVideoLockedClick: (Video) -> Unit = {},
    cacheStatus: Map<String, com.shuimu.course.domain.model.CachedVideo> = emptyMap(),
    modifier: Modifier = Modifier
) {
    // 🔥 箭头旋转动画
    val arrowRotation by animateFloatAsState(
        targetValue = if (isExpanded) 180f else 0f,
        animationSpec = tween(durationMillis = 300),
        label = "arrow_rotation"
    )
    // 使用Box包装，让徽章可以绝对定位在外层
    Box(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer { clip = false } // 🔥 关键修复：CategoryCard根容器禁用裁剪
    ) {
        CategoryShuimuCard(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onClick() }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 8.dp, end = 8.dp, top = 12.dp, bottom = 0.dp)
                    .graphicsLayer { clip = false }  // Column内容禁用裁剪
            ) {
                // 分类头部区域
                CategoryHeaderLayout(category = category, arrowRotation = arrowRotation)
                
                // 视频列表区域
                AnimatedVisibility(
                    visible = isExpanded,
                    modifier = Modifier.graphicsLayer { clip = false }  // AnimatedVisibility容器禁用裁剪
                ) {
                    Column(
                        modifier = Modifier
                            .padding(start = 0.dp, top = 8.dp)
                            .heightIn(max = 240.dp) // 设置最大高度
                            .verticalScroll(rememberScrollState()) // 添加垂直滚动
                            .graphicsLayer { clip = false },  // 视频列表Column禁用裁剪
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        category.videos.forEachIndexed { index, video ->
                            // 应用实时缓存状态，但不覆盖观看进度
                            val cachedVideo = cacheStatus[video.id]
                            val updatedVideo = if (cachedVideo != null) {
                                video.copy(
                                    cacheStatus = cachedVideo.status
                                    // ❌ 不再覆盖 progress 字段！
                                    // progress 字段专门用于观看进度，不应该被下载进度覆盖
                                )
                            } else {
                                video
                            }

                            VideoItem(
                                video = updatedVideo,
                                cachedVideo = cachedVideo, // 传递缓存信息，用于显示下载进度
                                isPurchasable = isPurchasable,
                                onPlay = onVideoPlay,
                                onShowCacheDialog = onVideoShowCacheDialog,
                                onLockedClick = onVideoLockedClick,
                                modifier = Modifier.defaultMinSize(minHeight = 40.dp)
                            )
                            
                            // 添加分割线，最后一项除外
                            if (index < category.videos.size - 1) {
                                HorizontalDivider(
                                    thickness = 0.5.dp,
                                    color = MaterialTheme.colorScheme.outlineVariant,
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
        
        // 悬浮的徽章 - 在外层Box中绝对定位，不受padding限制
        if ((category.isPurchased == true || category.isFree) && category.watchCount != null) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .offset(x = (-10).dp, y = 33.dp) // 恢复原来的位置设置
                    .wrapContentSize(unbounded = true)
                    .graphicsLayer { clip = false }
            ) {
                WatchCountBadge(
                    watchCount = category.watchCount!!,
                    modifier = Modifier.graphicsLayer { clip = false }
                )
            }
        }
    }
}

@Composable
private fun CategoryHeaderLayout(
    category: Category,
    arrowRotation: Float,
    modifier: Modifier = Modifier
) {
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer { clip = false }  // ConstraintLayout禁用裁剪
    ) {
                // 创建三个独立的约束引用
        val (titleRow, progressBar, textRow) = createRefs()

        // 标题区 - 基准元素
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .constrainAs(titleRow) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                    end.linkTo(parent.end)
                    width = Dimension.fillToConstraints
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 分类标题（包含价格）- 标题加粗，括号和价格不加粗
            val titleText = buildAnnotatedString {
                val displayTitle = category.displayTitle
                val parenIndex = displayTitle.indexOf(" (")
                
                if (parenIndex != -1) {
                    // 有价格信息，分别设置样式
                    // 标题部分 - 加粗
                    withStyle(style = SpanStyle(fontWeight = FontWeight.Medium)) {
                        append(displayTitle.substring(0, parenIndex))
                    }
                    // 括号和价格部分 - 不加粗
                    withStyle(style = SpanStyle(fontWeight = FontWeight.Normal)) {
                        append(displayTitle.substring(parenIndex))
                    }
                } else {
                    // 没有价格信息，整个标题加粗
                    withStyle(style = SpanStyle(fontWeight = FontWeight.Medium)) {
                        append(displayTitle)
                    }
                }
            }
            
            Text(
                text = titleText,
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.weight(1f)
            )

            // 展开箭头 - 使用KeyboardArrowDown + 向上偏移15dp + 旋转动画
            Icon(
                imageVector = Icons.Default.KeyboardArrowDown,
                contentDescription = if (arrowRotation > 90f) "折叠" else "展开",
                tint = Color(0xFF6B7280),
                modifier = Modifier
                    .size(20.dp)
                    .offset(y = (-15).dp)
                    .rotate(arrowRotation)
            )
        }

        // 进度条容器 - 固定4dp高度，零间距贴近标题
        if (category.isPurchased == true || category.isFree) {
            Box(
                modifier = Modifier
                    .constrainAs(progressBar) {
                        top.linkTo(titleRow.bottom, margin = 0.dp) // 零间距！紧贴标题底部
                        start.linkTo(parent.start)
                        end.linkTo(textRow.start, margin = 5.dp) // 与文字区域5dp间距
                        width = Dimension.fillToConstraints
                        height = Dimension.value(4.dp) // 固定4dp容器高度
                    }
            ) {
                VideoProgressBar(
                    progress = category.progress ?: 0f,
                    height = 1.dp, // 实际进度条1dp
                    backgroundColor = Color.Transparent,
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.TopStart) // 贴容器顶部，更接近标题
                )
            }
            
            // 百分比文字 - 精确定位，无padding间距
            Text(
                text = "${((category.progress ?: 0f) * 100).toInt()}%",
                style = MaterialTheme.typography.bodySmall,
                color = Color(0xFF6B7280),
                modifier = Modifier
                    .constrainAs(textRow) {
                        top.linkTo(progressBar.top) // 对齐进度条容器顶边
                        end.linkTo(parent.end, margin = 43.dp) // 徽章宽度约26dp + 调整间距17dp
                    }
                    .offset(y = (-4).dp) // 负偏移进一步贴近标题
                    // 注意：无padding(end = 4.dp)！间距通过margin精确控制
            )
            
            // 徽章已移至外层Box中进行绝对定位
        }
    }
}

// 方案1预览：徽章回到Row B，使用graphicsLayer让其溢出
@Composable
fun CategoryCardScheme1(
    category: Category,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    CategoryShuimuCard(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Row A: 标题区
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = category.displayTitle,
                    style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Medium),
                    modifier = Modifier.weight(1f)
                )
                
                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = "展开",
                    tint = Color(0xFF6B7280),
                    modifier = Modifier
                        .size(20.dp)
                        .offset(y = (-15).dp)
                )
            }
            
            // Row B: 进度区 - 徽章回到Row B中，三者同一行
            if (category.isPurchased == true || category.isFree) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(12.dp), // 压缩高度，减少标题与进度条间距
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 进度条 - 占据左侧空间
                    VideoProgressBar(
                        progress = category.progress ?: 0f,
                        height = 1.dp, // 进度条高度设为1dp
                        modifier = Modifier.weight(1f),
                        backgroundColor = Color.Transparent // 背景透明，只显示有进度的部分
                    )
                    
                    // 右对齐的百分比和徽章
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 百分比
                        Text(
                            text = "${((category.progress ?: 0f) * 100).toInt()}%",
                            style = MaterialTheme.typography.bodySmall,
                            color = Color(0xFF6B7280),
                            modifier = Modifier.padding(end = 4.dp)
                        )
                        
                        // 徽章 - 在Row B中，但可以向上溢出
                        if (category.watchCount != null) {
                            WatchCountBadge(
                                watchCount = category.watchCount!!,
                                modifier = Modifier
                                    .offset(y = (-2).dp) // 微调向上偏移，适配12dp行高
                                    .wrapContentSize(unbounded = true)
                                    .graphicsLayer { clip = false }
                            )
                        }
                    }
                }
            }
        }
    }
}

// 方案2预览：徽章在外层居中，百分比在Row B
@Composable
fun CategoryCardScheme2(
    category: Category,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier) {
        CategoryShuimuCard(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onClick() }
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // Row A: 标题区
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = category.displayTitle,
                        style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Medium),
                        modifier = Modifier.weight(1f)
                    )
                    
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = "展开",
                        tint = Color(0xFF6B7280),
                        modifier = Modifier
                            .size(20.dp)
                            .offset(y = (-15).dp)
                    )
                }
                
                // Row B: 进度区 - 只有进度条，保持1dp高度实现标题贴近
                if (category.isPurchased == true || category.isFree) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // 进度条占据左侧空间，为右侧百分比+徽章预留空间
                        VideoProgressBar(
                            progress = category.progress ?: 0f,
                            height = 1.dp,
                            modifier = Modifier.weight(1f),
                            backgroundColor = Color.Transparent
                        )
                        
                        // 5dp间距 + 百分比和徽章的占位空间（透明）
                        Spacer(modifier = Modifier.width(5.dp))
                        Spacer(modifier = Modifier.width(60.dp)) // 为百分比+徽章预留约60dp空间
                    }
                }
            }
        }
        
        // 悬浮的百分比和徽章 - 外层Box中，对准进度条右端
        if (category.isPurchased == true || category.isFree) {
            Row(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .offset(x = (-8).dp, y = 11.dp), // 对准进度条位置
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 百分比在徽章左边
                Text(
                    text = "${((category.progress ?: 0f) * 100).toInt()}%",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF6B7280),
                    modifier = Modifier.padding(end = 4.dp)
                )
                
                // 徽章
                if (category.watchCount != null) {
                    WatchCountBadge(watchCount = category.watchCount!!)
                }
            }
        }
    }
}

// 方案3预览：ConstraintLayout，标题与进度条精确间距控制
@Composable
fun CategoryCardScheme3(
    category: Category,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    CategoryShuimuCard(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
    ) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 0.dp)
        ) {
            // 创建引用 - 分离进度条和文字区域
            val (titleRow, progressBar, textRow) = createRefs()

            // 标题区
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(titleRow) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        width = Dimension.fillToConstraints
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = category.displayTitle,
                    style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Medium),
                    modifier = Modifier.weight(1f)
                )

                Icon(
                    imageVector = Icons.Default.KeyboardArrowDown,
                    contentDescription = "展开",
                    tint = Color(0xFF6B7280),
                    modifier = Modifier
                        .size(20.dp)
                        .offset(y = (-15).dp)
                )
            }

            // 进度条容器 - 固定4dp高度，显式控制0dp间距
            if (category.isPurchased == true || category.isFree) {
                Box(
                    modifier = Modifier
                        .constrainAs(progressBar) {
                            top.linkTo(titleRow.bottom, margin = 0.dp) // 终极压缩：0dp间距
                            start.linkTo(parent.start)
                            end.linkTo(textRow.start, margin = 5.dp)
                            width = Dimension.fillToConstraints
                            height = Dimension.value(4.dp) // 恢复到4dp高度容器
                        }
                ) {
                    VideoProgressBar(
                        progress = category.progress ?: 0f,
                        height = 1.dp,
                        backgroundColor = Color.Transparent,
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.TopStart) // 进度条贴顶部，更接近标题
                    )
                }
                
                // 百分比文字 - 对齐进度条顶边，精确控制与徽章间距
                Text(
                    text = "${((category.progress ?: 0f) * 100).toInt()}%",
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFF6B7280),
                    modifier = Modifier
                        .constrainAs(textRow) {
                            top.linkTo(progressBar.top) // 对齐进度条容器顶边
                            end.linkTo(parent.end, margin = 43.dp) // 徽章宽度约26dp + 7dp间距 = 33dp
                        }
                        .offset(y = (-4).dp) // 增强负offset，进一步贴近标题
                        // 移除 padding(end = 4.dp)，改用 margin 精确控制
                )
                
                // 徽章 - 完全悬浮，脱离排版流
                if (category.isPurchased == true || category.isFree) {
                    Box(
                        modifier = Modifier
                            .constrainAs(createRef()) {
                                top.linkTo(progressBar.top)
                                end.linkTo(parent.end)
                            }
                            .offset(y = (-10).dp) // 增强上浮，与百分比协调并更贴近标题
                            .wrapContentSize(unbounded = true) // 允许内容溢出
                    ) {
                        WatchCountBadge(
                            watchCount = category.watchCount ?: 0,
                            style = BadgeStyle.MIXED,
                            modifier = Modifier.graphicsLayer { clip = false } // 装饰溢出不裁剪
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CategoryCardPreview() {
    ShuimuCourseTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            val sampleCategory = Category(
                id = "1",
                title = "恋爱宝典",
                seriesId = "series1",
                price = 10000,
                isFree = false,
                isPurchased = true,
                videos = emptyList(),
                displayTitle = "恋爱宝典",
                watchCount = 12,
                progress = 0.75f
            )
            
            // 方案1：徽章回到Row B，使用graphicsLayer溢出
            Text("方案1：徽章在Row B中溢出", style = MaterialTheme.typography.labelMedium)
            CategoryCardScheme1(
                category = sampleCategory,
                onClick = {}
            )
            
            // 方案2：徽章在外层垂直居中
            Text("方案2：徽章在外层垂直居中", style = MaterialTheme.typography.labelMedium)
            CategoryCardScheme2(
                category = sampleCategory,
                onClick = {}
            )
            
            // 方案3：绝对定位布局
            Text("方案3：绝对定位布局", style = MaterialTheme.typography.labelMedium)
            CategoryCardScheme3(
                category = sampleCategory,
                onClick = {}
            )
        }
    }
} 