package com.shuimu.course.presentation.ui.screens.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuimu.course.domain.model.profile.UserProfile
import com.shuimu.course.domain.model.profile.UserStats
import com.shuimu.course.presentation.navigation.Routes
import com.shuimu.course.presentation.viewmodel.profile.ProfileUiState
import com.shuimu.course.presentation.viewmodel.profile.ProfileViewModel

@Composable
fun ProfileScreen(
    viewModel: ProfileViewModel = hiltViewModel(),
    onNavigateTo: (String) -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()

    Box(modifier = Modifier.fillMaxSize()) {
        when (val state = uiState) {
            is ProfileUiState.Loading -> CircularProgressIndicator(modifier = Modifier.align(Alignment.Center))
            is ProfileUiState.Success -> {
                ProfileContent(
                    userProfile = state.userProfile,
                    onNavigateTo = onNavigateTo
                )
            }
            is ProfileUiState.Error -> Text(
                text = state.message,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

@Composable
fun ProfileContent(
    userProfile: UserProfile,
    onNavigateTo: (String) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8FAFC))
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        ProfileHeader(userProfile)
        Spacer(modifier = Modifier.height(16.dp))
        StatsRow(userProfile.stats)
        Spacer(modifier = Modifier.height(16.dp))
        MenuSection(userProfile, onNavigateTo)
    }
}

@Composable
fun ProfileHeader(user: UserProfile) {
    val gradient = Brush.linearGradient(listOf(Color(0xFF667EEA), Color(0xFF764BA2)))
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(gradient)
                .padding(24.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Avatar
            Box(
                modifier = Modifier
                    .size(64.dp)
                    .clip(CircleShape)
                    .background(Color.White)
            )

            Spacer(modifier = Modifier.width(16.dp))

            // User Info
            Column(modifier = Modifier.weight(1f)) {
                Text(user.nickname, color = Color.White, fontSize = 20.sp, fontWeight = FontWeight.Bold)
                Spacer(modifier = Modifier.height(4.dp))
                Text("ID: ${user.id}", color = Color.White.copy(alpha = 0.8f), fontSize = 14.sp)
            }
        }
    }
}

@Composable
fun StatsRow(stats: UserStats) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            StatItem(number = stats.myCoursesCount.toString(), label = "我的课程")
            StatItem(number = stats.favoritesCount.toString(), label = "我的收藏")
            StatItem(number = stats.viewingHistoryCount.toString(), label = "观看历史")
        }
    }
}

@Composable
fun StatItem(number: String, label: String) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Text(number, fontSize = 24.sp, fontWeight = FontWeight.Bold, color = Color(0xFF667EEA))
        Spacer(modifier = Modifier.height(4.dp))
        Text(label, fontSize = 12.sp, color = Color.Gray)
    }
}

@Composable
fun MenuSection(userProfile: UserProfile, onNavigateTo: (String) -> Unit) {
    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
        MenuItem(icon = Icons.Default.ShoppingCart, title = "我的订单", onClick = { onNavigateTo(Routes.ORDERS) })
        MenuItem(icon = Icons.Default.BarChart, title = "学习报告", onClick = { onNavigateTo("reports") })
        
        val shareGradient = Brush.linearGradient(listOf(Color(0xFFFFECD2), Color(0xFFFCB69F)))
        ShareMenuItem(
            earnings = userProfile.shareEarnings,
            onClick = { onNavigateTo("share") },
            backgroundBrush = shareGradient
        )
        
        MenuItem(icon = Icons.Default.Settings, title = "账号设置", onClick = { onNavigateTo("settings") })
        MenuItem(icon = Icons.Default.HelpOutline, title = "帮助与反馈", onClick = { onNavigateTo("help") })
        MenuItem(icon = Icons.Default.Info, title = "关于我们", onClick = { onNavigateTo("about") })
    }
}

@Composable
fun MenuItem(icon: ImageVector, title: String, onClick: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(Color.White)
            .clickable(onClick = onClick)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(Color(0xFFEDE9FE))
                .padding(8.dp),
            tint = Color(0xFF8B5CF6)
        )
        Spacer(modifier = Modifier.width(12.dp))
        Text(title, modifier = Modifier.weight(1f), fontSize = 16.sp)
        Icon(Icons.Default.ChevronRight, contentDescription = "Navigate", tint = Color.Gray)
    }
}

@Composable
fun ShareMenuItem(earnings: Double, onClick: () -> Unit, backgroundBrush: Brush) {
     Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundBrush)
            .clickable(onClick = onClick)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.CardGiftcard,
            contentDescription = "分享赚钱",
            modifier = Modifier
                .size(40.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(Color(0xFFF9A825).copy(alpha = 0.2f))
                .padding(8.dp),
            tint = Color(0xFFF9A825)
        )
        Spacer(modifier = Modifier.width(12.dp))
        Column(modifier = Modifier.weight(1f)) {
             Text("分享赚钱", fontSize = 16.sp, fontWeight = FontWeight.Bold)
             Text("已累计收益 ¥${String.format("%.2f", earnings)}", fontSize = 12.sp, color = Color.Gray)
        }
        Icon(Icons.Default.ChevronRight, contentDescription = "Navigate", tint = Color.Gray)
    }
} 