package com.shuimu.course.presentation.ui.components.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.shuimu.course.presentation.ui.theme.BrandGreen
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import com.shuimu.course.presentation.ui.theme.TextSecondaryLight
import com.shuimu.course.presentation.ui.theme.White

data class PaymentResult(
    val success: Boolean,
    val orderNumber: String? = null,
    val courseTitle: String? = null,
    val amount: String? = null,
    val message: String? = null
)

@Composable
fun PaymentResultDialog(
    result: PaymentResult,
    onDismiss: () -> Unit,
    onViewOrders: (() -> Unit)? = null,
    onStartStudy: (() -> Unit)? = null,
    onRetry: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(30.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Result icon
                ResultIcon(
                    success = result.success,
                    modifier = Modifier.size(60.dp)
                )
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // Title
                Text(
                    text = if (result.success) "支付成功！" else "支付失败",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Message
                Text(
                    text = if (result.success) "恭喜您成功购买课程" else (result.message ?: "支付过程中出现问题"),
                    fontSize = 14.sp,
                    color = TextSecondaryLight,
                    textAlign = TextAlign.Center
                )
                
                // Success details
                if (result.success && result.courseTitle != null) {
                    Spacer(modifier = Modifier.height(20.dp))
                    
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFF9FAFB)
                        ),
                        shape = RoundedCornerShape(8.dp)
                    ) {
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        ) {
                            DetailRow("课程名称", result.courseTitle)
                            if (result.amount != null) {
                                DetailRow("支付金额", if (result.amount.startsWith("¥")) result.amount else "¥${result.amount}")
                            }
                            if (result.orderNumber != null) {
                                DetailRow("订单号", result.orderNumber)
                            }
                        }
                    }
                }
                
                Spacer(modifier = Modifier.height(24.dp))
                
                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    if (result.success) {
                        // Success buttons
                        OutlinedButton(
                            onClick = { onViewOrders?.invoke() },
                            modifier = Modifier.weight(1f),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text("查看订单")
                        }
                        
                        Button(
                            onClick = { onStartStudy?.invoke() },
                            modifier = Modifier.weight(1f),
                            shape = RoundedCornerShape(8.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.primary
                            )
                        ) {
                            Text("开始学习")
                        }
                    } else {
                        // Failure buttons
                        OutlinedButton(
                            onClick = onDismiss,
                            modifier = Modifier.weight(1f),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text("取消")
                        }
                        
                        Button(
                            onClick = { onRetry?.invoke() },
                            modifier = Modifier.weight(1f),
                            shape = RoundedCornerShape(8.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = MaterialTheme.colorScheme.primary
                            )
                        ) {
                            Text("重试支付")
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun ResultIcon(
    success: Boolean,
    modifier: Modifier = Modifier
) {
    val backgroundColor = if (success) BrandGreen else Color(0xFFEF4444)
    val icon = if (success) Icons.Default.Check else Icons.Default.Close
    
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(backgroundColor),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            imageVector = icon,
            contentDescription = if (success) "成功" else "失败",
            tint = White,
            modifier = Modifier.size(24.dp)
        )
    }
}

@Composable
private fun DetailRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = TextSecondaryLight
        )
        Text(
            text = value,
            fontSize = 14.sp,
            color = Color.Black,
            fontWeight = FontWeight.Medium
        )
    }
}

@Preview
@Composable
fun PaymentResultDialogSuccessPreview() {
    ShuimuCourseTheme {
        PaymentResultDialog(
            result = PaymentResult(
                success = true,
                orderNumber = "SM202312010001",
                courseTitle = "道：恋爱宝典系列",
                amount = "600.00"
            ),
            onDismiss = {},
            onViewOrders = {},
            onStartStudy = {}
        )
    }
}

@Preview
@Composable
fun PaymentResultDialogFailurePreview() {
    ShuimuCourseTheme {
        PaymentResultDialog(
            result = PaymentResult(
                success = false,
                message = "支付失败，请检查支付方式或稍后重试"
            ),
            onDismiss = {},
            onRetry = {}
        )
    }
} 