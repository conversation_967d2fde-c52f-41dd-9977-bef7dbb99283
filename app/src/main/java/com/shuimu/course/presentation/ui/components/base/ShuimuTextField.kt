package com.shuimu.course.presentation.ui.components.base

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme
import androidx.compose.material3.MaterialTheme

@Composable
fun ShuimuTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    placeholder: String = ""
) {
    OutlinedTextField(
        value = value,
        onValueChange = onValueChange,
        label = { Text(label) },
        placeholder = { Text(placeholder) },
        modifier = modifier.fillMaxWidth(),
        shape = MaterialTheme.shapes.medium,
        singleLine = true
    )
}

@Preview(name = "Shuimu Text Field Preview")
@Composable
fun ShuimuTextFieldPreview() {
    var text by remember { mutableStateOf("") }
    ShuimuCourseTheme {
        ShuimuTextField(
            value = text,
            onValueChange = { text = it },
            label = "Username",
            placeholder = "Enter your username",
            modifier = Modifier.padding(16.dp)
        )
    }
} 