package com.shuimu.course.presentation.ui.components.video

import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.shuimu.course.domain.model.Video
import com.shuimu.course.domain.model.CacheStatus
import com.shuimu.course.presentation.ui.components.display.VideoItem
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun PlaylistPanel(
    modifier: Modifier = Modifier,
    videos: List<Video>,
    currentVideoId: String,
    onVideoSelected: (String) -> Unit
) {
    LazyColumn(modifier = modifier) {
        items(videos) { video ->
            VideoItem(
                video = video,
                cachedVideo = null, // 播放列表中暂时不显示缓存信息
                isPurchasable = true, // 播放列表中的视频默认都是已购买的
                onPlay = { onVideoSelected(it.id) },
                onShowCacheDialog = { /* 这里可以添加缓存对话框逻辑 */ },
                onLockedClick = { /* 播放列表中不应该有锁定的视频 */ }
            )
        }
    }
}

@Preview
@Composable
fun PlaylistPanelPreview() {
    ShuimuCourseTheme {
        // 预览时显示空状态，实际数据由父组件传入
        PlaylistPanel(
            videos = emptyList(),
            currentVideoId = "",
            onVideoSelected = {}
        )
    }
} 