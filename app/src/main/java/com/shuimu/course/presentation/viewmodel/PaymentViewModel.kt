package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.repository.PaymentRepository
import com.shuimu.course.domain.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import javax.inject.Inject

data class PaymentState(
    val isLoading: Boolean = false,
    val paymentResult: String? = null,
    val error: String? = null,
    val serverConfirmed: Boolean? = null // 🔥 新增：服务器验证状态
)

@HiltViewModel
class PaymentViewModel @Inject constructor(
    private val paymentRepository: PaymentRepository,
    private val dataSyncManager: com.shuimu.course.data.manager.DataSyncManager,
    private val dataLayerManager: com.shuimu.course.data.manager.DataLayerManager,
    private val savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val _state = MutableStateFlow(PaymentState())
    val state = _state.asStateFlow()

    init {
        val itemId = savedStateHandle.get<String>("itemId")
        // Use itemId to fetch item details if necessary
    }

    fun processPayment(itemId: String, itemType: String, amount: Float) {
        viewModelScope.launch {
            try {
                android.util.Log.d("PaymentViewModel", "🔄 开始处理支付: $itemType $itemId ¥${amount/100}")

                // 设置加载状态
                _state.value = PaymentState(isLoading = true)

                // 1. 发起支付请求
                paymentRepository.createPayment(itemId, itemType, amount).collect { paymentResult ->
                    when (paymentResult) {
                        is Resource.Loading -> {
                            android.util.Log.d("PaymentViewModel", "💳 支付处理中...")
                            _state.value = PaymentState(isLoading = true)
                        }

                        is Resource.Success -> {
                            android.util.Log.d("PaymentViewModel", "✅ 支付成功，开始服务器验证")

                            // 2. 支付成功，进行服务器验证
                            val orderId = paymentResult.data?.substringAfter("Payment successful: ") ?: "unknown"

                            // 🔥 新增：服务器验证步骤
                            confirmPurchaseWithServer(itemId, itemType, amount, orderId)
                        }

                        is Resource.Error -> {
                            android.util.Log.e("PaymentViewModel", "❌ 支付失败: ${paymentResult.message}")
                            _state.value = PaymentState(
                                isLoading = false,
                                error = paymentResult.message ?: "支付失败"
                            )
                        }
                    }
                }

            } catch (e: Exception) {
                android.util.Log.e("PaymentViewModel", "💥 支付处理异常", e)
                _state.value = PaymentState(
                    isLoading = false,
                    error = "支付处理异常: ${e.message}"
                )
            }
        }
    }

    /**
     * 🔥 新增：服务器购买验证机制
     * 确保购买数据的安全性和一致性
     */
    private suspend fun confirmPurchaseWithServer(
        itemId: String,
        itemType: String,
        amount: Float,
        orderId: String
    ) {
        try {
            android.util.Log.d("PaymentViewModel", "🔍 服务器验证购买: $orderId")

            // 3. 上传购买信息到服务器进行验证
            val serverConfirmation = dataSyncManager.syncPurchaseImmediately(itemId, itemType, amount, orderId)

            if (serverConfirmation.isSuccess) {
                android.util.Log.d("PaymentViewModel", "✅ 服务器验证成功")

                // 4. 使用数据分层管理器的智能同步
                dataLayerManager.smartSync(
                    data = mapOf(
                        "itemId" to itemId,
                        "itemType" to itemType,
                        "amount" to amount,
                        "orderId" to orderId,
                        "verified" to true
                    ),
                    priority = com.shuimu.course.data.manager.SyncPriority.CRITICAL
                )

                // 5. 设置成功状态，包含验证信息
                _state.value = PaymentState(
                    isLoading = false,
                    paymentResult = "购买成功并已验证: $orderId",
                    serverConfirmed = true
                )

            } else {
                android.util.Log.e("PaymentViewModel", "❌ 服务器验证失败")

                // 6. 服务器验证失败，需要处理
                _state.value = PaymentState(
                    isLoading = false,
                    error = "购买验证失败，请联系客服处理订单: $orderId",
                    serverConfirmed = false
                )
            }

        } catch (e: Exception) {
            android.util.Log.e("PaymentViewModel", "🔍 服务器验证异常", e)

            // 7. 验证异常，但支付可能已成功，需要特殊处理
            _state.value = PaymentState(
                isLoading = false,
                error = "购买验证异常，请稍后重试或联系客服: $orderId",
                serverConfirmed = false
            )
        }
    }

    fun processPayment() {
        val itemId = savedStateHandle.get<String>("itemId") ?: "unknown"
        val itemType = savedStateHandle.get<String>("itemType") ?: "unknown"
        val amount = savedStateHandle.get<Float>("amount") ?: 0f

        processPayment(itemId, itemType, amount)
    }

    fun clearPaymentState() {
        _state.value = PaymentState()
    }
} 