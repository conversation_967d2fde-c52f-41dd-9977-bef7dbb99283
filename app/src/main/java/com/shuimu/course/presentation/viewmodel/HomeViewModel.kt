package com.shuimu.course.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuimu.course.domain.model.CachedVideo
import com.shuimu.course.domain.model.Series
import com.shuimu.course.domain.repository.CacheRepository
import com.shuimu.course.domain.repository.SeriesRepository
import com.shuimu.course.domain.repository.UserPreferenceRepository
import com.shuimu.course.domain.util.Resource
import com.shuimu.course.domain.usecase.cache.StartDownloadUseCase
import com.shuimu.course.data.workers.DownloadManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject
import com.shuimu.course.domain.model.Category
import com.shuimu.course.domain.model.Video
import com.shuimu.course.data.repository.update.DataChangeEvent
import com.shuimu.course.presentation.ui.update.UIUpdateState

data class HomeState(
    val isLoading: Boolean = true, // 🔥 默认加载状态，避免空白页
    val series: List<Series> = emptyList(),
    val cacheStatus: Map<String, CachedVideo> = emptyMap(),
    val expandedStates: Map<String, Boolean> = emptyMap(),
    val error: String? = null,
    val downloadMessage: String? = null,
    val currentDataSource: String = "正在加载...", // 🔥 优化初始显示
    val showSkeleton: Boolean = true, // 🔥 新增：骨架屏显示状态
    val uiUpdateState: UIUpdateState = UIUpdateState() // 🔥 新增：UI更新状态
)

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val seriesRepository: SeriesRepository,
    private val cacheRepository: CacheRepository,
    private val userPreferenceRepository: UserPreferenceRepository,
    private val startDownloadUseCase: StartDownloadUseCase,
    private val downloadManager: DownloadManager,
    private val dataLayerManager: com.shuimu.course.data.manager.DataLayerManager
) : ViewModel() {

    private val _state = MutableStateFlow(HomeState())
    val state = _state.asStateFlow()

    init {
        // 🔥 修复：删除自动获取数据，因为启动页已经预加载了数据
        // getSeries() // 删除这行，避免首页重复刷新

        // 🔥 新增：直接获取已经预加载的数据
        loadPreloadedData()

        // 🔥 新增：监听数据变化
        observeDataChanges()

        observeCacheStatus()
        observeExpandedStates()
        
        // 清理启动时的孤儿下载状态
        viewModelScope.launch {
            try {
                val result = cacheRepository.cleanupOrphanDownloads()
                if (result.isSuccess) {
                    val resetCount = result.getOrNull() ?: 0
                    if (resetCount > 0) {
                        android.util.Log.d("HomeViewModel", "已清理${resetCount}个孤儿下载状态")
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("HomeViewModel", "清理孤儿下载状态失败", e)
            }
        }
    }

    /**
     * 🔥 优化：智能检查预加载数据，避免重复请求
     */
    private fun loadPreloadedData() {
        viewModelScope.launch {
            try {
                android.util.Log.d("HomeViewModel", "🚀 首页初始化，检查预加载数据")

                // 🔥 优化：首先检查启动页是否已经有数据
                if (seriesRepository.hasPreloadedData()) {
                    val preloadedData = seriesRepository.getPreloadedData()
                    if (preloadedData != null) {
                        val (seriesData, dataSource) = preloadedData

                        // 🔥 关键检查：确保获取到的是实际数据，不是空数据
                        if (seriesData.isNotEmpty()) {
                            android.util.Log.d("HomeViewModel", "✅ 使用启动页预加载数据: ${seriesData.size}个系列，数据源: $dataSource")

                            // 🔥 瞬时显示预加载数据，无延迟
                            _state.value = _state.value.copy(
                                isLoading = false,
                                error = null,
                                series = sortSeries(seriesData),
                                currentDataSource = dataSource,
                                showSkeleton = false // 🔥 关闭骨架屏
                            )

                            android.util.Log.d("HomeViewModel", "✅ 首页数据显示完成，无需额外请求")
                            return@launch
                        } else {
                            android.util.Log.w("HomeViewModel", "⚠️ 预加载数据为空，降级到直接获取数据")
                        }
                    }
                }

                // 🔥 如果没有预加载数据，说明启动页可能失败了，降级处理
                android.util.Log.w("HomeViewModel", "⚠️ 没有预加载数据，启动页可能失败，降级到直接获取数据")
                getSeries()

            } catch (e: Exception) {
                android.util.Log.e("HomeViewModel", "检查预加载数据失败", e)
                // 出错时降级到正常获取数据
                getSeries()
            }
        }
    }

    /**
     * 🔥 新增：获取系列数据的统一入口
     */
    fun getSeries() {
        viewModelScope.launch {
            try {
                seriesRepository.getSeries().collect { result ->
                    when (result) {
                        is Resource.Success -> {
                            android.util.Log.d("HomeViewModel", "✅ 数据获取成功: ${result.data?.size}个系列")
                            _state.value = _state.value.copy(
                                isLoading = false,
                                error = null,
                                series = result.data?.let { sortSeries(it) } ?: emptyList(),
                                currentDataSource = result.dataSource ?: "未知",
                                showSkeleton = false // 🔥 关闭骨架屏
                            )
                        }
                        is Resource.Loading -> {
                            android.util.Log.d("HomeViewModel", "正在获取数据...")
                            _state.value = _state.value.copy(
                                isLoading = true,
                                error = null,
                                showSkeleton = true // 🔥 显示骨架屏
                            )
                        }
                        is Resource.Error -> {
                            android.util.Log.e("HomeViewModel", "❌ 获取数据失败: ${result.message}")
                            _state.value = _state.value.copy(
                                isLoading = false,
                                error = result.message,
                                showSkeleton = false // 🔥 关闭骨架屏
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("HomeViewModel", "💥 获取数据异常", e)
                _state.value = _state.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }



    /**
     * 🔥 新增：精确更新购买状态，替代全量刷新
     * 只更新相关的数据部分，其他内容保持不变
     */
    fun updatePurchaseStatus(itemId: String, itemType: String, isPurchased: Boolean) {
        viewModelScope.launch {
            try {
                android.util.Log.d("HomeViewModel", "🎯 精确更新购买状态: $itemType $itemId -> $isPurchased")

                val currentState = _state.value
                val updatedSeries = when (itemType) {
                    "category" -> updateCategoryPurchaseStatus(currentState.series, itemId, isPurchased)
                    "series" -> updateSeriesPurchaseStatus(currentState.series, itemId, isPurchased)
                    else -> {
                        android.util.Log.w("HomeViewModel", "未知的购买类型: $itemType")
                        currentState.series
                    }
                }

                // 只更新series数据，其他状态保持不变
                _state.value = currentState.copy(
                    series = sortSeries(updatedSeries),
                    currentDataSource = "${currentState.currentDataSource}(精确更新)" // 🔥 标记精确更新
                )

                android.util.Log.d("HomeViewModel", "✅ 购买状态更新完成，UI已刷新")

            } catch (e: Exception) {
                android.util.Log.e("HomeViewModel", "❌ 更新购买状态失败", e)
                // 失败时不影响UI，保持当前状态
            }
        }
    }

    /**
     * 更新分类的购买状态
     */
    private fun updateCategoryPurchaseStatus(
        seriesList: List<Series>,
        categoryId: String,
        isPurchased: Boolean
    ): List<Series> {
        return seriesList.map { series ->
            val updatedCategories = series.categories.map { category ->
                if (category.id == categoryId) {
                    android.util.Log.d("HomeViewModel", "📝 更新分类: ${category.title} -> isPurchased=$isPurchased")
                    category.copy(isPurchased = isPurchased)
                } else {
                    category
                }
            }

            // 如果有分类状态变化，重新计算系列价格
            val hasChanges = updatedCategories != series.categories
            if (hasChanges) {
                // 🔥 修复：需要重新计算所有系列价格，因为全套课程依赖其他系列
                android.util.Log.d("HomeViewModel", "💰 检测到分类变化，需要重新计算所有系列价格")
                val updatedSeries = series.copy(categories = updatedCategories)
                // 注意：这里应该重新计算所有系列，但暂时保持原价格
                updatedSeries
            } else {
                series.copy(categories = updatedCategories)
            }
        }
    }

    /**
     * 更新系列的购买状态
     */
    private fun updateSeriesPurchaseStatus(
        seriesList: List<Series>,
        seriesId: String,
        isPurchased: Boolean
    ): List<Series> {
        return seriesList.map { series ->
            if (series.id == seriesId) {
                android.util.Log.d("HomeViewModel", "📝 更新系列: ${series.title} -> isPurchased=$isPurchased")

                // 更新系列状态，同时更新所有分类状态
                val updatedCategories = series.categories.map { category ->
                    category.copy(isPurchased = isPurchased)
                }

                // 🔥 修复：需要重新计算所有系列价格，因为全套课程依赖其他系列
                android.util.Log.d("HomeViewModel", "💰 检测到购买状态变化，需要重新计算所有系列价格")
                val updatedSeries = series.copy(
                    isPurchased = isPurchased,
                    categories = updatedCategories
                )
                // 注意：这里应该重新计算所有系列，但暂时保持原价格
                updatedSeries
            } else {
                series
            }
        }
    }

    /**
     * 🔥 新增：智能刷新，只更新变化的数据部分
     * 替代全量刷新，提供更好的用户体验
     */
    fun refreshDataFromServer() {
        viewModelScope.launch {
            try {
                android.util.Log.d("HomeViewModel", "🔄 开始智能刷新数据")

                // 设置加载状态，但保持当前数据显示
                _state.value = _state.value.copy(isLoading = true)

                // 获取服务器最新数据
                seriesRepository.getSeries().collect { result ->
                    when (result) {
                        is Resource.Success -> {
                            val newData = result.data ?: emptyList()
                            val currentData = _state.value.series

                            // 检测数据变化
                            val hasChanges = detectDataChanges(currentData, newData)

                            if (hasChanges) {
                                android.util.Log.d("HomeViewModel", "📊 检测到数据变化，更新UI")
                                _state.value = _state.value.copy(
                                    isLoading = false,
                                    error = null,
                                    series = sortSeries(newData),
                                    currentDataSource = result.dataSource ?: "服务器数据(刷新)"
                                )
                            } else {
                                android.util.Log.d("HomeViewModel", "✅ 数据无变化，保持当前显示")
                                _state.value = _state.value.copy(
                                    isLoading = false,
                                    error = null,
                                    currentDataSource = "${_state.value.currentDataSource}(无变化)"
                                )
                            }
                            return@collect
                        }
                        is Resource.Error -> {
                            android.util.Log.e("HomeViewModel", "🔄 刷新失败，保持当前数据: ${result.message}")
                            _state.value = _state.value.copy(isLoading = false, error = null)
                            return@collect
                        }
                        is Resource.Loading -> {
                            // 已经设置了加载状态，不需要重复设置
                        }
                    }
                }

            } catch (e: Exception) {
                android.util.Log.e("HomeViewModel", "🔄 智能刷新异常", e)
                _state.value = _state.value.copy(isLoading = false, error = null)
            }
        }
    }

    /**
     * 检测数据变化
     */
    private fun detectDataChanges(currentData: List<Series>, newData: List<Series>): Boolean {
        if (currentData.size != newData.size) return true

        return currentData.zip(newData).any { (current, new) ->
            current.isPurchased != new.isPurchased ||
            current.price != new.price ||
            current.categories.size != new.categories.size ||
            current.categories.zip(new.categories).any { (currentCat, newCat) ->
                currentCat.isPurchased != newCat.isPurchased ||
                currentCat.price != newCat.price
            }
        }
    }

    // 🔥 删除临时的价格计算方法，现在使用DataLayerManager的统一逻辑

    private fun observeCacheStatus() {
        cacheRepository.getAllCacheInfo().onEach { cacheList ->
            android.util.Log.d("HomeViewModel", "缓存状态更新: ${cacheList.size}个缓存项")
            cacheList.forEach { cache ->
                android.util.Log.d("HomeViewModel", "缓存项: ${cache.videoId} - ${cache.status} - ${cache.localPath}")
            }

            val cacheMap = cacheList.associateBy { it.videoId }
            _state.value = _state.value.copy(cacheStatus = cacheMap)
        }.launchIn(viewModelScope)
    }

    private fun observeExpandedStates() {
        userPreferenceRepository.getAllExpandedStatesFlow().onEach { expandedStates ->
            _state.value = _state.value.copy(expandedStates = expandedStates)
        }.launchIn(viewModelScope)
    }

    private fun sortSeries(series: List<Series>): List<Series> {
        return series.sortedWith { a, b ->
            when {
                // Free series first
                a.isFree && !b.isFree -> -1
                !a.isFree && b.isFree -> 1
                // Complete package last
                a.isPackage && !b.isPackage -> 1
                !a.isPackage && b.isPackage -> -1
                // Otherwise maintain original order
                else -> 0
            }
        }
    }

    fun getExpandedState(id: String, defaultExpanded: Boolean): Boolean {
        return _state.value.expandedStates[id] ?: defaultExpanded
    }

    fun setExpandedState(id: String, isExpanded: Boolean) {
        viewModelScope.launch {
            userPreferenceRepository.setExpandedState(id, isExpanded)
        }
    }

    /**
     * 开始下载视频，返回详细的状态反馈
     */
    fun downloadVideo(videoId: String, cloudUrl: String, videoTitle: String = ""): Flow<String> {
        return flow {
            try {
                android.util.Log.d("HomeViewModel", "开始下载视频: $videoId")
                
                startDownloadUseCase(videoId).collect { resource ->
                    when (resource) {
                        is Resource.Loading -> {
                            _state.value = _state.value.copy(downloadMessage = "正在处理...")
                            emit("正在处理...")
                        }
                        is Resource.Success -> {
                            val message = resource.data ?: "下载已启动"
                            _state.value = _state.value.copy(downloadMessage = message)
                            emit(message)
                            android.util.Log.d("HomeViewModel", "下载启动成功: $message")
                        }
                        is Resource.Error -> {
                            val errorMessage = resource.message ?: "下载失败"
                            _state.value = _state.value.copy(downloadMessage = errorMessage)
                            emit(errorMessage)
                            android.util.Log.e("HomeViewModel", "下载启动失败: $errorMessage")
                        }
                    }
                }
            } catch (e: Exception) {
                val errorMessage = "启动下载失败：${e.message}"
                _state.value = _state.value.copy(downloadMessage = errorMessage)
                emit(errorMessage)
                android.util.Log.e("HomeViewModel", "下载异常", e)
            }
        }
    }
    
    /**
     * 启动视频下载（改进版，使用回调避免协程作用域问题）
     */
    fun startVideoDownload(
        videoId: String, 
        cloudUrl: String, 
        videoTitle: String = "",
        onResult: (String) -> Unit
    ) {
        android.util.Log.d("HomeViewModel", "=== startVideoDownload调用开始 ===")
        android.util.Log.d("HomeViewModel", "参数: videoId=$videoId, cloudUrl=$cloudUrl, videoTitle=$videoTitle")
        
        viewModelScope.launch {
            try {
                android.util.Log.d("HomeViewModel", "开始执行startDownloadUseCase...")
                
                startDownloadUseCase(videoId).collect { resource ->
                    android.util.Log.d("HomeViewModel", "收到UseCase结果: ${resource.javaClass.simpleName}")
                    when (resource) {
                        is Resource.Loading -> {
                            android.util.Log.d("HomeViewModel", "Resource.Loading收到")
                        }
                        is Resource.Success -> {
                            val message = resource.data ?: "下载已启动"
                            android.util.Log.d("HomeViewModel", "Resource.Success收到: $message")
                            onResult(message)

                            // 启动成功后，监控WorkManager状态以检测可能的失败
                            // 延迟一下再开始监控，避免重复提示
                            monitorDownloadStatus(videoId) { statusMessage ->
                                // 只有在状态发生重要变化时才显示消息
                                if (statusMessage.contains("失败") || statusMessage.contains("完成") || statusMessage.contains("错误")) {
                                    onResult(statusMessage)
                                }
                            }
                        }
                        is Resource.Error -> {
                            val errorMessage = resource.message ?: "下载失败"
                            android.util.Log.e("HomeViewModel", "Resource.Error收到: $errorMessage")
                            onResult("下载启动失败: $errorMessage")
                        }
                    }
                }
                
                android.util.Log.d("HomeViewModel", "startDownloadUseCase.collect执行完成")
            } catch (e: Exception) {
                val errorMessage = "启动下载失败：${e.message}"
                android.util.Log.e("HomeViewModel", "协程异常捕获", e)
                onResult(errorMessage)
            }
        }
        
        android.util.Log.d("HomeViewModel", "=== startVideoDownload调用结束 ===")
    }
    
    /**
     * 监控下载状态，检测WorkManager任务是否立即失败
     */
    private fun monitorDownloadStatus(videoId: String, onStatusUpdate: (String) -> Unit) {
        viewModelScope.launch {
            try {
                // 延迟一下再检查，给WorkManager时间启动任务
                delay(2000)
                
                var hasStartedRunning = false
                var enqueuedTime = System.currentTimeMillis()
                
                downloadManager.getDownloadProgress(videoId).collect { workInfo ->
                    android.util.Log.d("HomeViewModel", "WorkInfo状态监控: ${workInfo?.state}")
                    
                    when (workInfo?.state) {
                        androidx.work.WorkInfo.State.FAILED -> {
                            android.util.Log.e("HomeViewModel", "WorkManager任务失败")
                            onStatusUpdate("下载失败：网络连接异常或服务器错误")

                            // 更新缓存状态为失败
                            cacheRepository.updateDownloadStatus(
                                videoId,
                                com.shuimu.course.domain.model.CacheStatus.FAILED,
                                "WorkManager任务失败"
                            )
                            return@collect // 停止监控
                        }
                        androidx.work.WorkInfo.State.CANCELLED -> {
                            android.util.Log.w("HomeViewModel", "WorkManager任务被取消")
                            onStatusUpdate("下载任务被取消")
                        }
                        androidx.work.WorkInfo.State.RUNNING -> {
                            android.util.Log.d("HomeViewModel", "WorkManager任务正在运行")
                            hasStartedRunning = true
                            
                            // 检查Worker返回的进度数据
                            val progress = workInfo.progress.getInt("progress", 0)
                            val progressVideoId = workInfo.progress.getString("videoId")
                            
                            if (progressVideoId == videoId && progress > 0) {
                                android.util.Log.d("HomeViewModel", "下载进度更新: $progress%")
                                onStatusUpdate("正在下载: $progress%")
                            } else {
                                onStatusUpdate("正在下载...")
                            }
                            
                            // 更新状态为正在下载
                            cacheRepository.updateDownloadStatus(
                                videoId, 
                                com.shuimu.course.domain.model.CacheStatus.DOWNLOADING
                            )
                        }
                        androidx.work.WorkInfo.State.SUCCEEDED -> {
                            android.util.Log.d("HomeViewModel", "WorkManager任务成功完成")
                            onStatusUpdate("下载完成！")
                            // 任务成功，停止监控
                            return@collect
                        }
                        androidx.work.WorkInfo.State.ENQUEUED -> {
                            android.util.Log.d("HomeViewModel", "WorkManager任务已入队")
                            val currentTime = System.currentTimeMillis()
                            
                            // 如果任务排队超过5秒且从未运行过，可能是约束不满足
                            if (currentTime - enqueuedTime > 5000 && !hasStartedRunning) {
                                android.util.Log.w("HomeViewModel", "任务长时间排队，可能网络约束不满足")
                                onStatusUpdate("等待合适网络环境开始下载...")
                                
                                // 更新状态为等待网络
                                cacheRepository.updateDownloadStatus(
                                    videoId, 
                                    com.shuimu.course.domain.model.CacheStatus.WAITING_NETWORK,
                                    "等待网络条件满足"
                                )
                            }
                        }
                        else -> {
                            android.util.Log.d("HomeViewModel", "WorkManager任务状态: ${workInfo?.state}")
                        }
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("HomeViewModel", "监控下载状态异常", e)
            }
        }
    }
    
    /**
     * 清除下载消息
     */
    fun clearDownloadMessage() {
        _state.value = _state.value.copy(downloadMessage = null)
    }

    /**
     * 🔥 新增：监听数据变化
     */
    private fun observeDataChanges() {
        viewModelScope.launch {
            seriesRepository.observeDataChanges().collect { event ->
                handleDataChangeEvent(event)
            }
        }
    }

    /**
     * 🔥 新增：处理数据变化事件
     */
    private suspend fun handleDataChangeEvent(event: DataChangeEvent) {
        android.util.Log.d("HomeViewModel", "📢 收到数据变化事件: $event")

        when (event) {
            is DataChangeEvent.SeriesUpdated -> {
                updateSeriesData(event.seriesIds, event.changeType)
            }
            is DataChangeEvent.CategoriesUpdated -> {
                updateCategoriesData(event.categoryIds)
            }
            is DataChangeEvent.VideosUpdated -> {
                updateVideosData(event.videoIds)
            }
            is DataChangeEvent.DataDeleted -> {
                handleDataDeletion(event.deletedItems)
            }
            is DataChangeEvent.DataAdded -> {
                handleDataAddition(event.newItems)
            }
            is DataChangeEvent.PurchaseStatusUpdated -> {
                handlePurchaseStatusUpdate(event.seriesIds, event.categoryIds)
            }
            is DataChangeEvent.FullRefreshNeeded -> {
                refreshAllData()
            }
        }
    }

    /**
     * 🔥 新增：更新系列数据
     */
    private suspend fun updateSeriesData(
        changedSeriesIds: List<String>,
        changeType: DataChangeEvent.ChangeType
    ) {
        android.util.Log.d("HomeViewModel", "🔄 更新系列数据: ${changedSeriesIds.size}个")

        // 开始更新状态
        _state.value = _state.value.copy(
            uiUpdateState = UIUpdateState.startUpdate(
                DataChangeEvent.SeriesUpdated(changedSeriesIds, changeType),
                changedSeriesIds.size
            )
        )

        try {
            // 获取最新数据
            seriesRepository.getSeries().first().let { resource ->
                if (resource is Resource.Success && resource.data != null) {
                    val currentSeries = _state.value.series.toMutableList()

                    when (changeType) {
                        DataChangeEvent.ChangeType.MODIFIED -> {
                            // 更新变化的系列
                            changedSeriesIds.forEach { seriesId ->
                                val updatedSeries = resource.data.find { it.id == seriesId }
                                updatedSeries?.let { series ->
                                    val index = currentSeries.indexOfFirst { it.id == seriesId }
                                    if (index != -1) {
                                        currentSeries[index] = series
                                        android.util.Log.d("HomeViewModel", "✅ 更新系列: ${series.title}")
                                    }
                                }
                            }
                        }
                        DataChangeEvent.ChangeType.ADDED -> {
                            // 添加新系列
                            changedSeriesIds.forEach { seriesId ->
                                val newSeries = resource.data.find { it.id == seriesId }
                                newSeries?.let { series ->
                                    if (currentSeries.none { it.id == seriesId }) {
                                        currentSeries.add(series)
                                        android.util.Log.d("HomeViewModel", "✅ 添加系列: ${series.title}")
                                    }
                                }
                            }
                        }
                        DataChangeEvent.ChangeType.DELETED -> {
                            // 删除系列
                            currentSeries.removeAll { it.id in changedSeriesIds }
                            android.util.Log.d("HomeViewModel", "✅ 删除系列: ${changedSeriesIds.size}个")
                        }
                    }

                    // 更新UI状态
                    _state.value = _state.value.copy(
                        series = sortSeries(currentSeries),
                        currentDataSource = "已更新数据",
                        uiUpdateState = UIUpdateState.completeUpdate(_state.value.uiUpdateState)
                    )

                    // 3秒后隐藏通知
                    delay(3000)
                    _state.value = _state.value.copy(
                        uiUpdateState = UIUpdateState.hideNotification(_state.value.uiUpdateState)
                    )
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("HomeViewModel", "更新系列数据失败", e)
            // 更新失败，隐藏更新状态
            _state.value = _state.value.copy(
                uiUpdateState = UIUpdateState.hideNotification(_state.value.uiUpdateState)
            )
        }
    }

    /**
     * 🔥 新增：处理购买状态更新
     */
    private suspend fun handlePurchaseStatusUpdate(
        seriesIds: List<String>,
        categoryIds: List<String>
    ) {
        android.util.Log.d("HomeViewModel", "💰 处理购买状态更新")

        // 购买状态更新是重要事件，需要立即刷新数据
        updateSeriesData(seriesIds, DataChangeEvent.ChangeType.MODIFIED)
    }

    /**
     * 🔥 新增：处理其他数据变化（简化实现）
     */
    private suspend fun updateCategoriesData(categoryIds: List<String>) {
        android.util.Log.d("HomeViewModel", "🔄 分类数据更新: ${categoryIds.size}个")
        // 分类更新通常需要刷新整个系列数据
        refreshAllData()
    }

    private suspend fun updateVideosData(videoIds: List<String>) {
        android.util.Log.d("HomeViewModel", "🔄 视频数据更新: ${videoIds.size}个")
        // 视频更新可以静默处理，不需要UI通知
    }

    private suspend fun handleDataDeletion(deletedItems: com.shuimu.course.data.repository.update.DataDiffResult) {
        android.util.Log.d("HomeViewModel", "🗑️ 处理数据删除")
        refreshAllData()
    }

    private suspend fun handleDataAddition(newItems: com.shuimu.course.data.repository.update.DataDiffResult) {
        android.util.Log.d("HomeViewModel", "➕ 处理数据新增")
        refreshAllData()
    }

    private suspend fun refreshAllData() {
        android.util.Log.d("HomeViewModel", "🔄 完全刷新数据")
        getSeries()
    }
}