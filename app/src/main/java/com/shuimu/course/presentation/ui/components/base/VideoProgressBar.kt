package com.shuimu.course.presentation.ui.components.base

import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun VideoProgressBar(
    modifier: Modifier = Modifier,
    progress: Float, // A value from 0.0f to 1.0f
    height: Dp = 6.dp,
    color: Color = MaterialTheme.colorScheme.primary,
    backgroundColor: Color = color.copy(alpha = 0.3f),
    cornerRadius: Dp = height / 2
) {
    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = tween(durationMillis = 600, easing = CubicBezierEasing(0.4f, 0.0f, 0.2f, 1.0f)),
        label = "progress_animation"
    )

    val infiniteTransition = rememberInfiniteTransition(label = "shine_transition")
    val shinePosition by infiniteTransition.animateFloat(
        initialValue = -0.2f, // Start off-screen to the left
        targetValue = 1.2f,  // End off-screen to the right
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1500, delayMillis = 500, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "shine_position"
    )

    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(height)
    ) {
        // Draw the background track
        drawTrack(backgroundColor, cornerRadius.toPx())

        // Draw the progress bar
        val progressWidth = size.width * animatedProgress
        drawProgress(color, progressWidth, cornerRadius.toPx())

        // Draw the animated shine on top of the progress bar
        if (animatedProgress > 0) {
            drawShine(progressWidth, shinePosition)
        }
    }
}

private fun DrawScope.drawTrack(color: Color, cornerRadius: Float) {
    drawRoundRect(
        color = color,
        cornerRadius = CornerRadius(cornerRadius),
        size = size
    )
}

private fun DrawScope.drawProgress(color: Color, progressWidth: Float, cornerRadius: Float) {
    drawRoundRect(
        color = color,
        cornerRadius = CornerRadius(cornerRadius),
        size = size.copy(width = progressWidth)
    )
}

private fun DrawScope.drawShine(progressWidth: Float, position: Float) {
    val shineWidth = size.width * 0.15f
    val shineBrush = Brush.horizontalGradient(
        colors = listOf(Color.White.copy(0f), Color.White.copy(0.4f), Color.White.copy(0f)),
        startX = (progressWidth * position) - shineWidth,
        endX = progressWidth * position
    )
    drawRoundRect(
        brush = shineBrush,
        cornerRadius = CornerRadius(size.height / 2),
        size = size.copy(width = progressWidth)
    )
}

@Preview(showBackground = true, widthDp = 300)
@Composable
fun VideoProgressBarPreview() {
    ShuimuCourseTheme {
        Column(modifier = Modifier.padding(20.dp), verticalArrangement = Arrangement.spacedBy(20.dp)) {
            VideoProgressBar(progress = 0.75f)
            VideoProgressBar(progress = 0.3f, color = Color.Red)
            VideoProgressBar(progress = 1.0f, height = 4.dp)
        }
    }
} 