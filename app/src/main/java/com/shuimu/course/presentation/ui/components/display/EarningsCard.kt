package com.shuimu.course.presentation.ui.components.display

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.domain.model.Earnings
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun EarningsCard(
    modifier: Modifier = Modifier,
    earnings: Earnings
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text("累计收益: ¥${earnings.totalEarnings}", style = MaterialTheme.typography.titleLarge)
            Text("可提现: ¥${earnings.availableForWithdrawal}", style = MaterialTheme.typography.bodyMedium)
            Text("已提现: ¥${earnings.withdrawn}", style = MaterialTheme.typography.bodyMedium)
            Text("分享用户数: ${earnings.sharedUsersCount}", style = MaterialTheme.typography.bodyMedium)
        }
    }
}

@Preview
@Composable
fun EarningsCardPreview() {
    ShuimuCourseTheme {
        val dummy = Earnings(
            totalEarnings = 520.0,
            availableForWithdrawal = 200.0,
            withdrawn = 320.0,
            sharedUsersCount = 15,
            monthlyEarnings = emptyList()
        )
        EarningsCard(earnings = dummy)
    }
} 