package com.shuimu.course.presentation.utils

import java.text.DecimalFormat

/**
 * 格式化数字，用于显示"万"或"k"
 * @param count 待格式化的整数
 * @return 格式化后的字符串
 */
fun formatCount(count: Int?): String {
    if (count == null || count < 0) {
        return "0"
    }
    return when {
        count >= 10000 -> {
            val df = DecimalFormat("#.#万")
            df.format(count / 10000.0)
        }
        count >= 1000 -> {
            val df = DecimalFormat("#.#k")
            df.format(count / 1000.0)
        }
        else -> count.toString()
    }
}

/**
 * 格式化播放量（Long类型），用于显示"万"或"k"
 * @param playCount 待格式化的播放量
 * @return 格式化后的字符串
 */
fun formatPlayCount(playCount: Long?): String {
    if (playCount == null || playCount < 0) {
        return "0"
    }
    return when {
        playCount >= 10000 -> {
            val df = DecimalFormat("#.#万")
            df.format(playCount / 10000.0)
        }
        playCount >= 1000 -> {
            val df = DecimalFormat("#.#k")
            df.format(playCount / 1000.0)
        }
        else -> playCount.toString()
    }
}

/**
 * 格式化视频时长（秒转换为mm:ss格式）
 * @param durationSeconds 时长（秒）
 * @return 格式化后的时长字符串
 */
fun formatDuration(durationSeconds: Int): String {
    val minutes = durationSeconds / 60
    val seconds = durationSeconds % 60
    return String.format("%02d:%02d", minutes, seconds)
} 