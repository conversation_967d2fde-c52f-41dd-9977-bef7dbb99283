package com.shuimu.course.presentation.ui.components.base

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.ui.draw.shadow
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.shuimu.course.presentation.ui.theme.ShuimuCourseTheme

@Composable
fun CategoryShuimuCard(
    modifier: Modifier = Modifier,
    containerColor: Color = MaterialTheme.colorScheme.surface,
    content: @Composable ColumnScope.() -> Unit
) {
    val density = LocalDensity.current
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .shadow(2.dp, RoundedCornerShape(12.dp), clip = false)   // 阴影但不裁剪
            .background(containerColor, RoundedCornerShape(12.dp))   // 背景
            .graphicsLayer { clip = false }  // 🔥 关键修复：禁用裁剪，让徽章可以越界显示
    ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .drawBehind {
                        // 🔥 方案1：绘制足够宽的圆角矩形，避免被Card圆角裁切
                        val borderWidth = with(density) { 2.dp.toPx() }  // 可见宽度2dp
                        val safeWidth = with(density) { 6.dp.toPx() }    // 绘制宽度6dp (大于圆角裁切损耗)
                        val cornerRadius = with(density) { 12.dp.toPx() }
                        
                        // 绘制宽度足够的蓝色圆角矩形
                        drawRoundRect(
                            color = Color(0xFF667EEA), // #667eea 蓝色
                            topLeft = Offset.Zero,
                            size = Size(safeWidth, size.height),
                            cornerRadius = CornerRadius(cornerRadius, cornerRadius)
                        )
                        
                        // 用背景色覆盖多余部分，只保留左侧2dp可见
                        drawRect(
                            color = containerColor, // 使用卡片背景色覆盖
                            topLeft = Offset(borderWidth, 0f),
                            size = Size(safeWidth - borderWidth, size.height)
                        )
                    }
                    .padding(
                        start = 8.dp,    // 🔥 左边距8dp - 复刻原型的 padding-left: 8px
                        end = 0.dp,      // 🔥 右边距0dp - 复刻原型的 padding-right: 0
                        top = 6.dp,     // 🔥 上边距12dp - 复刻原型的 padding-top: 12px
                        bottom = 8.dp   // 🔥 下边距16dp - 复刻原型的 padding-bottom: 16px
                    )
            ) {
                content()
            }
    }
}

@Preview(name = "Category Shuimu Card Preview")
@Composable
fun CategoryShuimuCardPreview() {
    ShuimuCourseTheme {
        CategoryShuimuCard(modifier = Modifier.padding(16.dp)) {
            Text(text = "这是分类卡片的内容，带有圆角左边框装饰")
        }
    }
} 