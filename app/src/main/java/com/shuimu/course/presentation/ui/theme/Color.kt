package com.shuimu.course.presentation.ui.theme

import androidx.compose.ui.graphics.Color

// Primary brand colors
val GreenPrimary = Color(0xFF4CAF50)
val BlueAccent = Color(0xFF2196F3)
val BlueAccentDark = Color(0xFF1565C0)
val BackgroundLightGray = Color(0xFFF8FAFC)

// Payment specific brand colors
val BrandGreen = Color(0xFF34C759)  // iOS-style green for payment button
val DangerRed = Color(0xFFFF3B30)   // iOS-style red for price highlight

// Backgrounds & Surfaces
val BackgroundLight = Color(0xFFFFFFFF)
val BackgroundDark = Color(0xFF121212)

// Text Colors
val TextPrimaryLight = Color(0xFF212121)
val TextPrimaryDark = Color(0xFFFAFAFA)
val TextTertiaryLight = Color(0xFF9E9E9E)
val TextSecondaryLight = Color(0xFF757575)  // For subtitle text

// Utility
val White = Color(0xFFFFFFFF)
val RedError = Color(0xFFB00020) 