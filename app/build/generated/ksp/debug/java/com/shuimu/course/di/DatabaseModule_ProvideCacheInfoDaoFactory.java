package com.shuimu.course.di;

import com.shuimu.course.data.local.dao.CacheInfoDao;
import com.shuimu.course.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DatabaseModule_ProvideCacheInfoDaoFactory implements Factory<CacheInfoDao> {
  private final Provider<AppDatabase> dbProvider;

  public DatabaseModule_ProvideCacheInfoDaoFactory(Provider<AppDatabase> dbProvider) {
    this.dbProvider = dbProvider;
  }

  @Override
  public CacheInfoDao get() {
    return provideCacheInfoDao(dbProvider.get());
  }

  public static DatabaseModule_ProvideCacheInfoDaoFactory create(Provider<AppDatabase> dbProvider) {
    return new DatabaseModule_ProvideCacheInfoDaoFactory(dbProvider);
  }

  public static CacheInfoDao provideCacheInfoDao(AppDatabase db) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideCacheInfoDao(db));
  }
}
