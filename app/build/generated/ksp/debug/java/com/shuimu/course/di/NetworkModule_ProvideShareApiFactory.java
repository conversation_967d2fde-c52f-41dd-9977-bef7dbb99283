package com.shuimu.course.di;

import com.shuimu.course.data.remote.api.ShareApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideShareApiFactory implements Factory<ShareApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideShareApiFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public ShareApi get() {
    return provideShareApi(retrofitProvider.get());
  }

  public static NetworkModule_ProvideShareApiFactory create(Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideShareApiFactory(retrofitProvider);
  }

  public static ShareApi provideShareApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideShareApi(retrofit));
  }
}
