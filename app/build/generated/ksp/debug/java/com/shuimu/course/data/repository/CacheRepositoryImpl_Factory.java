package com.shuimu.course.data.repository;

import com.shuimu.course.data.local.dao.CacheInfoDao;
import com.shuimu.course.data.local.dao.VideoDao;
import com.shuimu.course.data.remote.api.CacheApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class CacheRepositoryImpl_Factory implements Factory<CacheRepositoryImpl> {
  private final Provider<CacheInfoDao> cacheInfoDaoProvider;

  private final Provider<VideoDao> videoDaoProvider;

  private final Provider<CacheApi> cacheApiProvider;

  public CacheRepositoryImpl_Factory(Provider<CacheInfoDao> cacheInfoDaoProvider,
      Provider<VideoDao> videoDaoProvider, Provider<CacheApi> cacheApiProvider) {
    this.cacheInfoDaoProvider = cacheInfoDaoProvider;
    this.videoDaoProvider = videoDaoProvider;
    this.cacheApiProvider = cacheApiProvider;
  }

  @Override
  public CacheRepositoryImpl get() {
    return newInstance(cacheInfoDaoProvider.get(), videoDaoProvider.get(), cacheApiProvider.get());
  }

  public static CacheRepositoryImpl_Factory create(Provider<CacheInfoDao> cacheInfoDaoProvider,
      Provider<VideoDao> videoDaoProvider, Provider<CacheApi> cacheApiProvider) {
    return new CacheRepositoryImpl_Factory(cacheInfoDaoProvider, videoDaoProvider, cacheApiProvider);
  }

  public static CacheRepositoryImpl newInstance(CacheInfoDao cacheInfoDao, VideoDao videoDao,
      CacheApi cacheApi) {
    return new CacheRepositoryImpl(cacheInfoDao, videoDao, cacheApi);
  }
}
