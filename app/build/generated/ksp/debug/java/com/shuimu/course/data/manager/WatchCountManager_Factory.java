package com.shuimu.course.data.manager;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class WatchCountManager_Factory implements Factory<WatchCountManager> {
  private final Provider<DataSyncManager> dataSyncManagerProvider;

  private final Provider<SmartBatchUpdateManager> smartBatchUpdateManagerProvider;

  public WatchCountManager_Factory(Provider<DataSyncManager> dataSyncManagerProvider,
      Provider<SmartBatchUpdateManager> smartBatchUpdateManagerProvider) {
    this.dataSyncManagerProvider = dataSyncManagerProvider;
    this.smartBatchUpdateManagerProvider = smartBatchUpdateManagerProvider;
  }

  @Override
  public WatchCountManager get() {
    return newInstance(dataSyncManagerProvider.get(), smartBatchUpdateManagerProvider.get());
  }

  public static WatchCountManager_Factory create(Provider<DataSyncManager> dataSyncManagerProvider,
      Provider<SmartBatchUpdateManager> smartBatchUpdateManagerProvider) {
    return new WatchCountManager_Factory(dataSyncManagerProvider, smartBatchUpdateManagerProvider);
  }

  public static WatchCountManager newInstance(DataSyncManager dataSyncManager,
      SmartBatchUpdateManager smartBatchUpdateManager) {
    return new WatchCountManager(dataSyncManager, smartBatchUpdateManager);
  }
}
