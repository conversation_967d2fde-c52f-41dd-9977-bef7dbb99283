package com.shuimu.course.data.repository.update;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DataDiffDetector_Factory implements Factory<DataDiffDetector> {
  @Override
  public DataDiffDetector get() {
    return newInstance();
  }

  public static DataDiffDetector_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DataDiffDetector newInstance() {
    return new DataDiffDetector();
  }

  private static final class InstanceHolder {
    private static final DataDiffDetector_Factory INSTANCE = new DataDiffDetector_Factory();
  }
}
