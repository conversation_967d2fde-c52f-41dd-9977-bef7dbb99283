package com.shuimu.course.data.manager;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class UserManager_Factory implements Factory<UserManager> {
  private final Provider<Context> contextProvider;

  public UserManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public UserManager get() {
    return newInstance(contextProvider.get());
  }

  public static UserManager_Factory create(Provider<Context> contextProvider) {
    return new UserManager_Factory(contextProvider);
  }

  public static UserManager newInstance(Context context) {
    return new UserManager(context);
  }
}
