package com.shuimu.course.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.net.ssl.HostnameVerifier;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideHostnameVerifierFactory implements Factory<HostnameVerifier> {
  @Override
  public HostnameVerifier get() {
    return provideHostnameVerifier();
  }

  public static NetworkModule_ProvideHostnameVerifierFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static HostnameVerifier provideHostnameVerifier() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideHostnameVerifier());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvideHostnameVerifierFactory INSTANCE = new NetworkModule_ProvideHostnameVerifierFactory();
  }
}
