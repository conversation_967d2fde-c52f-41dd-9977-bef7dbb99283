package com.shuimu.course.data.manager;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SmartBatchUpdateManager_Factory implements Factory<SmartBatchUpdateManager> {
  private final Provider<Context> contextProvider;

  private final Provider<DataSyncManager> dataSyncManagerProvider;

  public SmartBatchUpdateManager_Factory(Provider<Context> contextProvider,
      Provider<DataSyncManager> dataSyncManagerProvider) {
    this.contextProvider = contextProvider;
    this.dataSyncManagerProvider = dataSyncManagerProvider;
  }

  @Override
  public SmartBatchUpdateManager get() {
    return newInstance(contextProvider.get(), dataSyncManagerProvider.get());
  }

  public static SmartBatchUpdateManager_Factory create(Provider<Context> contextProvider,
      Provider<DataSyncManager> dataSyncManagerProvider) {
    return new SmartBatchUpdateManager_Factory(contextProvider, dataSyncManagerProvider);
  }

  public static SmartBatchUpdateManager newInstance(Context context,
      DataSyncManager dataSyncManager) {
    return new SmartBatchUpdateManager(context, dataSyncManager);
  }
}
