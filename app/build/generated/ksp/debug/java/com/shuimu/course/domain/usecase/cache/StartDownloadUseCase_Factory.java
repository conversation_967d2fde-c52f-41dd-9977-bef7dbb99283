package com.shuimu.course.domain.usecase.cache;

import com.shuimu.course.data.workers.DownloadManager;
import com.shuimu.course.domain.repository.VideoRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class StartDownloadUseCase_Factory implements Factory<StartDownloadUseCase> {
  private final Provider<DownloadManager> downloadManagerProvider;

  private final Provider<VideoRepository> videoRepositoryProvider;

  public StartDownloadUseCase_Factory(Provider<DownloadManager> downloadManagerProvider,
      Provider<VideoRepository> videoRepositoryProvider) {
    this.downloadManagerProvider = downloadManagerProvider;
    this.videoRepositoryProvider = videoRepositoryProvider;
  }

  @Override
  public StartDownloadUseCase get() {
    return newInstance(downloadManagerProvider.get(), videoRepositoryProvider.get());
  }

  public static StartDownloadUseCase_Factory create(
      Provider<DownloadManager> downloadManagerProvider,
      Provider<VideoRepository> videoRepositoryProvider) {
    return new StartDownloadUseCase_Factory(downloadManagerProvider, videoRepositoryProvider);
  }

  public static StartDownloadUseCase newInstance(DownloadManager downloadManager,
      VideoRepository videoRepository) {
    return new StartDownloadUseCase(downloadManager, videoRepository);
  }
}
