package com.shuimu.course.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import okhttp3.Dns;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideCustomDnsFactory implements Factory<Dns> {
  @Override
  public Dns get() {
    return provideCustomDns();
  }

  public static NetworkModule_ProvideCustomDnsFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static Dns provideCustomDns() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideCustomDns());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvideCustomDnsFactory INSTANCE = new NetworkModule_ProvideCustomDnsFactory();
  }
}
