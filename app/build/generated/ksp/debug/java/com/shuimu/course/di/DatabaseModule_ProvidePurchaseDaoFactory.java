package com.shuimu.course.di;

import com.shuimu.course.data.local.dao.PurchaseDao;
import com.shuimu.course.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DatabaseModule_ProvidePurchaseDaoFactory implements Factory<PurchaseDao> {
  private final Provider<AppDatabase> dbProvider;

  public DatabaseModule_ProvidePurchaseDaoFactory(Provider<AppDatabase> dbProvider) {
    this.dbProvider = dbProvider;
  }

  @Override
  public PurchaseDao get() {
    return providePurchaseDao(dbProvider.get());
  }

  public static DatabaseModule_ProvidePurchaseDaoFactory create(Provider<AppDatabase> dbProvider) {
    return new DatabaseModule_ProvidePurchaseDaoFactory(dbProvider);
  }

  public static PurchaseDao providePurchaseDao(AppDatabase db) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.providePurchaseDao(db));
  }
}
