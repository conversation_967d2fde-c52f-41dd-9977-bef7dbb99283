package com.shuimu.course.data.repository.update;

import com.shuimu.course.data.local.dao.CategoryDao;
import com.shuimu.course.data.local.dao.SeriesDao;
import com.shuimu.course.data.local.dao.VideoDao;
import com.shuimu.course.data.local.database.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class IncrementalUpdateStrategy_Factory implements Factory<IncrementalUpdateStrategy> {
  private final Provider<AppDatabase> databaseProvider;

  private final Provider<SeriesDao> seriesDaoProvider;

  private final Provider<CategoryDao> categoryDaoProvider;

  private final Provider<VideoDao> videoDaoProvider;

  public IncrementalUpdateStrategy_Factory(Provider<AppDatabase> databaseProvider,
      Provider<SeriesDao> seriesDaoProvider, Provider<CategoryDao> categoryDaoProvider,
      Provider<VideoDao> videoDaoProvider) {
    this.databaseProvider = databaseProvider;
    this.seriesDaoProvider = seriesDaoProvider;
    this.categoryDaoProvider = categoryDaoProvider;
    this.videoDaoProvider = videoDaoProvider;
  }

  @Override
  public IncrementalUpdateStrategy get() {
    return newInstance(databaseProvider.get(), seriesDaoProvider.get(), categoryDaoProvider.get(), videoDaoProvider.get());
  }

  public static IncrementalUpdateStrategy_Factory create(Provider<AppDatabase> databaseProvider,
      Provider<SeriesDao> seriesDaoProvider, Provider<CategoryDao> categoryDaoProvider,
      Provider<VideoDao> videoDaoProvider) {
    return new IncrementalUpdateStrategy_Factory(databaseProvider, seriesDaoProvider, categoryDaoProvider, videoDaoProvider);
  }

  public static IncrementalUpdateStrategy newInstance(AppDatabase database, SeriesDao seriesDao,
      CategoryDao categoryDao, VideoDao videoDao) {
    return new IncrementalUpdateStrategy(database, seriesDao, categoryDao, videoDao);
  }
}
