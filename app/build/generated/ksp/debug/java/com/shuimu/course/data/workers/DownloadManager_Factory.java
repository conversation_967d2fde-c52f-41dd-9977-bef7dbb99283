package com.shuimu.course.data.workers;

import android.content.Context;
import com.shuimu.course.data.manager.AppConfigManager;
import com.shuimu.course.data.manager.DataLayerManager;
import com.shuimu.course.domain.repository.CacheRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DownloadManager_Factory implements Factory<DownloadManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CacheRepository> cacheRepositoryProvider;

  private final Provider<AppConfigManager> appConfigManagerProvider;

  private final Provider<DataLayerManager> dataLayerManagerProvider;

  public DownloadManager_Factory(Provider<Context> contextProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<AppConfigManager> appConfigManagerProvider,
      Provider<DataLayerManager> dataLayerManagerProvider) {
    this.contextProvider = contextProvider;
    this.cacheRepositoryProvider = cacheRepositoryProvider;
    this.appConfigManagerProvider = appConfigManagerProvider;
    this.dataLayerManagerProvider = dataLayerManagerProvider;
  }

  @Override
  public DownloadManager get() {
    return newInstance(contextProvider.get(), cacheRepositoryProvider.get(), appConfigManagerProvider.get(), dataLayerManagerProvider.get());
  }

  public static DownloadManager_Factory create(Provider<Context> contextProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<AppConfigManager> appConfigManagerProvider,
      Provider<DataLayerManager> dataLayerManagerProvider) {
    return new DownloadManager_Factory(contextProvider, cacheRepositoryProvider, appConfigManagerProvider, dataLayerManagerProvider);
  }

  public static DownloadManager newInstance(Context context, CacheRepository cacheRepository,
      AppConfigManager appConfigManager, DataLayerManager dataLayerManager) {
    return new DownloadManager(context, cacheRepository, appConfigManager, dataLayerManager);
  }
}
