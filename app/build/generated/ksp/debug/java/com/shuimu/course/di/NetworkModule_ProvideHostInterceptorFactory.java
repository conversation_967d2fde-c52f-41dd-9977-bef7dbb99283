package com.shuimu.course.di;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import okhttp3.Interceptor;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideHostInterceptorFactory implements Factory<Interceptor> {
  @Override
  public Interceptor get() {
    return provideHostInterceptor();
  }

  public static NetworkModule_ProvideHostInterceptorFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static Interceptor provideHostInterceptor() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideHostInterceptor());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvideHostInterceptorFactory INSTANCE = new NetworkModule_ProvideHostInterceptorFactory();
  }
}
