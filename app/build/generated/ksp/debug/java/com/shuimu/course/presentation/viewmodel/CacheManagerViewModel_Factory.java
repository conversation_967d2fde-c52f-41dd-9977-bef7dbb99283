package com.shuimu.course.presentation.viewmodel;

import com.shuimu.course.domain.repository.CacheRepository;
import com.shuimu.course.domain.repository.SeriesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class CacheManagerViewModel_Factory implements Factory<CacheManagerViewModel> {
  private final Provider<CacheRepository> cacheRepositoryProvider;

  private final Provider<SeriesRepository> seriesRepositoryProvider;

  public CacheManagerViewModel_Factory(Provider<CacheRepository> cacheRepositoryProvider,
      Provider<SeriesRepository> seriesRepositoryProvider) {
    this.cacheRepositoryProvider = cacheRepositoryProvider;
    this.seriesRepositoryProvider = seriesRepositoryProvider;
  }

  @Override
  public CacheManagerViewModel get() {
    return newInstance(cacheRepositoryProvider.get(), seriesRepositoryProvider.get());
  }

  public static CacheManagerViewModel_Factory create(
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<SeriesRepository> seriesRepositoryProvider) {
    return new CacheManagerViewModel_Factory(cacheRepositoryProvider, seriesRepositoryProvider);
  }

  public static CacheManagerViewModel newInstance(CacheRepository cacheRepository,
      SeriesRepository seriesRepository) {
    return new CacheManagerViewModel(cacheRepository, seriesRepository);
  }
}
