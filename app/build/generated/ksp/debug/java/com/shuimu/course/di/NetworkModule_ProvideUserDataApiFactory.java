package com.shuimu.course.di;

import com.shuimu.course.data.remote.api.UserDataApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideUserDataApiFactory implements Factory<UserDataApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideUserDataApiFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public UserDataApi get() {
    return provideUserDataApi(retrofitProvider.get());
  }

  public static NetworkModule_ProvideUserDataApiFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideUserDataApiFactory(retrofitProvider);
  }

  public static UserDataApi provideUserDataApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideUserDataApi(retrofit));
  }
}
