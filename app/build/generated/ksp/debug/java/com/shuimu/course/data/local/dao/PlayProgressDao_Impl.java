package com.shuimu.course.data.local.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.EntityUpsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.shuimu.course.data.local.entities.PlayProgressEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PlayProgressDao_Impl implements PlayProgressDao {
  private final RoomDatabase __db;

  private final EntityUpsertionAdapter<PlayProgressEntity> __upsertionAdapterOfPlayProgressEntity;

  public PlayProgressDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__upsertionAdapterOfPlayProgressEntity = new EntityUpsertionAdapter<PlayProgressEntity>(new EntityInsertionAdapter<PlayProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT INTO `play_progress` (`video_id`,`watch_count`,`last_position_seconds`,`total_duration_seconds`,`updated_at`) VALUES (?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PlayProgressEntity entity) {
        statement.bindString(1, entity.getVideoId());
        statement.bindLong(2, entity.getWatchCount());
        statement.bindLong(3, entity.getLastPositionSeconds());
        statement.bindLong(4, entity.getTotalDurationSeconds());
        statement.bindLong(5, entity.getUpdatedAt());
      }
    }, new EntityDeletionOrUpdateAdapter<PlayProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE `play_progress` SET `video_id` = ?,`watch_count` = ?,`last_position_seconds` = ?,`total_duration_seconds` = ?,`updated_at` = ? WHERE `video_id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PlayProgressEntity entity) {
        statement.bindString(1, entity.getVideoId());
        statement.bindLong(2, entity.getWatchCount());
        statement.bindLong(3, entity.getLastPositionSeconds());
        statement.bindLong(4, entity.getTotalDurationSeconds());
        statement.bindLong(5, entity.getUpdatedAt());
        statement.bindString(6, entity.getVideoId());
      }
    });
  }

  @Override
  public Object upsertProgress(final PlayProgressEntity progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __upsertionAdapterOfPlayProgressEntity.upsert(progress);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<PlayProgressEntity> getProgressForVideo(final String videoId) {
    final String _sql = "SELECT * FROM play_progress WHERE video_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, videoId);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"play_progress"}, new Callable<PlayProgressEntity>() {
      @Override
      @Nullable
      public PlayProgressEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "video_id");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watch_count");
          final int _cursorIndexOfLastPositionSeconds = CursorUtil.getColumnIndexOrThrow(_cursor, "last_position_seconds");
          final int _cursorIndexOfTotalDurationSeconds = CursorUtil.getColumnIndexOrThrow(_cursor, "total_duration_seconds");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final PlayProgressEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastPositionSeconds;
            _tmpLastPositionSeconds = _cursor.getLong(_cursorIndexOfLastPositionSeconds);
            final long _tmpTotalDurationSeconds;
            _tmpTotalDurationSeconds = _cursor.getLong(_cursorIndexOfTotalDurationSeconds);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new PlayProgressEntity(_tmpVideoId,_tmpWatchCount,_tmpLastPositionSeconds,_tmpTotalDurationSeconds,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<PlayProgressEntity>> getAllProgress() {
    final String _sql = "SELECT * FROM play_progress";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"play_progress"}, new Callable<List<PlayProgressEntity>>() {
      @Override
      @NonNull
      public List<PlayProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "video_id");
          final int _cursorIndexOfWatchCount = CursorUtil.getColumnIndexOrThrow(_cursor, "watch_count");
          final int _cursorIndexOfLastPositionSeconds = CursorUtil.getColumnIndexOrThrow(_cursor, "last_position_seconds");
          final int _cursorIndexOfTotalDurationSeconds = CursorUtil.getColumnIndexOrThrow(_cursor, "total_duration_seconds");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final List<PlayProgressEntity> _result = new ArrayList<PlayProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PlayProgressEntity _item;
            final String _tmpVideoId;
            _tmpVideoId = _cursor.getString(_cursorIndexOfVideoId);
            final int _tmpWatchCount;
            _tmpWatchCount = _cursor.getInt(_cursorIndexOfWatchCount);
            final long _tmpLastPositionSeconds;
            _tmpLastPositionSeconds = _cursor.getLong(_cursorIndexOfLastPositionSeconds);
            final long _tmpTotalDurationSeconds;
            _tmpTotalDurationSeconds = _cursor.getLong(_cursorIndexOfTotalDurationSeconds);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new PlayProgressEntity(_tmpVideoId,_tmpWatchCount,_tmpLastPositionSeconds,_tmpTotalDurationSeconds,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
