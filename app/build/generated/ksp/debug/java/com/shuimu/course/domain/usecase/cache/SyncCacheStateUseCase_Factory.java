package com.shuimu.course.domain.usecase.cache;

import com.shuimu.course.domain.repository.CacheRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SyncCacheStateUseCase_Factory implements Factory<SyncCacheStateUseCase> {
  private final Provider<CacheRepository> cacheRepositoryProvider;

  public SyncCacheStateUseCase_Factory(Provider<CacheRepository> cacheRepositoryProvider) {
    this.cacheRepositoryProvider = cacheRepositoryProvider;
  }

  @Override
  public SyncCacheStateUseCase get() {
    return newInstance(cacheRepositoryProvider.get());
  }

  public static SyncCacheStateUseCase_Factory create(
      Provider<CacheRepository> cacheRepositoryProvider) {
    return new SyncCacheStateUseCase_Factory(cacheRepositoryProvider);
  }

  public static SyncCacheStateUseCase newInstance(CacheRepository cacheRepository) {
    return new SyncCacheStateUseCase(cacheRepository);
  }
}
