package com.shuimu.course.data.repository;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class UserPreferenceRepositoryImpl_Factory implements Factory<UserPreferenceRepositoryImpl> {
  private final Provider<Context> contextProvider;

  public UserPreferenceRepositoryImpl_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public UserPreferenceRepositoryImpl get() {
    return newInstance(contextProvider.get());
  }

  public static UserPreferenceRepositoryImpl_Factory create(Provider<Context> contextProvider) {
    return new UserPreferenceRepositoryImpl_Factory(contextProvider);
  }

  public static UserPreferenceRepositoryImpl newInstance(Context context) {
    return new UserPreferenceRepositoryImpl(context);
  }
}
