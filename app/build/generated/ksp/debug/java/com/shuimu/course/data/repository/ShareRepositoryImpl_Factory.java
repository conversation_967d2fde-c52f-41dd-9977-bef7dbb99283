package com.shuimu.course.data.repository;

import com.shuimu.course.data.remote.api.UserApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class ShareRepositoryImpl_Factory implements Factory<ShareRepositoryImpl> {
  private final Provider<UserApi> userApiProvider;

  public ShareRepositoryImpl_Factory(Provider<UserApi> userApiProvider) {
    this.userApiProvider = userApiProvider;
  }

  @Override
  public ShareRepositoryImpl get() {
    return newInstance(userApiProvider.get());
  }

  public static ShareRepositoryImpl_Factory create(Provider<UserApi> userApiProvider) {
    return new ShareRepositoryImpl_Factory(userApiProvider);
  }

  public static ShareRepositoryImpl newInstance(UserApi userApi) {
    return new ShareRepositoryImpl(userApi);
  }
}
