package com.shuimu.course.data.remote.interceptors;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class UserIdInterceptor_Factory implements Factory<UserIdInterceptor> {
  @Override
  public UserIdInterceptor get() {
    return newInstance();
  }

  public static UserIdInterceptor_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static UserIdInterceptor newInstance() {
    return new UserIdInterceptor();
  }

  private static final class InstanceHolder {
    private static final UserIdInterceptor_Factory INSTANCE = new UserIdInterceptor_Factory();
  }
}
