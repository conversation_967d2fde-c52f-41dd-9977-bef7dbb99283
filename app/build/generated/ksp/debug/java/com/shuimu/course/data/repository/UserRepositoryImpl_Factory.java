package com.shuimu.course.data.repository;

import com.shuimu.course.data.local.dao.UserDao;
import com.shuimu.course.data.remote.api.AuthApi;
import com.shuimu.course.data.remote.api.UserApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class UserRepositoryImpl_Factory implements Factory<UserRepositoryImpl> {
  private final Provider<AuthApi> authApiProvider;

  private final Provider<UserApi> userApiProvider;

  private final Provider<UserDao> userDaoProvider;

  public UserRepositoryImpl_Factory(Provider<AuthApi> authApiProvider,
      Provider<UserApi> userApiProvider, Provider<UserDao> userDaoProvider) {
    this.authApiProvider = authApiProvider;
    this.userApiProvider = userApiProvider;
    this.userDaoProvider = userDaoProvider;
  }

  @Override
  public UserRepositoryImpl get() {
    return newInstance(authApiProvider.get(), userApiProvider.get(), userDaoProvider.get());
  }

  public static UserRepositoryImpl_Factory create(Provider<AuthApi> authApiProvider,
      Provider<UserApi> userApiProvider, Provider<UserDao> userDaoProvider) {
    return new UserRepositoryImpl_Factory(authApiProvider, userApiProvider, userDaoProvider);
  }

  public static UserRepositoryImpl newInstance(AuthApi authApi, UserApi userApi, UserDao userDao) {
    return new UserRepositoryImpl(authApi, userApi, userDao);
  }
}
