package com.shuimu.course.presentation.viewmodel;

import com.shuimu.course.domain.repository.ShareRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class EarningsViewModel_Factory implements Factory<EarningsViewModel> {
  private final Provider<ShareRepository> shareRepositoryProvider;

  public EarningsViewModel_Factory(Provider<ShareRepository> shareRepositoryProvider) {
    this.shareRepositoryProvider = shareRepositoryProvider;
  }

  @Override
  public EarningsViewModel get() {
    return newInstance(shareRepositoryProvider.get());
  }

  public static EarningsViewModel_Factory create(
      Provider<ShareRepository> shareRepositoryProvider) {
    return new EarningsViewModel_Factory(shareRepositoryProvider);
  }

  public static EarningsViewModel newInstance(ShareRepository shareRepository) {
    return new EarningsViewModel(shareRepository);
  }
}
