package com.shuimu.course.data.workers;

import android.content.Context;
import androidx.work.WorkerParameters;
import com.shuimu.course.data.manager.DataLayerManager;
import com.shuimu.course.domain.repository.CacheRepository;
import com.shuimu.course.domain.usecase.cache.SyncCacheStateUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class VideoDownloadWorker_Factory {
  private final Provider<CacheRepository> cacheRepositoryProvider;

  private final Provider<SyncCacheStateUseCase> syncCacheStateUseCaseProvider;

  private final Provider<DataLayerManager> dataLayerManagerProvider;

  public VideoDownloadWorker_Factory(Provider<CacheRepository> cacheRepositoryProvider,
      Provider<SyncCacheStateUseCase> syncCacheStateUseCaseProvider,
      Provider<DataLayerManager> dataLayerManagerProvider) {
    this.cacheRepositoryProvider = cacheRepositoryProvider;
    this.syncCacheStateUseCaseProvider = syncCacheStateUseCaseProvider;
    this.dataLayerManagerProvider = dataLayerManagerProvider;
  }

  public VideoDownloadWorker get(Context context, WorkerParameters workerParams) {
    return newInstance(context, workerParams, cacheRepositoryProvider.get(), syncCacheStateUseCaseProvider.get(), dataLayerManagerProvider.get());
  }

  public static VideoDownloadWorker_Factory create(
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<SyncCacheStateUseCase> syncCacheStateUseCaseProvider,
      Provider<DataLayerManager> dataLayerManagerProvider) {
    return new VideoDownloadWorker_Factory(cacheRepositoryProvider, syncCacheStateUseCaseProvider, dataLayerManagerProvider);
  }

  public static VideoDownloadWorker newInstance(Context context, WorkerParameters workerParams,
      CacheRepository cacheRepository, SyncCacheStateUseCase syncCacheStateUseCase,
      DataLayerManager dataLayerManager) {
    return new VideoDownloadWorker(context, workerParams, cacheRepository, syncCacheStateUseCase, dataLayerManager);
  }
}
