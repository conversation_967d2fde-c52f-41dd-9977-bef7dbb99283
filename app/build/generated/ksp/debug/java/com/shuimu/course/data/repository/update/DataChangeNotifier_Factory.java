package com.shuimu.course.data.repository.update;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DataChangeNotifier_Factory implements Factory<DataChangeNotifier> {
  @Override
  public DataChangeNotifier get() {
    return newInstance();
  }

  public static DataChangeNotifier_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static DataChangeNotifier newInstance() {
    return new DataChangeNotifier();
  }

  private static final class InstanceHolder {
    private static final DataChangeNotifier_Factory INSTANCE = new DataChangeNotifier_Factory();
  }
}
