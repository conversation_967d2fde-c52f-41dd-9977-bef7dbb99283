package com.shuimu.course.domain.usecase.profile;

import com.shuimu.course.domain.repository.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class GetProfileDataUseCase_Factory implements Factory<GetProfileDataUseCase> {
  private final Provider<UserRepository> userRepositoryProvider;

  public GetProfileDataUseCase_Factory(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetProfileDataUseCase get() {
    return newInstance(userRepositoryProvider.get());
  }

  public static GetProfileDataUseCase_Factory create(
      Provider<UserRepository> userRepositoryProvider) {
    return new GetProfileDataUseCase_Factory(userRepositoryProvider);
  }

  public static GetProfileDataUseCase newInstance(UserRepository userRepository) {
    return new GetProfileDataUseCase(userRepository);
  }
}
