package com.shuimu.course.data.repository;

import android.content.Context;
import com.google.gson.Gson;
import com.shuimu.course.data.local.dao.CacheInfoDao;
import com.shuimu.course.data.local.dao.CategoryDao;
import com.shuimu.course.data.local.dao.PlayProgressDao;
import com.shuimu.course.data.local.dao.SeriesDao;
import com.shuimu.course.data.local.dao.VideoDao;
import com.shuimu.course.data.local.database.AppDatabase;
import com.shuimu.course.data.manager.DataLayerManager;
import com.shuimu.course.data.remote.api.SeriesApi;
import com.shuimu.course.data.repository.update.DataChangeNotifier;
import com.shuimu.course.data.repository.update.DataDiffDetector;
import com.shuimu.course.data.repository.update.IncrementalUpdateStrategy;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SeriesRepositoryImpl_Factory implements Factory<SeriesRepositoryImpl> {
  private final Provider<Context> contextProvider;

  private final Provider<Gson> gsonProvider;

  private final Provider<SeriesApi> seriesApiProvider;

  private final Provider<AppDatabase> databaseProvider;

  private final Provider<SeriesDao> seriesDaoProvider;

  private final Provider<CategoryDao> categoryDaoProvider;

  private final Provider<VideoDao> videoDaoProvider;

  private final Provider<PlayProgressDao> playProgressDaoProvider;

  private final Provider<CacheInfoDao> cacheInfoDaoProvider;

  private final Provider<DataLayerManager> dataLayerManagerProvider;

  private final Provider<DataDiffDetector> dataDiffDetectorProvider;

  private final Provider<IncrementalUpdateStrategy> incrementalUpdateStrategyProvider;

  private final Provider<DataChangeNotifier> dataChangeNotifierProvider;

  public SeriesRepositoryImpl_Factory(Provider<Context> contextProvider,
      Provider<Gson> gsonProvider, Provider<SeriesApi> seriesApiProvider,
      Provider<AppDatabase> databaseProvider, Provider<SeriesDao> seriesDaoProvider,
      Provider<CategoryDao> categoryDaoProvider, Provider<VideoDao> videoDaoProvider,
      Provider<PlayProgressDao> playProgressDaoProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider,
      Provider<DataLayerManager> dataLayerManagerProvider,
      Provider<DataDiffDetector> dataDiffDetectorProvider,
      Provider<IncrementalUpdateStrategy> incrementalUpdateStrategyProvider,
      Provider<DataChangeNotifier> dataChangeNotifierProvider) {
    this.contextProvider = contextProvider;
    this.gsonProvider = gsonProvider;
    this.seriesApiProvider = seriesApiProvider;
    this.databaseProvider = databaseProvider;
    this.seriesDaoProvider = seriesDaoProvider;
    this.categoryDaoProvider = categoryDaoProvider;
    this.videoDaoProvider = videoDaoProvider;
    this.playProgressDaoProvider = playProgressDaoProvider;
    this.cacheInfoDaoProvider = cacheInfoDaoProvider;
    this.dataLayerManagerProvider = dataLayerManagerProvider;
    this.dataDiffDetectorProvider = dataDiffDetectorProvider;
    this.incrementalUpdateStrategyProvider = incrementalUpdateStrategyProvider;
    this.dataChangeNotifierProvider = dataChangeNotifierProvider;
  }

  @Override
  public SeriesRepositoryImpl get() {
    return newInstance(contextProvider.get(), gsonProvider.get(), seriesApiProvider.get(), databaseProvider.get(), seriesDaoProvider.get(), categoryDaoProvider.get(), videoDaoProvider.get(), playProgressDaoProvider.get(), cacheInfoDaoProvider.get(), dataLayerManagerProvider.get(), dataDiffDetectorProvider.get(), incrementalUpdateStrategyProvider.get(), dataChangeNotifierProvider.get());
  }

  public static SeriesRepositoryImpl_Factory create(Provider<Context> contextProvider,
      Provider<Gson> gsonProvider, Provider<SeriesApi> seriesApiProvider,
      Provider<AppDatabase> databaseProvider, Provider<SeriesDao> seriesDaoProvider,
      Provider<CategoryDao> categoryDaoProvider, Provider<VideoDao> videoDaoProvider,
      Provider<PlayProgressDao> playProgressDaoProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider,
      Provider<DataLayerManager> dataLayerManagerProvider,
      Provider<DataDiffDetector> dataDiffDetectorProvider,
      Provider<IncrementalUpdateStrategy> incrementalUpdateStrategyProvider,
      Provider<DataChangeNotifier> dataChangeNotifierProvider) {
    return new SeriesRepositoryImpl_Factory(contextProvider, gsonProvider, seriesApiProvider, databaseProvider, seriesDaoProvider, categoryDaoProvider, videoDaoProvider, playProgressDaoProvider, cacheInfoDaoProvider, dataLayerManagerProvider, dataDiffDetectorProvider, incrementalUpdateStrategyProvider, dataChangeNotifierProvider);
  }

  public static SeriesRepositoryImpl newInstance(Context context, Gson gson, SeriesApi seriesApi,
      AppDatabase database, SeriesDao seriesDao, CategoryDao categoryDao, VideoDao videoDao,
      PlayProgressDao playProgressDao, CacheInfoDao cacheInfoDao, DataLayerManager dataLayerManager,
      DataDiffDetector dataDiffDetector, IncrementalUpdateStrategy incrementalUpdateStrategy,
      DataChangeNotifier dataChangeNotifier) {
    return new SeriesRepositoryImpl(context, gson, seriesApi, database, seriesDao, categoryDao, videoDao, playProgressDao, cacheInfoDao, dataLayerManager, dataDiffDetector, incrementalUpdateStrategy, dataChangeNotifier);
  }
}
