package com.shuimu.course.di;

import com.shuimu.course.data.remote.api.VideoApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideVideoApiFactory implements Factory<VideoApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideVideoApiFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public VideoApi get() {
    return provideVideoApi(retrofitProvider.get());
  }

  public static NetworkModule_ProvideVideoApiFactory create(Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideVideoApiFactory(retrofitProvider);
  }

  public static VideoApi provideVideoApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideVideoApi(retrofit));
  }
}
