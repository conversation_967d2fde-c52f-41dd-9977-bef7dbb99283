package com.shuimu.course.di;

import com.shuimu.course.data.remote.api.SeriesApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideSeriesApiFactory implements Factory<SeriesApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvideSeriesApiFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public SeriesApi get() {
    return provideSeriesApi(retrofitProvider.get());
  }

  public static NetworkModule_ProvideSeriesApiFactory create(Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvideSeriesApiFactory(retrofitProvider);
  }

  public static SeriesApi provideSeriesApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideSeriesApi(retrofit));
  }
}
