package com.shuimu.course.presentation.viewmodel;

import com.shuimu.course.data.local.dao.CacheInfoDao;
import com.shuimu.course.data.local.dao.SeriesDao;
import com.shuimu.course.data.manager.DataLayerManager;
import com.shuimu.course.domain.repository.CacheRepository;
import com.shuimu.course.domain.repository.SeriesRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SplashViewModel_Factory implements Factory<SplashViewModel> {
  private final Provider<SeriesRepository> seriesRepositoryProvider;

  private final Provider<CacheRepository> cacheRepositoryProvider;

  private final Provider<DataLayerManager> dataLayerManagerProvider;

  private final Provider<SeriesDao> seriesDaoProvider;

  private final Provider<CacheInfoDao> cacheInfoDaoProvider;

  public SplashViewModel_Factory(Provider<SeriesRepository> seriesRepositoryProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<DataLayerManager> dataLayerManagerProvider, Provider<SeriesDao> seriesDaoProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider) {
    this.seriesRepositoryProvider = seriesRepositoryProvider;
    this.cacheRepositoryProvider = cacheRepositoryProvider;
    this.dataLayerManagerProvider = dataLayerManagerProvider;
    this.seriesDaoProvider = seriesDaoProvider;
    this.cacheInfoDaoProvider = cacheInfoDaoProvider;
  }

  @Override
  public SplashViewModel get() {
    return newInstance(seriesRepositoryProvider.get(), cacheRepositoryProvider.get(), dataLayerManagerProvider.get(), seriesDaoProvider.get(), cacheInfoDaoProvider.get());
  }

  public static SplashViewModel_Factory create(Provider<SeriesRepository> seriesRepositoryProvider,
      Provider<CacheRepository> cacheRepositoryProvider,
      Provider<DataLayerManager> dataLayerManagerProvider, Provider<SeriesDao> seriesDaoProvider,
      Provider<CacheInfoDao> cacheInfoDaoProvider) {
    return new SplashViewModel_Factory(seriesRepositoryProvider, cacheRepositoryProvider, dataLayerManagerProvider, seriesDaoProvider, cacheInfoDaoProvider);
  }

  public static SplashViewModel newInstance(SeriesRepository seriesRepository,
      CacheRepository cacheRepository, DataLayerManager dataLayerManager, SeriesDao seriesDao,
      CacheInfoDao cacheInfoDao) {
    return new SplashViewModel(seriesRepository, cacheRepository, dataLayerManager, seriesDao, cacheInfoDao);
  }
}
