package com.shuimu.course.data.repository;

import com.shuimu.course.data.local.dao.VideoDao;
import com.shuimu.course.data.manager.UserManager;
import com.shuimu.course.data.remote.api.UserDataApi;
import com.shuimu.course.data.remote.api.VideoApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class VideoRepositoryImpl_Factory implements Factory<VideoRepositoryImpl> {
  private final Provider<VideoApi> videoApiProvider;

  private final Provider<UserDataApi> userDataApiProvider;

  private final Provider<VideoDao> videoDaoProvider;

  private final Provider<UserManager> userManagerProvider;

  public VideoRepositoryImpl_Factory(Provider<VideoApi> videoApiProvider,
      Provider<UserDataApi> userDataApiProvider, Provider<VideoDao> videoDaoProvider,
      Provider<UserManager> userManagerProvider) {
    this.videoApiProvider = videoApiProvider;
    this.userDataApiProvider = userDataApiProvider;
    this.videoDaoProvider = videoDaoProvider;
    this.userManagerProvider = userManagerProvider;
  }

  @Override
  public VideoRepositoryImpl get() {
    return newInstance(videoApiProvider.get(), userDataApiProvider.get(), videoDaoProvider.get(), userManagerProvider.get());
  }

  public static VideoRepositoryImpl_Factory create(Provider<VideoApi> videoApiProvider,
      Provider<UserDataApi> userDataApiProvider, Provider<VideoDao> videoDaoProvider,
      Provider<UserManager> userManagerProvider) {
    return new VideoRepositoryImpl_Factory(videoApiProvider, userDataApiProvider, videoDaoProvider, userManagerProvider);
  }

  public static VideoRepositoryImpl newInstance(VideoApi videoApi, UserDataApi userDataApi,
      VideoDao videoDao, UserManager userManager) {
    return new VideoRepositoryImpl(videoApi, userDataApi, videoDao, userManager);
  }
}
