package com.shuimu.course.data.manager;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SmartPersistenceManager_Factory implements Factory<SmartPersistenceManager> {
  private final Provider<Context> contextProvider;

  private final Provider<DataSyncManager> dataSyncManagerProvider;

  public SmartPersistenceManager_Factory(Provider<Context> contextProvider,
      Provider<DataSyncManager> dataSyncManagerProvider) {
    this.contextProvider = contextProvider;
    this.dataSyncManagerProvider = dataSyncManagerProvider;
  }

  @Override
  public SmartPersistenceManager get() {
    return newInstance(contextProvider.get(), dataSyncManagerProvider.get());
  }

  public static SmartPersistenceManager_Factory create(Provider<Context> contextProvider,
      Provider<DataSyncManager> dataSyncManagerProvider) {
    return new SmartPersistenceManager_Factory(contextProvider, dataSyncManagerProvider);
  }

  public static SmartPersistenceManager newInstance(Context context,
      DataSyncManager dataSyncManager) {
    return new SmartPersistenceManager(context, dataSyncManager);
  }
}
