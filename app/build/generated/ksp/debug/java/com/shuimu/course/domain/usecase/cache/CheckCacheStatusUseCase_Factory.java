package com.shuimu.course.domain.usecase.cache;

import com.shuimu.course.domain.repository.CacheRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class CheckCacheStatusUseCase_Factory implements Factory<CheckCacheStatusUseCase> {
  private final Provider<CacheRepository> cacheRepositoryProvider;

  public CheckCacheStatusUseCase_Factory(Provider<CacheRepository> cacheRepositoryProvider) {
    this.cacheRepositoryProvider = cacheRepositoryProvider;
  }

  @Override
  public CheckCacheStatusUseCase get() {
    return newInstance(cacheRepositoryProvider.get());
  }

  public static CheckCacheStatusUseCase_Factory create(
      Provider<CacheRepository> cacheRepositoryProvider) {
    return new CheckCacheStatusUseCase_Factory(cacheRepositoryProvider);
  }

  public static CheckCacheStatusUseCase newInstance(CacheRepository cacheRepository) {
    return new CheckCacheStatusUseCase(cacheRepository);
  }
}
