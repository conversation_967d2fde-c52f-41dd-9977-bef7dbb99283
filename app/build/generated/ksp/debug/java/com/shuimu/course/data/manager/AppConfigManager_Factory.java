package com.shuimu.course.data.manager;

import android.content.Context;
import com.shuimu.course.data.remote.api.CacheApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AppConfigManager_Factory implements Factory<AppConfigManager> {
  private final Provider<Context> contextProvider;

  private final Provider<CacheApi> cacheApiProvider;

  public AppConfigManager_Factory(Provider<Context> contextProvider,
      Provider<CacheApi> cacheApiProvider) {
    this.contextProvider = contextProvider;
    this.cacheApiProvider = cacheApiProvider;
  }

  @Override
  public AppConfigManager get() {
    return newInstance(contextProvider.get(), cacheApiProvider.get());
  }

  public static AppConfigManager_Factory create(Provider<Context> contextProvider,
      Provider<CacheApi> cacheApiProvider) {
    return new AppConfigManager_Factory(contextProvider, cacheApiProvider);
  }

  public static AppConfigManager newInstance(Context context, CacheApi cacheApi) {
    return new AppConfigManager(context, cacheApi);
  }
}
