#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接更新数据库表结构
"""

import sys
import os
import pymysql

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from config.config import Config

def update_database_structure():
    """更新数据库表结构"""
    print("🔄 开始更新数据库表结构...")
    
    try:
        # 连接数据库
        config = Config()
        connection = pymysql.connect(
            host=config.DATABASE_HOST,
            user=config.DATABASE_USER,
            password=config.DATABASE_PASSWORD,
            database=config.DATABASE_NAME,
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        
        # 1. 检查当前表结构
        print("📊 检查当前表结构...")
        cursor.execute("DESCRIBE series")
        series_structure = cursor.fetchall()
        print("当前 series 表结构:")
        for row in series_structure:
            print(f"  {row}")
        
        # 2. 删除外键约束
        print("🔗 删除外键约束...")
        try:
            cursor.execute("ALTER TABLE categories DROP FOREIGN KEY categories_ibfk_1")
            print("✅ 删除 categories 外键约束")
        except:
            print("⚠️ categories 外键约束不存在或已删除")
        
        try:
            cursor.execute("ALTER TABLE videos DROP FOREIGN KEY videos_ibfk_1")
            print("✅ 删除 videos 外键约束")
        except:
            print("⚠️ videos 外键约束不存在或已删除")
        
        # 3. 删除现有表
        print("🗑️ 删除现有表...")
        cursor.execute("DROP TABLE IF EXISTS videos")
        cursor.execute("DROP TABLE IF EXISTS categories")
        cursor.execute("DROP TABLE IF EXISTS series")
        print("✅ 现有表已删除")
        
        # 4. 重新创建系列表
        print("🏗️ 创建新的系列表...")
        series_sql = """
        CREATE TABLE series (
            id VARCHAR(50) PRIMARY KEY COMMENT '系列ID（字符串）',
            title VARCHAR(200) NOT NULL COMMENT '标题',
            description TEXT COMMENT '描述',
            price DECIMAL(10, 2) DEFAULT 0.00 COMMENT '价格',
            is_published BOOLEAN DEFAULT FALSE COMMENT '是否发布',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
        )
        """
        cursor.execute(series_sql)
        print("✅ 系列表创建完成")
        
        # 5. 重新创建分类表
        print("🏗️ 创建新的分类表...")
        categories_sql = """
        CREATE TABLE categories (
            id VARCHAR(50) PRIMARY KEY COMMENT '分类ID（字符串）',
            series_id VARCHAR(50) NOT NULL COMMENT '所属系列ID',
            title VARCHAR(200) NOT NULL COMMENT '分类标题',
            description TEXT COMMENT '分类描述',
            price DECIMAL(10, 2) DEFAULT 0.00 COMMENT '分类价格',
            order_index INT DEFAULT 0 COMMENT '排序序号',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE
        )
        """
        cursor.execute(categories_sql)
        print("✅ 分类表创建完成")
        
        # 6. 重新创建视频表
        print("🏗️ 创建新的视频表...")
        videos_sql = """
        CREATE TABLE videos (
            id VARCHAR(50) PRIMARY KEY COMMENT '视频ID（字符串）',
            category_id VARCHAR(50) NOT NULL COMMENT '所属分类ID',
            title VARCHAR(200) NOT NULL COMMENT '标题',
            description TEXT COMMENT '描述',
            video_url VARCHAR(500) COMMENT '视频地址',
            duration INT DEFAULT 0 COMMENT '时长(秒)',
            order_index INT DEFAULT 0 COMMENT '排序',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
        )
        """
        cursor.execute(videos_sql)
        print("✅ 视频表创建完成")
        
        # 7. 提交更改
        connection.commit()
        
        # 8. 验证新表结构
        print("🔍 验证新表结构...")
        cursor.execute("DESCRIBE series")
        new_series_structure = cursor.fetchall()
        print("新的 series 表结构:")
        for row in new_series_structure:
            print(f"  {row}")
        
        cursor.execute("DESCRIBE categories")
        new_categories_structure = cursor.fetchall()
        print("新的 categories 表结构:")
        for row in new_categories_structure:
            print(f"  {row}")
        
        cursor.execute("DESCRIBE videos")
        new_videos_structure = cursor.fetchall()
        print("新的 videos 表结构:")
        for row in new_videos_structure:
            print(f"  {row}")
        
        cursor.close()
        connection.close()
        
        print("\n✅ 数据库表结构更新完成！")
        print("🎯 所有ID字段已改为VARCHAR(50)类型")
        print("🔧 现在可以重启管理端程序测试系列更新功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库表结构更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 数据库表结构更新工具")
    print("🎯 目标：将所有ID字段从INTEGER改为VARCHAR(50)")
    print("=" * 60)
    
    success = update_database_structure()
    
    if success:
        print("\n🎉 更新成功！")
        print("📋 下一步操作：")
        print("  1. 重启管理端程序")
        print("  2. 测试系列更新功能")
        print("  3. 验证字符串ID是否正常工作")
    else:
        print("\n❌ 更新失败！")
        print("请检查错误信息并重试")

if __name__ == "__main__":
    main()
