-- 水幕课程管理端数据库迁移脚本
-- 创建分类表并调整现有结构

-- 1. 创建分类表
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `series_id` int NOT NULL COMMENT '所属系列ID',
  `title` varchar(200) NOT NULL COMMENT '分类标题',
  `description` text COMMENT '分类描述',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '分类价格',
  `order_index` int DEFAULT '0' COMMENT '排序序号',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_series_id` (`series_id`),
  KEY `idx_order_index` (`order_index`),
  CONSTRAINT `fk_categories_series` FOREIGN KEY (`series_id`) REFERENCES `series` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程分类表';

-- 2. 为videos表添加category_id字段
ALTER TABLE `videos` ADD COLUMN `category_id` int DEFAULT NULL COMMENT '所属分类ID' AFTER `series_id`;

-- 3. 添加外键约束
ALTER TABLE `videos` ADD CONSTRAINT `fk_videos_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

-- 4. 创建索引
ALTER TABLE `videos` ADD KEY `idx_category_id` (`category_id`);

-- 5. 修改series表，移除price字段（价格将动态计算）
-- 注意：如果已有数据，请先备份price字段的值
-- ALTER TABLE `series` DROP COLUMN `price`;

-- 6. 插入示例数据（可选）
-- 为现有系列创建默认分类
INSERT INTO `categories` (`series_id`, `title`, `description`, `price`, `order_index`)
SELECT 
    `id` as `series_id`,
    CONCAT(`title`, ' - 默认分类') as `title`,
    '系统自动创建的默认分类' as `description`,
    COALESCE(`price`, 0.00) as `price`,
    1 as `order_index`
FROM `series`
WHERE NOT EXISTS (
    SELECT 1 FROM `categories` WHERE `categories`.`series_id` = `series`.`id`
);

-- 7. 将现有视频关联到默认分类
UPDATE `videos` v
JOIN `categories` c ON c.series_id = v.series_id
SET v.category_id = c.id
WHERE v.category_id IS NULL
AND c.title LIKE '%默认分类%';

-- 8. 创建视图：系列价格动态计算
CREATE OR REPLACE VIEW `series_with_price` AS
SELECT 
    s.id,
    s.title,
    s.description,
    s.is_published,
    s.created_at,
    s.updated_at,
    COALESCE(SUM(c.price), 0.00) as total_price,
    COUNT(c.id) as category_count,
    (SELECT COUNT(*) FROM videos v WHERE v.category_id IN (
        SELECT id FROM categories WHERE series_id = s.id
    )) as video_count
FROM series s
LEFT JOIN categories c ON c.series_id = s.id
GROUP BY s.id, s.title, s.description, s.is_published, s.created_at, s.updated_at;

-- 9. 创建触发器：自动更新系列价格缓存（可选）
-- 如果需要缓存系列价格，可以添加price字段并用触发器维护

-- 验证数据完整性
SELECT 
    s.title as series_title,
    COUNT(c.id) as category_count,
    SUM(c.price) as total_price,
    COUNT(v.id) as video_count
FROM series s
LEFT JOIN categories c ON c.series_id = s.id
LEFT JOIN videos v ON v.category_id = c.id
GROUP BY s.id, s.title
ORDER BY s.id;
