# AI Tools Extended PowerShell Module
# Integrated mike-mcp service functions with ai-pipe friendly interface

# Import base AI-Tools module
if (Test-Path "D:\Tools\AI-Tools.psm1") {
    Import-Module "D:\Tools\AI-Tools.psm1" -Force -DisableNameChecking
}

# 1. Time Tool
function Get-AITime {
    param([string]$Format = "readable")
    $timeResult = Get-Date
    
    switch ($Format) {
        "iso" { return $timeResult.ToString("yyyy-MM-ddTHH:mm:ss") }
        "readable" { return $timeResult.ToString("yyyy-MM-dd HH:mm:ss") }
        "timestamp" { return [int][double]::Parse((Get-Date -UFormat %s)) }
        default { return $timeResult.ToString("yyyy-MM-dd HH:mm:ss") }
    }
}

# 2. System Info Tool
function Get-AISystemInfo {
    param([string]$InfoType = "basic")
    
    switch ($InfoType) {
        "basic" {
            $os = Get-CimInstance Win32_OperatingSystem
            $cs = Get-CimInstance Win32_ComputerSystem
            $cpu = Get-CimInstance Win32_Processor
            
            $info = @{
                "OS" = $os.Caption
                "Version" = $os.Version
                "Architecture" = $os.OSArchitecture
                "Computer" = $env:COMPUTERNAME
                "User" = $env:USERNAME
                "TotalMemory" = [math]::Round($cs.TotalPhysicalMemory / 1GB, 2).ToString() + " GB"
                "Processor" = $cpu.Name
            }
            return ($info | ConvertTo-Json -Depth 1)
        }
        "memory" {
            $memory = Get-CimInstance Win32_OperatingSystem
            return "Total: $([math]::Round($memory.TotalVisibleMemorySize/1024/1024, 2)) GB, Free: $([math]::Round($memory.FreePhysicalMemory/1024/1024, 2)) GB"
        }
        "cpu" {
            $cpu = Get-CimInstance Win32_Processor
            return "CPU: $($cpu.Name), Cores: $($cpu.NumberOfCores), Threads: $($cpu.NumberOfLogicalProcessors)"
        }
        default {
            return "Available options: basic, memory, cpu"
        }
    }
}

# 3. File Operation Tool
function Invoke-AIFileOperation {
    param(
        [string]$Operation,
        [string]$FilePath,
        [string]$Content = ""
    )
    
    try {
        switch ($Operation.ToLower()) {
            "read" {
                if (Test-Path $FilePath) {
                    return Get-Content $FilePath -Raw -Encoding UTF8
                } else {
                    return "File not found: $FilePath"
                }
            }
            "write" {
                $Content | Out-File -FilePath $FilePath -Encoding UTF8 -Force
                return "File written: $FilePath"
            }
            "size" {
                if (Test-Path $FilePath) {
                    $size = (Get-Item $FilePath).Length
                    return "File size: $([math]::Round($size/1KB, 2)) KB"
                } else {
                    return "File not found: $FilePath"
                }
            }
            "exists" {
                return (Test-Path $FilePath).ToString()
            }
            default {
                return "Supported operations: read, write, size, exists"
            }
        }
    } catch {
        return "Operation failed: $_"
    }
}

# 4. Math Tool
function Invoke-AIMath {
    param([string]$Expression)
    
    try {
        $safeExpression = $Expression -replace '[^0-9+\-*/().\s]', ''
        if ($safeExpression -ne $Expression) {
            return "Expression contains unsafe characters"
        }
        
        $result = Invoke-Expression $safeExpression
        return "$Expression = $result"
    } catch {
        return "Calculation error: $_"
    }
}

# 5. Text Processor Tool
function Invoke-AITextProcessor {
    param(
        [string]$Operation,
        [string]$Text
    )
    
    switch ($Operation.ToLower()) {
        "length" { return "Text length: $($Text.Length) characters" }
        "upper" { return $Text.ToUpper() }
        "lower" { return $Text.ToLower() }
        "wordcount" {
            $words = ($Text -split '\s+' | Where-Object { $_ -ne '' }).Count
            return "Word count: $words"
        }
        "trim" { return $Text.Trim() }
        default {
            return "Supported operations: length, upper, lower, wordcount, trim"
        }
    }
}

# AI-Pipe Convenience Functions

# Time Analysis Pipeline
function Invoke-AITimeAnalysis {
    param([string]$Question)
    $currentTime = Get-AITime -Format "readable"
    ai-pipe "echo '$currentTime'" "claude -p '$Question Current time: $currentTime'"
}

# System Analysis Pipeline
function Invoke-AISystemAnalysis {
    param([string]$Question = "Analyze this system information")
    $systemInfo = Get-AISystemInfo -InfoType "basic"
    ai-pipe "echo '$systemInfo'" "claude -p '$Question'"
}

# File Analysis Pipeline
function Invoke-AIFileAnalysis {
    param(
        [string]$FilePath,
        [string]$Question = "Analyze this file content"
    )
    $content = Invoke-AIFileOperation -Operation "read" -FilePath $FilePath
    if ($content -notlike "*not found*") {
        ai-pipe "echo '$content'" "claude -p '$Question'"
    } else {
        Write-Host $content -ForegroundColor Red
    }
}

# Math Analysis Pipeline
function Invoke-AIMathAnalysis {
    param(
        [string]$Expression,
        [string]$Question = "Explain this calculation"
    )
    $result = Invoke-AIMath -Expression $Expression
    ai-pipe "echo '$result'" "claude -p '$Question'"
}

# Text Analysis Pipeline
function Invoke-AITextAnalysis {
    param(
        [string]$Text,
        [string]$Question = "Analyze this text"
    )
    ai-pipe "echo '$Text'" "claude -p '$Question'"
}

# Export Functions
Export-ModuleMember -Function Get-AITime, Get-AISystemInfo, Invoke-AIFileOperation, Invoke-AIMath, Invoke-AITextProcessor
Export-ModuleMember -Function Invoke-AITimeAnalysis, Invoke-AISystemAnalysis, Invoke-AIFileAnalysis, Invoke-AIMathAnalysis, Invoke-AITextAnalysis

# Set Aliases
Set-Alias -Name ai-time -Value Invoke-AITimeAnalysis
Set-Alias -Name ai-system -Value Invoke-AISystemAnalysis
Set-Alias -Name ai-file -Value Invoke-AIFileAnalysis
Set-Alias -Name ai-math -Value Invoke-AIMathAnalysis
Set-Alias -Name ai-text -Value Invoke-AITextAnalysis

Set-Alias -Name get-time -Value Get-AITime
Set-Alias -Name get-sysinfo -Value Get-AISystemInfo
Set-Alias -Name file-op -Value Invoke-AIFileOperation
Set-Alias -Name math-calc -Value Invoke-AIMath
Set-Alias -Name text-proc -Value Invoke-AITextProcessor

Export-ModuleMember -Alias ai-time, ai-system, ai-file, ai-math, ai-text, get-time, get-sysinfo, file-op, math-calc, text-proc 