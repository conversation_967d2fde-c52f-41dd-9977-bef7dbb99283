<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 设置</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
        }
        .content::-webkit-scrollbar { display: none; }
        .setting-group {
            background: white;
            border-radius: 16px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .group-title {
            padding: 16px 20px 8px;
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            background: #f9fafb;
        }
        .setting-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: between;
        }
        .setting-item:last-child {
            border-bottom: none;
        }
        .setting-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: white;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #667eea;
        }
        input:checked + .slider:before {
            transform: translateX(20px);
        }
        .select-value {
            color: #6b7280;
            font-size: 14px;
        }
        .danger-item {
            color: #dc2626;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 播放设置 -->
                <div class="setting-group">
                    <div class="group-title">播放设置</div>
                    
                    <div class="setting-item" onclick="toggleSetting(this, 'autoPlay')">
                        <div class="setting-icon" style="background: #3b82f6;">
                            <i class="fas fa-play"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">自动播放下一个</p>
                            <p class="text-sm text-gray-500">视频结束后自动播放下一个</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked id="autoPlay">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item" onclick="toggleSetting(this, 'rememberVolume')">
                        <div class="setting-icon" style="background: #10b981;">
                            <i class="fas fa-volume-up"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">记住音量设置</p>
                            <p class="text-sm text-gray-500">保存上次的音量大小</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked id="rememberVolume">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item" onclick="showSpeedSelector()">
                        <div class="setting-icon" style="background: #f59e0b;">
                            <i class="fas fa-expand"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">默认播放速度</p>
                            <p class="text-sm text-gray-500">设置视频播放倍速</p>
                        </div>
                        <div class="flex items-center">
                            <span class="select-value mr-2" id="speedValue">1.0x</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="setting-item" onclick="toggleSetting(this, 'showSubtitles')">
                        <div class="setting-icon" style="background: #8b5cf6;">
                            <i class="fas fa-closed-captioning"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">显示字幕</p>
                            <p class="text-sm text-gray-500">开启视频字幕显示</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="showSubtitles">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <!-- 缓存设置 -->
                <div class="setting-group">
                    <div class="group-title">缓存设置</div>
                    
                    <div class="setting-item" onclick="toggleSetting(this, 'wifiOnlyCache')">
                        <div class="setting-icon" style="background: #06b6d4;">
                            <i class="fas fa-wifi"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">仅WiFi下缓存</p>
                            <p class="text-sm text-gray-500">节省移动数据流量</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked id="wifiOnlyCache">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item" onclick="toggleSetting(this, 'autoCleanCache')">
                        <div class="setting-icon" style="background: #ef4444;">
                            <i class="fas fa-trash-alt"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">自动清理缓存</p>
                            <p class="text-sm text-gray-500">30天未观看自动删除</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" id="autoCleanCache">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item" onclick="showQualitySelector()">
                        <div class="setting-icon" style="background: #84cc16;">
                            <i class="fas fa-hdd"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">缓存质量</p>
                            <p class="text-sm text-gray-500">选择视频缓存质量</p>
                        </div>
                        <div class="flex items-center">
                            <span class="select-value mr-2" id="qualityValue">高清</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 通知设置 -->
                <div class="setting-group">
                    <div class="group-title">通知设置</div>
                    
                    <div class="setting-item" onclick="toggleSetting(this, 'pushNotification')">
                        <div class="setting-icon" style="background: #f97316;">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">推送通知</p>
                            <p class="text-sm text-gray-500">接收课程更新通知</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked id="pushNotification">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item" onclick="toggleSetting(this, 'studyReminder')">
                        <div class="setting-icon" style="background: #ec4899;">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">学习提醒</p>
                            <p class="text-sm text-gray-500">每日学习时间提醒</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked id="studyReminder">
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item" onclick="toggleSetting(this, 'earningsNotification')">
                        <div class="setting-icon" style="background: #14b8a6;">
                            <i class="fas fa-share-alt"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">分享收益通知</p>
                            <p class="text-sm text-gray-500">有新的分享收益时通知</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked id="earningsNotification">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <!-- 隐私安全 -->
                <div class="setting-group">
                    <div class="group-title">隐私安全</div>
                    
                    <div class="setting-item">
                        <div class="setting-icon" style="background: #6366f1;">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">防录屏保护</p>
                            <p class="text-sm text-gray-500">禁止录屏截屏功能</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked disabled>
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item">
                        <div class="setting-icon" style="background: #64748b;">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">动态水印</p>
                            <p class="text-sm text-gray-500">视频播放时显示用户信息</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked disabled>
                            <span class="slider"></span>
                        </label>
                    </div>

                    <div class="setting-item" onclick="showChangePasswordModal()">
                        <div class="setting-icon" style="background: #059669;">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">修改密码</p>
                            <p class="text-sm text-gray-500">更改账号登录密码</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <!-- 其他设置 -->
                <div class="setting-group">
                    <div class="group-title">其他设置</div>
                    
                    <div class="setting-item" onclick="showThemeSelector()">
                        <div class="setting-icon" style="background: #7c3aed;">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">主题模式</p>
                            <p class="text-sm text-gray-500">选择应用主题</p>
                        </div>
                        <div class="flex items-center">
                            <span class="select-value mr-2" id="themeValue">浅色</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="setting-item" onclick="showLanguageSelector()">
                        <div class="setting-icon" style="background: #0891b2;">
                            <i class="fas fa-language"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">语言设置</p>
                            <p class="text-sm text-gray-500">选择应用语言</p>
                        </div>
                        <div class="flex items-center">
                            <span class="select-value mr-2" id="languageValue">简体中文</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>

                    <div class="setting-item" onclick="showClearCacheConfirm()">
                        <div class="setting-icon" style="background: #dc2626;">
                            <i class="fas fa-broom"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">清除缓存</p>
                            <p class="text-sm text-gray-500">清除应用缓存数据</p>
                        </div>
                        <div class="flex items-center">
                            <span class="select-value mr-2" id="cacheSize">1.2GB</span>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <!-- 账号管理 -->
                <div class="setting-group">
                    <div class="group-title">账号管理</div>
                    
                    <div class="setting-item" onclick="showEditProfileModal()">
                        <div class="setting-icon" style="background: #f59e0b;">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">编辑资料</p>
                            <p class="text-sm text-gray-500">修改个人信息</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="setting-item" onclick="showLogoutConfirm()">
                        <div class="setting-icon" style="background: #ef4444;">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800 danger-item">退出登录</p>
                            <p class="text-sm text-gray-500">退出当前账号</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>
        </div>
    </div>

    <!-- 弹窗遮罩 -->
    <div class="modal-overlay" id="modalOverlay" style="display: none;">
        <!-- 播放速度选择器 -->
        <div class="modal-content" id="speedModal" style="display: none;">
            <div class="modal-header">
                <h3>选择播放速度</h3>
                <button onclick="closeModal()" class="close-btn">×</button>
            </div>
            <div class="modal-body">
                <div class="option-list">
                    <div class="option-item" onclick="selectSpeed('0.5x')">0.5x</div>
                    <div class="option-item" onclick="selectSpeed('0.75x')">0.75x</div>
                    <div class="option-item selected" onclick="selectSpeed('1.0x')">1.0x</div>
                    <div class="option-item" onclick="selectSpeed('1.25x')">1.25x</div>
                    <div class="option-item" onclick="selectSpeed('1.5x')">1.5x</div>
                    <div class="option-item" onclick="selectSpeed('2.0x')">2.0x</div>
                </div>
            </div>
        </div>

        <!-- 缓存质量选择器 -->
        <div class="modal-content" id="qualityModal" style="display: none;">
            <div class="modal-header">
                <h3>选择缓存质量</h3>
                <button onclick="closeModal()" class="close-btn">×</button>
            </div>
            <div class="modal-body">
                <div class="option-list">
                    <div class="option-item" onclick="selectQuality('流畅')">流畅 (360p)</div>
                    <div class="option-item" onclick="selectQuality('标清')">标清 (480p)</div>
                    <div class="option-item selected" onclick="selectQuality('高清')">高清 (720p)</div>
                    <div class="option-item" onclick="selectQuality('超清')">超清 (1080p)</div>
                </div>
            </div>
        </div>

        <!-- 主题选择器 -->
        <div class="modal-content" id="themeModal" style="display: none;">
            <div class="modal-header">
                <h3>选择主题模式</h3>
                <button onclick="closeModal()" class="close-btn">×</button>
            </div>
            <div class="modal-body">
                <div class="option-list">
                    <div class="option-item selected" onclick="selectTheme('浅色')">浅色模式</div>
                    <div class="option-item" onclick="selectTheme('深色')">深色模式</div>
                    <div class="option-item" onclick="selectTheme('跟随系统')">跟随系统</div>
                </div>
            </div>
        </div>

        <!-- 语言选择器 -->
        <div class="modal-content" id="languageModal" style="display: none;">
            <div class="modal-header">
                <h3>选择语言</h3>
                <button onclick="closeModal()" class="close-btn">×</button>
            </div>
            <div class="modal-body">
                <div class="option-list">
                    <div class="option-item selected" onclick="selectLanguage('简体中文')">简体中文</div>
                    <div class="option-item" onclick="selectLanguage('繁體中文')">繁體中文</div>
                    <div class="option-item" onclick="selectLanguage('English')">English</div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            width: 320px;
            max-height: 400px;
            overflow: hidden;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 24px;
            color: #9ca3af;
            cursor: pointer;
        }

        .modal-body {
            padding: 0;
        }

        .option-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .option-item {
            padding: 16px 20px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-bottom: 1px solid #f3f4f6;
        }

        .option-item:hover {
            background-color: #f9fafb;
        }

        .option-item.selected {
            background-color: #eff6ff;
            color: #2563eb;
            font-weight: 500;
        }

        .option-item:last-child {
            border-bottom: none;
        }
    </style>

    <script src="data/navigation-manager.js"></script>
    <script src="data/component-templates-with-styles.js"></script>
    <script>
        // 设置管理器
        class SettingsManager {
            constructor() {
                this.settings = this.loadSettings();
                this.init();
            }

            init() {
                // 初始化设置状态
                this.updateUI();

                // 阻止开关点击事件冒泡
                document.querySelectorAll('.toggle-switch').forEach(toggle => {
                    toggle.addEventListener('click', (e) => {
                        e.stopPropagation();
                    });
                });
            }

            // 加载设置
            loadSettings() {
                const defaultSettings = {
                    autoPlay: true,
                    rememberVolume: true,
                    showSubtitles: false,
                    wifiOnlyCache: true,
                    autoCleanCache: false,
                    pushNotification: true,
                    studyReminder: true,
                    earningsNotification: true,
                    playSpeed: '1.0x',
                    cacheQuality: '高清',
                    theme: '浅色',
                    language: '简体中文'
                };

                try {
                    const saved = localStorage.getItem('appSettings');
                    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
                } catch (error) {
                    console.error('加载设置失败:', error);
                    return defaultSettings;
                }
            }

            // 保存设置
            saveSettings() {
                try {
                    localStorage.setItem('appSettings', JSON.stringify(this.settings));
                    console.log('设置已保存:', this.settings);
                } catch (error) {
                    console.error('保存设置失败:', error);
                }
            }

            // 更新UI显示
            updateUI() {
                // 更新开关状态
                Object.keys(this.settings).forEach(key => {
                    const element = document.getElementById(key);
                    if (element && element.type === 'checkbox') {
                        element.checked = this.settings[key];
                    }
                });

                // 更新选择器显示值
                const speedValue = document.getElementById('speedValue');
                if (speedValue) speedValue.textContent = this.settings.playSpeed;

                const qualityValue = document.getElementById('qualityValue');
                if (qualityValue) qualityValue.textContent = this.settings.cacheQuality;

                const themeValue = document.getElementById('themeValue');
                if (themeValue) themeValue.textContent = this.settings.theme;

                const languageValue = document.getElementById('languageValue');
                if (languageValue) languageValue.textContent = this.settings.language;
            }

            // 切换设置
            toggleSetting(key) {
                this.settings[key] = !this.settings[key];
                this.saveSettings();
                this.updateUI();

                // 显示反馈
                this.showToast(`${this.getSettingName(key)} ${this.settings[key] ? '已开启' : '已关闭'}`);
            }

            // 获取设置名称
            getSettingName(key) {
                const names = {
                    autoPlay: '自动播放',
                    rememberVolume: '记住音量',
                    showSubtitles: '显示字幕',
                    wifiOnlyCache: 'WiFi缓存',
                    autoCleanCache: '自动清理',
                    pushNotification: '推送通知',
                    studyReminder: '学习提醒',
                    earningsNotification: '收益通知'
                };
                return names[key] || key;
            }

            // 显示提示
            showToast(message) {
                // 创建提示元素
                const toast = document.createElement('div');
                toast.className = 'toast-message';
                toast.textContent = message;
                toast.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 14px;
                    z-index: 10000;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                `;

                document.body.appendChild(toast);

                // 显示动画
                setTimeout(() => {
                    toast.style.opacity = '1';
                }, 10);

                // 自动隐藏
                setTimeout(() => {
                    toast.style.opacity = '0';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }, 2000);
            }
        }

        // 全局设置管理器实例
        let settingsManager;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTopNavigation();
            settingsManager = new SettingsManager();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '设置'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        function goBack() {
            if (window.navigateTo) {
                navigateTo('03-我的页面.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '03-我的页面.html';
            }
        }

        // showShareModal函数已经在顶部导航组件中自动包含

        // 切换设置函数
        function toggleSetting(element, settingKey) {
            if (settingsManager) {
                settingsManager.toggleSetting(settingKey);
            }
        }

        // 弹窗管理函数
        function showModal(modalId) {
            const overlay = document.getElementById('modalOverlay');
            const modal = document.getElementById(modalId);

            // 隐藏所有弹窗
            document.querySelectorAll('.modal-content').forEach(m => {
                m.style.display = 'none';
            });

            // 显示指定弹窗
            overlay.style.display = 'flex';
            modal.style.display = 'block';
        }

        function closeModal() {
            document.getElementById('modalOverlay').style.display = 'none';
        }

        // 播放速度选择
        function showSpeedSelector() {
            showModal('speedModal');
        }

        function selectSpeed(speed) {
            if (settingsManager) {
                settingsManager.settings.playSpeed = speed;
                settingsManager.saveSettings();
                settingsManager.updateUI();
                settingsManager.showToast(`播放速度已设置为 ${speed}`);
            }
            closeModal();
        }

        // 缓存质量选择
        function showQualitySelector() {
            showModal('qualityModal');
        }

        function selectQuality(quality) {
            if (settingsManager) {
                settingsManager.settings.cacheQuality = quality;
                settingsManager.saveSettings();
                settingsManager.updateUI();
                settingsManager.showToast(`缓存质量已设置为 ${quality}`);
            }
            closeModal();
        }

        // 主题选择
        function showThemeSelector() {
            showModal('themeModal');
        }

        function selectTheme(theme) {
            if (settingsManager) {
                settingsManager.settings.theme = theme;
                settingsManager.saveSettings();
                settingsManager.updateUI();
                settingsManager.showToast(`主题已切换为 ${theme}`);
            }
            closeModal();
        }

        // 语言选择
        function showLanguageSelector() {
            showModal('languageModal');
        }

        function selectLanguage(language) {
            if (settingsManager) {
                settingsManager.settings.language = language;
                settingsManager.saveSettings();
                settingsManager.updateUI();
                settingsManager.showToast(`语言已切换为 ${language}`);
            }
            closeModal();
        }

        // 修改密码
        function showChangePasswordModal() {
            if (confirm('确定要修改密码吗？\n\n点击确定将跳转到密码修改页面。')) {
                settingsManager.showToast('正在跳转到密码修改页面...');
                // 这里可以跳转到密码修改页面
                setTimeout(() => {
                    alert('密码修改功能开发中...');
                }, 1000);
            }
        }

        // 编辑资料
        function showEditProfileModal() {
            if (confirm('确定要编辑个人资料吗？\n\n点击确定将跳转到资料编辑页面。')) {
                settingsManager.showToast('正在跳转到资料编辑页面...');
                // 这里可以跳转到资料编辑页面
                setTimeout(() => {
                    alert('资料编辑功能开发中...');
                }, 1000);
            }
        }

        // 清除缓存确认
        function showClearCacheConfirm() {
            if (confirm('确定要清除所有缓存数据吗？\n\n清除后需要重新下载视频缓存。')) {
                settingsManager.showToast('正在清除缓存...');

                // 模拟清除缓存过程
                setTimeout(() => {
                    document.getElementById('cacheSize').textContent = '0MB';
                    settingsManager.showToast('缓存清除完成');
                }, 2000);
            }
        }

        // 退出登录确认
        function showLogoutConfirm() {
            if (confirm('确定要退出登录吗？\n\n退出后需要重新登录才能使用。')) {
                settingsManager.showToast('正在退出登录...');

                // 模拟退出过程
                setTimeout(() => {
                    // 清除用户数据
                    localStorage.removeItem('userToken');
                    localStorage.removeItem('userInfo');

                    // 跳转到登录页面
                    if (window.navigateTo) {
                        navigateTo('05-登录页面.html');
                    } else {
                        window.location.href = '05-登录页面.html';
                    }
                }, 1500);
            }
        }

        // 点击遮罩关闭弹窗
        document.addEventListener('DOMContentLoaded', function() {
            const overlay = document.getElementById('modalOverlay');
            if (overlay) {
                overlay.addEventListener('click', function(e) {
                    if (e.target === overlay) {
                        closeModal();
                    }
                });
            }
        });
    </script>
</body>
</html>