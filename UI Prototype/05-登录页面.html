<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 登录</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
        }
        .login-modal {
            background: white;
            border-radius: 20px;
            padding: 32px 24px;
            margin: 0 24px;
            width: calc(100% - 48px);
            max-width: 320px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            position: relative;
        }
        .input-group {
            margin-bottom: 20px;
        }
        .input-field {
            width: 100%;
            padding: 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .login-button {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: bold;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        .close-button {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f3f4f6;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #6b7280;
        }
        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6b7280;
            cursor: pointer;
        }
        .background-content {
            padding: 16px;
            opacity: 0.3;
            filter: blur(2px);
        }
        
        /* ========== 组件系统 ========== */
        
        /* Logo组件基础样式 */
        .logo-component {
            border-radius: 50%;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo-component i {
            color: white;
        }
        
        /* Logo尺寸变体 */
        .logo-sm {
            width: 48px;
            height: 48px;
        }
        
        .logo-sm i {
            font-size: 20px;
        }
        
        /* Logo边距变体 */
        .logo-mr {
            margin-right: 12px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 背景内容（模糊） -->
            <div class="background-content">
                <div class="flex items-center justify-between py-4">
                    <div class="flex items-center">
                        <div class="logo-component logo-sm logo-mr">
                            <i class="fas fa-water"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-800">水幕</h1>
                            <p class="text-sm text-gray-500">点击登录</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg p-4 mb-4">
                    <h3 class="font-semibold text-gray-800 mb-2">免费体验</h3>
                    <p class="text-sm text-gray-500">登录后查看更多内容</p>
                </div>
            </div>

            <!-- 登录弹窗 -->
            <div class="modal-overlay">
                <div class="login-modal">
                    <button class="close-button" onclick="closeLogin()">
                        <i class="fas fa-times"></i>
                    </button>
                    
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                            <i class="fas fa-user text-white text-2xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">账号登录</h2>
                        <p class="text-gray-500">登录后享受完整学习体验</p>
                    </div>

                    <form onsubmit="handleLogin(event)" id="loginForm">
                        <div class="input-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2">账号</label>
                            <input type="text"
                                   class="input-field"
                                   placeholder="请输入手机号或用户名"
                                   id="usernameInput"
                                   required>
                            <div class="error-message" id="usernameError"></div>
                        </div>

                        <div class="input-group">
                            <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                            <div class="relative">
                                <input type="password"
                                       class="input-field pr-12"
                                       placeholder="请输入密码"
                                       id="passwordInput"
                                       required>
                                <button type="button"
                                        class="password-toggle"
                                        onclick="togglePassword()">
                                    <i class="fas fa-eye" id="passwordIcon"></i>
                                </button>
                            </div>
                            <div class="error-message" id="passwordError"></div>
                        </div>

                        <div class="flex items-center justify-between mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2 rounded" id="rememberPassword">
                                <span class="text-sm text-gray-600">记住密码</span>
                            </label>
                            <a href="#" class="text-sm text-blue-500" onclick="showForgotPassword()">忘记密码？</a>
                        </div>

                        <button type="submit" class="login-button" id="loginButton">
                            <span id="loginButtonText">登录</span>
                            <i class="fas fa-spinner fa-spin" id="loginSpinner" style="display: none;"></i>
                        </button>
                    </form>

                    <div class="text-center mt-6">
                        <p class="text-sm text-gray-500">
                            还没有账号？
                            <a href="#" class="text-blue-500" onclick="showRegister()">立即注册</a>
                        </p>
                    </div>

                    <div class="text-center mt-4">
                        <p class="text-xs text-gray-400">
                            登录即表示同意
                            <a href="#" class="text-blue-500" onclick="showUserAgreement()">用户协议</a>
                            和
                            <a href="#" class="text-blue-500" onclick="showPrivacyPolicy()">隐私政策</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .error-message {
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
            min-height: 16px;
        }

        .input-field.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .login-button:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
        }

        .toast {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .toast.success {
            background: rgba(34, 197, 94, 0.9);
        }

        .toast.error {
            background: rgba(239, 68, 68, 0.9);
        }
    </style>

    <script src="data/navigation-manager.js"></script>
    <script>
        // 登录管理器
        class LoginManager {
            constructor() {
                this.isLoading = false;
                this.init();
            }

            init() {
                // 加载记住的用户名
                this.loadRememberedCredentials();

                // 添加输入验证
                this.addInputValidation();
            }

            // 加载记住的凭据
            loadRememberedCredentials() {
                try {
                    const remembered = localStorage.getItem('rememberedCredentials');
                    if (remembered) {
                        const credentials = JSON.parse(remembered);
                        document.getElementById('usernameInput').value = credentials.username || '';
                        document.getElementById('rememberPassword').checked = true;
                    }
                } catch (error) {
                    console.error('加载记住的凭据失败:', error);
                }
            }

            // 添加输入验证
            addInputValidation() {
                const usernameInput = document.getElementById('usernameInput');
                const passwordInput = document.getElementById('passwordInput');

                usernameInput.addEventListener('blur', () => this.validateUsername());
                passwordInput.addEventListener('blur', () => this.validatePassword());

                // 清除错误状态
                usernameInput.addEventListener('input', () => this.clearError('username'));
                passwordInput.addEventListener('input', () => this.clearError('password'));
            }

            // 验证用户名
            validateUsername() {
                const username = document.getElementById('usernameInput').value.trim();
                const errorElement = document.getElementById('usernameError');
                const inputElement = document.getElementById('usernameInput');

                if (!username) {
                    this.showError('username', '请输入用户名或手机号');
                    return false;
                }

                if (username.length < 3) {
                    this.showError('username', '用户名至少3个字符');
                    return false;
                }

                // 手机号验证
                if (/^\d+$/.test(username) && !/^1[3-9]\d{9}$/.test(username)) {
                    this.showError('username', '请输入正确的手机号');
                    return false;
                }

                this.clearError('username');
                return true;
            }

            // 验证密码
            validatePassword() {
                const password = document.getElementById('passwordInput').value;

                if (!password) {
                    this.showError('password', '请输入密码');
                    return false;
                }

                if (password.length < 6) {
                    this.showError('password', '密码至少6个字符');
                    return false;
                }

                this.clearError('password');
                return true;
            }

            // 显示错误
            showError(field, message) {
                const errorElement = document.getElementById(field + 'Error');
                const inputElement = document.getElementById(field + 'Input');

                errorElement.textContent = message;
                inputElement.classList.add('error');
            }

            // 清除错误
            clearError(field) {
                const errorElement = document.getElementById(field + 'Error');
                const inputElement = document.getElementById(field + 'Input');

                errorElement.textContent = '';
                inputElement.classList.remove('error');
            }

            // 显示提示
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.textContent = message;

                document.body.appendChild(toast);

                setTimeout(() => toast.style.opacity = '1', 10);
                setTimeout(() => {
                    toast.style.opacity = '0';
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }

            // 设置加载状态
            setLoading(loading) {
                this.isLoading = loading;
                const button = document.getElementById('loginButton');
                const buttonText = document.getElementById('loginButtonText');
                const spinner = document.getElementById('loginSpinner');

                if (loading) {
                    button.disabled = true;
                    buttonText.style.display = 'none';
                    spinner.style.display = 'inline-block';
                } else {
                    button.disabled = false;
                    buttonText.style.display = 'inline';
                    spinner.style.display = 'none';
                }
            }

            // 处理登录
            async handleLogin(event) {
                event.preventDefault();

                if (this.isLoading) return;

                // 验证输入
                const isUsernameValid = this.validateUsername();
                const isPasswordValid = this.validatePassword();

                if (!isUsernameValid || !isPasswordValid) {
                    this.showToast('请检查输入信息', 'error');
                    return;
                }

                const username = document.getElementById('usernameInput').value.trim();
                const password = document.getElementById('passwordInput').value;
                const remember = document.getElementById('rememberPassword').checked;

                this.setLoading(true);

                try {
                    // 模拟登录请求
                    await this.simulateLogin(username, password);

                    // 保存凭据
                    if (remember) {
                        this.saveCredentials(username);
                    } else {
                        this.clearSavedCredentials();
                    }

                    this.showToast('登录成功！', 'success');

                    // 跳转到首页
                    setTimeout(() => {
                        if (window.navigateTo) {
                            navigateTo('01-首页.html');
                        } else {
                            window.location.href = '01-首页.html';
                        }
                    }, 1500);

                } catch (error) {
                    this.showToast(error.message, 'error');
                } finally {
                    this.setLoading(false);
                }
            }

            // 模拟登录
            async simulateLogin(username, password) {
                return new Promise((resolve, reject) => {
                    setTimeout(() => {
                        // 模拟验证逻辑
                        if (username === 'admin' && password === '123456') {
                            // 保存用户信息
                            const userInfo = {
                                username: username,
                                nickname: '张三',
                                avatar: '',
                                loginTime: new Date().toISOString()
                            };
                            localStorage.setItem('userInfo', JSON.stringify(userInfo));
                            localStorage.setItem('userToken', 'mock_token_' + Date.now());
                            resolve();
                        } else if (username === 'test' && password === '123456') {
                            const userInfo = {
                                username: username,
                                nickname: '测试用户',
                                avatar: '',
                                loginTime: new Date().toISOString()
                            };
                            localStorage.setItem('userInfo', JSON.stringify(userInfo));
                            localStorage.setItem('userToken', 'mock_token_' + Date.now());
                            resolve();
                        } else {
                            reject(new Error('用户名或密码错误'));
                        }
                    }, 2000);
                });
            }

            // 保存凭据
            saveCredentials(username) {
                try {
                    const credentials = { username };
                    localStorage.setItem('rememberedCredentials', JSON.stringify(credentials));
                } catch (error) {
                    console.error('保存凭据失败:', error);
                }
            }

            // 清除保存的凭据
            clearSavedCredentials() {
                try {
                    localStorage.removeItem('rememberedCredentials');
                } catch (error) {
                    console.error('清除凭据失败:', error);
                }
            }
        }

        // 全局登录管理器实例
        let loginManager;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loginManager = new LoginManager();
        });

        // 全局函数
        function closeLogin() {
            console.log('关闭登录弹窗');
            if (window.goBack) {
                goBack();
            } else {
                window.location.href = '01-首页.html';
            }
        }

        function togglePassword() {
            const passwordInput = document.getElementById('passwordInput');
            const passwordIcon = document.getElementById('passwordIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }

        function handleLogin(event) {
            if (loginManager) {
                loginManager.handleLogin(event);
            }
        }

        function showForgotPassword() {
            alert('忘记密码功能开发中...\n\n请联系客服重置密码：\n客服微信：shuimu_service');
        }

        function showRegister() {
            alert('注册功能开发中...\n\n请联系客服开通账号：\n客服微信：shuimu_service');
        }

        function showUserAgreement() {
            alert('用户协议\n\n1. 用户应遵守相关法律法规\n2. 禁止传播违法内容\n3. 保护个人隐私信息\n4. 合理使用平台资源\n\n详细协议请联系客服获取');
        }

        function showPrivacyPolicy() {
            alert('隐私政策\n\n1. 我们重视用户隐私保护\n2. 仅收集必要的用户信息\n3. 不会泄露用户个人信息\n4. 用户可随时删除个人数据\n\n详细政策请联系客服获取');
        }
    </script>
</body>
</html> 