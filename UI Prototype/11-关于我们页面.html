<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 关于我们</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <script src="data/component-templates-with-styles.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
        }
        .content::-webkit-scrollbar { display: none; }
        .about-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .logo-section {
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            color: white;
            margin: 16px 0;
        }
        .version-badge {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            margin-top: 8px;
            display: inline-block;
        }
        .feature-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        .feature-item:last-child {
            border-bottom: none;
        }
        .feature-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: white;
        }
        .link-item {
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
        }
        .link-item:last-child {
            border-bottom: none;
        }
        .team-member {
            text-align: center;
            padding: 16px;
            background: #f8fafc;
            border-radius: 12px;
            margin: 8px;
        }
        .member-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        /* ========== 组件系统 ========== */
        
        /* Logo组件基础样式 */
        .logo-component {
            border-radius: 50%;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo-component i {
            color: white;
        }
        
        /* Logo尺寸变体 */
        .logo-lg {
            width: 80px;
            height: 80px;
        }
        
        .logo-lg i {
            font-size: 32px;
        }
        
        /* Logo边距变体 */
        .logo-center {
            margin: 0 auto 16px auto;
        }
        
        /* Logo边框变体 */
        .logo-border {
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- Logo和版本信息 -->
                <div class="logo-section">
                    <div class="logo-component logo-lg logo-center logo-border">
                        <i class="fas fa-water"></i>
                    </div>
                    <h2 class="text-2xl font-bold mb-2">水幕</h2>
                    <p class="text-white text-opacity-80 mb-4">专业的恋爱学习平台</p>
                    <div class="version-badge">版本 1.2.0</div>
                </div>

                <!-- 应用介绍 -->
                <div class="about-card">
                    <h3 class="font-semibold text-gray-800 mb-4">应用介绍</h3>
                    <p class="text-gray-600 leading-relaxed mb-4">
                        水幕是一款专注于恋爱技巧学习的移动应用，为用户提供系统化的恋爱课程体系。我们致力于帮助用户提升情感交流能力，建立健康的恋爱关系。
                    </p>
                    <p class="text-gray-600 leading-relaxed">
                        通过专业的课程内容、创新的学习模式和完善的社群服务，让每一位用户都能在恋爱的道路上更加自信和成功。
                    </p>
                </div>

                <!-- 核心特色 -->
                <div class="about-card">
                    <h3 class="font-semibold text-gray-800 mb-4">核心特色</h3>
                    
                    <div class="feature-item">
                        <div class="feature-icon" style="background: #10b981;">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">系统化课程</p>
                            <p class="text-sm text-gray-500">专业的恋爱技巧课程体系</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon" style="background: #3b82f6;">
                            <i class="fas fa-download"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">离线学习</p>
                            <p class="text-sm text-gray-500">支持视频缓存，随时随地学习</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon" style="background: #f59e0b;">
                            <i class="fas fa-share-alt"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">分享分成</p>
                            <p class="text-sm text-gray-500">分享获得30%收益分成</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon" style="background: #8b5cf6;">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">版权保护</p>
                            <p class="text-sm text-gray-500">完善的防盗版技术保护</p>
                        </div>
                    </div>

                    <div class="feature-item">
                        <div class="feature-icon" style="background: #ef4444;">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-800">专属社群</p>
                            <p class="text-sm text-gray-500">购买后加入专属学习群</p>
                        </div>
                    </div>
                </div>

                <!-- 团队介绍 -->
                <div class="about-card">
                    <h3 class="font-semibold text-gray-800 mb-4">团队介绍</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="team-member">
                            <div class="member-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <p class="font-medium text-gray-800">张导师</p>
                            <p class="text-sm text-gray-500">首席情感导师</p>
                        </div>
                        
                        <div class="team-member">
                            <div class="member-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <p class="font-medium text-gray-800">李老师</p>
                            <p class="text-sm text-gray-500">资深恋爱顾问</p>
                        </div>
                        
                        <div class="team-member">
                            <div class="member-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <p class="font-medium text-gray-800">王教练</p>
                            <p class="text-sm text-gray-500">实战技巧专家</p>
                        </div>
                        
                        <div class="team-member">
                            <div class="member-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <p class="font-medium text-gray-800">赵分析师</p>
                            <p class="text-sm text-gray-500">心理分析师</p>
                        </div>
                    </div>
                </div>

                <!-- 法律信息 -->
                <div class="about-card">
                    <h3 class="font-semibold text-gray-800 mb-4">法律信息</h3>
                    
                    <div class="link-item" onclick="showUserAgreement()">
                        <span class="font-medium text-gray-800">用户协议</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="link-item" onclick="showPrivacyPolicy()">
                        <span class="font-medium text-gray-800">隐私政策</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="link-item" onclick="showCopyright()">
                        <span class="font-medium text-gray-800">版权声明</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="link-item" onclick="showDisclaimer()">
                        <span class="font-medium text-gray-800">免责声明</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <!-- 版本信息 -->
                <div class="about-card">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-gray-800">版本信息</h3>
                        <button class="check-update-btn" onclick="checkForUpdates()">
                            <i class="fas fa-sync-alt mr-1"></i>
                            检查更新
                        </button>
                    </div>

                    <div class="space-y-3">
                        <div class="flex justify-between" onclick="showVersionDetails()">
                            <span class="text-gray-600">当前版本</span>
                            <span class="font-medium text-gray-800">1.2.0</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">发布日期</span>
                            <span class="font-medium text-gray-800">2024-01-15</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">应用大小</span>
                            <span class="font-medium text-gray-800">45.2 MB</span>
                        </div>

                        <div class="flex justify-between">
                            <span class="text-gray-600">系统要求</span>
                            <span class="font-medium text-gray-800">iOS 13.0+</span>
                        </div>

                        <div class="flex justify-between" onclick="showDeveloperInfo()">
                            <span class="text-gray-600">开发商</span>
                            <span class="font-medium text-gray-800">水幕科技</span>
                        </div>
                    </div>
                </div>

                <!-- 更新日志 -->
                <div class="about-card">
                    <h3 class="font-semibold text-gray-800 mb-4">更新日志</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="font-medium text-gray-800">v1.2.0</span>
                                <span class="text-sm text-gray-500">2024-01-15</span>
                            </div>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 新增分享收益功能</li>
                                <li>• 优化视频播放体验</li>
                                <li>• 修复已知问题</li>
                            </ul>
                        </div>
                        
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="font-medium text-gray-800">v1.1.0</span>
                                <span class="text-sm text-gray-500">2023-12-20</span>
                            </div>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 新增学习报告功能</li>
                                <li>• 优化缓存管理</li>
                                <li>• 提升应用性能</li>
                            </ul>
                        </div>
                        
                        <div>
                            <div class="flex items-center justify-between mb-2">
                                <span class="font-medium text-gray-800">v1.0.0</span>
                                <span class="text-sm text-gray-500">2023-11-01</span>
                            </div>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• 水幕正式上线</li>
                                <li>• 支持课程购买和学习</li>
                                <li>• 完整的用户体系</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 联系信息 -->
                <div class="about-card">
                    <h3 class="font-semibold text-gray-800 mb-4">联系我们</h3>
                    
                    <div class="space-y-3">
                        <div class="flex items-center contact-item" onclick="copyEmail()">
                            <i class="fas fa-envelope text-blue-500 w-6"></i>
                            <span class="text-gray-600 ml-3"><EMAIL></span>
                            <i class="fas fa-copy text-gray-400 ml-auto"></i>
                        </div>

                        <div class="flex items-center contact-item" onclick="callPhone()">
                            <i class="fas fa-phone text-green-500 w-6"></i>
                            <span class="text-gray-600 ml-3">************</span>
                            <i class="fas fa-phone text-gray-400 ml-auto"></i>
                        </div>

                        <div class="flex items-center contact-item" onclick="copyWechat()">
                            <i class="fab fa-weixin text-green-600 w-6"></i>
                            <span class="text-gray-600 ml-3">shuimu_kf001</span>
                            <i class="fas fa-copy text-gray-400 ml-auto"></i>
                        </div>

                        <div class="flex items-center contact-item" onclick="showLocation()">
                            <i class="fas fa-map-marker-alt text-red-500 w-6"></i>
                            <span class="text-gray-600 ml-3">北京市朝阳区科技园</span>
                            <i class="fas fa-map text-gray-400 ml-auto"></i>
                        </div>
                    </div>
                </div>

                <div class="text-center py-8 text-gray-400 text-sm">
                    © 2024 水幕科技 版权所有
                </div>

                <div class="h-20"></div>
            </div>
        </div>
    </div>

    <style>
        .check-update-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .check-update-btn:hover {
            background: #2563eb;
        }

        .contact-item {
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            background-color: #f9fafb;
        }

        .link-item {
            cursor: pointer;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .link-item:hover {
            background-color: #f9fafb;
            padding-left: 8px;
        }

        .link-item:last-child {
            border-bottom: none;
        }

        .toast {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 10000;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .toast.success {
            background: rgba(34, 197, 94, 0.9);
        }

        .toast.info {
            background: rgba(59, 130, 246, 0.9);
        }
    </style>

    <script>
        // 关于我们页面管理器
        class AboutManager {
            constructor() {
                this.init();
            }

            init() {
                // 初始化页面
                console.log('关于我们页面已加载');
            }

            // 显示提示
            showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.textContent = message;

                document.body.appendChild(toast);

                setTimeout(() => toast.style.opacity = '1', 10);
                setTimeout(() => {
                    toast.style.opacity = '0';
                    setTimeout(() => toast.remove(), 300);
                }, 3000);
            }

            // 复制到剪贴板
            async copyToClipboard(text) {
                try {
                    if (navigator.clipboard) {
                        await navigator.clipboard.writeText(text);
                        return true;
                    } else {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        const success = document.execCommand('copy');
                        document.body.removeChild(textArea);
                        return success;
                    }
                } catch (error) {
                    console.error('复制失败:', error);
                    return false;
                }
            }
        }

        // 全局管理器实例
        let aboutManager;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTopNavigation();
            aboutManager = new AboutManager();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '关于我们'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        function goBack() {
            if (window.navigateTo) {
                navigateTo('03-我的页面.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '03-我的页面.html';
            }
        }

        // showShareModal函数已经在顶部导航组件中自动包含

        // 法律信息函数
        function showUserAgreement() {
            alert('用户协议\n\n1. 服务条款\n用户在使用水幕应用时，需遵守相关法律法规和本协议条款。\n\n2. 用户权利\n用户有权享受应用提供的各项服务，包括课程学习、分享收益等。\n\n3. 用户义务\n用户应当合法使用应用，不得从事违法违规活动。\n\n4. 知识产权\n应用内容受知识产权保护，用户不得擅自传播或商用。\n\n详细协议请联系客服获取完整版本。');
        }

        function showPrivacyPolicy() {
            alert('隐私政策\n\n1. 信息收集\n我们仅收集必要的用户信息，用于提供更好的服务。\n\n2. 信息使用\n用户信息仅用于应用功能实现，不会用于其他商业目的。\n\n3. 信息保护\n我们采用先进的安全技术保护用户隐私信息。\n\n4. 信息共享\n未经用户同意，我们不会向第三方分享用户信息。\n\n5. 用户权利\n用户有权查看、修改或删除个人信息。\n\n详细政策请联系客服获取。');
        }

        function showCopyright() {
            alert('版权声明\n\n1. 内容版权\n水幕应用内所有课程内容、文字、图片、视频等均受版权保护。\n\n2. 使用限制\n用户仅可在应用内观看学习，不得录制、截图、传播。\n\n3. 侵权处理\n对于侵犯版权的行为，我们将依法追究法律责任。\n\n4. 授权使用\n如需商业使用相关内容，请联系我们获得授权。\n\n© 2024 水幕科技 版权所有');
        }

        function showDisclaimer() {
            alert('免责声明\n\n1. 服务性质\n水幕提供的是教育咨询服务，不保证学习效果。\n\n2. 内容免责\n课程内容仅供参考，用户应结合实际情况理性学习。\n\n3. 技术免责\n因网络、设备等技术问题导致的服务中断，我们不承担责任。\n\n4. 第三方免责\n对于第三方平台或服务，我们不承担相关责任。\n\n5. 法律适用\n本声明适用中华人民共和国法律。');
        }

        // 版本相关函数
        function checkForUpdates() {
            aboutManager.showToast('正在检查更新...', 'info');

            // 模拟检查更新
            setTimeout(() => {
                const hasUpdate = Math.random() > 0.7; // 30%概率有更新

                if (hasUpdate) {
                    if (confirm('发现新版本 v1.3.0\n\n更新内容：\n• 新增AI智能推荐\n• 优化用户体验\n• 修复已知问题\n\n是否立即更新？')) {
                        aboutManager.showToast('正在跳转到应用商店...', 'info');
                        // 这里可以跳转到应用商店
                    }
                } else {
                    aboutManager.showToast('当前已是最新版本', 'success');
                }
            }, 2000);
        }

        function showVersionDetails() {
            alert('版本详情\n\nv1.2.0 更新内容：\n• 新增分享收益功能\n• 优化视频播放体验\n• 新增学习报告统计\n• 完善缓存管理功能\n• 修复已知问题\n• 提升应用稳定性\n\n发布时间：2024年1月15日\n应用大小：45.2 MB\n兼容性：iOS 13.0 或更高版本');
        }

        function showDeveloperInfo() {
            alert('开发商信息\n\n公司名称：水幕科技有限公司\n成立时间：2023年\n主营业务：移动应用开发、在线教育\n\n团队介绍：\n我们是一支专注于情感教育的技术团队，致力于通过科技手段帮助用户提升恋爱技能，建立健康的情感关系。\n\n联系方式：\n邮箱：<EMAIL>\n地址：北京市朝阳区科技园');
        }

        // 联系方式函数
        async function copyEmail() {
            const email = '<EMAIL>';
            const success = await aboutManager.copyToClipboard(email);

            if (success) {
                aboutManager.showToast('邮箱地址已复制到剪贴板', 'success');
            } else {
                aboutManager.showToast('复制失败，邮箱：' + email, 'info');
            }
        }

        function callPhone() {
            const phone = '************';
            if (confirm(`确定要拨打客服电话吗？\n\n${phone}\n\n服务时间：9:00-21:00`)) {
                // 尝试拨打电话
                try {
                    window.location.href = `tel:${phone}`;
                } catch (error) {
                    aboutManager.showToast('无法拨打电话，请手动拨打：' + phone, 'info');
                }
            }
        }

        async function copyWechat() {
            const wechat = 'shuimu_kf001';
            const success = await aboutManager.copyToClipboard(wechat);

            if (success) {
                aboutManager.showToast('微信号已复制，请在微信中搜索添加', 'success');
            } else {
                aboutManager.showToast('复制失败，微信号：' + wechat, 'info');
            }
        }

        function showLocation() {
            alert('公司地址\n\n北京市朝阳区科技园\n水幕科技大厦 15层\n\n交通指南：\n地铁：10号线科技园站 A出口\n公交：科技园北站\n\n营业时间：\n周一至周五 9:00-18:00\n\n如需到访，请提前预约：\n电话：************');
        }
    </script>
</body>
</html>