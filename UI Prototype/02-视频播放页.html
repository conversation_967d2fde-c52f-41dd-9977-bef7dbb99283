<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 视频播放</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- 引入样式文件 -->
    <link rel="stylesheet" href="data/badge-styles-dual.css">
    <link rel="stylesheet" href="data/progress-styles.css">
    
    <!-- 引入数据文件 -->
    <script src="data/app-data.js"></script>
    <script src="data/user-state.js"></script>
    <script src="data/cache-manager.js"></script>
    <script src="data/data-manager.js"></script>
    <!-- 使用现有的组件模板 -->
    <script src="data/component-templates-with-styles.js"></script>
    <script src="data/navigation-manager.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #000;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #fff;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 10;
        }
        .nav-bar {
            height: 44px;
            background: rgba(0,0,0,0.8);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            position: absolute;
            top: 44px;
            left: 0;
            right: 0;
            z-index: 20;
        }
        .video-container {
            width: 100%;
            height: 280px;
            background: #000;
            position: absolute;
            top: calc(50% - 20px);
            left: 0;
            right: 0;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
        }
        .video-player {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #1a1a1a, #333);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.5);
        }
        .play-button {
            width: 70px;
            height: 70px;
            background: rgba(255,255,255,0.95);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }
        
        .play-button:hover {
            transform: scale(1.1);
            background: rgba(255,255,255,1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.4);
        }
        .video-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 20px 16px 16px;
        }
        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            margin-bottom: 12px;
            position: relative;
        }
        .progress-fill {
            width: 35%;
            height: 100%;
            background: #667eea;
            border-radius: 2px;
        }
        .content {
            background: #374151;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: auto;
            overflow: visible;
            padding: 0;
            border-radius: 16px 16px 0 0;
            z-index: 10;
        }

        .video-info-section {
            background: transparent;
            padding: 16px 16px 16px 8px;
            color: white;
            position: absolute;
            top: calc(50% + 140px - 40px);
            left: 0;
            right: 0;
            z-index: 5;
        }

        /* 搜索弹窗样式 */
        .search-modal {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 60%;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 20px;
            display: none;
            z-index: 999999; /* 进一步提高z-index确保在全屏模式下可见 */
            overflow: hidden;
        }

        .search-modal.show {
            display: flex;
            flex-direction: column;
        }

        /* 搜索输入框样式 */
        .search-input-container {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #3b82f6;
        }

        .search-clear-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            font-size: 18px;
            display: none;
        }

        .search-clear-btn:hover {
            color: #6b7280;
        }

        /* 搜索下拉框样式 */
        .search-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-top: none;
            border-radius: 0 0 12px 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }

        /* 隐藏搜索下拉框的滚动条 */
        .search-dropdown::-webkit-scrollbar {
            display: none;
        }

        .search-dropdown {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .search-dropdown.show {
            display: block;
        }

        .search-dropdown-header {
            padding: 8px 16px;
            font-size: 12px;
            color: #6b7280;
            background: #f9fafb;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-dropdown-clear {
            color: #3b82f6;
            cursor: pointer;
            font-size: 12px;
        }

        .search-dropdown-clear:hover {
            text-decoration: underline;
        }

        .search-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-dropdown-item:hover {
            background-color: #f9fafb;
        }

        .search-dropdown-item:last-child {
            border-bottom: none;
        }

        .search-dropdown-icon {
            color: #9ca3af;
            font-size: 14px;
        }

        .search-dropdown-text {
            flex: 1;
            font-size: 14px;
            color: #374151;
        }

        .search-dropdown-delete {
            color: #9ca3af;
            font-size: 12px;
            opacity: 0;
            transition: all 0.2s ease;
            cursor: pointer;
            padding: 2px;
            border-radius: 2px;
        }

        .search-dropdown-item:hover .search-dropdown-delete {
            opacity: 1;
        }

        .search-dropdown-delete:hover {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            transform: scale(1.1);
        }

        /* 搜索历史样式 */
        .search-history-section {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 16px;
        }

        .search-section-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
        }

        .search-history-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            max-height: 120px;
            overflow-y: auto;
        }

        /* 隐藏搜索历史列表的滚动条 */
        .search-history-list::-webkit-scrollbar {
            display: none;
        }

        .search-history-list {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .search-history-item {
            background: #f3f4f6;
            color: #6b7280;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .search-history-item:hover {
            background: #e5e7eb;
            color: #374151;
        }

        /* 搜索建议样式 */
        .search-suggestions {
            flex: 1;
            overflow-y: auto;
            max-height: 300px;
            min-height: 100px;
            padding-right: 8px;
            margin-top: -5px;
        }

        /* 隐藏搜索建议区域的滚动条 */
        .search-suggestions::-webkit-scrollbar {
            display: none;
        }

        .search-suggestions {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .search-empty-state {
            text-align: center;
            color: #9ca3af;
            font-size: 14px;
            padding: 40px 20px;
        }

        /* 当搜索历史隐藏时，搜索建议占用更多空间 */
        .search-suggestions.full-height {
            flex: 2;
            max-height: 350px; /* 搜索时给更多高度 */
        }

        /* 搜索结果展示，建议项样式 - 适应两行布局 */
        .search-suggestion-item {
            padding: 10px 6px; /* 增加上下内边距适应两行布局 */
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 6px;
            margin: 0 -6px;
            display: flex;
            align-items: flex-start; /* 改为顶部对齐，适应多行内容 */
            gap: 8px;
        }

        /* 搜索结果中的播放图标位置调整 */
        .search-suggestion-item .video-play-icon {
            margin-top: 2px; /* 向下偏移一点，与第一行文字对齐 */
        }

        .search-suggestion-item:hover {
            background-color: #f9fafb;
            transform: translateX(2px);
            border-left: 3px solid #3b82f6;
            padding-left: 8px;
        }

        .search-suggestion-item:last-child {
            border-bottom: none;
        }

        .search-suggestion-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .search-suggestion-title {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            line-height: 1.3;
        }

        .search-suggestion-category {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.2;
            margin-top: 5px; /* 增加与上方内容的间距 */
        }

        /* 搜索结果中的视频标题组件调整 */
        .search-suggestion-item .video-title-component {
            margin-bottom: 0; /* 重置底部边距 */
        }

        /* 搜索结果中的进度行组件-间距调整 */
        .search-suggestion-item .video-progress-row-component {
            margin-top: -6px; /* 增加与标题的间距 */
            margin-bottom: -11px; /* 重置底部边距 */
        }

        /* 视频锁组件样式 */
        .video-lock-component {
            font-size: 12px;
            color: #9ca3af;
            margin-left: auto;
            flex-shrink: 0;
            padding-left: 8px;
            padding-right: 10px;
            display: flex;
            align-items: center;
        }

        /* 弹窗组件系统 */
        .modal-component {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 20px;
            display: none;
            z-index: 1000;
        }

        /* 弹窗尺寸变体 */
        .modal-small {
            height: 24%;
            padding: 0px 20px 24px 20px;
        }

        .modal-medium {
            height: 40%;
        }

        .modal-header-component {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .modal-title-component {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
        }

        .modal-close-component {
            color: #9ca3af;
            font-size: 20px;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
            margin-left: auto;
        }

        .modal-close-component:hover {
            color: #6b7280;
        }

        .payment-modal {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 24%;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 0px 20px 24px 20px;
            display: none;
            z-index: 9999999; /* 提高z-index确保在全屏模式下可见 */
        }

        .video-play-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        /* 模态框组件 */
        .modal-header-component {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .modal-title-component {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
        }

        .modal-close-component {
            color: #9ca3af;
            font-size: 20px;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
            margin-left: auto;
        }

        .modal-close-component:hover {
            color: #6b7280;
        }
        .content::-webkit-scrollbar { display: none; }
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: rgba(255,255,255,0.3);
            font-size: 12px;
            pointer-events: none;
        }
        .playlist-item {
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
        }
        .playlist-item.active {
            background: #eaf4ff;
            border-radius: 8px;
            padding: 12px;
            margin: 0 -12px;
        }
        .course-info-bar {
            background: #f8f9fa;
            padding: 6px;
            border-radius: 0;
            box-shadow: none;
            margin: 0;
            cursor: pointer;
            color: #374151;
            border-top: 1px solid #e5e7eb;
            min-height: 65px;
            display: flex;
            align-items: center;
        }

        .playlist-section {
            background: #f8f9fa;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .playlist-section.expanded {
            max-height: 300px;
        }

        .playlist-content {
            padding: 3px;
            margin-top: -17px;
            margin-bottom: 15px;
            max-height: 240px;
            overflow-y: auto;
        }
        
        .playlist-content::-webkit-scrollbar {
            display: none;
        }

        /* 复用首页的视频项组件样式 */
        .video-item-component {
            padding: 8px 0;
            margin-left: 8px;
            border-bottom: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .video-item-component:hover {
            background-color: #f4faff;
            transform: translateX(4px);
            padding-left: 12px;
        }

        .video-item-component:hover .video-title-component {
            color: #2563eb;
        }

        .video-item-component:hover .video-play-icon {
            background: #1d4ed8;
            transform: scale(1.1);
        }

        .video-item-component.last-watched {
            background-color: #eaf4ff;
        }

        .video-content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        .video-title-component {
            font-size: 13px;
            line-height: 1.2;
            color: #4a5568;
            margin-bottom: -8px;
            padding: 0;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .video-play-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .video-title-text {
            flex: 1;
        }

        .video-progress-row-component {
            display: flex;
            align-items: center;
            gap: 0;
            margin-bottom: -5px;
            transition: all 0.3s ease;
        }

        .progress-component {
            background: transparent;
            border-radius: 2px;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }

        .progress-thin {
            height: 1px;
            margin-right: 3px;
        }

        .progress-flex {
            flex: 1 1 auto;
            min-width: 0;
        }

        .progress-fill-component {
            height: 100%;
            border-radius: inherit;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(90deg, #84dd99, #22c55e);
            position: absolute;
            top: 0;
            left: 0;
            transform-origin: left center;
        }

        .progress-fill-component.animated {
            animation: progressFill 1.2s ease-out;
        }

        .progress-fill-component::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255,255,255,0.4),
                transparent
            );
            animation: shimmer 2s infinite;
        }

        .progress-info-component {
            display: flex;
            padding-right: 10px;
            align-items: center;
            gap: 2px;
            min-width: 80px;
            width: auto;
            justify-content: flex-end;
            flex-shrink: 0;
            font-size: 12px;
            color: #6b7280;
        }

        @keyframes progress-fill {
            from { width: 0%; }
        }

        /* ========== 徽章组件系统 - 引入双套样式系统 ========== */

        /* ========== 缓存状态样式（从首页复制） ========== */
        
        /* 缓存状态样式 - 紧挨着标题显示 */
        .cache-status {
            font-size: 12px;
            color: #6b7280;
            font-weight: 400;
            margin-left: 0;
        }

        .cache-status.downloading {
            color: #3b82f6;
            font-weight: 500;
        }

        .cache-status.success {
            color: #10b981;
            font-weight: 500;
        }

        .cache-status.cached {
            color: #10b981;
            font-weight: 400;
        }

        /* 收藏按钮样式 */
        .cache-collect-btn {
            background: transparent;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
        }

        .cache-collect-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #ef4444;
            transform: scale(1.05);
        }

        .cache-collect-btn.collected {
            color: #ef4444;
        }

        .cache-collect-btn.collected:hover {
            color: #dc2626;
        }

        .cache-collect-btn i {
            transition: all 0.3s ease;
        }

        .cache-collect-btn.collected i {
            animation: heart-beat 0.6s ease-in-out;
        }

        @keyframes heart-beat {
            0% { transform: scale(1); }
            25% { transform: scale(1.2); }
            50% { transform: scale(1); }
            75% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* 缓存弹窗样式（从首页复制） */
        .cache-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999999; /* 提高z-index确保在全屏模式下可见 */
        }

        .cache-modal-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            width: 280px;
            text-align: center;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .cache-modal-close {
            position: absolute;
            top: 12px;
            right: 16px;
            background: none;
            border: none;
            font-size: 24px;
            color: #9ca3af;
            cursor: pointer;
            padding: 4px;
            line-height: 1;
        }

        .cache-modal-close:hover {
            color: #6b7280;
        }

        .cache-video-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            padding-right: 20px;
        }

        .cache-download-btn {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cache-download-btn:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .cache-download-btn i {
            margin-right: 8px;
        }
        
        /* ========== 组件系统 ========== */
        
        /* 分享按钮组件 */
        .share-button-component {
            padding: 8px;
            color: #6b7280;
            background: transparent;
            border: none;
            border-radius: 8px;
            margin-right: 50px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: visible;
        }

        .share-button-component:hover {
            transform: scale(1.1);
        }

        .share-button-component i {
            font-size: 30px;
            transition: none !important;
        }

        /* 分享按钮白色变体 */
        .share-button-white {
            color: white;
        }

        .share-button-white:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* 分享按钮右上角的金币动画 */
        .share-button-component::after {
            content: '💰';
            position: absolute;
            right: -8px;
            font-size: 18px;
            animation: coin-scale-pulse 3s ease-in-out infinite;
            pointer-events: none;
            z-index: 2;
            transform-origin: center;
        }

        /* 搜索按钮组件 */
        .search-button-component {
            padding: 8px;
            color: #6b7280;
            background: transparent;
            border: none;
            border-radius: 8px;
            margin-right: 50px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: visible;
        }

        .search-button-component:hover {
            transform: scale(1.1);
        }

        .search-button-white {
            color: white;
        }

        .search-button-white:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* 金币由小变大的缩放动画 */
        @keyframes coin-scale-pulse {
            0% {
                transform: scale(0.8);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(0.8);
                opacity: 0.7;
            }
        }
        
        /* 自定义橙紫渐变 */
        .orange-purple-gradient {
            background: linear-gradient(90deg, #f97316, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
        }
        
        /* 增强版本 - 带悬停效果和发光 */
        .orange-purple-gradient-enhanced {
            background: linear-gradient(90deg, #f97316, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 2px rgba(249, 115, 22, 0.3));
        }

        .orange-purple-gradient-enhanced:hover {
            background: linear-gradient(90deg, #fb923c, #a855f7);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 6px rgba(249, 115, 22, 0.6));
            animation: gradient-pulse 2s ease-in-out infinite;
        }

        /* 蓝绿渐变 - 水幕主题色（与首页保持一致） */
        .blue-green-gradient-enhanced {
            background: linear-gradient(90deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.3));
        }

        .blue-green-gradient-enhanced:hover {
            background: linear-gradient(90deg, #60a5fa, #34d399);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6));
            animation: blue-green-pulse 2s ease-in-out infinite;
        }

        /* 亮色版本 - 深色背景用 */
        .orange-purple-gradient-bright {
            background: linear-gradient(90deg, #fbbf24, #c084fc);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 3px rgba(251, 191, 36, 0.4));
        }

        .orange-purple-gradient-bright:hover {
            background: linear-gradient(90deg, #fcd34d, #ddd6fe);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.8));
            animation: gradient-pulse-bright 2s ease-in-out infinite;
        }

        /* 动画关键帧 */
        @keyframes gradient-pulse {
            0%, 100% { 
                filter: drop-shadow(0 0 6px rgba(249, 115, 22, 0.6));
            }
            50% { 
                filter: drop-shadow(0 0 10px rgba(147, 51, 234, 0.8));
            }
        }

        @keyframes gradient-pulse-bright {
            0%, 100% {
                filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.8));
            }
            50% {
                filter: drop-shadow(0 0 12px rgba(192, 132, 252, 1));
            }
        }

        @keyframes blue-green-pulse {
            0%, 100% {
                filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6));
            }
            50% {
                filter: drop-shadow(0 0 10px rgba(16, 185, 129, 0.8));
            }
        }

        /* 视频控制增强 */
        .video-controls-enhanced {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 20px 16px 16px;
        }

        .control-button {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 4px;
        }

        .control-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .speed-selector {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .speed-selector option {
            background: #1f2937;
            color: white;
            padding: 4px 8px;
        }

        .speed-selector option:hover {
            background: #374151;
        }

        .speed-selector option:checked {
            background: #3b82f6;
            color: white;
        }

        /* 分享弹窗样式 */
        .share-modal {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 40%;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 20px;
            display: none;
            z-index: 999999; /* 进一步提高z-index确保在全屏模式下可见 */
        }

        .share-modal.show {
            display: block;
        }

        /* 分享选项组件 */
        .share-option-component {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .share-option-component:hover {
            background: #f8fafc;
            transform: translateY(-2px);
        }

        .share-icon-component {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 24px;
            color: white;
            position: relative;
            overflow: visible;
            animation: share-icon-pulse 2s infinite;
        }

        .share-label-component {
            font-size: 14px;
            color: #374151;
            transition: color 0.3s ease;
        }

        .share-option-component:hover .share-label-component {
            color: #22c55e;
            font-weight: 500;
        }

        /* 分享图标颜色变体 */
        .share-icon-wechat {
            background: linear-gradient(135deg, #1aad19, #0d8912);
            box-shadow: 0 4px 12px rgba(26, 173, 25, 0.3);
        }

        .share-icon-link {
            background: linear-gradient(135deg, #667eea, #4f46e5);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        /* 分享图标动画 */
        @keyframes share-icon-pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
            }
        }

        /* 更多功能菜单样式 */
        .more-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: flex-end;
            justify-content: center;
            z-index: 9999999; /* 提高z-index确保在全屏模式下可见 */
        }

        .more-menu-content {
            background: white;
            border-radius: 16px 16px 0 0;
            width: 359px;
            max-height: 50%;
            overflow-y: auto;
            animation: slideUp 0.3s ease-out;
        }

        .more-menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 20px 16px;
            border-bottom: 1px solid #f3f4f6;
        }

        .more-menu-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
            margin: 0;
        }

        .more-menu-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #9ca3af;
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }

        .more-menu-close:hover {
            color: #6b7280;
        }

        .more-menu-list {
            padding: 8px 0 20px;
        }

        .more-menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f9fafb;
        }

        .more-menu-item:hover {
            background: #f9fafb;
            transform: translateX(4px);
        }

        .more-menu-item:last-child {
            border-bottom: none;
        }

        .more-menu-item i {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #6b7280;
            font-size: 16px;
        }

        .more-menu-item span {
            font-size: 16px;
            color: #374151;
            font-weight: 500;
        }

        .more-menu-item:hover i {
            color: #3b82f6;
        }

        .more-menu-item:hover span {
            color: #1f2937;
        }

        @keyframes slideUp {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* 增强返回按钮样式 - 扩大点击范围 */
        .back-button-enhanced {
            background: transparent;
            border: none;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            
            /* 扩大点击范围 */
            padding: 12px 16px;
            margin: -8px -12px -8px -4px;
            
            /* 视觉反馈 */
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            /* 确保图标居中 */
            min-width: 44px;
            min-height: 44px;
        }

        .back-button-enhanced:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: scale(1.05);
        }

        .back-button-enhanced:active {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(0.95);
        }

        /* 确保图标样式 */
        .back-button-enhanced i {
            pointer-events: none;
            transition: none;
        }

        /* ========== 全屏播放功能样式 ========== */
        
        /* 全屏状态下的基本样式 */
        .fullscreen-mode {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 9999 !important;
            background: #000 !important;
        }

        /* 全屏时隐藏的元素 */
        .fullscreen-hidden {
            display: none !important;
        }

        /* 全屏控制界面 */
        .fullscreen-controls {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: transparent;
            z-index: 50000;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            display: none;
        }

        /* 只有在全屏模式下才显示控制界面 */
        .fullscreen-mode .fullscreen-controls,
        body.fullscreen-mode .fullscreen-controls {
            display: block !important;
        }

        /* 全屏控制界面可见时的样式 - 修复pointer-events冲突 */
        .fullscreen-controls.visible {
            opacity: 1 !important;
            pointer-events: auto !important; /* 允许控制界面响应点击 */
        }

        /* 控制界面子元素的显示/隐藏 */
        .fullscreen-controls .fullscreen-nav-bar,
        .fullscreen-controls .fullscreen-video-controls,
        .fullscreen-controls .fullscreen-video-info,
        .fullscreen-controls .fullscreen-back-btn {
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .fullscreen-controls.visible .fullscreen-nav-bar,
        .fullscreen-controls.visible .fullscreen-video-controls,
        .fullscreen-controls.visible .fullscreen-video-info,
        .fullscreen-controls.visible .fullscreen-back-btn {
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        /* 全屏控制界面的背景区域不响应点击，但子元素可以 */
        .fullscreen-controls.visible > .fullscreen-touch-area {
            pointer-events: auto;
        }

        /* 确保全屏控制界面中的交互元素可点击 - 简化样式 */
        .fullscreen-controls button,
        .fullscreen-controls select,
        .fullscreen-controls .video-item-component {
            pointer-events: auto;
            cursor: pointer;
            position: relative;
            z-index: 10;
        }

        /* 确保导航栏、控制栏、视频信息区域可交互 */
        .fullscreen-nav-bar,
        .fullscreen-video-controls,
        .fullscreen-video-info,
        .fullscreen-playlist-panel {
            pointer-events: auto;
            position: relative;
            z-index: 10;
        }

        /* 全屏时的播放列表按钮 */
        .fullscreen-playlist-btn {
            position: fixed;
            bottom: 13px;
            right: 80px;
            width: 50px;
            height: 50px;
            background: transparent;
            border: none;
            border-radius: 50%;
            color: white;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 50001;
            pointer-events: auto; /* 确保可点击 */
            opacity: 0;
            transform: translateY(20px);
        }

        /* 全屏模式下显示播放列表按钮 */
        .fullscreen-mode .fullscreen-playlist-btn,
        body.fullscreen-mode .fullscreen-playlist-btn {
            display: flex;
        }

        /* 控制界面可见时显示播放列表按钮 */
        .fullscreen-controls.visible .fullscreen-playlist-btn {
            opacity: 1;
            transform: translateY(0);
        }

        .fullscreen-playlist-btn:hover {
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transform: scale(1.1);
        }

        .fullscreen-playlist-btn i {
            font-size: 18px;
        }

        /* 全屏播放列表面板 */
        .fullscreen-playlist-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: rgba(0, 0, 0, 0.95);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(255, 255, 255, 0.1);
            transition: right 0.3s ease;
            z-index: 50002;
            overflow-y: auto;
            overflow-x: hidden; /* 防止水平滚动条 */
            padding: 20px;
            /* 稳定滚动条显示 */
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
        }

        /* Webkit浏览器滚动条样式 */
        .fullscreen-playlist-panel::-webkit-scrollbar {
            width: 6px;
        }

        .fullscreen-playlist-panel::-webkit-scrollbar-track {
            background: transparent;
        }

        .fullscreen-playlist-panel::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .fullscreen-playlist-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .fullscreen-playlist-panel.visible {
            right: 0;
        }

        /* 全屏播放列表头部 */
        .fullscreen-playlist-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .fullscreen-playlist-title {
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .fullscreen-playlist-close {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.7);
            font-size: 24px;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .fullscreen-playlist-close:hover {
            color: white;
            background: rgba(255, 255, 255, 0.1);
        }

        /* 全屏播放列表项 - 兼容标准视频项组件 */
        .fullscreen-playlist-panel .video-item-component {
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
            background: transparent;
            border-radius: 0;
        }

        .fullscreen-playlist-panel .video-item-component:hover {
            background: rgba(255, 255, 255, 0.05) !important;
            padding-left: 8px;
            border-radius: 8px;
        }

        .fullscreen-playlist-panel .video-item-component.current,
        .fullscreen-playlist-panel .video-item-component.last-watched {
            background: rgba(59, 130, 246, 0.2) !important;
            border-radius: 8px;
            padding-left: 8px;
        }

        /* 确保全屏播放列表中的视频项文字为白色 */
        .fullscreen-playlist-panel .video-title-text {
            color: white !important;
        }

        .fullscreen-playlist-panel .progress-percentage-component {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .fullscreen-playlist-panel .cache-status {
            color: rgba(34, 197, 94, 0.9) !important;
        }

        /* 确保全屏模式下的收藏按钮样式与非全屏模式一致 */
        .fullscreen-video-info .cache-collect-btn {
            background: transparent;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
        }

        .fullscreen-video-info .cache-collect-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #ef4444;
            transform: scale(1.05);
        }

        .fullscreen-video-info .cache-collect-btn.collected {
            color: #ef4444;
        }

        .fullscreen-video-info .cache-collect-btn.collected:hover {
            color: #dc2626;
        }

        .fullscreen-video-info .cache-collect-btn i {
            transition: all 0.3s ease;
        }

        .fullscreen-video-info .cache-collect-btn.collected i {
            animation: heart-beat 0.6s ease-in-out;
        }

        /* 保持原有的fullscreen-video-item样式作为备用 */
        .fullscreen-video-item {
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .fullscreen-video-item:hover {
            background: rgba(255, 255, 255, 0.05);
            padding-left: 8px;
            border-radius: 8px;
        }

        .fullscreen-video-item.current {
            background: rgba(59, 130, 246, 0.2);
            border-radius: 8px;
            padding-left: 8px;
        }

        .fullscreen-video-play-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            flex-shrink: 0;
        }

        .fullscreen-video-content {
            flex: 1;
            color: white;
        }

        .fullscreen-video-title {
            font-size: 14px;
            line-height: 1.3;
            margin-bottom: 4px;
        }

        .fullscreen-video-progress {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .fullscreen-progress-bar {
            flex: 1;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 1px;
            overflow: hidden;
        }

        .fullscreen-progress-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 1px;
            transition: width 0.3s ease;
        }

        /* 全屏时的视频控制栏 */
        .fullscreen-video-controls {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            padding: 20px;
            z-index: 50001; /* 比控制界面稍高 */
        }

        /* 全屏时的视频信息区域 */
        .fullscreen-video-info {
            position: fixed;
            bottom: 100px; /* 在控制栏上方 */
            left: 80px;
            right: 80px;
            background: transparent;
            padding: 16px 20px;
            z-index: 50001;
            color: white;
            border-radius: 0;
            backdrop-filter: none;
            max-width: 800px; /* 最大宽度限制 */
            margin: 0 auto;
            box-sizing: border-box;
            text-align: center;
        }

        .fullscreen-video-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .fullscreen-video-category {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 12px;
        }

        .fullscreen-video-progress-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 8px;
        }

        .fullscreen-progress-bar-info {
            flex: 1;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
        }

        .fullscreen-progress-fill-info {
            height: 100%;
            background: linear-gradient(90deg, #84dd99, #22c55e);
            border-radius: 2px;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .fullscreen-video-stats {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .fullscreen-video-stats-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .fullscreen-video-stats-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 全屏时的导航栏 */
        .fullscreen-nav-bar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: linear-gradient(rgba(0,0,0,0.8), transparent);
            padding: 20px;
            z-index: 50001; /* 比控制界面稍高 */
            pointer-events: auto !important; /* 确保导航栏可以响应点击 */
        }

        /* 确保全屏导航栏中的按钮可点击 */
        .fullscreen-nav-bar button {
            position: relative;
            z-index: 50002 !important;
            pointer-events: auto !important;
        }

        /* 完全禁用导航栏的悬停效果，避免干扰返回按钮 */
        .fullscreen-nav-bar:hover {
            /* 移除悬停效果 */
        }

        /* 响应式设计 - 移动设备 */
        @media (max-width: 480px) {
            .fullscreen-playlist-panel {
                width: 100vw;
                right: -100vw;
            }
            
            .fullscreen-playlist-btn {
                bottom: 15px;
                right: 15px;
                width: 45px;
                height: 45px;
            }
        }

        /* 全屏时的触摸区域 - 简单实现 */
        .fullscreen-touch-area {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1;
            cursor: pointer;
            background: transparent;
            pointer-events: none;
            opacity: 0;
        }
        
        /* 全屏时触摸区域激活 */
        .fullscreen-mode .fullscreen-touch-area,
        body.fullscreen-mode .fullscreen-touch-area {
            pointer-events: auto;
            opacity: 1;
        }
        


        /* 全屏状态指示器 */
        .fullscreen-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 50001; /* 比控制界面稍高 */
            backdrop-filter: blur(10px);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .fullscreen-indicator.visible {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-100">
<!-- 徽章样式容器 - 默认使用混合版 -->
<div class="badge-style-mixed">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 导航栏 -->
            <div class="nav-bar">
                <div class="flex items-center">
                    <button class="back-button-enhanced" onclick="goBackToHome()" title="返回首页">
                        <i class="fas fa-chevron-left text-lg"></i>
                    </button>
                </div>
                <div class="flex items-center">
                    <button class="search-button-component search-button-white" onclick="showSearchModal()">
                        <i class="fas fa-search text-white text-lg"></i>
                    </button>
                    <button class="share-button-component share-button-white" onclick="toggleShareModal()">
                        <i class="fas fa-share-alt blue-green-gradient-enhanced"></i>
                    </button>
                    <button class="text-white" onclick="toggleMoreMenu()" title="更多功能">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <!-- 视频播放器 -->
            <div class="video-container">
                <div class="video-player">
                    <div class="play-button">
                        <i class="fas fa-play"></i>
                    </div>
                    
                    <!-- 动态水印 -->
                    <div class="watermark">
                        张三 138****5678
                    </div>
                    
                    <!-- 视频控制栏 -->
                    <div class="video-controls">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <div class="flex items-center justify-between text-white text-sm">
                            <span>05:32</span>
                            <div class="flex items-center space-x-2">
                                <button class="control-button"><i class="fas fa-volume-up"></i></button>
                                <select class="speed-selector">
                                    <option value="0.5">0.5x</option>
                                    <option value="0.75">0.75x</option>
                                    <option value="1" selected>1x</option>
                                    <option value="1.25">1.25x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2">2x</option>
                                </select>
                                <button class="control-button" onclick="toggleFullscreen()"><i class="fas fa-expand"></i></button>
                            </div>
                            <span>15:48</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频信息 -->
            <div class="video-info-section">
                <h2 class="text-lg font-bold text-white mb-1">06. 深度沟通艺术</h2>
                
                <!-- 进度条和徽章信息 -->
                <div class="video-progress-row-component mb-2 -mt-3">
                    <div class="progress-component progress-thin progress-flex">
                        <div class="progress-fill-component animated" style="width: 85%;"></div>
                    </div>
                    <div class="progress-info-component">
                        <span class="progress-percentage-component">85%</span>
                        <span class="badge-component badge-level-6">×6</span>
                    </div>
                </div>
                
                <p class="text-sm text-gray-300 mb-2">道：恋爱宝典系列·恋爱宝典1</p>
                <div class="flex items-center justify-between text-xs text-gray-400 mb-2">
                    <span><i class="fas fa-play-circle mr-1"></i>1.2万播放</span>
                    <button class="cache-collect-btn" onclick="toggleVideoCollection()" title="收藏视频">
                        <i class="fas fa-heart" id="collectIcon"></i>
                        <span id="collectText">收藏</span>
                    </button>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content" id="content">
                <!-- 课程信息栏 -->
                <div class="course-info-bar" onclick="togglePlaylist()">
                    <div class="flex items-center justify-between w-full" style="margin-top: -10px;">
                        <div class="flex items-center">
                            <i class="fas fa-heart text-red-500 text-2xl mr-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">恋爱宝典1·共12集</p>
                                <p class="text-sm text-gray-500">点击查看课程列表</p>
                            </div>
                        </div>
                        <i class="fas fa-chevron-up text-gray-400" id="playlistToggle"  style="margin-top: -20px;padding-right: 5px;"></i>
                    </div>
                </div>

                <!-- 播放列表 -->
                <div class="playlist-section" id="playlist">
                    <div class="playlist-content">
                        <div class="mt-2 space-y-1" id="playlistContainer">
                            <!-- 播放列表将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 全屏时的触摸区域 -->
            <div class="fullscreen-touch-area" id="fullscreenTouchArea" onclick="toggleFullscreenControls()"></div>

            <!-- 测试按钮已移除，使用正常的全屏功能测试 -->

            <!-- 全屏播放控制界面 -->
    <div class="fullscreen-controls" id="fullscreenControls" style="opacity: 0; display: none;">
        <!-- 全屏状态指示器 -->
        <div class="fullscreen-indicator" id="fullscreenIndicator">
            全屏播放中
        </div>
        
        <!-- 返回按钮区域：完全独立，不在导航栏内 -->
        <div class="fullscreen-back-btn flex items-center justify-center" onclick="goBackToHome()" title="返回首页"
             style="position: fixed; top: 20px; left: 20px; z-index: 999999999;
                    width: 48px; height: 48px; cursor: pointer; border-radius: 50%;
                    background: transparent; backdrop-filter: blur(10px);
                    transition: all 0.2s ease;"
             onmouseover="this.style.backgroundColor='rgba(128,128,128,0.5)'; this.style.transform='scale(1.1)'"
             onmouseout="this.style.backgroundColor='transparent'; this.style.transform='scale(1)'">
            <!-- 移除按钮的onclick，只保留容器的onclick，避免按钮本身的遮挡问题 -->
            <i class="fas fa-chevron-left text-white text-lg" style="pointer-events: none;"></i>
        </div>

        <!-- 全屏时的导航栏 -->
        <div class="fullscreen-nav-bar">
            <div class="flex items-center justify-between">
                <!-- 左边留空，返回按钮已经独立出去了 -->
                <div></div>
                <div class="flex items-center">
                    <button class="search-button-component search-button-white" onclick="showSearchModal()">
                        <i class="fas fa-search text-white text-lg"></i>
                    </button>
                    <button class="share-button-component share-button-white" onclick="toggleShareModal()">
                        <i class="fas fa-share-alt blue-green-gradient-enhanced"></i>
                    </button>
                    <button class="text-white" onclick="toggleMoreMenu()" title="更多功能">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 全屏时的视频控制栏 -->
        <div class="fullscreen-video-controls">
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <div class="flex items-center justify-between text-white text-sm mt-3">
                <span>05:32</span>
                <div class="flex items-center space-x-2">
                    <button class="control-button"><i class="fas fa-volume-up"></i></button>
                    <select class="speed-selector">
                        <option value="0.5">0.5x</option>
                        <option value="0.75">0.75x</option>
                        <option value="1" selected>1x</option>
                        <option value="1.25">1.25x</option>
                        <option value="1.5">1.5x</option>
                        <option value="2">2x</option>
                    </select>
                    <button class="control-button" onclick="exitFullscreen()" title="退出全屏">
                        <i class="fas fa-compress"></i>
                    </button>
                </div>
                <span>15:48</span>
            </div>
        </div>
        
        <!-- 全屏时的视频信息区域 -->
        <div class="fullscreen-video-info" id="fullscreenVideoInfo">
            <div class="fullscreen-video-title" id="fullscreenVideoTitle">06. 深度沟通艺术</div>
            <div class="fullscreen-video-category" id="fullscreenVideoCategory">道：恋爱宝典系列·恋爱宝典1</div>
            <div class="fullscreen-video-progress-info">
                <div class="fullscreen-progress-bar-info">
                    <div class="fullscreen-progress-fill-info" id="fullscreenProgressFill" style="width: 85%;"></div>
                </div>
                <span id="fullscreenProgressText">85%</span>
                <span class="badge-component badge-level-6" id="fullscreenBadge">×6</span>
            </div>
            <div class="fullscreen-video-stats">
                <div class="fullscreen-video-stats-left">
                    <span><i class="fas fa-play-circle mr-1"></i><span id="fullscreenPlayCount">1.2万</span>播放</span>
                </div>
                <div class="fullscreen-video-stats-right">
                    <button class="cache-collect-btn" onclick="toggleVideoCollection()" title="收藏视频">
                        <i class="fas fa-heart" id="fullscreenCollectIcon"></i>
                        <span id="fullscreenCollectText">收藏</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 全屏播放列表按钮 -->
        <button class="fullscreen-playlist-btn" onclick="toggleFullscreenPlaylist()" title="播放列表">
            <i class="fas fa-list"></i>
        </button>
            <!-- 全屏播放列表面板 -->
            <div class="fullscreen-playlist-panel" id="fullscreenPlaylistPanel">
                <div class="fullscreen-playlist-header">
                    <h3 class="fullscreen-playlist-title" id="fullscreenPlaylistTitle">恋爱宝典1·共12集</h3>
                    <button class="fullscreen-playlist-close" onclick="closeFullscreenPlaylist()">×</button>
                </div>
                <div id="fullscreenPlaylistContent">
                    <!-- 播放列表内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 搜索弹窗 - 移动到screen内部以支持全屏显示 -->
        <div class="search-modal" id="searchModal" onclick="event.stopPropagation()">
            <div class="modal-header-component">
                <h3 class="modal-title-component">搜索课程</h3>
                <button class="modal-close-component" onclick="closeSearchModal()">×</button>
            </div>

            <!-- 搜索输入框 -->
            <div class="search-input-container">
                <input type="text" class="search-input" id="searchInput" placeholder="搜索视频标题..." autocomplete="off" oninput="handleSearchInput()" onkeypress="handleSearchKeypress(event)" onfocus="showSearchDropdown()" onblur="handleSearchBlur()">
                <button class="search-clear-btn" id="searchClearBtn" onclick="clearSearch()">×</button>

                <!-- 搜索下拉框 -->
                <div class="search-dropdown" id="searchDropdown">
                    <div class="search-dropdown-header">
                        <span>搜索历史</span>
                        <span class="search-dropdown-clear" onclick="clearSearchHistory(event)">清空</span>
                    </div>
                    <div id="searchDropdownList">
                        <!-- 历史记录项将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 搜索历史 -->
            <div class="search-history-section" id="searchHistorySection">
                <div class="search-section-title">搜索历史</div>
                <div class="search-history-list" id="searchHistoryList">
                    <div class="search-history-item" onclick="searchKeyword('恋爱技巧')">恋爱技巧</div>
                    <div class="search-history-item" onclick="searchKeyword('聊天方法')">聊天方法</div>
                    <div class="search-history-item" onclick="searchKeyword('约会攻略')">约会攻略</div>
                    <div class="search-history-item" onclick="searchKeyword('表白技巧')">表白技巧</div>
                </div>
            </div>

            <!-- 搜索建议/结果 -->
            <div class="search-suggestions" id="searchSuggestions">
                <div class="search-empty-state" id="searchEmptyState">
                    输入关键词开始搜索
                </div>
            </div>
        </div>

        <!-- 分享弹窗 - 移动到screen内部以支持全屏显示 -->
        <div class="share-modal" id="shareModal">
            <div class="modal-header-component">
                <h3 class="modal-title-component">分享推广计划</h3>
                <button class="modal-close-component" onclick="toggleShareModal()">×</button>
            </div>

            <div class="text-center mb-4">
                <p class="text-sm text-green-600 font-medium">分享成功购买可获得30%收益</p>
                <p class="text-xs text-gray-500 mt-1">邀请好友一起学习，共同成长</p>
            </div>

            <div class="grid grid-cols-3 gap-4 mb-6">
                <div class="share-option-component" onclick="shareToWechat()">
                    <div class="share-icon-component share-icon-wechat">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <span class="share-label-component">微信好友</span>
                </div>

                <div class="share-option-component" onclick="shareToMoments()">
                    <div class="share-icon-component share-icon-wechat">
                        <i class="fas fa-users"></i>
                    </div>
                    <span class="share-label-component">朋友圈</span>
                </div>

                <div class="share-option-component" onclick="copyShareLink()">
                    <div class="share-icon-component share-icon-link">
                        <i class="fas fa-link"></i>
                    </div>
                    <span class="share-label-component">复制链接</span>
                </div>
            </div>

            <div class="text-center">
                <button class="w-full bg-gray-100 text-gray-600 py-3 rounded-lg font-medium" onclick="toggleShareModal()">
                    取消
                </button>
            </div>
        </div>

        <!-- 支付弹窗 - 移动到screen内部以支持全屏显示 -->
        <div class="modal-component modal-small payment-modal" id="paymentModal" style="display: none;">
            <div class="modal-header-component" style="padding-top: 0px;">
                <button class="modal-close-component" onclick="closePayment()">×</button>
            </div>
            <div class="mb-1" style="margin-top: -10px;">
                <h3 class="text-base font-medium text-blue-600 text-left" id="paymentTitle" style="color: #667eea;">系列名·分类名</h3>
            </div>
            <div class="text-center mb-2" style="margin-top: 8px;">
                <p class="text-3xl font-bold text-red-500" id="paymentPrice">¥100</p>
                <p class="text-xs text-gray-500 mt-1">购买后可享受30%分享分成</p>
            </div>
            <div class="flex justify-center" style="margin-top: 5px;">
                <button class="w-4/5 bg-green-500 text-white py-3 rounded-lg font-semibold text-lg" style="background-color: #4caf50;" onclick="goToPaymentPage()">
                    立即支付
                </button>
            </div>
        </div>

        <!-- 缓存提示弹窗 - 移动到screen内部以支持全屏显示 -->
        <div class="cache-modal-overlay" id="cacheModal" style="display: none;" onclick="event.stopPropagation()">
            <div class="cache-modal-content" onclick="event.stopPropagation()">
                <button class="cache-modal-close" onclick="closeCacheModal()">×</button>
                <p class="cache-video-title" id="cacheVideoTitle">视频标题</p>
                <button class="cache-download-btn" onclick="startVideoCache()">
                    <i class="fas fa-download"></i>
                    <span>缓存到本地</span>
                </button>
            </div>
        </div>

        <!-- 更多功能菜单 - 移动到screen内部以支持全屏显示 -->
        <div class="more-menu-overlay" id="moreMenu" style="display: none;" onclick="closeMoreMenuOnOverlay(event)">
            <div class="more-menu-content" onclick="event.stopPropagation()">
                <div class="more-menu-header">
                    <h3>更多功能</h3>
                    <button class="more-menu-close" onclick="toggleMoreMenu()">×</button>
                </div>

                <div class="more-menu-list">
                    <div class="more-menu-item" onclick="showVideoInfo()">
                        <i class="fas fa-info-circle"></i>
                        <span>视频详情</span>
                    </div>

                    <div class="more-menu-item" onclick="reportVideo()">
                        <i class="fas fa-flag"></i>
                        <span>举报视频</span>
                    </div>

                    <div class="more-menu-item" onclick="downloadVideo()">
                        <i class="fas fa-download"></i>
                        <span>下载视频</span>
                    </div>

                    <div class="more-menu-item" onclick="adjustPlaybackSpeed()">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>播放速度</span>
                    </div>

                    <div class="more-menu-item" onclick="toggleSubtitles()">
                        <i class="fas fa-closed-captioning"></i>
                        <span>字幕设置</span>
                    </div>

                    <div class="more-menu-item" onclick="showStudyNotes()">
                        <i class="fas fa-sticky-note"></i>
                        <span>学习笔记</span>
                    </div>
                </div>
            </div>
        </div>
    </div>











    <script>
        // 搜索功能管理 - 完全复制首页的SearchManager
        const SearchManager = {
            modal: null,
            input: null,
            clearBtn: null,
            historySection: null,
            historyList: null,
            suggestions: null,
            emptyState: null,
            dropdown: null,
            dropdownList: null,
            currentKeyword: '',
            searchHistory: ['恋爱技巧', '聊天方法', '约会攻略', '表白技巧'],
            dropdownVisible: false,
            isVisible: false,
            isDeleting: false,

            init() {
                try {
                    this.modal = document.getElementById('searchModal');
                    this.input = document.getElementById('searchInput');
                    this.clearBtn = document.getElementById('searchClearBtn');
                    this.historySection = document.getElementById('searchHistorySection');
                    this.historyList = document.getElementById('searchHistoryList');
                    this.suggestions = document.getElementById('searchSuggestions');
                    this.emptyState = document.getElementById('searchEmptyState');
                    this.dropdown = document.getElementById('searchDropdown');
                    this.dropdownList = document.getElementById('searchDropdownList');

                    if (!this.modal || !this.input) {
                        console.warn('Search elements not found, search functionality will be limited');
                        return; // 提前返回，避免后续操作null元素
                    }

                    // 从localStorage加载搜索历史
                    this.loadSearchHistory();
                    
                    // 添加全局点击监听器
                    this.addGlobalClickListener();
                    
                    console.log('SearchManager initialized successfully');
                } catch (error) {
                    console.error('Error initializing SearchManager:', error);
                }
            },

            addGlobalClickListener() {
                // 创建绑定的处理器函数并保存引用
                this.boundClickHandler = (event) => {
                    this.handleGlobalClick(event);
                };
                this.boundTouchHandler = (event) => {
                    this.handleGlobalClick(event);
                };

                // 添加全局点击和触摸监听器
                document.addEventListener('click', this.boundClickHandler);
                
                // 移动设备触摸支持
                document.addEventListener('touchstart', this.boundTouchHandler);
            },

            removeGlobalClickListener() {
                // 移除全局点击和触摸监听器
                if (this.boundClickHandler) {
                    document.removeEventListener('click', this.boundClickHandler);
                    this.boundClickHandler = null;
                }
                if (this.boundTouchHandler) {
                    document.removeEventListener('touchstart', this.boundTouchHandler);
                    this.boundTouchHandler = null;
                }
            },

            handleGlobalClick(event) {
                // 如果搜索弹窗没有显示，不需要处理
                if (!this.isVisible) return;

                // 检查点击的元素是否在搜索弹窗内
                if (this.modal && this.modal.contains(event.target)) {
                    return; // 点击在弹窗内，不关闭
                }

                // 检查是否点击了搜索按钮（避免点击搜索按钮时立即关闭）
                const searchButton = event.target.closest('.search-button-component');
                if (searchButton) {
                    return; // 点击的是搜索按钮，不关闭
                }

                // 检查是否点击了缓存弹窗或其内容（避免缓存弹窗操作时关闭搜索弹窗）
                const cacheModal = document.getElementById('cacheModal');
                if (cacheModal && cacheModal.style.display === 'flex' && cacheModal.contains(event.target)) {
                    return; // 点击在缓存弹窗内，不关闭搜索弹窗
                }

                // 检查缓存弹窗是否显示（如果显示则不关闭搜索弹窗）
                if (cacheModal && cacheModal.style.display === 'flex') {
                    return; // 缓存弹窗正在显示，不关闭搜索弹窗
                }

                // 点击在弹窗外，关闭搜索弹窗
                this.hide();
            },

            loadSearchHistory() {
                try {
                    const saved = localStorage.getItem('searchHistory');
                    if (saved) {
                        this.searchHistory = JSON.parse(saved);
                    }
                } catch (error) {
                    console.warn('Failed to load search history:', error);
                }
            },

            saveSearchHistory() {
                try {
                    localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
                } catch (error) {
                    console.warn('Failed to save search history:', error);
                }
            },

            showDropdown() {
                if (!this.dropdown || !this.dropdownList) return;

                this.updateDropdownList();
                this.dropdown.classList.add('show');
                this.dropdownVisible = true;
            },

            hideDropdown() {
                if (!this.dropdown) return;

                // 延迟隐藏，允许点击下拉项
                setTimeout(() => {
                    this.dropdown.classList.remove('show');
                    this.dropdownVisible = false;

                    // 不隐藏历史记录区域，保持显示
                }, 150);
            },

            updateDropdownList() {
                if (!this.dropdownList) return;

                if (this.searchHistory.length === 0) {
                    this.dropdownList.innerHTML = '<div style="padding: 16px; text-align: center; color: #9ca3af; font-size: 14px;">暂无搜索历史</div>';
                    return;
                }

                let html = '';
                this.searchHistory.forEach((keyword, index) => {
                    html += `
                        <div class="search-dropdown-item" onclick="selectDropdownItem('${keyword}')">
                            <i class="fas fa-clock search-dropdown-icon"></i>
                            <span class="search-dropdown-text">${keyword}</span>
                            <i class="fas fa-times search-dropdown-delete" onclick="deleteHistoryItem(${index}, event)" title="删除此记录"></i>
                        </div>
                    `;
                });

                this.dropdownList.innerHTML = html;
            },

            deleteHistoryItem(index) {
                this.isDeleting = true;
                this.searchHistory.splice(index, 1);
                this.saveSearchHistory();
                this.updateDropdownList();
                this.updateHistoryDisplay();
                
                // 删除后保持下拉框显示状态
                this.showDropdown();
                
                // 重置删除标志
                setTimeout(() => {
                    this.isDeleting = false;
                }, 200);
            },

            clearAllHistory() {
                this.isDeleting = true;
                this.searchHistory = [];
                this.saveSearchHistory();
                this.updateDropdownList();
                this.updateHistoryDisplay();
                
                // 清空后保持下拉框显示，显示"暂无搜索历史"
                this.showDropdown();
                
                // 重置删除标志
                setTimeout(() => {
                    this.isDeleting = false;
                }, 200);
            },

            show() {
                if (!this.modal) return;
                this.modal.classList.add('show');
                this.modal.style.display = 'flex';
                this.isVisible = true;

                this.updateHistoryDisplay();
                this.showDefaultState();
            },

            hide() {
                if (!this.modal) return;
                this.modal.classList.remove('show');
                this.modal.style.display = 'none';
                this.isVisible = false;
                this.clearInput();
            },

            clearInput() {
                if (this.input) {
                    this.input.value = '';
                    this.currentKeyword = '';
                }
                if (this.clearBtn) {
                    this.clearBtn.style.display = 'none';
                }
                this.showDefaultState();
            },

            updateHistoryDisplay() {
                if (!this.historyList) return;

                this.historyList.innerHTML = '';
                this.searchHistory.forEach(keyword => {
                    const item = document.createElement('div');
                    item.className = 'search-history-item';
                    item.textContent = keyword;
                    item.onclick = () => this.searchKeyword(keyword);
                    this.historyList.appendChild(item);
                });
            },

            showDefaultState() {
                if (this.historySection) {
                    this.historySection.style.display = 'block'; // 默认显示历史记录区域
                }
                if (this.suggestions) {
                    this.suggestions.classList.remove('full-height');
                    this.suggestions.innerHTML = '<div class="search-empty-state">输入关键词开始搜索</div>';
                }
            },

            showSearchResults(keyword) {
                if (!this.suggestions) return;

                // 隐藏历史记录
                if (this.historySection) {
                    this.historySection.style.display = 'none';
                }

                // 给搜索建议区域更多空间
                this.suggestions.classList.add('full-height');

                // 模拟搜索结果
                const mockResults = this.getMockSearchResults(keyword);

                if (mockResults.length === 0) {
                    this.suggestions.innerHTML = '<div class="search-empty-state">未找到相关课程</div>';
                    return;
                }

                let resultsHtml = '';
                mockResults.forEach((result, index) => {
                    // 获取视频数据 - 优先使用搜索结果中的数据
                    const videoData = {
                        progress: result.defaultProgress || this.getVideoData(result.title, result.category).progress,
                        watchCount: result.defaultWatchCount || this.getVideoData(result.title, result.category).watchCount
                    };
                    
                    // 检查缓存状态
                    const videoId = VideoCacheManager.getVideoId(result.title, result.category);
                    let cacheStatus = '';
                    
                    if (VideoCacheManager.isDownloading(result.title, result.category)) {
                        const progress = VideoCacheManager.downloadingVideos.get(videoId);
                        cacheStatus = `<span class="cache-status downloading">(${progress}%)</span>`;
                    } else if (VideoCacheManager.isCached(result.title, result.category)) {
                        cacheStatus = '<span class="cache-status cached">(已缓存)</span>';
                    }
                    
                    // 检查是否为免费课程或已购买
                    const isFree = result.categoryIsFree || result.category.includes('免费');
                    const isPurchased = result.categoryIsPurchased || this.isPurchasedSeries(result.category);
                    const showProgress = isFree || isPurchased;
                    
                    // 使用组件模板生成搜索项
                    if (showProgress) {
                        // 已购买或免费课程
                        resultsHtml += ComponentTemplates.createPurchasedSearchItem({
                            title: result.title,
                            category: result.category,
                            progress: videoData.progress,
                            watchCount: videoData.watchCount,
                            cacheStatus: cacheStatus.replace(/<[^>]*>/g, ''), // 移除HTML标签，只保留文本
                            dataTitle: result.title,
                            dataCategory: result.category,
                            dataIndex: index,
                            onClick: `handleSearchVideoClick('${result.title}', '${result.category}', event)`
                        });
                    } else {
                        // 未购买课程
                        resultsHtml += ComponentTemplates.createUnpurchasedSearchItem({
                            title: result.title,
                            category: result.category,
                            dataTitle: result.title,
                            dataCategory: result.category,
                            dataIndex: index,
                            onClick: `handleSearchVideoClick('${result.title}', '${result.category}', event)`
                        });
                    }
                });

                this.suggestions.innerHTML = resultsHtml;
            },

            getMockSearchResults(keyword) {
                // 优先使用新的数据结构
                if (window.AppData && AppData.utils && AppData.utils.searchVideos) {
                    try {
                        const searchResults = AppData.utils.searchVideos(keyword);
                        console.log('搜索结果:', searchResults); // 调试信息
                        // 转换数据格式以匹配旧的接口
                        return searchResults.map(result => ({
                            title: result.title,
                            category: result.category,
                            categoryTitle: result.categoryTitle,
                            series: result.series,
                            categoryPrice: result.categoryPrice,
                            categoryIsFree: result.categoryIsFree,
                            categoryIsPurchased: result.categoryIsPurchased,
                            // 添加视频的其他属性
                            defaultProgress: result.defaultProgress || 0,
                            defaultWatchCount: result.defaultWatchCount || 0,
                            isCached: result.isCached || false
                        }));
                    } catch (error) {
                        console.error('搜索出错:', error);
                    }
                }

                // 回退到旧的搜索逻辑（保持不变作为备用）
                const allCourses = [
                    // 免费精品系列·约会技巧
                    { title: '01. 约会前的准备工作，学习约会前的各种准备工作，包括形象打理', category: '免费精品系列·约会技巧' },
                    { title: '02. 约会地点的选择', category: '免费精品系列·约会技巧' },

                    // 免费精品系列·搭讪技术
                    { title: '01. 搭讪基础心态', category: '免费精品系列·搭讪技术' },
                    { title: '02. 自然开场技巧', category: '免费精品系列·搭讪技术' },
                    { title: '03. 克服紧张情绪', category: '免费精品系列·搭讪技术' },

                    // 道：恋爱宝典系列·恋爱宝典1
                    { title: '01. 初识吸引力法则', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '02. 建立自信的方法', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '03. 第一印象的重要性', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '04. 肢体语言解读', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '05. 情感表达技巧', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '06. 深度沟通艺术', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '07. 约会策略制定', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '08. 魅力提升秘诀', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '09. 关系升级技巧', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '10. 长期关系维护', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '11. 冲突化解智慧', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '12. 恋爱大师之路', category: '道：恋爱宝典系列·恋爱宝典1' },

                    // 道：恋爱宝典系列·恋爱宝典2
                    { title: '01. 深度沟通技巧', category: '道：恋爱宝典系列·恋爱宝典2' },
                    { title: '02. 情感表达方式', category: '道：恋爱宝典系列·恋爱宝典2' },
                    { title: '03. 冲突处理艺术', category: '道：恋爱宝典系列·恋爱宝典2' },

                    // 道：恋爱宝典系列·恋爱宝典3
                    { title: '01. 长期关系维护', category: '道：恋爱宝典系列·恋爱宝典3' },
                    { title: '02. 信任建立方法', category: '道：恋爱宝典系列·恋爱宝典3' },
                    { title: '03. 未来规划讨论', category: '道：恋爱宝典系列·恋爱宝典3' },

                    // 术：聊天技术系列·聊天技术1
                    { title: '01. 开场白技巧', category: '术：聊天技术系列·聊天技术1' },
                    { title: '02. 话题延续方法', category: '术：聊天技术系列·聊天技术1' },
                    { title: '03. 幽默感培养', category: '术：聊天技术系列·聊天技术1' },

                    // 术：聊天技术系列·聊天技术2
                    { title: '01. 情绪调动技巧', category: '术：聊天技术系列·聊天技术2' },
                    { title: '02. 深度话题引导', category: '术：聊天技术系列·聊天技术2' },
                    { title: '03. 暧昧升级方法', category: '术：聊天技术系列·聊天技术2' },

                    // 术：聊天技术系列·聊天技术3
                    { title: '01. 微信聊天技巧', category: '术：聊天技术系列·聊天技术3' },
                    { title: '02. 语音通话技巧', category: '术：聊天技术系列·聊天技术3' },
                    { title: '03. 视频聊天技巧', category: '术：聊天技术系列·聊天技术3' }
                ];

                const lowerKeyword = keyword.toLowerCase();
                return allCourses.filter(course =>
                    course.title.toLowerCase().includes(lowerKeyword)
                );
            },

            addToHistory(keyword) {
                if (!keyword || this.searchHistory.includes(keyword)) return;

                this.searchHistory.unshift(keyword);
                if (this.searchHistory.length > 8) {
                    this.searchHistory = this.searchHistory.slice(0, 8);
                }

                // 保存到localStorage
                this.saveSearchHistory();
            },

            searchKeyword(keyword) {
                if (!keyword.trim()) return;

                this.currentKeyword = keyword;
                if (this.input) {
                    this.input.value = keyword;
                }

                this.addToHistory(keyword);
                this.showSearchResults(keyword);

                // 显示清除按钮
                if (this.clearBtn) {
                    this.clearBtn.style.display = 'block';
                }

                // 隐藏下拉框
                this.hideDropdown();
            },

            // 获取视频数据（进度、观看次数等）
            getVideoData(title, category) {
                // 模拟视频数据，实际项目中应该从API获取
                const videoDataMap = {
                    // 道：恋爱宝典系列·恋爱宝典1
                    '01. 初识吸引力法则': { progress: 0, watchCount: 0 },
                    '02. 建立自信的方法': { progress: 20, watchCount: 1 },
                    '03. 第一印象的重要性': { progress: 45, watchCount: 2 },
                    '04. 肢体语言解读': { progress: 60, watchCount: 3 },
                    '05. 情感表达技巧': { progress: 75, watchCount: 4 },
                    '06. 深度沟通艺术': { progress: 85, watchCount: 6 },
                    '07. 约会策略制定': { progress: 100, watchCount: 6 },
                    '08. 魅力提升秘诀': { progress: 100, watchCount: 7 },
                    '09. 关系升级技巧': { progress: 100, watchCount: 8 },
                    '10. 长期关系维护': { progress: 100, watchCount: 9 },
                    '11. 冲突化解智慧': { progress: 100, watchCount: 10 },
                    '12. 恋爱大师之路': { progress: 100, watchCount: 11 },
                    
                    // 其他系列的默认数据
                    '01. 约会前的准备工作，学习约会前的各种准备工作，包括形象打理': { progress: 30, watchCount: 2 },
                    '02. 约会地点的选择': { progress: 60, watchCount: 3 },
                    '01. 搭讪基础心态': { progress: 80, watchCount: 4 },
                    '02. 自然开场技巧': { progress: 40, watchCount: 2 },
                    '03. 克服紧张情绪': { progress: 90, watchCount: 5 },
                    '01. 深度沟通技巧': { progress: 70, watchCount: 3 },
                    '02. 情感表达方式': { progress: 50, watchCount: 2 },
                    '03. 冲突处理艺术': { progress: 85, watchCount: 4 },
                    '01. 开场白技巧': { progress: 95, watchCount: 6 },
                    '02. 话题延续方法': { progress: 65, watchCount: 3 },
                    '03. 幽默感培养': { progress: 75, watchCount: 4 }
                };

                return videoDataMap[title] || { progress: 0, watchCount: 0 };
            },

            // 获取徽章样式类
            getBadgeClass(watchCount) {
                if (watchCount >= 10) {
                    return 'badge-level-legendary';
                }
                return `badge-level-${watchCount}`;
            },

            // 获取徽章文字
            getBadgeText(watchCount) {
                return `×${watchCount}`;
            },

            // 检查系列是否已购买（视频播放页简化版本）
            isPurchasedSeries(category) {
                // 在视频播放页，模拟一些已购买的系列
                const purchasedSeries = [
                    '道：恋爱宝典系列·恋爱宝典1', // 当前播放的系列
                    '道：恋爱宝典系列·恋爱宝典2', // 假设也已购买
                ];
                return purchasedSeries.includes(category);
            },

            // 更新搜索结果的缓存状态显示
            updateCacheStatus() {
                if (!this.suggestions) return;

                const videoItems = this.suggestions.querySelectorAll('.search-suggestion-item');
                videoItems.forEach(item => {
                    const title = item.getAttribute('data-title');
                    const category = item.getAttribute('data-category');
                    
                    if (title && category) {
                        const titleElement = item.querySelector('.video-title-text');
                        if (titleElement) {
                            // 移除现有的缓存状态
                            const existingCacheStatus = titleElement.querySelector('.cache-status');
                            if (existingCacheStatus) {
                                existingCacheStatus.remove();
                            }

                            // 检查当前缓存状态并添加标识
                            const videoId = VideoCacheManager.getVideoId(title, category);
                            let statusText = '';
                            let statusClass = '';

                            if (VideoCacheManager.isDownloading(title, category)) {
                                const progress = VideoCacheManager.downloadingVideos.get(videoId);
                                statusText = `(${progress}%)`;
                                statusClass = 'downloading';
                            } else if (VideoCacheManager.isCached(title, category)) {
                                statusText = '(已缓存)';
                                statusClass = 'cached';
                            }

                            if (statusText) {
                                const cacheStatus = document.createElement('span');
                                cacheStatus.className = `cache-status ${statusClass}`;
                                cacheStatus.textContent = statusText;
                                titleElement.appendChild(cacheStatus);
                            }
                        }
                    }
                });
            }
        };

        // 性能监控管理器
        const PerformanceMonitor = {
            enabled: false, // 可以通过控制台启用：PerformanceMonitor.enabled = true
            timers: new Map(),

            start(label) {
                if (this.enabled) {
                    this.timers.set(label, performance.now());
                }
            },

            end(label) {
                if (this.enabled && this.timers.has(label)) {
                    const duration = performance.now() - this.timers.get(label);
                    console.log(`[性能] ${label}: ${duration.toFixed(2)}ms`);
                    this.timers.delete(label);
                    return duration;
                }
                return 0;
            },

            measure(label, fn) {
                if (this.enabled) {
                    this.start(label);
                    const result = fn();
                    this.end(label);
                    return result;
                } else {
                    return fn();
                }
            }
        };

        // 组件缓存管理器
        const ComponentCache = {
            cache: new Map(),
            maxCacheSize: 50, // 最大缓存数量
            hitCount: 0,
            missCount: 0,

            getCachedComponent(key, generator) {
                if (this.cache.has(key)) {
                    this.hitCount++;
                    PerformanceMonitor.enabled && console.log(`[缓存命中] ${key}`);
                    return this.cache.get(key);
                }

                this.missCount++;
                PerformanceMonitor.enabled && console.log(`[缓存未命中] ${key}`);

                // 如果缓存已满，清除最旧的项
                if (this.cache.size >= this.maxCacheSize) {
                    const firstKey = this.cache.keys().next().value;
                    this.cache.delete(firstKey);
                    console.log(`[缓存清理] 删除最旧项: ${firstKey}`);
                }

                const component = PerformanceMonitor.measure(`生成组件: ${key}`, generator);
                this.cache.set(key, component);
                return component;
            },

            clearCache() {
                this.cache.clear();
                this.hitCount = 0;
                this.missCount = 0;
                console.log('组件缓存已清空');
            },

            getCacheStats() {
                const hitRate = this.hitCount + this.missCount > 0 ?
                    (this.hitCount / (this.hitCount + this.missCount) * 100).toFixed(2) : 0;

                return {
                    size: this.cache.size,
                    maxSize: this.maxCacheSize,
                    hitCount: this.hitCount,
                    missCount: this.missCount,
                    hitRate: `${hitRate}%`,
                    keys: Array.from(this.cache.keys())
                };
            }
        };

        // 当前视频状态管理
        const CurrentVideoManager = {
            currentTitle: '06. 深度沟通艺术', // 默认当前视频
            currentCategory: '道：恋爱宝典系列·恋爱宝典1',
            
            setCurrentVideo(title, category) {
                this.currentTitle = title;
                this.currentCategory = category;
                console.log(`🎬 设置当前视频: ${title} - ${category}`);
            },
            
            isCurrentVideo(title, category) {
                return this.currentTitle === title && this.currentCategory === category;
            },
            
            getCurrentVideo() {
                return {
                    title: this.currentTitle,
                    category: this.currentCategory
                };
            }
        };

        // 播放列表管理器
        const PlaylistManager = {
            playlist: null,
            toggle: null,
            courseInfoBar: null,
            isExpanded: false,
            currentCategory: '道：恋爱宝典系列·恋爱宝典1', // 当前分类，默认值
            lastGeneratedCategory: null, // 上次生成的分类，用于避免重复生成
            
            // 检查是否为当前播放的视频
            isCurrentVideo(title, category) {
                return CurrentVideoManager.isCurrentVideo(title, category);
            },

            init() {
                this.playlist = document.getElementById('playlist');
                this.toggle = document.getElementById('playlistToggle');
                this.courseInfoBar = document.querySelector('.course-info-bar');

                // 生成播放列表
                this.generatePlaylist();

                // 创建绑定的处理器函数并保存引用
                this.boundClickHandler = (event) => {
                    this.handleGlobalClick(event);
                };
                this.boundTouchHandler = (event) => {
                    this.handleGlobalClick(event);
                };

                // 添加全局点击和触摸监听器
                document.addEventListener('click', this.boundClickHandler);

                // 移动设备触摸支持
                document.addEventListener('touchstart', this.boundTouchHandler);
            },

            removeGlobalClickListener() {
                // 移除全局点击和触摸监听器
                if (this.boundClickHandler) {
                    document.removeEventListener('click', this.boundClickHandler);
                    this.boundClickHandler = null;
                }
                if (this.boundTouchHandler) {
                    document.removeEventListener('touchstart', this.boundTouchHandler);
                    this.boundTouchHandler = null;
                }
            },

            // 生成播放列表
            generatePlaylist(category = null, forceRegenerate = false) {
                return PerformanceMonitor.measure('生成播放列表', () => {
                    try {
                        const container = document.getElementById('playlistContainer');
                        if (!container) {
                            console.error('播放列表容器未找到');
                            return;
                        }

                        // 获取当前分类的视频列表
                        const currentCategory = category || this.currentCategory;

                        // 避免重复生成相同分类的播放列表
                        if (!forceRegenerate && this.lastGeneratedCategory === currentCategory) {
                            console.log('播放列表已是最新，跳过重新生成');
                            return;
                        }

                        this.currentCategory = currentCategory; // 更新当前分类
                        this.lastGeneratedCategory = currentCategory; // 记录已生成的分类
                        let videoList = [];

                    // 检查AppData可用性
                    if (!window.AppData) {
                        console.error('AppData未加载，使用默认数据');
                        this.generateFallbackPlaylist(container, currentCategory);
                        return;
                    }

                    if (!AppData.categories) {
                        console.error('AppData.categories未定义，使用默认数据');
                        this.generateFallbackPlaylist(container, currentCategory);
                        return;
                    }

                    // 优先使用AppData获取视频列表
                    if (AppData.categories[currentCategory]) {
                        const categoryData = AppData.categories[currentCategory];
                    if (categoryData.videos) {
                        videoList = categoryData.videos.map(video => ({
                            title: video.title,
                            category: currentCategory,
                            progress: video.defaultProgress || 0,
                            watchCount: video.defaultWatchCount || 0,
                            isLastWatched: this.isCurrentVideo(video.title, currentCategory) // 当前播放的视频
                        }));
                    }
                }

                // 如果AppData中没有数据，使用默认数据
                if (videoList.length === 0) {
                    videoList = [
                        { title: '01. 初识吸引力法则', category: currentCategory, progress: 0, watchCount: 0, isLastWatched: false },
                        { title: '02. 建立自信的方法', category: currentCategory, progress: 20, watchCount: 1, isLastWatched: false },
                        { title: '03. 第一印象的重要性', category: currentCategory, progress: 45, watchCount: 2, isLastWatched: false },
                        { title: '04. 肢体语言解读', category: currentCategory, progress: 60, watchCount: 3, isLastWatched: false },
                        { title: '05. 情感表达技巧', category: currentCategory, progress: 75, watchCount: 4, isLastWatched: false },
                        { title: '06. 深度沟通艺术', category: currentCategory, progress: 85, watchCount: 6, isLastWatched: true },
                        { title: '07. 约会策略制定', category: currentCategory, progress: 100, watchCount: 6, isLastWatched: false },
                        { title: '08. 魅力提升秘诀', category: currentCategory, progress: 100, watchCount: 7, isLastWatched: false },
                        { title: '09. 关系升级技巧', category: currentCategory, progress: 100, watchCount: 8, isLastWatched: false },
                        { title: '10. 长期关系维护', category: currentCategory, progress: 100, watchCount: 9, isLastWatched: false },
                        { title: '11. 冲突化解智慧', category: currentCategory, progress: 100, watchCount: 10, isLastWatched: false },
                        { title: '12. 恋爱大师之路', category: currentCategory, progress: 100, watchCount: 11, isLastWatched: false }
                    ];
                }

                // 生成HTML
                let playlistHtml = '';
                videoList.forEach(video => {
                    // 检查缓存状态
                    let cacheStatus = '';
                    if (VideoCacheManager && VideoCacheManager.isDownloading && VideoCacheManager.isDownloading(video.title, video.category)) {
                        const videoId = VideoCacheManager.getVideoId(video.title, video.category);
                        const progress = VideoCacheManager.downloadingVideos.get(videoId);
                        cacheStatus = `(${progress}%)`;
                    } else if (VideoCacheManager && VideoCacheManager.isCached && VideoCacheManager.isCached(video.title, video.category)) {
                        cacheStatus = '(已缓存)';
                    }

                    // 使用缓存机制生成组件，避免重复计算
                    const cacheKey = `video_${video.title}_${video.progress}_${video.watchCount}_${cacheStatus}_${video.isLastWatched}`;

                    const videoComponent = ComponentCache.getCachedComponent(cacheKey, () => {
                        // 使用ComponentTemplates生成视频项
                        if (window.ComponentTemplates && ComponentTemplates.createPurchasedVideoItem) {
                            return ComponentTemplates.createPurchasedVideoItem({
                                title: video.title,
                                progress: video.progress,
                                watchCount: video.watchCount,
                                cacheStatus: cacheStatus,
                                isLastWatched: video.isLastWatched,
                                onClick: `handleVideoClick('${video.title}', event)`
                            });
                        } else {
                            // 回退到基本HTML结构
                            const lastWatchedClass = video.isLastWatched ? ' last-watched' : '';
                            return `
                                <div class="video-item-component${lastWatchedClass}" onclick="handleVideoClick('${video.title}', event)">
                                    <i class="fas fa-play video-play-icon"></i>
                                    <div class="video-content-wrapper">
                                        <div class="video-title-component">
                                            <span class="video-title-text">${video.title}${cacheStatus}</span>
                                        </div>
                                        <div class="video-progress-row-component">
                                            <div class="progress-component progress-thin progress-flex">
                                                <div class="progress-fill-component ${video.progress > 0 ? 'animated' : ''}" style="width: ${video.progress}%;"></div>
                                            </div>
                                            <div class="progress-info-component">
                                                <span class="progress-percentage-component">${video.progress}%</span>
                                                <span class="badge-component badge-level-${video.watchCount > 10 ? 'legendary' : video.watchCount}">×${video.watchCount}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }
                    });

                    playlistHtml += videoComponent;
                });

                container.innerHTML = playlistHtml;

                // 更新课程信息栏
                this.updateCourseInfoBar(currentCategory, videoList.length);

                        console.log('播放列表已生成，包含', videoList.length, '个视频，分类：', currentCategory);

                    } catch (error) {
                        console.error('生成播放列表失败:', error);
                        // 使用默认数据作为备用方案
                        this.generateFallbackPlaylist(document.getElementById('playlistContainer'), this.currentCategory);
                    }
                });
            },

            // 生成备用播放列表（当AppData不可用时）
            generateFallbackPlaylist(container, category) {
                if (!container) return;

                console.log('使用备用播放列表数据');

                // 默认的播放列表数据
                const fallbackData = [
                    { title: '01. 初识吸引力法则', progress: 0, watchCount: 0, isCached: true },
                    { title: '02. 建立自信的方法', progress: 20, watchCount: 1, isCached: true },
                    { title: '03. 第一印象的重要性', progress: 40, watchCount: 2, isCached: false }
                ];

                let playlistHtml = '';
                fallbackData.forEach(video => {
                    const cacheStatus = video.isCached ? '(已缓存)' : '';
                    playlistHtml += ComponentTemplates.createPurchasedVideoItem({
                        title: video.title,
                        category: category || '默认分类',
                        progress: video.progress,
                        watchCount: video.watchCount,
                        cacheStatus: cacheStatus,
                        onClick: `handleVideoClick('${video.title}', event)`
                    });
                });

                container.innerHTML = playlistHtml;
                this.updateCourseInfoBar(category || '默认分类', fallbackData.length);
            },

            // 更新播放列表（当缓存状态变化时调用）
            updatePlaylist(category = null, forceRegenerate = true) {
                // 缓存状态变化时需要强制重新生成
                this.generatePlaylist(category, forceRegenerate);
            },

            // 更新课程信息栏
            updateCourseInfoBar(category, videoCount) {
                const courseInfoElement = document.querySelector('.course-info-bar p.font-medium');
                if (courseInfoElement) {
                    // 从分类名称中提取系列名称
                    let seriesName = category;
                    if (category.includes('·')) {
                        const parts = category.split('·');
                        seriesName = parts[parts.length - 1]; // 取最后一部分作为系列名
                    }
                    courseInfoElement.textContent = `${seriesName}·共${videoCount}集`;
                }
            },

            togglePlaylist() {
                if (this.playlist.classList.contains('expanded')) {
                    this.collapsePlaylist();
                } else {
                    this.expandPlaylist();
                }
            },

            expandPlaylist() {
                if (this.playlist && this.toggle) {
                    this.playlist.classList.add('expanded');
                    this.toggle.className = 'fas fa-chevron-down text-gray-400';
                    this.isExpanded = true;
                }
            },

            collapsePlaylist() {
                if (this.playlist && this.toggle) {
                    this.playlist.classList.remove('expanded');
                    this.toggle.className = 'fas fa-chevron-up text-gray-400';
                    this.isExpanded = false;
                }
            },

            handleGlobalClick(event) {
                // 如果播放列表没有展开，不需要处理
                if (!this.isExpanded) return;

                // 检查点击的元素是否在播放列表区域内
                const playlistSection = document.querySelector('.playlist-section');
                const courseInfoBar = this.courseInfoBar;

                // 如果点击的是课程信息栏或播放列表内容，不收起
                if (courseInfoBar && courseInfoBar.contains(event.target)) {
                    return;
                }

                if (playlistSection && playlistSection.contains(event.target)) {
                    return;
                }

                // 点击在播放列表区域外，收起播放列表
                this.collapsePlaylist();
            }
        };

        function togglePlaylist() {
            PlaylistManager.togglePlaylist();
        }

        // 分享弹窗管理器
        const ShareManager = {
            modal: null,
            isVisible: false,

            init() {
                try {
                    this.modal = document.getElementById('shareModal');

                    if (!this.modal) {
                        console.warn('Share modal not found, share functionality will be limited');
                        return;
                    }

                    // 添加全局点击监听器
                    this.addGlobalClickListener();

                    console.log('ShareManager initialized successfully');
                } catch (error) {
                    console.error('Error initializing ShareManager:', error);
                }
            },

            addGlobalClickListener() {
                // 创建绑定的处理器函数并保存引用
                this.boundClickHandler = (event) => {
                    this.handleGlobalClick(event);
                };
                this.boundTouchHandler = (event) => {
                    this.handleGlobalClick(event);
                };

                // 添加全局点击和触摸监听器
                document.addEventListener('click', this.boundClickHandler);

                // 移动设备触摸支持
                document.addEventListener('touchstart', this.boundTouchHandler);
            },

            removeGlobalClickListener() {
                // 移除全局点击和触摸监听器
                if (this.boundClickHandler) {
                    document.removeEventListener('click', this.boundClickHandler);
                    this.boundClickHandler = null;
                }
                if (this.boundTouchHandler) {
                    document.removeEventListener('touchstart', this.boundTouchHandler);
                    this.boundTouchHandler = null;
                }
            },

            handleGlobalClick(event) {
                // 如果分享弹窗没有显示，不需要处理
                if (!this.isVisible) return;

                // 检查点击的元素是否在分享弹窗内
                if (this.modal && this.modal.contains(event.target)) {
                    return; // 点击在弹窗内，不关闭
                }

                // 检查是否点击了分享按钮（避免点击分享按钮时立即关闭）
                const shareButton = event.target.closest('.share-button-component');
                if (shareButton) {
                    return; // 点击的是分享按钮，不关闭
                }

                // 点击在弹窗外，关闭分享弹窗
                this.hide();
            },

            show() {
                if (this.modal) {
                    this.modal.classList.add('show');
                    this.isVisible = true;
                }
            },

            hide() {
                if (this.modal) {
                    this.modal.classList.remove('show');
                    this.isVisible = false;
                }
            },

            toggle() {
                if (this.isVisible) {
                    this.hide();
                } else {
                    this.show();
                }
            }
        };

        function toggleShareModal() {
            try {
                console.log('📤 toggleShareModal 被调用');
                console.log('📤 当前全屏状态:', FullscreenManager.isFullscreen);

                const modal = document.getElementById('shareModal');
                console.log('📤 分享弹窗元素:', modal);

                if (modal) {
                    // 使用ShareManager来管理显示状态
                    ShareManager.toggle();

                    // 无论是否全屏都设置高z-index
                    modal.style.setProperty('z-index', '9999999', 'important');
                    modal.style.setProperty('position', 'fixed', 'important');

                    if (FullscreenManager.isFullscreen) {
                        console.log('📤 全屏模式下设置分享弹窗高z-index');
                    }

                    console.log('📤 分享弹窗类名:', modal.classList.toString());

                    // 检查弹窗是否正确显示
                    setTimeout(() => {
                        const computedStyle = getComputedStyle(modal);
                        console.log('📤 分享弹窗样式检查:', {
                            display: computedStyle.display,
                            zIndex: computedStyle.zIndex,
                            position: computedStyle.position,
                            visibility: computedStyle.visibility,
                            opacity: computedStyle.opacity,
                            classes: modal.classList.toString()
                        });
                    }, 100);
                } else {
                    console.error('📤 找不到分享弹窗元素！');
                }
            } catch (error) {
                console.error('Error toggling share modal:', error);
            }
        }

        // 全屏播放管理器
        const FullscreenManager = {
            isFullscreen: false,
            controlsVisible: false,
            controlsTimer: null,
            playlistVisible: false,
            globalClickHandler: null,
            
            // 检查是否为当前播放的视频
            isCurrentVideo(title, category) {
                return CurrentVideoManager.isCurrentVideo(title, category);
            },
            
            // 初始化
            init() {
                // 监听全屏状态变化
                document.addEventListener('fullscreenchange', () => this.handleFullscreenChange());
                document.addEventListener('webkitfullscreenchange', () => this.handleFullscreenChange());
                document.addEventListener('mozfullscreenchange', () => this.handleFullscreenChange());
                document.addEventListener('MSFullscreenChange', () => this.handleFullscreenChange());
                
                // 监听键盘事件
                document.addEventListener('keydown', (e) => this.handleKeydown(e));
                

                
                console.log('FullscreenManager initialized');
            },
            
            // 进入全屏
            enterFullscreen() {
                console.log('🎬 enterFullscreen 开始执行');
                console.log('🎬 当前状态:', {
                    isFullscreen: this.isFullscreen,
                    controlsVisible: this.controlsVisible,
                    playlistVisible: this.playlistVisible
                });

                const screen = document.querySelector('.screen');
                console.log('🎬 screen元素:', screen);
                if (!screen) {
                    console.error('🎬 找不到.screen元素！');
                    return;
                }
                
                try {
                    if (screen.requestFullscreen) {
                        screen.requestFullscreen();
                    } else if (screen.webkitRequestFullscreen) {
                        screen.webkitRequestFullscreen();
                    } else if (screen.mozRequestFullScreen) {
                        screen.mozRequestFullScreen();
                    } else if (screen.msRequestFullscreen) {
                        screen.msRequestFullscreen();
                    }
                } catch (error) {
                    console.error('Error entering fullscreen:', error);
                    // 如果浏览器全屏失败，使用CSS模拟全屏
                    this.simulateFullscreen();
                }
            },
            
            // 退出全屏
            exitFullscreen() {
                try {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                    }
                } catch (error) {
                    console.error('Error exiting fullscreen:', error);
                    // 如果浏览器退出全屏失败，手动退出模拟全屏
                    this.exitSimulateFullscreen();
                }
            },
            
            // 模拟全屏（用于不支持浏览器全屏的情况）
            simulateFullscreen() {
                const screen = document.querySelector('.screen');
                const videoContainer = document.querySelector('.video-container');
                
                if (screen && videoContainer) {
                    screen.classList.add('fullscreen-mode');
                    videoContainer.style.height = '100vh';
                    videoContainer.style.width = '100vw';
                    this.applyFullscreenStyles();
                }
            },
            
            // 退出模拟全屏
            exitSimulateFullscreen() {
                const screen = document.querySelector('.screen');
                const videoContainer = document.querySelector('.video-container');
                
                if (screen && videoContainer) {
                    screen.classList.remove('fullscreen-mode');
                    videoContainer.style.height = '280px';
                    videoContainer.style.width = '100%';
                    this.removeFullscreenStyles();
                }
            },
            
            // 处理全屏状态变化 - 统一状态管理
            handleFullscreenChange() {
                console.log('🎬 handleFullscreenChange 被调用');

                const isCurrentlyFullscreen = this.checkFullscreenStatus();

                console.log('🎬 全屏状态检查:', {
                    isCurrentlyFullscreen,
                    managerIsFullscreen: this.isFullscreen
                });
                
                if (isCurrentlyFullscreen && !this.isFullscreen) {
                    // 进入全屏
                    this.applyFullscreenStyles();
                } else if (!isCurrentlyFullscreen && this.isFullscreen) {
                    // 退出全屏 - 添加强制清理机制
                    console.log('🎬 检测到全屏退出，开始清理...');
                    this.removeFullscreenStyles();
                    
                    // 强制清理可能导致页面重叠的元素
                    setTimeout(() => {
                        this.forceCleanupAfterFullscreen();
                    }, 100);
                }
            },
            
            // 强制清理全屏退出后的残留问题
            forceCleanupAfterFullscreen() {
                console.log('🎬 执行强制清理...');
                
                // 确保所有全屏相关的元素都被正确隐藏
                const elementsToHide = [
                    '#fullscreenControls',
                    '#fullscreenTouchArea',
                    '.fullscreen-playlist',
                    '.fullscreen-nav-bar',
                    '.fullscreen-video-info',
                    '.fullscreen-playlist-btn'
                ];
                
                elementsToHide.forEach(selector => {
                    const element = document.querySelector(selector);
                    if (element) {
                        element.style.display = 'none';
                        element.style.opacity = '0';
                        element.style.pointerEvents = 'none';
                        element.style.zIndex = '';
                        console.log(`🎬 强制隐藏元素: ${selector}`);
                    }
                });
                
                // 确保页面元素可以正常点击
                const body = document.body;
                const screen = document.querySelector('.screen');
                
                if (body) {
                    body.style.pointerEvents = '';
                    body.classList.remove('fullscreen-mode');
                }
                
                if (screen) {
                    screen.style.pointerEvents = '';
                    screen.classList.remove('fullscreen-mode');
                }
                
                // 重置视频容器
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    videoContainer.style.position = '';
                    videoContainer.style.top = '';
                    videoContainer.style.left = '';
                    videoContainer.style.width = '';
                    videoContainer.style.height = '';
                    videoContainer.style.zIndex = '';
                    videoContainer.style.transform = '';
                    videoContainer.style.pointerEvents = '';
                }
                
                console.log('🎬 强制清理完成');
            },

            // 统一的全屏状态检测方法
            checkFullscreenStatus() {
                return !!(document.fullscreenElement ||
                    document.webkitFullscreenElement ||
                    document.mozFullScreenElement ||
                    document.msFullscreenElement);
            },


            
            // 应用全屏样式
            applyFullscreenStyles() {
                this.isFullscreen = true;

                // 添加全屏模式类到body或screen元素
                const screen = document.querySelector('.screen');
                const body = document.body;
                if (screen) {
                    screen.classList.add('fullscreen-mode');
                }
                if (body) {
                    body.classList.add('fullscreen-mode');
                }

                // 隐藏非全屏元素
                const elementsToHide = [
                    '.status-bar',
                    '.nav-bar',
                    '.video-info-section',
                    '.content',
                    '.play-button',  // 全屏时隐藏播放按钮
                    '.video-controls'  // 隐藏非全屏控制栏，避免重复
                ];

                elementsToHide.forEach(selector => {
                    const element = document.querySelector(selector);
                    if (element) {
                        element.classList.add('fullscreen-hidden');
                    }
                });

                // 特别处理视频控制栏，确保隐藏
                const videoControls = document.querySelector('.video-controls');
                if (videoControls) {
                    videoControls.style.display = 'none';
                }

                // 调整视频容器 - 确保占满整个屏幕
                const videoContainer = document.querySelector('.video-container');
                const videoPlayer = document.querySelector('.video-player');

                if (videoContainer) {
                    // 移除原有的transform和定位
                    videoContainer.style.position = 'fixed';
                    videoContainer.style.top = '0';
                    videoContainer.style.left = '0';
                    videoContainer.style.width = '100vw';
                    videoContainer.style.height = '100vh';
                    videoContainer.style.zIndex = '9998';
                    videoContainer.style.transform = 'none'; // 移除translateY
                    videoContainer.style.cursor = 'pointer';
                }

                // 确保视频播放器也占满容器
                if (videoPlayer) {
                    videoPlayer.style.width = '100%';
                    videoPlayer.style.height = '100%';
                    videoPlayer.style.borderRadius = '0'; // 全屏时移除圆角

                    // 隐藏原生视频控制器，避免重复
                    videoPlayer.controls = false;
                    videoPlayer.setAttribute('controls', false);
                    videoPlayer.style.cursor = 'pointer';
                }


                
                // 显示全屏控制界面（初始显示，然后自动隐藏）
                setTimeout(() => {
                    this.showFullscreenControls();

                    // 强制确保控制界面可见
                    const controls = document.getElementById('fullscreenControls');
                    if (controls) {
                        // 使用多种方法强制设置opacity
                        controls.style.setProperty('opacity', '1', 'important');
                        controls.style.setProperty('pointer-events', 'auto', 'important');
                        controls.style.setProperty('display', 'block', 'important');
                        controls.classList.add('visible');

                        // 如果还是不行，直接修改cssText
                        if (getComputedStyle(controls).opacity === '0') {
                            console.log('🎬 opacity还是0，使用cssText强制设置');
                            // 完全重写style属性，移除transition
                            controls.setAttribute('style', 'opacity: 1 !important; pointer-events: auto !important; display: block !important; position: fixed !important; top: 0 !important; left: 0 !important; right: 0 !important; bottom: 0 !important; z-index: 999999 !important; background: transparent !important; transition: none !important;');

                            // 再次检查
                            setTimeout(() => {
                                const finalOpacity = getComputedStyle(controls).opacity;
                                console.log('🎬 移除transition后的opacity:', finalOpacity);
                                if (finalOpacity === '0') {
                                    console.log('🎬 仍然无效，可能需要创建新元素');
                                }
                            }, 50);
                        }

                        console.log('🎬 强制设置控制界面可见，类名:', controls.classList.toString());
                        console.log('🎬 控制界面样式:', {
                            opacity: getComputedStyle(controls).opacity,
                            display: getComputedStyle(controls).display,
                            pointerEvents: getComputedStyle(controls).pointerEvents
                        });

                        // 检查body和screen是否有fullscreen-mode类
                        const body = document.body;
                        const screen = document.querySelector('.screen');
                        console.log('🎬 fullscreen-mode类检查:', {
                            bodyHasClass: body.classList.contains('fullscreen-mode'),
                            screenHasClass: screen ? screen.classList.contains('fullscreen-mode') : 'screen不存在'
                        });

                        // 强制修复所有元素位置
                        const videoInfo = document.getElementById('fullscreenVideoInfo');
                        const videoControls = document.querySelector('.fullscreen-video-controls');
                        const navBar = document.querySelector('.fullscreen-nav-bar');
                        const playlistBtn = document.querySelector('.fullscreen-playlist-btn');

                        // 修复导航栏位置（顶部）
                        if (navBar) {
                            navBar.style.setProperty('position', 'fixed', 'important');
                            navBar.style.setProperty('top', '0', 'important');
                            navBar.style.setProperty('bottom', 'auto', 'important');
                            navBar.style.setProperty('left', '0', 'important');
                            navBar.style.setProperty('right', '0', 'important');
                            console.log('🎬 强制修复导航栏位置（顶部）');

                            // 添加鼠标悬停事件，防止控制界面闪烁
                            navBar.addEventListener('mouseenter', () => {
                                console.log('🎬 鼠标进入导航栏，暂停自动隐藏');
                                if (FullscreenManager.controlsTimer) {
                                    clearTimeout(FullscreenManager.controlsTimer);
                                    FullscreenManager.controlsTimer = null;
                                }
                                // 确保控制界面显示
                                if (!FullscreenManager.controlsVisible) {
                                    FullscreenManager.showFullscreenControls();
                                }
                            });

                            navBar.addEventListener('mouseleave', () => {
                                console.log('🎬 鼠标离开导航栏，重新启动自动隐藏');
                                // 延迟重新启动自动隐藏，给用户时间操作
                                setTimeout(() => {
                                    if (FullscreenManager.isFullscreen && FullscreenManager.controlsVisible && !FullscreenManager.playlistVisible) {
                                        FullscreenManager.resetControlsTimer();
                                    }
                                }, 1000); // 1秒后重新启动
                            });

                            // 检查返回按钮是否存在（现在使用和搜索按钮一样的class）
                            const backButton = navBar.querySelector('button[onclick="goBackToHome()"]');
                            if (backButton) {
                                console.log('🏠 返回按钮已找到，使用和搜索按钮完全一样的结构');
                                console.log('🏠 返回按钮class:', backButton.className);
                                console.log('🏠 返回按钮onclick:', backButton.getAttribute('onclick'));

                                // 添加点击检测
                                backButton.addEventListener('click', (e) => {
                                    console.log('🏠 返回按钮点击事件触发！', e);
                                    console.log('🏠 事件目标:', e.target);
                                    console.log('🏠 事件当前目标:', e.currentTarget);
                                });

                                // 添加鼠标事件检测
                                backButton.addEventListener('mousedown', (e) => {
                                    console.log('🏠 返回按钮鼠标按下！', e);
                                });

                                backButton.addEventListener('mouseup', (e) => {
                                    console.log('🏠 返回按钮鼠标抬起！', e);
                                });
                            } else {
                                console.error('🏠 找不到返回按钮！');
                            }
                        }

                        // 修复视频控制栏位置（底部）
                        if (videoControls) {
                            videoControls.style.setProperty('position', 'fixed', 'important');
                            videoControls.style.setProperty('bottom', '0', 'important');
                            videoControls.style.setProperty('top', 'auto', 'important');
                            videoControls.style.setProperty('left', '0', 'important');
                            videoControls.style.setProperty('right', '0', 'important');
                            console.log('🎬 强制修复视频控制栏位置（底部）');
                        }

                        // 修复视频信息位置（底部中间）
                        if (videoInfo) {
                            videoInfo.style.setProperty('position', 'fixed', 'important');
                            videoInfo.style.setProperty('bottom', '100px', 'important');
                            videoInfo.style.setProperty('top', 'auto', 'important');
                            videoInfo.style.setProperty('left', '80px', 'important');
                            videoInfo.style.setProperty('right', '80px', 'important');
                            videoInfo.style.setProperty('margin', '0 auto', 'important');
                            videoInfo.style.setProperty('text-align', 'center', 'important');
                            console.log('🎬 强制修复视频信息位置（底部中间）');
                        }

                        // 修复播放列表按钮位置（和时长在同一水平行，往下移7px）
                        if (playlistBtn) {
                            playlistBtn.style.setProperty('position', 'fixed', 'important');
                            playlistBtn.style.setProperty('bottom', '13px', 'important');
                            playlistBtn.style.setProperty('right', '80px', 'important');
                            playlistBtn.style.setProperty('top', 'auto', 'important');
                            playlistBtn.style.setProperty('left', 'auto', 'important');
                            playlistBtn.style.setProperty('z-index', '50001', 'important');
                            playlistBtn.style.setProperty('display', 'flex', 'important');
                            console.log('🎬 强制修复播放列表按钮位置（和时长在同一水平行，往下移7px）');

                            // 检查修复后的样式
                            setTimeout(() => {
                                const computedStyle = getComputedStyle(playlistBtn);
                                console.log('🎬 播放列表按钮样式检查:', {
                                    position: computedStyle.position,
                                    bottom: computedStyle.bottom,
                                    right: computedStyle.right,
                                    left: computedStyle.left,
                                    top: computedStyle.top,
                                    zIndex: computedStyle.zIndex,
                                    display: computedStyle.display,
                                    visibility: computedStyle.visibility
                                });
                            }, 100);
                        } else {
                            console.error('🎬 找不到播放列表按钮元素！');
                        }
                    }
                }, 100); // 延迟一点确保DOM更新完成
                
                // 生成全屏播放列表
                this.generateFullscreenPlaylist();

                // 更新全屏视频信息
                this.updateFullscreenVideoInfo();

                // 显示状态指示器
                this.showIndicator();
                
                // 确保播放列表按钮可见
                const playlistBtn = document.querySelector('.fullscreen-playlist-btn');
                if (playlistBtn) {
                    playlistBtn.style.display = 'flex';
                }

                // 在播放按钮上也添加点击事件（以防没有被隐藏）
                const playButton = document.querySelector('.play-button');
                if (playButton) {
                    playButton.addEventListener('click', function(e) {
                        if (FullscreenManager.isFullscreen) {
                            e.stopPropagation();
                            console.log('播放按钮点击事件触发（全屏模式）');
                            toggleFullscreenControls();
                        }
                    });
                }

                // 强制激活触摸区域
                const touchArea = document.getElementById('fullscreenTouchArea');
                if (touchArea) {
                    touchArea.style.pointerEvents = 'auto';
                    touchArea.style.opacity = '1';
                    touchArea.style.zIndex = '10000'; // 设置合适的层级，不阻挡按钮
                    // 确保触摸区域不覆盖导航栏
                    touchArea.style.setProperty('top', '80px', 'important');
                    touchArea.style.setProperty('height', 'calc(100vh - 80px)', 'important');
                    console.log('强制激活触摸区域，设置合适层级，不覆盖导航栏');

                    // 添加点击事件监听器作为备用
                    touchArea.addEventListener('click', function(e) {
                        console.log('触摸区域直接点击事件触发，目标:', e.target);

                        // 阻止事件冒泡，防止触发全局点击检测
                        e.stopPropagation();

                        // 检查点击的是否是交互元素
                        const isInteractiveElement = e.target.closest('button') ||
                                                   e.target.closest('select') ||
                                                   e.target.closest('input') ||
                                                   e.target.closest('.fullscreen-video-item') ||
                                                   e.target.closest('.fullscreen-playlist-panel') ||
                                                   e.target.closest('.fullscreen-nav-bar') ||
                                                   e.target.closest('.fullscreen-video-controls') ||
                                                   e.target.closest('.fullscreen-video-info');

                        // 如果点击的是交互元素，不处理
                        if (isInteractiveElement) {
                            console.log('触摸区域：点击了交互元素，不切换控制界面');
                            return;
                        }

                        // 如果点击的是空白区域，切换控制界面
                        console.log('触摸区域：触发控制界面切换');
                        toggleFullscreenControls();
                    });
                }

                // 创建全局点击处理器并保存引用
                this.globalClickHandler = function(e) {
                    if (FullscreenManager.isFullscreen) {
                        console.log('全局点击检测：检测到点击:', e.target);

                        // 特别检测左上角区域的点击
                        const rect = e.target.getBoundingClientRect();
                        if (rect.left < 200 && rect.top < 100) {
                            console.log('🔍 左上角区域点击检测:', {
                                element: e.target,
                                tagName: e.target.tagName,
                                className: e.target.className,
                                id: e.target.id,
                                position: { left: rect.left, top: rect.top, width: rect.width, height: rect.height },
                                zIndex: getComputedStyle(e.target).zIndex,
                                pointerEvents: getComputedStyle(e.target).pointerEvents,
                                parent: e.target.parentElement ? e.target.parentElement.tagName + '.' + e.target.parentElement.className : 'none'
                            });

                            // 检查点击位置的所有元素
                            const elementsAtPoint = document.elementsFromPoint(e.clientX, e.clientY);
                            console.log('🔍 点击位置的所有元素:', elementsAtPoint.map(el => ({
                                tag: el.tagName,
                                class: el.className,
                                id: el.id,
                                zIndex: getComputedStyle(el).zIndex
                            })));
                        }

                        // 检查是否点击了可交互的元素
                        const isInteractiveElement = e.target.closest('button') ||
                                                   e.target.closest('select') ||
                                                   e.target.closest('input') ||
                                                   e.target.closest('.fullscreen-video-item') ||
                                                   e.target.closest('.fullscreen-playlist-panel');

                        // 特殊处理：如果点击的是控制界面容器本身（不是子元素），允许切换
                        const isControlsContainer = e.target.classList.contains('fullscreen-controls');

                        console.log('全局点击检测：点击分析:', {
                            target: e.target,
                            isInteractiveElement,
                            isControlsContainer,
                            targetClasses: e.target.classList.toString()
                        });

                        // 如果点击了交互元素且不是控制界面容器，不触发控制界面切换
                        if (isInteractiveElement && !isControlsContainer) {
                            console.log('全局点击检测：点击了交互元素，不切换控制界面:', e.target);
                            return;
                        }

                        // 如果点击的是空白区域或控制界面容器，触发控制界面切换
                        console.log('全局点击检测：触发控制界面切换');
                        toggleFullscreenControls();
                    }
                };

                // 添加全局点击检测（作为备用机制）
                document.addEventListener('click', this.globalClickHandler);

                // 重新添加其他组件的全局点击监听器
                if (window.SearchManager && SearchManager.addGlobalClickListener) {
                    SearchManager.addGlobalClickListener();
                    console.log('🎬 已重新添加SearchManager全局点击监听器');
                }
                if (window.PlaylistManager && PlaylistManager.addGlobalClickListener) {
                    PlaylistManager.addGlobalClickListener();
                    console.log('🎬 已重新添加PlaylistManager全局点击监听器');
                }
                if (window.ShareManager && ShareManager.addGlobalClickListener) {
                    ShareManager.addGlobalClickListener();
                    console.log('🎬 已重新添加ShareManager全局点击监听器');
                }

                // 确保调试按钮在全屏时也可见
                const debugBtn = document.getElementById('debugBtn');
                const testElement = document.getElementById('testElement');
                if (debugBtn) {
                    debugBtn.style.setProperty('z-index', '999999', 'important');
                    debugBtn.style.setProperty('position', 'fixed', 'important');
                    console.log('🔧 确保调试按钮可见');
                }
                if (testElement) {
                    testElement.style.setProperty('z-index', '999999', 'important');
                    testElement.style.setProperty('position', 'fixed', 'important');
                    console.log('🔧 确保测试元素可见');
                }

                console.log('Entered fullscreen mode');
            },
            
            // 移除全屏样式
            removeFullscreenStyles() {
                this.isFullscreen = false;
                this.controlsVisible = false;
                this.playlistVisible = false;

                // 清除定时器，防止内存泄漏
                this.clearControlsTimer();

                // 移除全局点击监听器，防止退出全屏后页面无法点击
                if (this.globalClickHandler) {
                    document.removeEventListener('click', this.globalClickHandler);
                    this.globalClickHandler = null;
                    console.log('🎬 已移除FullscreenManager全局点击监听器');
                }

                // 移除播放列表外部点击监听器
                if (this.boundPlaylistOutsideClick) {
                    document.removeEventListener('click', this.boundPlaylistOutsideClick);
                    this.boundPlaylistOutsideClick = null;
                    console.log('🎬 已移除播放列表外部点击监听器');
                }

                // 强制清理所有可能残留的事件监听器
                console.log('🎬 开始强制清理所有事件监听器...');
                
                // 重新创建一个新的document元素来清理所有监听器（这是一个激进的方法）
                // 但这可能会影响其他功能，所以我们先尝试手动清理
                
                // 清理可能的残留监听器
                const allClickHandlers = [
                    this.globalClickHandler,
                    this.boundPlaylistOutsideClick
                ];
                
                allClickHandlers.forEach((handler, index) => {
                    if (handler) {
                        try {
                            document.removeEventListener('click', handler);
                            console.log(`🎬 清理了第${index + 1}个点击监听器`);
                        } catch (e) {
                            console.log(`🎬 清理第${index + 1}个监听器时出错:`, e);
                        }
                    }
                });

                // 移除其他组件的全局点击监听器
                if (window.SearchManager && SearchManager.removeGlobalClickListener) {
                    SearchManager.removeGlobalClickListener();
                    console.log('🎬 已移除SearchManager全局点击监听器');
                }
                if (window.PlaylistManager && PlaylistManager.removeGlobalClickListener) {
                    PlaylistManager.removeGlobalClickListener();
                    console.log('🎬 已移除PlaylistManager全局点击监听器');
                }
                if (window.ShareManager && ShareManager.removeGlobalClickListener) {
                    ShareManager.removeGlobalClickListener();
                    console.log('🎬 已移除ShareManager全局点击监听器');
                }

                // 移除全屏模式类
                const screen = document.querySelector('.screen');
                const body = document.body;
                if (screen) {
                    screen.classList.remove('fullscreen-mode');
                }
                if (body) {
                    body.classList.remove('fullscreen-mode');
                }

                // 显示非全屏元素
                const elementsToShow = document.querySelectorAll('.fullscreen-hidden');
                elementsToShow.forEach(element => {
                    element.classList.remove('fullscreen-hidden');
                });

                // 特别处理视频控制栏，确保显示
                const videoControls = document.querySelector('.video-controls');
                if (videoControls) {
                    videoControls.style.display = 'block';
                }

                // 恢复视频容器样式
                const videoContainer = document.querySelector('.video-container');
                const videoPlayer = document.querySelector('.video-player');
                
                if (videoContainer) {
                    videoContainer.style.position = 'absolute';
                    videoContainer.style.top = 'calc(50% - 20px)';
                    videoContainer.style.left = '0';
                    videoContainer.style.width = '100%';
                    videoContainer.style.height = '280px';
                    videoContainer.style.zIndex = '1';
                    videoContainer.style.transform = 'translateY(-50%)';
                }
                
                // 恢复视频播放器样式
                if (videoPlayer) {
                    videoPlayer.style.width = '100%';
                    videoPlayer.style.height = '100%';
                    videoPlayer.style.borderRadius = '8px'; // 恢复圆角

                    // 恢复原生视频控制器
                    videoPlayer.controls = true;
                }
                
                // 隐藏全屏控制界面
                this.hideFullscreenControls();

                // 隐藏全屏控制界面
                const controls = document.getElementById('fullscreenControls');
                if (controls) {
                    controls.classList.remove('visible');
                    console.log('🎬 隐藏全屏控制界面');
                }
                
                // 隐藏播放列表
                this.hideFullscreenPlaylist();
                
                // 隐藏播放列表按钮
                const playlistBtn = document.querySelector('.fullscreen-playlist-btn');
                if (playlistBtn) {
                    playlistBtn.style.display = 'none';
                }

                // 强制重置所有可能阻挡点击的元素
                console.log('🎬 开始重置可能阻挡点击的元素...');
                
                // 重置全屏控制界面的z-index和pointer-events
                const fullscreenControls = document.getElementById('fullscreenControls');
                if (fullscreenControls) {
                    fullscreenControls.style.zIndex = '';
                    fullscreenControls.style.pointerEvents = '';
                    fullscreenControls.style.position = '';
                    console.log('🎬 重置了全屏控制界面样式');
                }
                
                // 重置视频容器的pointer-events
                if (videoContainer) {
                    videoContainer.style.pointerEvents = '';
                    videoContainer.style.cursor = '';
                    console.log('🎬 重置了视频容器样式');
                }
                
                // 重置视频播放器的pointer-events
                if (videoPlayer) {
                    videoPlayer.style.pointerEvents = '';
                    videoPlayer.style.cursor = '';
                    console.log('🎬 重置了视频播放器样式');
                }
                
                // 重置body的pointer-events
                if (body) {
                    body.style.pointerEvents = '';
                    console.log('🎬 重置了body样式');
                }
                
                // 重置screen的pointer-events
                if (screen) {
                    screen.style.pointerEvents = '';
                    console.log('🎬 重置了screen样式');
                }
                
                // 隐藏全屏触摸区域（这是导致页面无法点击的主要原因）
                const fullscreenTouchArea = document.getElementById('fullscreenTouchArea');
                if (fullscreenTouchArea) {
                    fullscreenTouchArea.style.display = 'none';
                    fullscreenTouchArea.style.pointerEvents = 'none';
                    fullscreenTouchArea.style.zIndex = '';
                    console.log('🎬 隐藏了全屏触摸区域 - 这是关键修复！');
                }

                console.log('Exited fullscreen mode');
            },
            
            // 显示全屏控制界面 - CSS控制子元素显示
            showFullscreenControls() {
                console.log('🎬 showFullscreenControls 被调用');
                const controls = document.getElementById('fullscreenControls');
                if (controls) {
                    controls.classList.add('visible');
                    this.controlsVisible = true;
                    console.log('🎬 控制界面已显示（包括导航栏、视频信息、播放列表按钮）');

                    // 设置自动隐藏定时器
                    this.resetControlsTimer();
                } else {
                    console.error('🎬 找不到控制界面元素！');
                }
            },

            // 隐藏全屏控制界面 - CSS控制子元素隐藏
            hideFullscreenControls() {
                console.log('🎬 hideFullscreenControls 被调用');
                const controls = document.getElementById('fullscreenControls');
                if (controls) {
                    controls.classList.remove('visible');
                    this.controlsVisible = false;
                    console.log('🎬 控制界面已隐藏（包括导航栏、视频信息、播放列表按钮）');
                } else {
                    console.error('🎬 找不到控制界面元素！');
                }
            },
            
            // 切换控制界面显示状态
            toggleControls() {
                console.log('🎬 toggleControls 被调用');
                console.log('🎬 当前状态:', {
                    isFullscreen: this.isFullscreen,
                    controlsVisible: this.controlsVisible
                });

                if (!this.isFullscreen) {
                    console.log('🎬 不在全屏模式，不执行切换');
                    return;
                }

                if (this.controlsVisible) {
                    console.log('🎬 控制界面当前可见，准备隐藏');
                    this.hideFullscreenControls();
                } else {
                    console.log('🎬 控制界面当前隐藏，准备显示');
                    this.showFullscreenControls();
                }
            },
            
            // 重置控制界面自动隐藏定时器 - 优化内存管理
            resetControlsTimer() {
                console.log('🎬 resetControlsTimer 被调用');
                this.clearControlsTimer();

                this.controlsTimer = setTimeout(() => {
                    console.log('🎬 自动隐藏定时器触发，当前状态:', {
                        isFullscreen: this.isFullscreen,
                        controlsVisible: this.controlsVisible,
                        playlistVisible: this.playlistVisible
                    });
                    if (this.isFullscreen && this.controlsVisible && !this.playlistVisible) {
                        console.log('🎬 执行自动隐藏');
                        this.hideFullscreenControls();
                    } else {
                        console.log('🎬 不满足自动隐藏条件，跳过');
                    }
                }, 5000); // 恢复到5秒

                console.log('🎬 设置新的自动隐藏定时器（5秒）');
            },

            // 清除控制界面定时器
            clearControlsTimer() {
                if (this.controlsTimer) {
                    clearTimeout(this.controlsTimer);
                    this.controlsTimer = null;
                    console.log('🎬 清除控制界面定时器');
                }
            },
            
            // 显示状态指示器
            showIndicator() {
                const indicator = document.getElementById('fullscreenIndicator');
                if (indicator) {
                    indicator.classList.add('visible');
                    
                    // 2秒后自动隐藏
                    setTimeout(() => {
                        indicator.classList.remove('visible');
                    }, 2000);
                }
            },
            
            // 生成全屏播放列表
            generateFullscreenPlaylist() {
                const content = document.getElementById('fullscreenPlaylistContent');
                const title = document.getElementById('fullscreenPlaylistTitle');
                
                if (!content || !title) return;
                
                // 获取当前分类信息
                const currentCategory = PlaylistManager ? PlaylistManager.currentCategory : '道：恋爱宝典系列·恋爱宝典1';
                
                // 更新标题
                let seriesName = currentCategory;
                if (currentCategory.includes('·')) {
                    const parts = currentCategory.split('·');
                    seriesName = parts[parts.length - 1];
                }
                
                // 获取视频列表
                let videoList = [];
                if (window.AppData && AppData.categories && AppData.categories[currentCategory]) {
                    const categoryData = AppData.categories[currentCategory];
                    if (categoryData.videos) {
                        videoList = categoryData.videos.map(video => ({
                            title: video.title,
                            progress: video.defaultProgress || 0,
                            watchCount: video.defaultWatchCount || 0,
                            isCurrent: FullscreenManager.isCurrentVideo(video.title, currentCategory)
                        }));
                    }
                }
                
                // 如果没有数据，使用默认数据
                if (videoList.length === 0) {
                    videoList = [
                        { title: '01. 初识吸引力法则', progress: 0, watchCount: 0, isCurrent: false },
                        { title: '02. 建立自信的方法', progress: 20, watchCount: 1, isCurrent: false },
                        { title: '03. 第一印象的重要性', progress: 45, watchCount: 2, isCurrent: false },
                        { title: '04. 肢体语言解读', progress: 60, watchCount: 3, isCurrent: false },
                        { title: '05. 情感表达技巧', progress: 75, watchCount: 4, isCurrent: false },
                        { title: '06. 深度沟通艺术', progress: 85, watchCount: 6, isCurrent: true },
                        { title: '07. 约会策略制定', progress: 100, watchCount: 6, isCurrent: false },
                        { title: '08. 魅力提升秘诀', progress: 100, watchCount: 7, isCurrent: false },
                        { title: '09. 关系升级技巧', progress: 100, watchCount: 8, isCurrent: false },
                        { title: '10. 长期关系维护', progress: 100, watchCount: 9, isCurrent: false },
                        { title: '11. 冲突化解智慧', progress: 100, watchCount: 10, isCurrent: false },
                        { title: '12. 恋爱大师之路', progress: 100, watchCount: 11, isCurrent: false }
                    ];
                }
                
                title.textContent = `${seriesName}·共${videoList.length}集`;
                
                // 生成播放列表HTML - 使用标准的视频项组件
                let playlistHtml = '';
                videoList.forEach((video, index) => {
                    // 检查缓存状态 - 完全模仿非全屏模式的逻辑
                    let cacheStatus = '';
                    if (VideoCacheManager && VideoCacheManager.isDownloading && VideoCacheManager.isDownloading(video.title, currentCategory)) {
                        const videoId = VideoCacheManager.getVideoId(video.title, currentCategory);
                        const progress = VideoCacheManager.downloadingVideos.get(videoId);
                        cacheStatus = `(${progress}%)`;
                    } else if (VideoCacheManager && VideoCacheManager.isCached && VideoCacheManager.isCached(video.title, currentCategory)) {
                        cacheStatus = '(已缓存)';
                    }

                    // 使用标准的视频项组件模板
                    const videoItemHtml = ComponentTemplates.createPurchasedVideoItem({
                        title: video.title,
                        category: currentCategory,
                        progress: video.progress,
                        watchCount: video.watchCount,
                        cacheStatus: cacheStatus,
                        isLastWatched: video.isCurrent,
                        onClick: `handleFullscreenVideoClick('${video.title}', event)`,
                        customClass: video.isCurrent ? 'current' : ''
                    });

                    playlistHtml += videoItemHtml;
                });
                
                content.innerHTML = playlistHtml;
            },
            
            // 显示全屏播放列表
            showFullscreenPlaylist() {
                const panel = document.getElementById('fullscreenPlaylistPanel');
                if (panel) {
                    panel.classList.add('visible');
                    this.playlistVisible = true;

                    // 播放列表显示时，保持控制界面显示
                    if (this.controlsTimer) {
                        clearTimeout(this.controlsTimer);
                    }

                    // 强制稳定播放列表面板样式，防止闪烁
                    panel.style.setProperty('overflow-y', 'auto', 'important');
                    panel.style.setProperty('overflow-x', 'hidden', 'important');
                    panel.style.setProperty('z-index', '50002', 'important');

                    // 创建绑定的事件处理函数（如果还没有的话）
                    if (!this.boundPlaylistOutsideClick) {
                        this.boundPlaylistOutsideClick = this.handlePlaylistOutsideClick.bind(this);
                    }

                    // 添加点击其他区域隐藏播放列表的监听器
                    setTimeout(() => {
                        document.addEventListener('click', this.boundPlaylistOutsideClick);
                    }, 100); // 延迟添加，避免立即触发

                    console.log('🎬 播放列表已显示，防闪烁处理完成');
                }
            },
            
            // 隐藏全屏播放列表
            hideFullscreenPlaylist() {
                const panel = document.getElementById('fullscreenPlaylistPanel');
                if (panel) {
                    panel.classList.remove('visible');
                    this.playlistVisible = false;
                    
                    // 移除点击外部区域的监听器
                    if (this.boundPlaylistOutsideClick) {
                        document.removeEventListener('click', this.boundPlaylistOutsideClick);
                    }
                    
                    // 重新启动控制界面自动隐藏定时器
                    this.resetControlsTimer();
                }
            },
            
            // 切换播放列表显示状态
            togglePlaylist() {
                if (!this.isFullscreen) return;
                
                if (this.playlistVisible) {
                    this.hideFullscreenPlaylist();
                } else {
                    this.showFullscreenPlaylist();
                }
            },

            // 处理点击播放列表外部区域
            handlePlaylistOutsideClick(event) {
                if (!this.playlistVisible) return;

                const panel = document.getElementById('fullscreenPlaylistPanel');
                const playlistBtn = document.querySelector('.fullscreen-playlist-btn');
                
                // 检查点击的是否是播放列表面板内部或播放列表按钮
                const isInsidePanel = panel && panel.contains(event.target);
                const isPlaylistBtn = playlistBtn && (playlistBtn.contains(event.target) || playlistBtn === event.target);
                
                // 如果点击的不是播放列表面板内部也不是播放列表按钮，则隐藏播放列表
                if (!isInsidePanel && !isPlaylistBtn) {
                    console.log('🎬 点击播放列表外部区域，隐藏播放列表');
                    this.hideFullscreenPlaylist();
                }
            },
            
            // 更新全屏视频信息
            updateFullscreenVideoInfo() {
                try {
                    // 获取当前视频信息
                    const titleElement = document.querySelector('.video-info-section h2');
                    const categoryElement = document.querySelector('.video-info-section p.text-sm.text-gray-300');
                    const progressFill = document.querySelector('.video-info-section .progress-fill-component');
                    const progressPercentage = document.querySelector('.video-info-section .progress-percentage-component');
                    const badge = document.querySelector('.video-info-section .badge-component');
                    const playCountElement = document.querySelector('.video-info-section .text-xs.text-gray-400 span');
                    const collectIcon = document.getElementById('collectIcon');
                    const collectText = document.getElementById('collectText');

                    // 更新全屏视频信息区域
                    const fullscreenTitle = document.getElementById('fullscreenVideoTitle');
                    const fullscreenCategory = document.getElementById('fullscreenVideoCategory');
                    const fullscreenProgressFill = document.getElementById('fullscreenProgressFill');
                    const fullscreenProgressText = document.getElementById('fullscreenProgressText');
                    const fullscreenBadge = document.getElementById('fullscreenBadge');
                    const fullscreenPlayCount = document.getElementById('fullscreenPlayCount');
                    const fullscreenCollectIcon = document.getElementById('fullscreenCollectIcon');
                    const fullscreenCollectText = document.getElementById('fullscreenCollectText');

                    if (titleElement && fullscreenTitle) {
                        fullscreenTitle.textContent = titleElement.textContent;
                    }
                    if (categoryElement && fullscreenCategory) {
                        fullscreenCategory.textContent = categoryElement.textContent;
                    }
                    if (progressFill && fullscreenProgressFill) {
                        fullscreenProgressFill.style.width = progressFill.style.width;
                    }
                    if (progressPercentage && fullscreenProgressText) {
                        fullscreenProgressText.textContent = progressPercentage.textContent;
                    }
                    if (badge && fullscreenBadge) {
                        fullscreenBadge.className = badge.className;
                        fullscreenBadge.textContent = badge.textContent;
                    }
                    if (playCountElement && fullscreenPlayCount) {
                        const playCountText = playCountElement.textContent;
                        const match = playCountText.match(/(\d+\.?\d*[万千]?)播放/);
                        if (match) {
                            fullscreenPlayCount.textContent = match[1];
                        }
                    }
                    if (collectIcon && fullscreenCollectIcon) {
                        fullscreenCollectIcon.className = collectIcon.className;
                    }
                    if (collectText && fullscreenCollectText) {
                        fullscreenCollectText.textContent = collectText.textContent;
                    }
                } catch (error) {
                    console.error('Error updating fullscreen video info:', error);
                }
            },

            // 处理键盘事件
            handleKeydown(event) {
                if (!this.isFullscreen) return;

                switch (event.key) {
                    case 'Escape':
                        this.exitFullscreen();
                        break;
                    case ' ':
                        event.preventDefault();
                        this.toggleControls();
                        break;
                    case 'l':
                    case 'L':
                        this.togglePlaylist();
                        break;
                }
            }
        };

        // 调试函数：手动显示控制界面
        function debugShowControls() {
            console.log('🧪 手动显示控制界面');
            if (FullscreenManager.isFullscreen) {
                FullscreenManager.showFullscreenControls();
            } else {
                console.log('🧪 当前不在全屏模式');
            }
        }

        // 全屏相关的全局函数
        function toggleFullscreen() {
            console.log('🎬 toggleFullscreen 被调用');
            try {
                if (FullscreenManager.isFullscreen) {
                    console.log('🎬 当前是全屏，准备退出');
                    FullscreenManager.exitFullscreen();
                } else {
                    console.log('🎬 当前非全屏，准备进入');
                    FullscreenManager.enterFullscreen();
                }
            } catch (error) {
                console.error('🎬 toggleFullscreen 错误:', error);
                alert('全屏切换错误: ' + error.message);
            }
        }

        function exitFullscreen() {
            try {
                console.log('🎬 exitFullscreen 被调用');
                console.log('🎬 当前全屏状态:', FullscreenManager.isFullscreen);
                FullscreenManager.exitFullscreen();
            } catch (error) {
                console.error('🎬 exitFullscreen 错误:', error);
                alert('退出全屏错误: ' + error.message);
            }
        }

        // 返回首页函数 - 直接使用浏览器原生API退出全屏
        function goBackToHome() {
            try {
                console.log('🏠 goBackToHome 被调用');
                console.log('🏠 当前全屏状态:', FullscreenManager.isFullscreen);
                console.log('🏠 浏览器全屏状态:', !!document.fullscreenElement);

                // 如果在全屏模式，先退出全屏，然后延迟跳转
                if (FullscreenManager.isFullscreen || document.fullscreenElement) {
                    console.log('🏠 当前在全屏模式，先退出全屏');

                    // 直接使用浏览器原生API退出全屏
                    console.log('🏠 使用浏览器原生API退出全屏');
                    if (document.exitFullscreen) {
                        document.exitFullscreen().then(() => {
                            console.log('🏠 浏览器退出全屏成功');
                        }).catch(error => {
                            console.error('🏠 浏览器退出全屏失败:', error);
                        });
                    } else if (document.webkitExitFullscreen) {
                        document.webkitExitFullscreen();
                        console.log('🏠 使用webkit退出全屏');
                    } else if (document.mozCancelFullScreen) {
                        document.mozCancelFullScreen();
                        console.log('🏠 使用moz退出全屏');
                    } else if (document.msExitFullscreen) {
                        document.msExitFullscreen();
                        console.log('🏠 使用ms退出全屏');
                    }

                    // 同时调用FullscreenManager的退出方法，确保状态同步
                    FullscreenManager.removeFullscreenStyles();

                    // 等待退出全屏动画完成后再跳转
                    setTimeout(() => {
                        console.log('🏠 全屏退出完成，准备返回首页');
                        // 使用导航管理器返回首页
                        if (window.goHome) {
                            goHome();
                        } else {
                            window.location.href = '01-首页.html';
                        }
                    }, 1000); // 延长到1秒，确保能看到退出全屏效果
                } else {
                    // 非全屏模式直接返回
                    console.log('🏠 非全屏模式，直接返回首页');
                    // 使用导航管理器返回首页
                    if (window.goHome) {
                        goHome();
                    } else {
                        window.location.href = '01-首页.html';
                    }
                }
            } catch (error) {
                console.error('🏠 goBackToHome 错误:', error);
            }
        }

        function toggleFullscreenControls() {
            if (FullscreenManager.isFullscreen) {
                FullscreenManager.toggleControls();
            }
        }


        
        // 调试功能：显示触摸区域
        function debugTouchArea(show = true) {
            const touchArea = document.getElementById('fullscreenTouchArea');
            if (touchArea) {
                if (show) {
                    touchArea.style.background = 'rgba(255, 0, 0, 0.1)';
                    touchArea.style.border = '2px dashed red';
                    console.log('🎬 触摸区域边界已显示');
                } else {
                    touchArea.style.background = 'transparent';
                    touchArea.style.border = 'none';
                    console.log('🎬 触摸区域边界已隐藏');
                }
            }
        }
        

        
        // 调试功能：检查控制界面状态
        function debugControlsStatus() {
            const controls = document.getElementById('fullscreenControls');
            const touchArea = document.getElementById('fullscreenTouchArea');
            const playlistBtn = document.querySelector('.fullscreen-playlist-btn');
            const navBar = document.querySelector('.fullscreen-nav-bar');
            const videoControls = document.querySelector('.fullscreen-video-controls');
            
            console.log('=== 全屏控制界面状态 ===');
            console.log('控制界面元素:', controls);
            console.log('控制界面类名:', controls ? controls.className : 'element not found');
            console.log('FullscreenManager状态:', {
                isFullscreen: FullscreenManager.isFullscreen,
                controlsVisible: FullscreenManager.controlsVisible
            });
            
            console.log('=== 层级关系 ===');
            console.log('触摸区域 z-index:', touchArea ? getComputedStyle(touchArea).zIndex : 'not found');
            console.log('播放列表按钮 z-index:', playlistBtn ? getComputedStyle(playlistBtn).zIndex : 'not found');
            console.log('导航栏 z-index:', navBar ? getComputedStyle(navBar).zIndex : 'not found');
            console.log('视频控制栏 z-index:', videoControls ? getComputedStyle(videoControls).zIndex : 'not found');
            
            console.log('=== 触摸区域状态 ===');
            console.log('opacity:', touchArea ? getComputedStyle(touchArea).opacity : 'not found');
            console.log('pointerEvents:', touchArea ? getComputedStyle(touchArea).pointerEvents : 'not found');
        }

        function toggleFullscreenPlaylist() {
            FullscreenManager.togglePlaylist();
        }

        function closeFullscreenPlaylist() {
            FullscreenManager.hideFullscreenPlaylist();
        }

        function handleFullscreenVideoClick(title, event) {
            try {
                if (event) {
                    event.stopPropagation();
                }

                const category = PlaylistManager ? PlaylistManager.currentCategory : '道：恋爱宝典系列·恋爱宝典1';

                // 检查是否为免费课程或已购买 - 使用AppData进行准确检查
                const isFree = category.includes('免费');
                let isPurchased = false;

                // 优先使用AppData检查购买状态
                if (window.AppData && AppData.categories) {
                    const categoryData = AppData.categories[category];
                    isPurchased = categoryData ? categoryData.isPurchased : false;
                } else {
                    // 回退到SearchManager检查
                    isPurchased = SearchManager && SearchManager.isPurchasedSeries ? SearchManager.isPurchasedSeries(category) : true; // 默认已购买
                }

                if (isFree || isPurchased) {
                    // 免费或已购买：先关闭可能存在的支付弹窗，然后检查缓存状态
                    closePayment();

                    if (VideoCacheManager.isCached(title, category)) {
                        // 已缓存，直接播放
                        updateVideoInfo(title, category);

                        // 重新生成播放列表以更新当前播放项
                        FullscreenManager.generateFullscreenPlaylist();

                        // 更新全屏视频信息
                        FullscreenManager.updateFullscreenVideoInfo();

                        // 隐藏播放列表
                        FullscreenManager.hideFullscreenPlaylist();

                        // 显示控制界面，让用户看到视频切换效果
                        FullscreenManager.showFullscreenControls();

                        console.log(`全屏播放视频: ${title} - ${category}`);
                    } else {
                        // 未缓存，显示缓存弹窗，保持在全屏模式
                        VideoCacheManager.showCacheModal(title, category, false); // 传入false表示来自播放列表
                    }
                } else {
                    // 未购买：清理可能存在的缓存，然后显示支付弹窗
                    if (VideoCacheManager.isCached(title, category)) {
                        VideoCacheManager.removeFromCache(title, category);
                        console.log('已清理未购买视频的缓存');
                    }

                    // 获取正确的价格信息
                    let categoryPrice = '¥100'; // 默认价格
                    if (window.AppData && AppData.categories) {
                        const categoryData = AppData.categories[category];
                        if (categoryData && categoryData.price) {
                            categoryPrice = categoryData.price;
                        }
                    }

                    // 显示支付弹窗，保持在全屏模式
                    console.log(`显示支付弹窗: ${category}, 价格: ${categoryPrice}`);
                    showPayment(event, category, categoryPrice);
                }
            } catch (error) {
                console.error('Error handling fullscreen video click:', error);
            }
        }

        // 搜索相关函数 - 完全复制首页的搜索函数
        function showSearchModal() {
            try {
                console.log('🔍 showSearchModal 被调用');
                console.log('🔍 当前全屏状态:', FullscreenManager.isFullscreen);

                SearchManager.show();

                // 在全屏模式下，确保搜索弹窗有足够高的z-index
                const searchModal = document.getElementById('searchModal');
                console.log('🔍 搜索弹窗元素:', searchModal);

                if (searchModal) {
                    // 无论是否全屏都设置高z-index
                    searchModal.style.setProperty('z-index', '9999999', 'important');
                    searchModal.style.setProperty('position', 'fixed', 'important');

                    if (FullscreenManager.isFullscreen) {
                        console.log('🔍 全屏模式下设置搜索弹窗高z-index');
                    }

                    // 检查弹窗是否正确显示
                    setTimeout(() => {
                        const computedStyle = getComputedStyle(searchModal);
                        console.log('🔍 搜索弹窗样式检查:', {
                            display: computedStyle.display,
                            zIndex: computedStyle.zIndex,
                            position: computedStyle.position,
                            visibility: computedStyle.visibility,
                            opacity: computedStyle.opacity,
                            classes: searchModal.classList.toString()
                        });
                    }, 100);
                } else {
                    console.error('🔍 找不到搜索弹窗元素！');
                }
            } catch (error) {
                console.error('Error showing search modal:', error);
            }
        }

        function closeSearchModal() {
            try {
                SearchManager.hide();
            } catch (error) {
                console.error('Error closing search modal:', error);
            }
        }

        function handleSearchInput() {
            try {
                const keyword = SearchManager.input ? SearchManager.input.value.trim() : '';

                // 显示/隐藏清除按钮
                if (SearchManager.clearBtn) {
                    SearchManager.clearBtn.style.display = keyword ? 'block' : 'none';
                }

                if (keyword) {
                    SearchManager.showSearchResults(keyword);
                } else {
                    SearchManager.showDefaultState();
                }
            } catch (error) {
                console.error('Error handling search input:', error);
            }
        }

        function handleSearchKeypress(event) {
            try {
                if (event.key === 'Enter') {
                    const keyword = SearchManager.input ? SearchManager.input.value.trim() : '';
                    if (keyword) {
                        SearchManager.searchKeyword(keyword);
                        SearchManager.hideDropdown(); // 隐藏下拉框
                    }
                }
            } catch (error) {
                console.error('Error handling search keypress:', error);
            }
        }

        function clearSearch() {
            try {
                SearchManager.clearInput();
            } catch (error) {
                console.error('Error clearing search:', error);
            }
        }

        function searchKeyword(keyword) {
            try {
                SearchManager.searchKeyword(keyword);
            } catch (error) {
                console.error('Error searching keyword:', error);
            }
        }

        function selectSearchResult(title, category) {
            try {
                // 模拟跳转延迟
                setTimeout(() => {
                    closeSearchModal();
                    // 跳转到对应页面（移除提示，直接跳转）
                    console.log(`跳转到: ${title} - ${category}`);
                }, 500);
            } catch (error) {
                console.error('Error selecting search result:', error);
            }
        }

        // 搜索结果视频项点击处理函数
        function handleSearchVideoClick(title, category, event) {
            try {
                // 阻止事件冒泡，防止触发全局点击事件
                if (event) {
                    event.stopPropagation();
                }

                // 检查是否为免费课程或已购买 - 使用AppData进行准确检查
                const isFree = category.includes('免费');
                let isPurchased = false;

                // 使用统一的查找方法检查购买状态
                const videoId = VideoCacheManager.getVideoId(title, category);
                const videoResult = VideoCacheManager.findVideoByIdOrTitle(videoId, title, category);
                
                if (videoResult && videoResult.video) {
                    // 检查分类的购买状态
                    const categoryData = window.AppData?.categories?.[videoResult.category];
                    isPurchased = categoryData ? categoryData.isPurchased : false;
                } else {
                    // 回退到SearchManager检查
                    isPurchased = SearchManager.isPurchasedSeries(category);
                }

                if (isFree || isPurchased) {
                    // 免费或已购买：先关闭可能存在的支付弹窗，然后检查缓存状态
                    closePayment();

                    if (VideoCacheManager.isCached(title, category)) {
                        // 已缓存，关闭搜索弹窗、折叠播放列表并播放
                        closeSearchModal();
                        PlaylistManager.collapsePlaylist();

                        // 更新播放页信息
                        updateVideoInfo(title, category);

                        // 如果当前在全屏模式，保持全屏并更新全屏信息
                        if (FullscreenManager && FullscreenManager.isFullscreen) {
                            console.log(`全屏模式下播放视频: ${title} - ${category}`);
                            // 更新全屏视频信息
                            FullscreenManager.updateFullscreenVideoInfo();
                            // 重新生成全屏播放列表（如果需要切换分类）
                            FullscreenManager.generateFullscreenPlaylist();
                            // 确保控制界面显示
                            FullscreenManager.showFullscreenControls();
                        } else {
                            console.log(`播放视频: ${title} - ${category}`);
                        }
                    } else {
                        // 未缓存，显示缓存弹窗，保持搜索弹窗打开
                        VideoCacheManager.showCacheModal(title, category, true); // 传入true表示来自搜索
                    }
                } else {
                    // 未购买：清理可能存在的缓存，然后显示支付弹窗
                    if (VideoCacheManager.isCached(title, category)) {
                        VideoCacheManager.removeFromCache(title, category);
                        console.log('已清理未购买视频的缓存');
                    }
                    
                    // 获取正确的价格信息
                    let categoryPrice = '¥100'; // 默认价格
                    if (window.AppData && AppData.categories) {
                        const categoryData = AppData.categories[category];
                        if (categoryData && categoryData.price) {
                            categoryPrice = categoryData.price;
                        }
                    }
                    
                    // 显示支付弹窗，保持搜索弹窗打开
                    console.log(`显示支付弹窗: ${category}, 价格: ${categoryPrice}`);
                    showPayment(event, category, categoryPrice);
                }
            } catch (error) {
                console.error('Error handling search video click:', error);
                alert('操作失败，请重试');
            }
        }

        // 下拉框相关函数
        function showSearchDropdown() {
            try {
                SearchManager.showDropdown();
            } catch (error) {
                console.error('Error showing search dropdown:', error);
            }
        }

        function hideSearchDropdown() {
            try {
                SearchManager.hideDropdown();
            } catch (error) {
                console.error('Error hiding search dropdown:', error);
            }
        }

        function handleSearchBlur() {
            try {
                // 延迟隐藏下拉框，给删除操作留出时间
                setTimeout(() => {
                    // 检查是否有删除操作正在进行
                    if (!SearchManager.isDeleting) {
                        SearchManager.hideDropdown();
                    }
                }, 150);
            } catch (error) {
                console.error('Error handling search blur:', error);
            }
        }

        function selectDropdownItem(keyword) {
            try {
                SearchManager.searchKeyword(keyword);
                SearchManager.hideDropdown();
            } catch (error) {
                console.error('Error selecting dropdown item:', error);
            }
        }

        function deleteHistoryItem(index, event) {
            try {
                event.stopPropagation(); // 防止触发父元素的点击事件
                event.preventDefault(); // 防止默认行为
                
                // 阻止输入框失焦
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.focus();
                }
                
                SearchManager.deleteHistoryItem(index);
            } catch (error) {
                console.error('Error deleting history item:', error);
            }
        }

        function clearSearchHistory(event) {
            try {
                if (event) {
                    event.stopPropagation();
                    event.preventDefault();
                    
                    // 阻止输入框失焦
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
                
                SearchManager.clearAllHistory();
                // 搜索历史已清空（移除提示，通过界面更新反馈）
                console.log('搜索历史已清空');
            } catch (error) {
                console.error('Error clearing search history:', error);
            }
        }

        // 徽章动画初始化函数
        function initBadgeAnimations() {
            const badges = document.querySelectorAll('.badge-component');
            badges.forEach(badge => {
                // 为每个徽章添加随机延迟，让动画更自然
                const delay = Math.random() * 2; // 0-2秒随机延迟
                badge.style.animationDelay = `${delay}s`;
            });
        }

        // 视频收藏管理
        const VideoCollectionManager = {
            collectedVideos: new Set(), // 已收藏的视频
            currentVideo: null, // 当前播放的视频

            init() {
                // 从localStorage加载已收藏的视频列表
                const collected = localStorage.getItem('collectedVideos');
                if (collected) {
                    this.collectedVideos = new Set(JSON.parse(collected));
                }
                
                // 设置当前视频
                this.currentVideo = {
                    title: '06. 深度沟通艺术',
                    category: '道：恋爱宝典系列·恋爱宝典1'
                };
                
                this.updateCollectionStatus();
            },

            // 生成视频唯一ID


            // 检查视频是否已收藏
            isCollected(title, category) {
                const videoId = VideoCacheManager.getVideoId(title, category);
                return this.collectedVideos.has(videoId);
            },

            // 切换收藏状态
            toggleCollection() {
                if (!this.currentVideo) return;

                const { title, category } = this.currentVideo;
                const videoId = VideoCacheManager.getVideoId(title, category);

                if (this.collectedVideos.has(videoId)) {
                    // 取消收藏
                    this.collectedVideos.delete(videoId);
                    this.showMessage('已取消收藏');
                } else {
                    // 添加收藏
                    this.collectedVideos.add(videoId);
                    this.showMessage('已添加到收藏');
                }

                this.saveCollectedVideos();
                this.updateCollectionStatus();
            },

            // 更新收藏按钮状态
            updateCollectionStatus() {
                if (!this.currentVideo) return;

                const { title, category } = this.currentVideo;
                const isCollected = this.isCollected(title, category);

                // 更新非全屏模式的收藏按钮
                const btn = document.querySelector('.cache-collect-btn');
                const icon = document.getElementById('collectIcon');
                const text = document.getElementById('collectText');

                if (btn && icon && text) {
                    if (isCollected) {
                        btn.classList.add('collected');
                        icon.className = 'fas fa-heart';
                        text.textContent = '已收藏';
                    } else {
                        btn.classList.remove('collected');
                        icon.className = 'far fa-heart';
                        text.textContent = '收藏';
                    }
                }

                // 更新全屏模式的收藏按钮
                const fullscreenBtn = document.querySelector('.fullscreen-video-info .cache-collect-btn');
                const fullscreenIcon = document.getElementById('fullscreenCollectIcon');
                const fullscreenText = document.getElementById('fullscreenCollectText');

                if (fullscreenBtn && fullscreenIcon && fullscreenText) {
                    if (isCollected) {
                        fullscreenBtn.classList.add('collected');
                        fullscreenIcon.className = 'fas fa-heart';
                        fullscreenText.textContent = '已收藏';
                    } else {
                        fullscreenBtn.classList.remove('collected');
                        fullscreenIcon.className = 'far fa-heart';
                        fullscreenText.textContent = '收藏';
                    }
                }
            },

            // 更新当前视频
            setCurrentVideo(title, category) {
                this.currentVideo = { title, category };
                this.updateCollectionStatus();
            },

            // 显示提示消息
            showMessage(message) {
                // 创建临时提示元素
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    font-size: 14px;
                    z-index: 9999;
                    pointer-events: none;
                `;
                toast.textContent = message;
                document.body.appendChild(toast);

                // 2秒后移除
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 2000);
            },

            // 保存已收藏视频列表到localStorage
            saveCollectedVideos() {
                localStorage.setItem('collectedVideos', JSON.stringify([...this.collectedVideos]));
            }
        };

        // 视频缓存管理（从首页完整复制）
        const VideoCacheManager = {
            cachedVideos: new Set(), // 已缓存的视频
            downloadingVideos: new Map(), // 正在下载的视频 {videoId: progress}
            currentVideoInfo: null, // 当前要缓存的视频信息

            init() {
                // 从localStorage加载已缓存的视频列表（用于用户动态缓存的视频）
                const cached = localStorage.getItem('cachedVideos');
                if (cached) {
                    this.cachedVideos = new Set(JSON.parse(cached));
                } else {
                    this.cachedVideos = new Set();
                }

                // 从首页的缓存状态同步（读取videoCacheState）
                this.syncCacheFromHomePage();

                // 从AppData中同步已缓存的视频到localStorage（确保一致性）
                this.syncCacheFromAppData();
                
                this.updateAllVideoStatus();
            },

            // 从首页的缓存状态同步
            syncCacheFromHomePage() {
                try {
                    const homePageCacheState = localStorage.getItem('videoCacheState');
                    if (homePageCacheState) {
                        const cacheState = JSON.parse(homePageCacheState);
                        let syncCount = 0;
                        
                        // 将首页的缓存状态同步到AppData
                        Object.keys(cacheState).forEach(videoId => {
                            if (cacheState[videoId]) {
                                // 解析videoId获取标题和分类
                                const parts = videoId.split('|||');
                                if (parts.length === 2) {
                                    const category = parts[0];
                                    const title = parts[1];
                                    
                                    // 使用统一的查找方法更新AppData中的缓存状态
                                    const videoId = this.getVideoId(title, category);
                                    const result = this.findVideoByIdOrTitle(videoId, title, category);
                                    
                                    if (result && result.video && !result.video.isCached) {
                                        result.video.isCached = true;
                                        syncCount++;
                                        console.log(`🎬 从首页同步缓存状态: ${title} - ${category} (ID: ${result.video.id})`);
                                    }
                                }
                            }
                        });
                        
                        if (syncCount > 0) {
                            console.log(`🎬 从首页同步了 ${syncCount} 个缓存状态到AppData`);
                        }
                    }
                } catch (error) {
                    console.error('从首页同步缓存状态失败:', error);
                }
            },

            // 从AppData同步缓存状态到localStorage
            syncCacheFromAppData() {
                if (!window.AppData || !AppData.categories) {
                    console.warn('AppData不可用，跳过缓存同步');
                    return;
                }

                let syncCount = 0;
                for (const [categoryKey, categoryData] of Object.entries(AppData.categories)) {
                    if (categoryData.videos) {
                        categoryData.videos.forEach(video => {
                            if (video.isCached && video.id) {
                                this.cachedVideos.add(video.id);
                                syncCount++;
                            }
                        });
                    }
                }

                if (syncCount > 0) {
                    this.saveCachedVideos();
                    console.log(`🎬 从AppData同步了 ${syncCount} 个已缓存视频:`, [...this.cachedVideos]);
                }
            },

            // 根据标题和分类查找视频ID
            getVideoId(title, category) {
                // 使用DataManager的统一方法
                if (window.DataManager && DataManager.getVideoId) {
                    return DataManager.getVideoId(title, category);
                }
                
                // 回退到原有逻辑
                if (!window.AppData || !AppData.categories) {
                    console.warn('AppData未加载，使用fallback方法生成ID');
                    return `${category}|||${title}`;
                }

                // 在指定分类中查找视频
                if (category && AppData.categories[category] && AppData.categories[category].videos) {
                    const video = AppData.categories[category].videos.find(v => v.title === title);
                    if (video && video.id) {
                        return video.id;
                    }
                }

                // 如果在指定分类中没找到，在所有分类中查找
                for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                    if (categoryData.videos) {
                        const video = categoryData.videos.find(v => v.title === title);
                        if (video && video.id) {
                            return video.id;
                        }
                    }
                }

                console.warn(`未找到视频ID: ${title} in ${category}，使用fallback方法`);
                return `${category}|||${title}`;
            },
            
            // 根据ID或标题查找视频对象
            findVideoByIdOrTitle(videoId, title, category) {
                // 使用DataManager的统一方法
                if (window.DataManager && DataManager.findVideoByIdOrTitle) {
                    return DataManager.findVideoByIdOrTitle(videoId, title, category);
                }
                
                // 回退到原有逻辑
                if (!window.AppData || !AppData.categories) {
                    console.warn('AppData未加载');
                    return null;
                }

                // 首先尝试通过ID查找（如果ID不是fallback格式）
                if (videoId && !videoId.includes('|||')) {
                    for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                        if (categoryData.videos) {
                            const video = categoryData.videos.find(v => v.id === videoId);
                            if (video) {
                                return { video, category: cat };
                            }
                        }
                    }
                }

                // 如果通过ID没找到，回退到标题查找
                if (title) {
                    // 在指定分类中查找
                    if (category && AppData.categories[category] && AppData.categories[category].videos) {
                        const video = AppData.categories[category].videos.find(v => v.title === title);
                        if (video) {
                            return { video, category };
                        }
                    }

                    // 在所有分类中查找
                    for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                        if (categoryData.videos) {
                            const video = categoryData.videos.find(v => v.title === title);
                            if (video) {
                                return { video, category: cat };
                            }
                        }
                    }
                }

                return null;
            },

            // 检查视频是否已缓存（与首页保持一致的逻辑）
            isCached(title, category) {
                // 使用DataManager的统一方法
                if (window.DataManager && DataManager.isCached) {
                    return DataManager.isCached(title, category);
                }
                
                // 回退到原有逻辑
                const videoId = this.getVideoId(title, category);
                const result = this.findVideoByIdOrTitle(videoId, title, category);
                
                if (result && result.video) {
                    const isCached = !!result.video.isCached;
                    console.log(`🎬 缓存检查: ${title} - ${category} = ${isCached}`, result.video);
                    return isCached;
                }
                
                console.log(`🎬 缓存检查: ${title} - ${category} = false (视频未找到)`);
                return false;
            },

            // 检查视频是否正在下载
            isDownloading(title, category) {
                const videoId = this.getVideoId(title, category);
                return this.downloadingVideos.has(videoId);
            },

            // 更新单个视频状态显示
            updateVideoStatus(title, category) {
                const videoId = this.getVideoId(title, category);

                // 查找对应的视频元素
                const videoElements = document.querySelectorAll('.video-title-text');
                videoElements.forEach(element => {
                    // 获取原始标题（去除可能已存在的状态信息）
                    const originalText = element.textContent.replace(/\s*\([^)]*\)\s*$/, '').trim();

                    if (originalText === title) {
                        // 确定新的状态文本
                        let statusText = '';
                        let statusClass = '';

                        if (this.isDownloading(title, category)) {
                            const progress = this.downloadingVideos.get(videoId);
                            statusText = `(${progress}%)`;
                            statusClass = 'downloading';
                        } else if (this.isCached(title, category)) {
                            statusText = '(已缓存)';
                            statusClass = 'cached';
                        }

                        // 更新元素文本内容
                        if (statusText) {
                            element.innerHTML = `${title}<span class="cache-status ${statusClass}">${statusText}</span>`;
                        } else {
                            element.textContent = title;
                        }
                    }
                });
            },

            // 更新所有视频状态
            updateAllVideoStatus() {
                const videoElements = document.querySelectorAll('.video-title-text');
                videoElements.forEach(element => {
                    const title = element.textContent.trim();
                    // 使用当前播放列表的分类
                    const category = PlaylistManager ? PlaylistManager.currentCategory : '道：恋爱宝典系列·恋爱宝典1';
                    this.updateVideoStatus(title, category);
                });

                // 更新播放列表（使用ComponentTemplates重新生成）
                if (typeof PlaylistManager !== 'undefined' && PlaylistManager.updatePlaylist) {
                    PlaylistManager.updatePlaylist();
                }

                // 同时更新搜索结果的缓存状态
                if (typeof SearchManager !== 'undefined' && SearchManager.updateCacheStatus) {
                    SearchManager.updateCacheStatus();
                }
            },

            // 更新搜索结果中的视频状态
            updateSearchResultStatus(title, category) {
                const videoId = this.getVideoId(title, category);
                
                // 查找搜索结果中对应的视频元素
                const searchItems = document.querySelectorAll('.search-suggestion-item');
                searchItems.forEach(item => {
                    const itemTitle = item.getAttribute('data-title');
                    const itemCategory = item.getAttribute('data-category');
                    
                    if (itemTitle === title && itemCategory === category) {
                        const titleElement = item.querySelector('.video-title-text');
                        if (titleElement) {
                            // 移除现有的缓存状态
                            const existingCacheStatus = titleElement.querySelector('.cache-status');
                            if (existingCacheStatus) {
                                existingCacheStatus.remove();
                            }

                            // 确定新的状态文本
                            let statusText = '';
                            let statusClass = '';

                            if (this.isDownloading(title, category)) {
                                const progress = this.downloadingVideos.get(videoId);
                                statusText = `(${progress}%)`;
                                statusClass = 'downloading';
                            } else if (this.isCached(title, category)) {
                                statusText = '(已缓存)';
                                statusClass = 'cached';
                            }

                            // 更新元素文本内容
                            if (statusText) {
                                const cacheStatus = document.createElement('span');
                                cacheStatus.className = `cache-status ${statusClass}`;
                                cacheStatus.textContent = statusText;
                                titleElement.appendChild(cacheStatus);
                            }
                        }
                    }
                });
            },

            // 显示缓存弹窗
            showCacheModal(title, category, fromSearch = false) {
                // 显示缓存弹窗时关闭可能存在的支付弹窗
                if (typeof closePayment === 'function') {
                    closePayment();
                }
                
                this.currentVideoInfo = { title, category, fromSearch };
                const modal = document.getElementById('cacheModal');
                const titleElement = document.getElementById('cacheVideoTitle');

                if (modal && titleElement) {
                    titleElement.textContent = title;
                    modal.style.display = 'flex';
                }
            },

            // 关闭缓存弹窗
            closeCacheModal() {
                const modal = document.getElementById('cacheModal');
                if (modal) {
                    modal.style.display = 'none';
                }
                this.currentVideoInfo = null;
                
                // 缓存弹窗关闭后，搜索弹窗可以重新响应全局点击事件
                // 这里不需要额外操作，因为SearchManager会自动检查缓存弹窗状态
            },

            // 开始缓存视频
            async startCache() {
                if (!this.currentVideoInfo) return;

                const { title, category, fromSearch } = this.currentVideoInfo;
                const videoId = this.getVideoId(title, category);

                try {
                    // 关闭缓存弹窗
                    this.closeCacheModal();

                    // 开始下载
                    this.downloadingVideos.set(videoId, 0);
                    this.updateVideoStatus(title, category);
                    
                    // 如果来自搜索，也要更新搜索结果中的状态
                    if (fromSearch) {
                        this.updateSearchResultStatus(title, category);
                    }

                    // 模拟下载进度
                    await this.simulateDownload(videoId, title, category, fromSearch);

                    // 下载完成
                    this.downloadingVideos.delete(videoId);
                    this.cachedVideos.add(videoId);
                    
                    // 更新AppData中的isCached字段
                    this.updateAppDataCacheStatus(title, category, true);
                    
                    // 保存到首页格式的缓存状态
                    this.saveCacheStateToStorage();
                    
                    this.saveCachedVideos();
                    this.updateVideoStatus(title, category);

                    // 更新所有视频状态（包括搜索结果）
                    this.updateAllVideoStatus();

                    // 缓存完成后不自动播放，用户可以继续缓存其他视频
                    // 用户需要再次点击已缓存的视频才会播放
                    console.log(`${title} 缓存完成`);

                } catch (error) {
                    console.error('Cache failed:', error);
                    this.downloadingVideos.delete(videoId);
                    this.updateVideoStatus(title, category);
                    if (fromSearch) {
                        this.updateSearchResultStatus(title, category);
                    }
                    alert('缓存失败，请重试');
                }
            },

            // 模拟下载进度
            async simulateDownload(videoId, title, category, fromSearch = false) {
                return new Promise((resolve) => {
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += Math.random() * 15 + 5; // 每次增加5-20%
                        if (progress >= 100) {
                            progress = 100;
                            clearInterval(interval);
                            resolve();
                        }

                        this.downloadingVideos.set(videoId, Math.floor(progress));
                        this.updateVideoStatus(title, category);
                        
                        // 如果来自搜索，也要更新搜索结果中的状态
                        if (fromSearch) {
                            this.updateSearchResultStatus(title, category);
                        }
                    }, 200); // 每200ms更新一次
                });
            },

            // 保存已缓存视频列表到localStorage
            saveCachedVideos() {
                localStorage.setItem('cachedVideos', JSON.stringify([...this.cachedVideos]));
            },

            // 保存缓存状态到存储（与首页保持一致的格式）
            saveCacheStateToStorage() {
                try {
                    const cacheState = {};

                    // 从AppData中收集所有缓存状态
                    if (window.AppData && AppData.categories) {
                        Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                            if (categoryData.videos) {
                                categoryData.videos.forEach(video => {
                                    if (video.isCached) {
                                        // 使用与首页相同的videoId格式
                                        const videoId = `${categoryKey}|||${video.title}`;
                                        cacheState[videoId] = true;
                                    }
                                });
                            }
                        });
                    }

                    // 保存到localStorage
                    localStorage.setItem('videoCacheState', JSON.stringify(cacheState));
                    console.log('🎬 缓存状态已保存到videoCacheState');
                    
                } catch (error) {
                    console.error('保存缓存状态失败:', error);
                }
            },

            // 更新AppData中的缓存状态（基于ID优先，标题备用）
            updateAppDataCacheStatus(title, category, isCached) {
                if (!window.AppData || !AppData.categories) {
                    console.warn('AppData不可用，无法更新缓存状态');
                    return;
                }

                // 首先尝试通过ID查找
                const videoId = this.getVideoId(title, category);
                
                if (videoId && !videoId.includes('|||') && !videoId.includes('_')) {
                    // 这是真实的视频ID，通过ID查找并更新
                    for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                        if (categoryData.videos) {
                            const video = categoryData.videos.find(v => v.id === videoId);
                            if (video) {
                                video.isCached = isCached;
                                console.log(`已更新AppData缓存状态(ID): ${title} - ${isCached} (ID: ${videoId})`);
                                return;
                            }
                        }
                    }
                }

                // 如果通过ID没找到，回退到标题匹配
                // 在指定分类中查找视频
                if (category && AppData.categories[category] && AppData.categories[category].videos) {
                    const video = AppData.categories[category].videos.find(v => v.title === title);
                    if (video) {
                        video.isCached = isCached;
                        console.log(`已更新AppData缓存状态(标题): ${title} - ${isCached}`);
                        return;
                    }
                }

                // 如果在指定分类中没找到，在所有分类中查找
                for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                    if (categoryData.videos) {
                        const video = categoryData.videos.find(v => v.title === title);
                        if (video) {
                            video.isCached = isCached;
                            console.log(`已更新AppData缓存状态(标题): ${title} - ${isCached} (在分类 ${cat} 中找到)`);
                            return;
                        }
                    }
                }

                console.warn(`未在AppData中找到视频: ${title} in ${category}`);
            },

            // 从缓存中移除视频
            removeFromCache(title, category) {
                const videoId = this.getVideoId(title, category);
                
                // 从缓存集合中移除
                this.cachedVideos.delete(videoId);
                
                // 如果正在下载，也停止下载
                this.downloadingVideos.delete(videoId);
                
                // 更新AppData中的isCached字段
                this.updateAppDataCacheStatus(title, category, false);
                
                // 保存到首页格式的缓存状态
                this.saveCacheStateToStorage();
                
                // 保存到localStorage
                this.saveCachedVideos();
                
                // 更新视频状态显示
                this.updateVideoStatus(title, category);
                this.updateSearchResultStatus(title, category);
                
                console.log(`已移除缓存: ${title} - ${category}`);
            },

            // 获取已缓存视频列表
            getCachedVideosList() {
                const videoList = [];
                this.cachedVideos.forEach(videoId => {
                    // 从videoId解析出title和category
                    const parts = videoId.split('_');
                    if (parts.length >= 2) {
                        const category = parts[0].replace(/_/g, ' ');
                        const title = parts.slice(1).join(' ').replace(/_/g, ' ');
                        videoList.push({ title, category });
                    }
                });
                return videoList;
            },

            // 调试方法：显示当前缓存状态统计
            showCacheStats() {
                let appDataCount = 0;
                let localStorageCount = 0;
                
                // 统计AppData中的缓存状态
                if (window.AppData && AppData.categories) {
                    Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                        if (categoryData.videos) {
                            categoryData.videos.forEach(video => {
                                if (video.isCached) {
                                    appDataCount++;
                                }
                            });
                        }
                    });
                }
                
                // 统计localStorage中的缓存状态
                try {
                    const savedCacheState = localStorage.getItem('videoCacheState');
                    if (savedCacheState) {
                        const cacheState = JSON.parse(savedCacheState);
                        localStorageCount = Object.keys(cacheState).filter(key => cacheState[key]).length;
                    }
                } catch (error) {
                    console.error('读取localStorage失败:', error);
                }
                
                console.log('🎬 缓存状态统计:');
                console.log(`  AppData中已缓存视频: ${appDataCount} 个`);
                console.log(`  localStorage中已缓存视频: ${localStorageCount} 个`);
                console.log(`  当前显示的缓存状态基于: ${localStorageCount > 0 ? 'localStorage' : 'AppData'}`);
                
                return { appDataCount, localStorageCount };
            }
        };

        // 页面加载完成后初始化搜索功能、徽章动画、缓存管理、收藏管理、播放列表管理和全屏管理
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化数据管理器（必须最先初始化）
            if (window.DataManager) {
                DataManager.init();
            }
            
            SearchManager.init();
            ShareManager.init(); // 初始化分享管理器
            initBadgeAnimations();
            VideoCacheManager.init();
            VideoCollectionManager.init();
            PlaylistManager.init();
            FullscreenManager.init(); // 初始化全屏管理器
            
            // 调试信息
            setTimeout(() => {
                console.log('💡 全屏调试方法:');
                console.log('  debugTouchArea(true/false) - 显示/隐藏触摸区域边界');
                console.log('  debugShowControls() - 手动显示控制界面');
                console.log('  debugControlsStatus() - 检查控制界面状态');
                console.log('  FullscreenManager.isFullscreen - 检查全屏状态');
                console.log('💡 已回滚到简单实现，点击屏幕切换控制界面');
                console.log('💡 缓存状态调试方法:');
                console.log('  VideoCacheManager.showCacheStats() - 显示缓存状态统计');
                VideoCacheManager.showCacheStats();
            }, 1000);
        });

        function shareToWechat() {
            // 分享到微信好友功能（移除提示，直接执行）
            console.log('分享到微信好友');
            toggleShareModal();
        }

        function shareToMoments() {
            // 分享到朋友圈功能（移除提示，直接执行）
            console.log('分享到朋友圈');
            toggleShareModal();
        }

        function copyShareLink() {
            navigator.clipboard.writeText(window.location.href);
            // 链接复制成功（移除提示，通过其他方式反馈）
            console.log('链接已复制到剪贴板');
            toggleShareModal();
        }

        // 缓存相关全局函数
        function closeCacheModal() {
            try {
                VideoCacheManager.closeCacheModal();
            } catch (error) {
                console.error('Error closing cache modal:', error);
            }
        }

        function startVideoCache() {
            try {
                VideoCacheManager.startCache();
            } catch (error) {
                console.error('Error starting video cache:', error);
                alert('缓存失败，请重试');
            }
        }

        // 支付相关函数
        function showPayment(event, seriesName, price) {
            try {
                const paymentModal = document.getElementById('paymentModal');
                const paymentTitle = document.getElementById('paymentTitle');
                const paymentPrice = document.getElementById('paymentPrice');
                
                if (paymentModal && paymentTitle && paymentPrice) {
                    paymentTitle.textContent = seriesName;
                    paymentPrice.textContent = price;
                    paymentModal.style.display = 'block';
                }
            } catch (error) {
                console.error('Error showing payment modal:', error);
            }
        }

        function closePayment() {
            try {
                const paymentModal = document.getElementById('paymentModal');
                if (paymentModal) {
                    paymentModal.style.display = 'none';
                }
            } catch (error) {
                console.error('Error closing payment modal:', error);
            }
        }

        function goToPaymentPage() {
            try {
                // 关闭支付弹窗
                closePayment();
                
                // 关闭搜索弹窗（如果打开）
                if (SearchManager && SearchManager.isVisible) {
                    SearchManager.hide();
                }
                
                // 如果在全屏模式，先退出全屏
                if (FullscreenManager && FullscreenManager.isFullscreen) {
                    // 强制退出全屏并清理状态
                    FullscreenManager.exitFullscreen();
                    FullscreenManager.removeFullscreenStyles();
                    
                    // 等待全屏退出完成后再跳转
                    setTimeout(() => {
                        // 跳转到支付页面
                        window.location.href = '04-支付页面.html';
                    }, 500); // 增加等待时间确保全屏完全退出
                } else {
                    // 直接跳转到支付页面
                    window.location.href = '04-支付页面.html';
                }
                
                console.log('正在跳转到支付页面...');
            } catch (error) {
                console.error('Error going to payment page:', error);
                // 如果跳转失败，显示提示
                alert('跳转失败，请重试');
            }
        }

        // 收藏按钮点击函数
        function toggleVideoCollection() {
            try {
                VideoCollectionManager.toggleCollection();
            } catch (error) {
                console.error('Error toggling video collection:', error);
                alert('收藏操作失败，请重试');
            }
        }

        // 返回首页功能
        function goBackToHome() {
            try {
                // 尝试浏览器返回
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    // 如果没有历史记录，尝试跳转到首页
                    // 检查是否存在首页文件
                    const homePages = ['01-首页.html', 'index.html', '../index.html'];
                    
                    // 尝试跳转到首页
                    window.location.href = '01-首页.html';
                }
                
                console.log('返回操作执行');
            } catch (error) {
                console.error('Error going back to home:', error);
                // 备用方案：强制刷新当前页面
                window.location.reload();
            }
        }

        // 切换更多功能菜单
        function toggleMoreMenu() {
            try {
                const menu = document.getElementById('moreMenu');
                if (menu) {
                    if (menu.style.display === 'none' || !menu.style.display) {
                        menu.style.display = 'flex';
                    } else {
                        menu.style.display = 'none';
                    }
                }
            } catch (error) {
                console.error('Error toggling more menu:', error);
            }
        }

        // 更多功能菜单项的功能
        function showVideoInfo() {
            toggleMoreMenu();
            // 显示视频详情（保留信息展示）
            alert('视频详情：\n\n标题：06. 深度沟通艺术\n系列：道：恋爱宝典系列·恋爱宝典1\n时长：15:48\n播放量：1.2万\n进度：85%\n观看次数：6次');
        }

        function reportVideo() {
            toggleMoreMenu();
            if (confirm('确定要举报这个视频吗？')) {
                // 举报已提交（移除提示，通过界面反馈）
                console.log('举报已提交，我们会尽快处理');
            }
        }

        function downloadVideo() {
            toggleMoreMenu();
            // 开始下载视频到本地（移除提示，通过下载进度反馈）
            console.log('开始下载视频到本地');
        }

        function adjustPlaybackSpeed() {
            toggleMoreMenu();
            const speeds = ['0.5x', '0.75x', '1x', '1.25x', '1.5x', '2x'];
            const currentSpeed = prompt('请选择播放速度：\n' + speeds.join(', '), '1x');
            if (currentSpeed && speeds.includes(currentSpeed)) {
                // 播放速度设置成功（移除提示，通过界面反馈）
                console.log(`播放速度已设置为 ${currentSpeed}`);
            }
        }

        function toggleSubtitles() {
            toggleMoreMenu();
            const options = ['关闭字幕', '中文字幕', '英文字幕'];
            const choice = prompt('请选择字幕设置：\n' + options.join(', '), '中文字幕');
            if (choice && options.includes(choice)) {
                // 字幕设置成功（移除提示，通过界面反馈）
                console.log(`字幕已设置为：${choice}`);
            }
        }

        function showStudyNotes() {
            toggleMoreMenu();
            alert('学习笔记功能：\n\n• 可以在视频播放时添加时间戳笔记\n• 支持文字和图片笔记\n• 可以导出笔记到其他应用\n• 与其他学员分享笔记\n\n功能开发中...');
        }

        // 点击菜单外部关闭菜单
        function closeMoreMenuOnOverlay(event) {
            if (event.target === event.currentTarget) {
                toggleMoreMenu();
            }
        }

        // 更新视频信息区域
        function updateVideoInfo(title, category = null) {
            try {
                console.log(`🎬 updateVideoInfo 被调用: title="${title}", category="${category}"`);
                
                // 使用AppData作为数据源
                if (!window.AppData || !AppData.categories) {
                    console.error('🎬 AppData未加载或categories未定义');
                    return;
                }

                // 查找视频信息 - 使用统一的查找方法
                const videoId = VideoCacheManager.getVideoId(title, category);
                const result = VideoCacheManager.findVideoByIdOrTitle(videoId, title, category);

                if (!result || !result.video) {
                    console.warn(`🎬 Video not found: ${title} in category: ${category}`);
                    console.warn(`🎬 Available categories:`, Object.keys(AppData.categories));
                    return;
                }
                
                const video = result.video;
                const videoCategory = result.category;
                
                console.log(`🎬 找到视频:`, video, `分类: ${videoCategory}`);

                // 更新标题
                const titleElement = document.querySelector('.video-info-section h2');
                if (titleElement) {
                    titleElement.textContent = title;
                    console.log(`🎬 标题已更新: ${title}`);
                } else {
                    console.warn(`🎬 找不到标题元素`);
                }

                // 更新系列·分类名
                const categoryElement = document.querySelector('.video-info-section p.text-sm.text-gray-300');
                if (categoryElement && videoCategory) {
                    categoryElement.textContent = videoCategory;
                }

                // 更新进度条
                const progressFill = document.querySelector('.video-info-section .progress-fill-component');
                const progress = video.defaultProgress || 0;
                if (progressFill) {
                    progressFill.style.width = `${progress}%`;
                    if (progress > 0) {
                        progressFill.classList.add('animated');
                    } else {
                        progressFill.classList.remove('animated');
                    }
                    console.log(`🎬 进度条已更新: ${progress}%`);
                } else {
                    console.warn(`🎬 找不到进度条元素`);
                }

                // 更新进度百分比
                const progressPercentage = document.querySelector('.video-info-section .progress-percentage-component');
                if (progressPercentage) {
                    progressPercentage.textContent = `${progress}%`;
                }

                // 更新徽章
                const badge = document.querySelector('.video-info-section .badge-component');
                const watchCount = video.defaultWatchCount || 0;
                if (badge) {
                    // 移除所有徽章类
                    badge.className = 'badge-component';
                    // 添加对应级别的徽章类
                    badge.classList.add(`badge-level-${watchCount}`);
                    badge.textContent = `×${watchCount}`;
                }

                // 更新当前视频状态
                CurrentVideoManager.setCurrentVideo(title, videoCategory || '道：恋爱宝典系列·恋爱宝典1');
                
                // 更新收藏状态
                VideoCollectionManager.setCurrentVideo(title, videoCategory || '道：恋爱宝典系列·恋爱宝典1');

                // 更新播放列表显示对应分类的视频
                if (videoCategory && typeof PlaylistManager !== 'undefined' && PlaylistManager.updatePlaylist) {
                    PlaylistManager.updatePlaylist(videoCategory);
                }

                console.log(`视频信息已更新: ${title} - ${videoCategory}`);

            } catch (error) {
                console.error('Error updating video info:', error);
            }
        }

        // 从首页传递的视频信息更新视频播放页
        function updateVideoInfoFromHomePage() {
            try {
                // 从 sessionStorage 获取视频信息
                const videoInfoStr = sessionStorage.getItem('currentVideoInfo');
                if (!videoInfoStr) {
                    console.log('没有从首页传递的视频信息');
                    return;
                }

                const videoInfo = JSON.parse(videoInfoStr);
                console.log('接收到首页传递的视频信息:', videoInfo);

                // 更新标题
                const titleElement = document.querySelector('.video-info-section h2');
                if (titleElement) {
                    titleElement.textContent = videoInfo.title;
                }

                // 更新系列·分类名
                const categoryElement = document.querySelector('.video-info-section p.text-sm.text-gray-300');
                if (categoryElement) {
                    categoryElement.textContent = videoInfo.category;
                }

                // 更新进度条
                const progressFill = document.querySelector('.video-info-section .progress-fill-component');
                if (progressFill) {
                    progressFill.style.width = `${videoInfo.progress}%`;
                    if (videoInfo.progress > 0) {
                        progressFill.classList.add('animated');
                    } else {
                        progressFill.classList.remove('animated');
                    }
                }

                // 更新进度百分比
                const progressPercentage = document.querySelector('.video-info-section .progress-percentage-component');
                if (progressPercentage) {
                    progressPercentage.textContent = `${videoInfo.progress}%`;
                }

                // 更新徽章
                const badge = document.querySelector('.video-info-section .badge-component');
                if (badge) {
                    // 移除所有徽章类
                    badge.className = 'badge-component';
                    // 添加对应级别的徽章类
                    badge.classList.add(`badge-level-${videoInfo.watchCount}`);
                    badge.textContent = `×${videoInfo.watchCount}`;
                }

                // 更新播放次数
                const playCountElement = document.querySelector('.video-info-section .text-xs.text-gray-400 span');
                if (playCountElement) {
                    playCountElement.innerHTML = `<i class="fas fa-play-circle mr-1"></i>${videoInfo.playCount}播放`;
                }

                // 更新收藏状态
                if (VideoCollectionManager && VideoCollectionManager.setCurrentVideo) {
                    VideoCollectionManager.setCurrentVideo(videoInfo.title, videoInfo.category);
                }

                // 更新播放列表显示对应分类的视频
                if (videoInfo.category && typeof PlaylistManager !== 'undefined' && PlaylistManager.updatePlaylist) {
                    PlaylistManager.updatePlaylist(videoInfo.category);
                }

                // 清除 sessionStorage 中的视频信息，避免重复使用
                sessionStorage.removeItem('currentVideoInfo');

                console.log(`视频信息已从首页更新: ${videoInfo.title} - ${videoInfo.category}`);

            } catch (error) {
                console.error('Error updating video info from home page:', error);
            }
        }

        // 视频项点击处理函数
        function handleVideoClick(title, event) {
            try {
                console.log(`🎬 handleVideoClick 被调用: ${title}`);
                
                // 阻止事件冒泡，防止触发全局点击事件
                if (event) {
                    event.stopPropagation();
                }

                // 使用当前播放列表的分类
                const category = PlaylistManager.currentCategory;
                console.log(`🎬 当前分类: ${category}`);

                // 详细的缓存状态检查
                const videoId = VideoCacheManager.getVideoId(title, category);
                const isCached = VideoCacheManager.isCached(title, category);
                const isDownloading = VideoCacheManager.isDownloading(title, category);
                
                console.log(`🎬 缓存状态检查:`, {
                    title: title,
                    category: category,
                    videoId: videoId,
                    isCached: isCached,
                    isDownloading: isDownloading,
                    cachedVideos: Array.from(VideoCacheManager.cachedVideos),
                    downloadingVideos: Array.from(VideoCacheManager.downloadingVideos.keys())
                });

                // 更新视频信息区域
                updateVideoInfo(title, category);

                // 检查视频是否已缓存
                if (isCached) {
                    console.log(`🎬 视频已缓存，开始播放: ${title} - ${category}`);
                    // 已缓存，折叠播放列表并直接播放
                    PlaylistManager.collapsePlaylist();
                    
                    // 如果当前在全屏模式，更新全屏信息并显示控制界面
                    if (FullscreenManager && FullscreenManager.isFullscreen) {
                        console.log(`🎬 全屏模式下播放视频: ${title} - ${category}`);
                        // 更新全屏视频信息
                        FullscreenManager.updateFullscreenVideoInfo();
                        // 重新生成全屏播放列表
                        FullscreenManager.generateFullscreenPlaylist();
                        // 显示控制界面，让用户看到视频切换效果
                        FullscreenManager.showFullscreenControls();
                    } else {
                        console.log(`🎬 非全屏模式下播放视频: ${title} - ${category}`);
                    }
                } else if (isDownloading) {
                    console.log(`🎬 视频正在下载中: ${title} - ${category}`);
                    alert('视频正在缓存中，请稍候...');
                } else {
                    console.log(`🎬 视频未缓存，显示缓存弹窗: ${title} - ${category}`);
                    // 未缓存，显示缓存弹窗
                    VideoCacheManager.showCacheModal(title, category);
                }
            } catch (error) {
                console.error('🎬 Error handling video click:', error);
                console.error('🎬 Error details:', {
                    title: title,
                    category: PlaylistManager ? PlaylistManager.currentCategory : 'undefined',
                    VideoCacheManager: !!VideoCacheManager,
                    PlaylistManager: !!PlaylistManager
                });
                alert('播放失败，请重试');
            }
        }

        // 徽章样式切换功能
        class BadgeStyleManager {
            static currentStyle = 'mixed'; // 默认混合版
            
            static switchToOriginal() {
                const container = document.querySelector('.badge-style-mixed, .badge-style-original');
                if (container) {
                    container.className = 'badge-style-original';
                    this.currentStyle = 'original';
                    console.log('已切换到原版11级钻石徽章');
                }
            }
            
            static switchToMixed() {
                const container = document.querySelector('.badge-style-mixed, .badge-style-original');
                if (container) {
                    container.className = 'badge-style-mixed';
                    this.currentStyle = 'mixed';
                    console.log('已切换到混合10级图标徽章');
                }
            }
            
            static getCurrentStyle() {
                return this.currentStyle;
            }
            
            static toggle() {
                if (this.currentStyle === 'mixed') {
                    this.switchToOriginal();
                } else {
                    this.switchToMixed();
                }
            }
        }

        // 全局函数供控制台调用
        function switchBadgeStyle(style) {
            if (style === 'original') {
                BadgeStyleManager.switchToOriginal();
            } else if (style === 'mixed') {
                BadgeStyleManager.switchToMixed();
            } else {
                console.log('可用样式: "original" (原版11级钻石) 或 "mixed" (混合10级图标)');
                console.log('当前样式:', BadgeStyleManager.getCurrentStyle());
            }
        }

        function toggleBadgeStyle() {
            BadgeStyleManager.toggle();
        }

        // 页面加载完成后检查是否有从首页传递的视频信息
        document.addEventListener('DOMContentLoaded', function() {
            updateVideoInfoFromHomePage();
            
            // 显示徽章样式切换提示
            setTimeout(() => {
                console.log('💡 徽章样式切换方法:');
                console.log('  switchBadgeStyle("original") - 切换到原版11级钻石徽章');
                console.log('  switchBadgeStyle("mixed") - 切换到混合10级图标徽章');
                console.log('  toggleBadgeStyle() - 在两种样式间切换');
                console.log('  当前样式:', BadgeStyleManager.getCurrentStyle());
            }, 1000);
        });
    </script>
</div> <!-- 结束徽章样式容器 -->



</body>
</html>
</html>