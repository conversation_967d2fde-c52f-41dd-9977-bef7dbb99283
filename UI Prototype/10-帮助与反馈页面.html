<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 帮助与反馈</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <script src="data/component-templates-with-styles.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
        }
        .content::-webkit-scrollbar { display: none; }
        .help-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .faq-item {
            border-bottom: 1px solid #f3f4f6;
            padding: 16px 0;
            cursor: pointer;
        }
        .faq-item:last-child {
            border-bottom: none;
        }
        .faq-question {
            display: flex;
            align-items: center;
            justify-content: between;
            font-weight: 500;
            color: #374151;
        }
        .faq-answer {
            margin-top: 12px;
            color: #6b7280;
            font-size: 14px;
            line-height: 1.6;
            display: none;
        }
        .contact-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: between;
        }
        .contact-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }
        .feedback-form {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 16px;
        }
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
            transition: border-color 0.3s ease;
        }
        .form-textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
            border: none;
            cursor: pointer;
        }
        .tab-container {
            display: flex;
            background: #f3f4f6;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 16px;
        }
        .tab-btn {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .tab-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 选项卡 -->
                <div class="tab-container">
                    <div class="tab-btn active" onclick="switchTab('help')">常见问题</div>
                    <div class="tab-btn" onclick="switchTab('contact')">联系我们</div>
                    <div class="tab-btn" onclick="switchTab('feedback')">意见反馈</div>
                </div>

                <!-- 常见问题 -->
                <div id="helpTab" class="help-card">
                    <h3 class="font-semibold text-gray-800 mb-4">常见问题</h3>
                    
                    <div class="faq-item" onclick="toggleFaq(this)">
                        <div class="faq-question">
                            <span>如何购买课程？</span>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                        <div class="faq-answer">
                            在首页选择您想要的课程系列，点击课程后会弹出支付页面。选择支付方式（支付宝或微信支付）完成付款即可。购买后可立即观看所有课程内容。
                        </div>
                    </div>

                    <div class="faq-item" onclick="toggleFaq(this)">
                        <div class="faq-question">
                            <span>视频无法播放怎么办？</span>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                        <div class="faq-answer">
                            请检查网络连接是否正常。如果网络正常，请尝试重新缓存视频。在缓存管理页面删除该视频，然后重新下载缓存。如果问题仍然存在，请联系客服。
                        </div>
                    </div>

                    <div class="faq-item" onclick="toggleFaq(this)">
                        <div class="faq-question">
                            <span>如何获得分享收益？</span>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                        <div class="faq-answer">
                            购买课程后，您可以分享App给好友。好友通过您的分享链接购买课程后，您将获得该订单30%的分成收益。收益会在订单完成后24小时内到账。
                        </div>
                    </div>

                    <div class="faq-item" onclick="toggleFaq(this)">
                        <div class="faq-question">
                            <span>忘记密码怎么办？</span>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                        <div class="faq-answer">
                            在登录页面点击"忘记密码"，输入您的手机号码，系统会发送验证码到您的手机。输入验证码后可以重新设置密码。
                        </div>
                    </div>

                    <div class="faq-item" onclick="toggleFaq(this)">
                        <div class="faq-question">
                            <span>可以在多个设备上登录吗？</span>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                        <div class="faq-answer">
                            为了保护版权，每个账号只能在一个设备上登录。如果在新设备登录，原设备会自动退出登录。
                        </div>
                    </div>

                    <div class="faq-item" onclick="toggleFaq(this)">
                        <div class="faq-question">
                            <span>缓存的视频占用太多空间怎么办？</span>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                        <div class="faq-answer">
                            您可以在缓存管理页面删除不需要的视频缓存。建议开启"自动清理缓存"功能，系统会自动删除30天未观看的视频。
                        </div>
                    </div>

                    <div class="faq-item" onclick="toggleFaq(this)">
                        <div class="faq-question">
                            <span>如何加入学习群？</span>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                        <div class="faq-answer">
                            购买课程后，在"我的"页面可以看到客服微信号。添加客服微信并验证身份后，客服会拉您进入专属学习群。
                        </div>
                    </div>
                </div>

                <!-- 联系我们 -->
                <div id="contactTab" class="help-card" style="display: none;">
                    <h3 class="font-semibold text-gray-800 mb-4">联系我们</h3>
                    
                    <div class="contact-item">
                        <div class="contact-icon" style="background: #10b981;">
                            <i class="fab fa-weixin"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">客服微信</p>
                            <p class="text-sm text-gray-500">shuimu_kf001</p>
                        </div>
                        <button class="bg-green-500 text-white px-3 py-1 rounded text-sm">
                            复制
                        </button>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon" style="background: #3b82f6;">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">邮箱支持</p>
                            <p class="text-sm text-gray-500"><EMAIL></p>
                        </div>
                        <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm">
                            复制
                        </button>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon" style="background: #f59e0b;">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">客服热线</p>
                            <p class="text-sm text-gray-500">************</p>
                        </div>
                        <button class="bg-orange-500 text-white px-3 py-1 rounded text-sm">
                            拨打
                        </button>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon" style="background: #8b5cf6;">
                            <i class="fab fa-qq"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">QQ群</p>
                            <p class="text-sm text-gray-500">123456789</p>
                        </div>
                        <button class="bg-purple-500 text-white px-3 py-1 rounded text-sm">
                            加群
                        </button>
                    </div>

                    <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                        <h4 class="font-medium text-blue-800 mb-2">
                            <i class="fas fa-clock mr-2"></i>服务时间
                        </h4>
                        <p class="text-sm text-blue-600">
                            周一至周日：9:00 - 22:00<br>
                            节假日：10:00 - 18:00
                        </p>
                    </div>
                </div>

                <!-- 意见反馈 -->
                <div id="feedbackTab" class="feedback-form" style="display: none;">
                    <h3 class="font-semibold text-gray-800 mb-4">意见反馈</h3>
                    
                    <form onsubmit="submitFeedback(event)">
                        <div class="form-group">
                            <label class="form-label">反馈类型</label>
                            <select class="form-input">
                                <option>功能建议</option>
                                <option>Bug反馈</option>
                                <option>内容问题</option>
                                <option>支付问题</option>
                                <option>其他问题</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">问题描述</label>
                            <textarea class="form-textarea" placeholder="请详细描述您遇到的问题或建议..." required></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">联系方式（可选）</label>
                            <input type="text" class="form-input" placeholder="手机号或微信号，方便我们联系您">
                        </div>

                        <div class="form-group">
                            <label class="form-label">设备信息</label>
                            <input type="text" class="form-input" value="iPhone 13 Pro, iOS 16.1" readonly>
                        </div>

                        <button type="submit" class="submit-btn">
                            提交反馈
                        </button>
                    </form>

                    <div class="mt-4 p-3 bg-yellow-50 rounded-lg">
                        <p class="text-sm text-yellow-700">
                            <i class="fas fa-info-circle mr-2"></i>
                            我们会在24小时内处理您的反馈，感谢您的支持！
                        </p>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTopNavigation();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '帮助与反馈'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        function goBack() {
            if (window.navigateTo) {
                navigateTo('03-我的页面.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '03-我的页面.html';
            }
        }

        // showShareModal函数已经在顶部导航组件中自动包含

        function switchTab(tab) {
            // 隐藏所有标签页
            document.getElementById('helpTab').style.display = 'none';
            document.getElementById('contactTab').style.display = 'none';
            document.getElementById('feedbackTab').style.display = 'none';
            
            // 移除所有按钮的active类
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签页
            if (tab === 'help') {
                document.getElementById('helpTab').style.display = 'block';
                document.querySelectorAll('.tab-btn')[0].classList.add('active');
            } else if (tab === 'contact') {
                document.getElementById('contactTab').style.display = 'block';
                document.querySelectorAll('.tab-btn')[1].classList.add('active');
            } else if (tab === 'feedback') {
                document.getElementById('feedbackTab').style.display = 'block';
                document.querySelectorAll('.tab-btn')[2].classList.add('active');
            }
        }

        function toggleFaq(item) {
            const answer = item.querySelector('.faq-answer');
            const icon = item.querySelector('.fa-chevron-down, .fa-chevron-up');
            
            if (answer.style.display === 'none' || answer.style.display === '') {
                answer.style.display = 'block';
                icon.className = 'fas fa-chevron-up text-gray-400';
            } else {
                answer.style.display = 'none';
                icon.className = 'fas fa-chevron-down text-gray-400';
            }
        }

        function submitFeedback(event) {
            event.preventDefault();
            alert('反馈提交成功！我们会尽快处理您的问题。');
        }
    </script>
</body>
</html> 