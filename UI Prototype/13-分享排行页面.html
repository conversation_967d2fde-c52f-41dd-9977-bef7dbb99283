<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 分享排行</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <script src="data/component-templates-with-styles.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
        }
        .content::-webkit-scrollbar { display: none; }
        .ranking-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 24px;
            margin: 16px 0;
            color: white;
            text-align: center;
        }
        .ranking-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .top-three {
            display: flex;
            justify-content: center;
            align-items: end;
            margin: 20px 0;
            gap: 16px;
        }
        .top-user {
            text-align: center;
            position: relative;
        }
        .top-user.first {
            order: 2;
            margin-bottom: 20px;
        }
        .top-user.second {
            order: 1;
        }
        .top-user.third {
            order: 3;
        }
        .top-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 8px;
            position: relative;
            border: 3px solid;
        }
        .top-user.first .top-avatar {
            width: 80px;
            height: 80px;
            border-color: #fbbf24;
        }
        .top-user.second .top-avatar,
        .top-user.third .top-avatar {
            border-color: #e5e7eb;
        }
        .crown {
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 24px;
            color: #fbbf24;
        }
        .rank-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .rank-1 { background: #fbbf24; }
        .rank-2 { background: #9ca3af; }
        .rank-3 { background: #cd7c2f; }
        .ranking-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        .ranking-item:last-child {
            border-bottom: none;
        }
        .rank-number {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 12px;
            background: #f3f4f6;
            color: #6b7280;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
        }
        .my-ranking {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            border-radius: 12px;
            padding: 16px;
            margin: 16px 0;
            color: white;
        }
        .time-filter {
            display: flex;
            background: #f3f4f6;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 16px;
        }
        .filter-btn {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .filter-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .reward-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        .reward-item:last-child {
            border-bottom: none;
        }
        .reward-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 排行榜头部 -->
                <div class="ranking-header">
                    <h2 class="text-lg font-semibold mb-2">本月分享排行榜</h2>
                    <p class="text-white text-opacity-80">分享越多，收益越高</p>
                </div>

                <!-- 前三名展示 -->
                <div class="ranking-card">
                    <div class="top-three">
                        <!-- 第二名 -->
                        <div class="top-user second">
                            <div class="top-avatar">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face" 
                                     class="w-full h-full rounded-full object-cover" alt="用户头像">
                                <div class="rank-badge rank-2">2</div>
                            </div>
                            <p class="font-medium text-gray-800 text-sm">小美</p>
                            <p class="text-xs text-gray-500">¥1,856</p>
                        </div>

                        <!-- 第一名 -->
                        <div class="top-user first">
                            <div class="crown">👑</div>
                            <div class="top-avatar">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face" 
                                     class="w-full h-full rounded-full object-cover" alt="用户头像">
                                <div class="rank-badge rank-1">1</div>
                            </div>
                            <p class="font-medium text-gray-800">阿强</p>
                            <p class="text-sm text-gray-500">¥3,245</p>
                        </div>

                        <!-- 第三名 -->
                        <div class="top-user third">
                            <div class="top-avatar">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face" 
                                     class="w-full h-full rounded-full object-cover" alt="用户头像">
                                <div class="rank-badge rank-3">3</div>
                            </div>
                            <p class="font-medium text-gray-800 text-sm">小雨</p>
                            <p class="text-xs text-gray-500">¥1,234</p>
                        </div>
                    </div>
                </div>

                <!-- 我的排名 -->
                <div class="my-ranking">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" 
                                 class="w-10 h-10 rounded-full mr-3" alt="我的头像">
                            <div>
                                <p class="font-medium">我的排名</p>
                                <p class="text-sm opacity-80">本月收益 ¥456.50</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold">#15</div>
                            <div class="text-sm opacity-80">共1,234人</div>
                        </div>
                    </div>
                </div>

                <!-- 时间筛选 -->
                <div class="time-filter">
                    <div class="filter-btn">本周</div>
                    <div class="filter-btn active">本月</div>
                    <div class="filter-btn">本年</div>
                </div>

                <!-- 排行榜列表 -->
                <div class="ranking-card">
                    <h3 class="font-semibold text-gray-800 mb-4">完整排行榜</h3>
                    
                    <div class="space-y-0">
                        <div class="ranking-item">
                            <div class="rank-number" style="background: #fbbf24; color: white;">1</div>
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" 
                                 class="user-avatar" alt="用户头像">
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">阿强</p>
                                <p class="text-sm text-gray-500">分享人数：89人</p>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-gray-800">¥3,245</div>
                                <div class="text-sm text-green-600">+¥234</div>
                            </div>
                        </div>

                        <div class="ranking-item">
                            <div class="rank-number" style="background: #9ca3af; color: white;">2</div>
                            <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" 
                                 class="user-avatar" alt="用户头像">
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">小美</p>
                                <p class="text-sm text-gray-500">分享人数：67人</p>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-gray-800">¥1,856</div>
                                <div class="text-sm text-green-600">+¥156</div>
                            </div>
                        </div>

                        <div class="ranking-item">
                            <div class="rank-number" style="background: #cd7c2f; color: white;">3</div>
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face" 
                                 class="user-avatar" alt="用户头像">
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">小雨</p>
                                <p class="text-sm text-gray-500">分享人数：45人</p>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-gray-800">¥1,234</div>
                                <div class="text-sm text-green-600">+¥89</div>
                            </div>
                        </div>

                        <div class="ranking-item">
                            <div class="rank-number">4</div>
                            <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=40&h=40&fit=crop&crop=face" 
                                 class="user-avatar" alt="用户头像">
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">小明</p>
                                <p class="text-sm text-gray-500">分享人数：38人</p>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-gray-800">¥987</div>
                                <div class="text-sm text-green-600">+¥67</div>
                            </div>
                        </div>

                        <div class="ranking-item">
                            <div class="rank-number">5</div>
                            <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=40&h=40&fit=crop&crop=face" 
                                 class="user-avatar" alt="用户头像">
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">小丽</p>
                                <p class="text-sm text-gray-500">分享人数：32人</p>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-gray-800">¥876</div>
                                <div class="text-sm text-green-600">+¥45</div>
                            </div>
                        </div>

                        <div class="ranking-item">
                            <div class="rank-number">6</div>
                            <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=40&h=40&fit=crop&crop=face" 
                                 class="user-avatar" alt="用户头像">
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">小王</p>
                                <p class="text-sm text-gray-500">分享人数：28人</p>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-gray-800">¥765</div>
                                <div class="text-sm text-green-600">+¥34</div>
                            </div>
                        </div>

                        <div class="ranking-item">
                            <div class="rank-number">7</div>
                            <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=40&h=40&fit=crop&crop=face" 
                                 class="user-avatar" alt="用户头像">
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">小张</p>
                                <p class="text-sm text-gray-500">分享人数：25人</p>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-gray-800">¥654</div>
                                <div class="text-sm text-green-600">+¥23</div>
                            </div>
                        </div>

                        <div class="ranking-item">
                            <div class="rank-number">8</div>
                            <img src="https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=40&h=40&fit=crop&crop=face" 
                                 class="user-avatar" alt="用户头像">
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">小李</p>
                                <p class="text-sm text-gray-500">分享人数：22人</p>
                            </div>
                            <div class="text-right">
                                <div class="font-bold text-gray-800">¥543</div>
                                <div class="text-sm text-green-600">+¥18</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 奖励说明 -->
                <div class="ranking-card">
                    <h3 class="font-semibold text-gray-800 mb-4">排行榜奖励</h3>
                    
                    <div class="space-y-0">
                        <div class="reward-item">
                            <div class="reward-icon" style="background: #fbbf24;">
                                <i class="fas fa-crown"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">第1名</p>
                                <p class="text-sm text-gray-500">额外奖励500元现金</p>
                            </div>
                            <div class="text-yellow-600 font-bold">¥500</div>
                        </div>

                        <div class="reward-item">
                            <div class="reward-icon" style="background: #9ca3af;">
                                <i class="fas fa-medal"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">第2名</p>
                                <p class="text-sm text-gray-500">额外奖励300元现金</p>
                            </div>
                            <div class="text-gray-600 font-bold">¥300</div>
                        </div>

                        <div class="reward-item">
                            <div class="reward-icon" style="background: #cd7c2f;">
                                <i class="fas fa-award"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">第3名</p>
                                <p class="text-sm text-gray-500">额外奖励200元现金</p>
                            </div>
                            <div class="text-orange-600 font-bold">¥200</div>
                        </div>

                        <div class="reward-item">
                            <div class="reward-icon" style="background: #10b981;">
                                <i class="fas fa-gift"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-gray-800">前10名</p>
                                <p class="text-sm text-gray-500">专属荣誉徽章</p>
                            </div>
                            <div class="text-green-600 font-bold">徽章</div>
                        </div>
                    </div>
                </div>

                <!-- 活动说明 -->
                <div class="ranking-card">
                    <h3 class="font-semibold text-gray-800 mb-4">活动说明</h3>
                    
                    <div class="space-y-3 text-sm text-gray-600">
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>排行榜按月统计，每月1日重置排名</p>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>排名依据为当月分享收益总额</p>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>奖励将在次月5日前发放到账</p>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>如有疑问请联系客服：shuimu_kf001</p>
                        </div>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTopNavigation();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '分享排行'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        function goBack() {
            if (window.navigateTo) {
                navigateTo('03-我的页面.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '03-我的页面.html';
            }
        }

        // showShareModal函数已经在顶部导航组件中自动包含

        function goToMaterials() {
            if (window.navigateTo) {
                navigateTo('14-分享素材页面.html');
            } else {
                window.location.href = '14-分享素材页面.html';
            }
        }
    </script>
</body>
</html>