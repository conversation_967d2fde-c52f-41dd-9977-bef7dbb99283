<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 购买记录</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <script src="data/component-templates-with-styles.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
        }
        .content::-webkit-scrollbar { display: none; }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            color: white;
        }
        .order-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        .order-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        .timeline-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            position: absolute;
            left: 20px;
            top: 24px;
        }
        .timeline-line {
            width: 2px;
            background: #e5e7eb;
            position: absolute;
            left: 25px;
            top: 36px;
            bottom: 0;
        }
        .order-item {
            position: relative;
            padding-left: 40px;
            margin-bottom: 16px;
        }
        .order-details {
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px;
            margin-top: 8px;
            display: none;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 消费统计 -->
                <div class="stats-card">
                    <h3 class="font-semibold text-lg mb-4">消费统计</h3>
                    <div class="flex justify-between">
                        <div class="text-center">
                            <div class="text-2xl font-bold">3</div>
                            <div class="text-sm text-white text-opacity-80">订单总数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold">¥1,400</div>
                            <div class="text-sm text-white text-opacity-80">累计消费</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold">¥180</div>
                            <div class="text-sm text-white text-opacity-80">分享收益</div>
                        </div>
                    </div>
                </div>

                <!-- 筛选选项 -->
                <div class="flex space-x-2 mb-4">
                    <button class="px-4 py-2 bg-blue-500 text-white rounded-lg text-sm">全部</button>
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg text-sm">已完成</button>
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg text-sm">进行中</button>
                </div>

                <!-- 订单时间线 -->
                <div class="relative">
                    <!-- 2024年1月 -->
                    <div class="order-item">
                        <div class="timeline-dot"></div>
                        <div class="timeline-line"></div>
                        <div class="order-card" onclick="toggleDetails(this)">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <span class="font-semibold text-gray-800">全套课程</span>
                                    <span class="order-status status-success ml-2">已完成</span>
                                </div>
                                <span class="text-lg font-bold text-red-500">¥1,400</span>
                            </div>
                            <div class="text-sm text-gray-500 mb-2">
                                订单号：SM202401150001
                            </div>
                            <div class="flex items-center justify-between text-sm text-gray-400">
                                <span>2024-01-15 14:30</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            
                            <div class="order-details">
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span>道：恋爱宝典系列</span>
                                        <span>¥600</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>术：聊天技术系列</span>
                                        <span>¥1,000</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>全套优惠</span>
                                        <span class="text-green-500">-¥200</span>
                                    </div>
                                    <div class="border-t pt-2 flex justify-between font-semibold">
                                        <span>实付金额</span>
                                        <span class="text-red-500">¥1,400</span>
                                    </div>
                                </div>
                                <div class="mt-3 pt-3 border-t text-xs text-gray-500">
                                    支付方式：支付宝 | 分享收益：¥420
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2023年12月 -->
                    <div class="order-item">
                        <div class="timeline-dot" style="background: #10b981;"></div>
                        <div class="timeline-line"></div>
                        <div class="order-card" onclick="toggleDetails(this)">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <span class="font-semibold text-gray-800">术：聊天技术系列</span>
                                    <span class="order-status status-success ml-2">已完成</span>
                                </div>
                                <span class="text-lg font-bold text-red-500">¥1,000</span>
                            </div>
                            <div class="text-sm text-gray-500 mb-2">
                                订单号：SM202312280002
                            </div>
                            <div class="flex items-center justify-between text-sm text-gray-400">
                                <span>2023-12-28 16:45</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            
                            <div class="order-details">
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span>聊天技术1</span>
                                        <span>¥350</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>聊天技术2</span>
                                        <span>¥350</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>聊天技术3</span>
                                        <span>¥300</span>
                                    </div>
                                    <div class="border-t pt-2 flex justify-between font-semibold">
                                        <span>实付金额</span>
                                        <span class="text-red-500">¥1,000</span>
                                    </div>
                                </div>
                                <div class="mt-3 pt-3 border-t text-xs text-gray-500">
                                    支付方式：微信支付 | 分享收益：¥300
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2023年11月 -->
                    <div class="order-item">
                        <div class="timeline-dot" style="background: #f59e0b;"></div>
                        <div class="order-card" onclick="toggleDetails(this)">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <span class="font-semibold text-gray-800">道：恋爱宝典系列</span>
                                    <span class="order-status status-success ml-2">已完成</span>
                                </div>
                                <span class="text-lg font-bold text-red-500">¥600</span>
                            </div>
                            <div class="text-sm text-gray-500 mb-2">
                                订单号：SM202311120003
                            </div>
                            <div class="flex items-center justify-between text-sm text-gray-400">
                                <span>2023-11-12 10:20</span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            
                            <div class="order-details">
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span>恋爱宝典1</span>
                                        <span>¥100</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>恋爱宝典2</span>
                                        <span>¥100</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>恋爱宝典3</span>
                                        <span>¥100</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>恋爱宝典4</span>
                                        <span>¥100</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>恋爱宝典5</span>
                                        <span>¥100</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>恋爱宝典6</span>
                                        <span>¥100</span>
                                    </div>
                                    <div class="border-t pt-2 flex justify-between font-semibold">
                                        <span>实付金额</span>
                                        <span class="text-red-500">¥600</span>
                                    </div>
                                </div>
                                <div class="mt-3 pt-3 border-t text-xs text-gray-500">
                                    支付方式：支付宝 | 分享收益：¥180
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTopNavigation();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '购买记录'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        function goBack() {
            if (window.navigateTo) {
                navigateTo('03-我的页面.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '03-我的页面.html';
            }
        }





        function toggleDetails(card) {
            const details = card.querySelector('.order-details');
            const icon = card.querySelector('.fa-chevron-down, .fa-chevron-up');
            
            if (details.style.display === 'none' || details.style.display === '') {
                details.style.display = 'block';
                icon.className = 'fas fa-chevron-up';
            } else {
                details.style.display = 'none';
                icon.className = 'fas fa-chevron-down';
            }
        }
    </script>
</body>
</html> 