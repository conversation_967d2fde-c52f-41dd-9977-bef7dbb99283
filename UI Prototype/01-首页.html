<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 首页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- 引入数据文件 -->
    <script src="data/app-data.js"></script>
    <script src="data/user-state.js"></script>
    <script src="data/data-manager.js"></script>
    <!-- 使用现有的组件模板 -->
    <script src="data/component-templates-with-styles.js"></script>
    <script src="data/navigation-manager.js"></script>
    <style>
        body {
            overflow: hidden;
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .content {
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
            padding: 0 16px 20px 16px;
        }
        .content::-webkit-scrollbar { display: none; }
        .series-card {
            background: white;
            border-radius: 16px;
            padding: 20px 8px;
            margin-bottom: 12px;
            color: #374151;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            /* border-left: 4px solid #3b82f6; */
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .series-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        .series-card.free {
            background: #f0fdf4;
            color: #374151;
        }
        .category-card {
            background: white;
            border-radius: 12px;
            padding: 12px 0 16px 8px;
            margin-bottom: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 2px solid #667eea;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative; /* 添加相对定位，为内容提供定位上下文 */
            z-index: 1; /* 确保每个分类卡片有独立的层级 */
        }
        .category-card:hover {
            background: #f8fafc;
            transform: translateX(4px);
        }
        .category-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .category-content.expanded {
            max-height: 200px;
            overflow-y: auto; /* 垂直方向允许滚动 */
            overflow-x: hidden; /* 分类容器水平方向裁剪 */
            padding-bottom: 8px; /* 添加底部间距，防止与下一个分类重叠 */
            padding-right: 30px; /* 为视频项右边内容预留空间 */
            margin-right: -30px; /* 负边距抵消padding，保持容器宽度 */
        }

        /* 隐藏滚动条 */
        .category-content::-webkit-scrollbar {
            display: none;
        }

        .category-content {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }
        .video-item {
            padding: 8px 0;
            padding-left: 32px;
            font-size: 13px;
            line-height: 1.2;
            color: #4a5568;
            border-bottom: 1px solid #e2e8f0;
        }
        .video-item:hover {
            background-color: #f4faff;
        }
        .video-item.last-watched {
            background-color: #eaf4ff;
        }
        .payment-modal {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 24%;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 0px 20px 24px 20px;
            display: none;
            z-index: 9999;
        }

        .share-modal {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 40%;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 20px;
            display: none;
            z-index: 9999;
        }

        /* 搜索弹窗样式 */
        .search-modal {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 60%;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 20px;
            display: none;
            z-index: 9999;
            overflow: hidden;
        }

        .search-modal.show {
            display: flex;
            flex-direction: column;
        }

        /* 搜索输入框样式 */
        .search-input-container {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            border-color: #3b82f6;
        }

        .search-clear-btn {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            font-size: 18px;
            display: none;
        }

        .search-clear-btn:hover {
            color: #6b7280;
        }

        /* 搜索下拉框样式 */
        .search-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e5e7eb;
            border-top: none;
            border-radius: 0 0 12px 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1001;
            display: none;
        }

        /* 隐藏搜索下拉框的滚动条 */
        .search-dropdown::-webkit-scrollbar {
            display: none;
        }

        .search-dropdown {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .search-dropdown.show {
            display: block;
        }

        .search-dropdown-header {
            padding: 8px 16px;
            font-size: 12px;
            color: #6b7280;
            background: #f9fafb;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .search-dropdown-clear {
            color: #3b82f6;
            cursor: pointer;
            font-size: 12px;
        }

        .search-dropdown-clear:hover {
            text-decoration: underline;
        }

        .search-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid #f3f4f6;
        }

        .search-dropdown-item:hover {
            background-color: #f9fafb;
        }

        .search-dropdown-item:last-child {
            border-bottom: none;
        }

        .search-dropdown-item-title {
            font-size: 14px;
            color: #374151;
            margin-bottom: 2px;
        }

        .search-dropdown-item-category {
            font-size: 12px;
            color: #6b7280;
        }

        /* 底部导航样式 */


        /* 搜索历史样式 */
        .search-history-section {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 16px;
        }

        .search-section-title {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 12px;
        }

        .search-history-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            max-height: 120px;
            overflow-y: auto;
        }

        /* 隐藏搜索历史列表的滚动条 */
        .search-history-list::-webkit-scrollbar {
            display: none;
        }

        .search-history-list {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .search-history-item {
            background: #f3f4f6;
            color: #6b7280;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .search-history-item:hover {
            background: #e5e7eb;
            color: #374151;
        }

        /* 搜索建议样式 */
        .search-suggestions {
            flex: 1;
            overflow-y: auto;
            max-height: 300px; /* 恢复最大高度限制以触发滚动 */
            min-height: 100px;
            padding-right: 8px; /* 为滚动操作预留空间 */
            margin-top: 8px;
        }

        /* 当搜索历史隐藏时，搜索建议占用更多空间 */
        .search-suggestions.full-height {
            flex: 2;
            max-height: 350px; /* 搜索时给更多高度 */
        }

        /* 隐藏搜索建议区域的滚动条 */
        .search-suggestions::-webkit-scrollbar {
            display: none;
        }

        .search-suggestions {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .search-empty-state {
            text-align: center;
            color: #9ca3af;
            font-size: 14px;
            padding: 40px 20px;
        }

        /* 搜索结果展示，建议项样式 - 适应两行布局 */
        .search-suggestion-item {
            padding: 10px 6px; /* 增加上下内边距适应两行布局 */
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 6px;
            margin: 0 -6px;
            display: flex;
            align-items: flex-start; /* 改为顶部对齐，适应多行内容 */
            gap: 8px;
        }

        /* 搜索结果中的播放图标位置调整 */
        .search-suggestion-item .video-play-icon {
            margin-top: 2px; /* 向下偏移一点，与第一行文字对齐 */
        }

        .search-suggestion-item:hover {
            background-color: #f9fafb;
            transform: translateX(2px);
            border-left: 3px solid #3b82f6;
            padding-left: 8px;
        }

        .search-suggestion-item:hover .video-title-component {
            color: #2563eb;
        }

        .search-suggestion-item:hover .video-play-icon {
            background: #1d4ed8;
            transform: scale(1.1);
        }

        .search-suggestion-item:last-child {
            border-bottom: none;
        }

        .search-suggestion-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .search-suggestion-title {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            line-height: 1.3;
        }

        .search-suggestion-category {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.2;
            margin-top: 5px; /* 增加与标题的间距 */
        }

        /* 搜索结果中的视频标题组件调整 */
        .search-suggestion-item .video-title-component {
            margin-bottom: 0; /* 重置底部边距 */
        }

        /* 搜索结果中的进度行组件调整 */
        .search-suggestion-item .video-progress-row-component {
            margin-top: -6px; /* 增加与标题的间距 */
            margin-bottom: -11px; /* 重置底部边距 */
        }

        /* 复用视频项组件样式 */
        .video-content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        .video-title-component {
            font-size: 13px;
            line-height: 1.2;
            color: #4a5568;
            margin-bottom: -8px;
            padding: 0;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .video-title-text {
            flex: 1;
        }

        .video-progress-row-component {
            display: flex;
            align-items: center;
            gap: 0;
            margin-bottom: -5px;
            transition: all 0.3s ease;
        }

        .progress-component {
            background: transparent;
            border-radius: 2px;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }

        .progress-thin {
            height: 1px;
            margin-right: 3px;
        }

        .progress-flex {
            flex: 1 1 auto;
            min-width: 0;
        }

        .progress-fill-component {
            height: 100%;
            border-radius: inherit;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(90deg, #84dd99, #22c55e);
            position: absolute;
            top: 0;
            left: 0;
            transform-origin: left center;
        }

        .progress-fill-component.animated {
            animation: progressFill 1.2s ease-out;
        }

        .progress-fill-component::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255,255,255,0.4),
                transparent
            );
            animation: shimmer 2s infinite;
        }

        .progress-info-component {
            display: flex;
            padding-right: 10px;
            align-items: center;
            gap: 2px;
            min-width: 80px;
            width: auto;
            justify-content: flex-end;
            flex-shrink: 0;
            font-size: 12px;
            color: #6b7280;
        }

        @keyframes progress-fill {
            from { width: 0%; }
        }



        .video-play-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .share-button-component:hover {
            transform: scale(1.1);
        }

        /* 搜索按钮组件 */
        .search-button-component {
            padding: 8px;
            color: #6b7280;
            background: transparent;
            border: none;
            border-radius: 8px;
            margin-right: 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: visible;
        }

        .search-button-component:hover {
            transform: scale(1.1);
        }

        /* 分享按钮组件 */
        .share-button-component {
            padding: 8px;
            color: #6b7280;
            background: transparent;
            border: none;
            border-radius: 8px;
            margin-right: 15px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: visible;
        }

        .share-button-component:hover {
            transform: scale(1.1);
        }

        /* Logo组件基础样式 */
        .logo-component {
            border-radius: 50%;
            background: #3b82f6;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-component i {
            color: white;
        }

        /* Logo尺寸变体 */
        .logo-sm {
            width: 48px;
            height: 48px;
        }

        .logo-sm i {
            font-size: 20px;
        }

        .logo-lg {
            width: 80px;
            height: 80px;
        }

        .logo-lg i {
            font-size: 32px;
        }

        /* Logo边距变体 */
        .logo-mr {
            margin-right: 12px;
        }

        .logo-center {
            margin: 0 auto 16px auto;
        }

        /* Logo边框变体 */
        .logo-border {
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* 增加选择器特异性，确保动画优先级 */
        .share-button-component i,
        .share-button-component i.orange-purple-gradient-enhanced {
            font-size: 30px;
            /* animation: share-icon-wiggle 3s infinite !important; */
            /* 覆盖渐变类的transition */
            transition: none !important;
        }

        /* 分享按钮右上角的金币动画 - 改为由小变大的缩放动画 */
        .share-button-component::after {
            content: '💰'; /* 使用更直观的钱袋emoji */
            position: absolute;

            right: -8px;
            font-size: 18px;
            animation: coin-scale-pulse 3s ease-in-out infinite;
            pointer-events: none;
            z-index: 2;
            transform-origin: center;
        }

        /* 金币由小变大的缩放动画 */
        @keyframes coin-scale-pulse {
            0% {
                transform: scale(0.8);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(0.8);
                opacity: 0.7;
            }
        }

        /* 分享图标摆动动画 */
        @keyframes share-icon-wiggle {
            0%, 100% {
                transform: rotate(0deg);
            }
            25% {
                transform: rotate(5deg);
            }
            50% {
                transform: rotate(0deg);
            }
            75% {
                transform: rotate(-5deg);
            }
        }

        /* 金币旋转动画 */
        @keyframes coin-spin {
            0% {
                transform: rotateY(0deg) scale(1);
            }
            50% {
                transform: rotateY(180deg) scale(1.1);
            }
            100% {
                transform: rotateY(360deg) scale(1);
            }
        }

        /* 金币发光动画 */
        @keyframes coin-glow {
            0%, 100% {
                filter: brightness(1) drop-shadow(0 0 2px rgba(255, 215, 0, 0.5));
            }
            50% {
                filter: brightness(1.5) drop-shadow(0 0 6px rgba(255, 215, 0, 0.8));
            }
        }

        /* ========== 新增组件系统 ========== */

        /* 自定义橙紫渐变 */
        .orange-purple-gradient {
            background: linear-gradient(90deg, #f97316, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
        }

        /* 增强版本 - 带悬停效果和发光 */
        .orange-purple-gradient-enhanced {
            background: linear-gradient(90deg, #f97316, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 2px rgba(249, 115, 22, 0.3));
        }

        .orange-purple-gradient-enhanced:hover {
            background: linear-gradient(90deg, #fb923c, #a855f7);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 6px rgba(249, 115, 22, 0.6));
            animation: gradient-pulse 2s ease-in-out infinite;
        }

        /* 亮色版本 - 深色背景用 */
        .orange-purple-gradient-bright {
            background: linear-gradient(90deg, #fbbf24, #c084fc);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 3px rgba(251, 191, 36, 0.4));
        }

        .orange-purple-gradient-bright:hover {
            background: linear-gradient(90deg, #fcd34d, #ddd6fe);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.8));
            animation: gradient-pulse-bright 2s ease-in-out infinite;
        }

        /* 动画关键帧 */
        @keyframes gradient-pulse {
            0%, 100% {
                filter: drop-shadow(0 0 6px rgba(249, 115, 22, 0.6));
            }
            50% {
                filter: drop-shadow(0 0 10px rgba(147, 51, 234, 0.8));
            }
        }

        @keyframes gradient-pulse-bright {
            0%, 100% {
                filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.8));
            }
            50% {
                filter: drop-shadow(0 0 12px rgba(192, 132, 252, 1));
            }
        }

        /* 蓝绿渐变动画 */
        @keyframes blue-green-pulse {
            0%, 100% {
                filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6));
            }
            50% {
                filter: drop-shadow(0 0 10px rgba(16, 185, 129, 0.8));
            }
        }

        @keyframes progressFill {
            0% {
                width: 0%;
                opacity: 0.7;
            }
            50% {
                opacity: 1;
            }
            100% {
                opacity: 0.9;
            }
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes breathe {
            0%, 100% {
                background: #22c55e;
                box-shadow: 0 0 12px rgba(34, 197, 94, 0.6);
            }
            50% {
                background: #16a34a;
                box-shadow: 0 0 20px rgba(34, 197, 94, 0.8);
            }
        }



        /* 购物车图标橙色样式 */
        .shopping-cart-orange {
            color: #f97316 !important;
        }

        /* 1. 进度条组件系统 */
        .progress-component {
            background: transparent;
            border-radius: 2px;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }

        /* 进度条尺寸变体 */
        .progress-thick {
            height: 3px;
            margin: 4px 0;
        }

        .progress-medium {
            height: 2px;
            margin-right: 0px;
        }

        .progress-thin {
            height: 1px;
            margin-right: 3px;
        }

        /* 进度条布局变体 */
        .progress-flex {
            flex: 1 1 auto;
            min-width: 0;
        }

        /* 进度条填充组件 */
        .progress-fill-component {
            height: 100%;
            border-radius: inherit;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(90deg, #84dd99, #22c55e);
            position: absolute;
            top: 0;
            left: 0;
            transform-origin: left center;
        }

        .progress-fill-component.animated {
            animation: progressFill 1.2s ease-out;
        }

        .progress-fill-component::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255,255,255,0.4),
                transparent
            );
            animation: shimmer 2s infinite;
        }

        /* 2. 观看次数徽章组件 - 引入双套样式系统 */



        /* 3. 折叠箭头组件 */
        .toggle-arrow-component {
            flex-shrink: 0;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        /* 箭头尺寸变体 */
        .arrow-large {
            font-size: 16px;
            color: #6b7280;
        }

        .arrow-small {
            font-size: 14px;
            color: #9ca3af;
            margin-top: -20px;
            margin-right: 16px;        /* 👈 改用margin-right代替padding */
            transform-origin: center center;  /* 👈 设置旋转中心点 */
        }

        /* 箭头状态变体 */
        .arrow-expanded {
            transform: rotate(180deg);
            transform-origin: center center;  /* 👈 确保旋转基于中心 */
        }

        /* 4. 系列卡片头部组件 */
        .series-header-component {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2px;
            margin-top: 2px;
        }

        .series-header-left {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .series-icon-component {
            font-size: 24px;
            margin-right: 16px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
        }

        .series-title-component {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
            line-height: 1.2;
            display: flex;
            align-items: center;
            margin: 0;
            padding: 0;
        }

        .series-price-component {
            font-size: 16px;
            font-weight: 400;
            color: #6b7280;
            margin-left: 4px;
            line-height: 1.2;
        }

        /* 5. 分类卡片头部组件 */
        .category-header-component {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .category-title-component {
            font-weight: 500;
            color: #374151;
            display: flex;
            align-items: center;
            line-height: 1.2;
            margin: 0;
            padding: 0;
        }

        .category-price-component {
            font-size: 14px;
            font-weight: 400;
            color: #6b7280;
            line-height: 1.2;
        }

        /* 6. 视频项组件 */
        .video-item-component {
            padding: 8px 0;
            margin-left: 8px;
            border-bottom: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative; /* 确保定位上下文 */
            min-height: 40px; /* 设置最小高度，防止重叠 */
        }

        .video-item-component:hover {
            background-color: #f4faff;
            transform: translateX(4px);
            padding-left: 12px;
        }

        .video-item-component:hover .video-title-component {
            color: #2563eb;
        }

        .video-item-component:hover .video-play-icon {
            background: #1d4ed8;
            transform: scale(1.1);
        }

        .video-item-component.last-watched {
            background-color: #eaf4ff;
        }

        .video-content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        .video-title-component {
            font-size: 13px;
            line-height: 1.2;
            color: #4a5568;
            margin-bottom: -8px;
            padding: 0;
            transition: color 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .video-play-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .video-title-text {
            flex: 1;
        }

        .video-title-row-component {
            display: flex;
            align-items: center;
            font-size: 13px;
            line-height: 1.2;
            color: #4a5568;
            margin: 0;
            padding: 0;
            gap: 8px;
        }

        .video-title-row-component .video-title-component {
            flex: 1;
            margin: 0;
            padding: 0;
        }

        .video-lock-component {
            font-size: 12px;
            color: #F59E0B;
            margin-left: auto;
            flex-shrink: 0;
            padding-left: 8px;
            padding-right: 10px;
            display: flex;
            align-items: center;
        }

        .video-progress-row-component {
            display: flex;
            align-items: center;
            gap: 0;
            margin-bottom: -5px;
            transition: all 0.3s ease;
        }

        /* 7. 进度信息组件-组合组件 */
        .progress-info-component {
            display: flex;
            padding-right: 10px;
            align-items: center;
            gap: 2px;
            min-width: 80px;
            width: auto;
            justify-content: flex-end;
            flex-shrink: 0;
            white-space: nowrap; /* 防止文字换行 */
        }

        .progress-percentage-component {
            font-size: 12px;
            color: #6b7280;
            margin-right: 2px;
            flex-shrink: 0;
            white-space: nowrap;
        }

        /* 8. 弹窗组件系统 */
        .modal-component {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 20px;
            display: none;
            z-index: 9999;
        }

        /* 弹窗尺寸变体 */
        .modal-small {
            height: 24%;
            padding: 0px 20px 24px 20px;
        }

        .modal-medium {
            height: 40%;
        }

        .modal-header-component {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .modal-title-component {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
        }

        .modal-close-component {
            color: #9ca3af;
            font-size: 20px;
            cursor: pointer;
            background: none;
            border: none;
            padding: 0;
            margin-left: auto;
        }

        .modal-close-component:hover {
            color: #6b7280;
        }

        /* 9. 分享选项组件 */
        .share-option-component {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .share-option-component:hover {
            background: #f8fafc;
            transform: translateY(-2px);
        }

        .share-icon-component {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 24px;
            color: white;
            position: relative;
            overflow: visible;
            animation: share-icon-pulse 2s infinite;
        }

        .share-label-component {
            font-size: 14px;
            color: #374151;
            transition: color 0.3s ease;
        }

        .share-option-component:hover .share-label-component {
            color: #22c55e;
            font-weight: 500;
        }

        /* 分享图标颜色变体 */
        .share-icon-wechat {
            background: linear-gradient(135deg, #1aad19, #0d8912);
            box-shadow: 0 4px 12px rgba(26, 173, 25, 0.3);
        }

        /* 微信分享图标的人民币 */
        /*
        .share-icon-wechat::after {
            content: '💰';
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 16px;
            animation: money-bounce 1.5s infinite, money-sparkle 3s infinite;
            pointer-events: none;
            z-index: 2;
        }
        */

        .share-icon-link {
            background: linear-gradient(135deg, #667eea, #4f46e5);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        /* 复制链接图标的美元 */
        /*
        .share-icon-link::after {
            content: '💵';
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 16px;
            animation: money-bounce 1.8s infinite, money-sparkle 2.5s infinite;
            pointer-events: none;
            z-index: 2;
        }
        */

        /* 朋友圈图标的金币（使用微信样式但不同钱币） */
        /*
        .share-option-component:nth-child(2) .share-icon-component::after {
            content: '🪙';
            position: absolute;
            top: -8px;
            right: -8px;
            font-size: 16px;
            animation: money-bounce 1.3s infinite, money-sparkle 3.5s infinite;
            pointer-events: none;
            z-index: 2;
        }
        */

        /* 分享图标动画 */
        @keyframes share-icon-pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
            }
        }

        /* 美元钱币弹跳动画 */
        @keyframes money-bounce {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.9;
            }
            25% {
                transform: translateY(-3px) scale(1.1);
                opacity: 1;
            }
            50% {
                transform: translateY(-6px) scale(1.2);
                opacity: 1;
            }
            75% {
                transform: translateY(-3px) scale(1.1);
                opacity: 1;
            }
        }

        /* 美元钱币闪烁动画 */
        @keyframes money-sparkle {
            0%, 100% {
                filter: brightness(1) drop-shadow(0 0 2px rgba(255, 215, 0, 0.6));
            }
            33% {
                filter: brightness(1.3) drop-shadow(0 0 6px rgba(255, 215, 0, 0.8));
            }
            66% {
                filter: brightness(1.6) drop-shadow(0 0 10px rgba(255, 215, 0, 1));
            }
        }

        /* 10. 状态栏组件 */
        .status-bar-component {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }

        .status-time-component {
            font-weight: 600;
        }

        .status-icons-component {
            display: flex;
            gap: 4px;
        }

        /* 11. 顶部导航组件 */
        .top-nav-component {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 0;
        }

        .nav-left-component {
            display: flex;
            align-items: center;
        }

        .nav-title-component {
            font-size: 20px;
            font-weight: bold;
            color: #374151;
        }

        .nav-right-component {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-user-component {
            font-size: 14px;
            color: #6b7280;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .nav-user-component:hover {
            color: #2563eb;
        }

        /* 12. 系列进度行组件 */
        .series-progress-component {
            display: flex;
            align-items: center;
            margin-top: -8px;
            gap: 1px;
        }

        /* ========== 强制重置HTML标签默认样式 ========== */
        .series-title-component h3,
        .series-header-component h3,
        h3.series-title-component {
            margin: 0 !important;
            padding: 0 !important;
            line-height: 1.2 !important;
            font-size: inherit !important;
            font-weight: inherit !important;
            color: inherit !important;
        }

        .category-title-component span,
        span.category-title-component {
            margin: 0 !important;
            padding: 0 !important;
            line-height: 1.2 !important;
        }

        /* 传奇徽章样式 - 11次及以上 */
        .badge-level-legendary {
            background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #92400e);
            background-size: 300% 300%;
            color: #ffffff;
            font-weight: bold;
            font-size: 12px;
            box-shadow: 0 0 15px rgba(251, 191, 36, 0.6), 0 0 30px rgba(251, 191, 36, 0.3);
            animation: pulse-legendary 0.8s infinite, breathe-legendary 1.5s infinite, gradient-shift 3s infinite;
            position: relative;
            overflow: visible;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 传奇徽章的皇冠 - 柔和不刺眼 */
        .badge-level-legendary::before {
            content: '👑';
            position: absolute;
            top: -20px;
            left: -12px;
            font-size: 20px;
            animation: crown-sparkle 1.2s infinite;
            pointer-events: none;
            z-index: 2;
        }

        /* 传奇徽章的女孩头像 - 柔和不刺眼 */
        .badge-level-legendary::after {
            content: '👧';
            position: absolute;
            top: -20px;
            right: -10px;
            font-size: 18px;
            animation: girl-sparkle 1.2s infinite;
            pointer-events: none;
            z-index: 2;
        }

        /* 可选：使用Font Awesome女性图标的版本 */
        .badge-level-legendary.fa-version::after {
            content: '';
            position: absolute;
            top: -8px;
            right: -8px;
            width: 10px;
            height: 10px;
            animation: girl-sparkle 1.2s infinite;
            pointer-events: none;
            z-index: 2;
            background: none;
        }

        .badge-level-legendary.fa-version::after::before {
            font-family: "Font Awesome 6 Free";
            content: "\f182"; /* fa-female 的Unicode */
            font-weight: 900;
            font-size: 10px;
            color: #ff69b4;
        }



        /* 蓝绿渐变 - 水幕主题色 */
        .blue-green-gradient-enhanced {
            background: linear-gradient(90deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.3));
        }

        .blue-green-gradient-enhanced:hover {
            background: linear-gradient(90deg, #60a5fa, #34d399);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6));
            animation: blue-green-pulse 2s ease-in-out infinite;
        }

        /* Toast提示组件样式 */
        .toast-container {
            position: fixed;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            pointer-events: none;
        }

        .toast {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.3s ease;
            pointer-events: auto;
            max-width: 300px;
            text-align: center;
            word-wrap: break-word;
        }

        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }

        .toast.success {
            background: rgba(34, 197, 94, 0.9);
        }

        .toast.error {
            background: rgba(239, 68, 68, 0.9);
        }

        .toast.warning {
            background: rgba(245, 158, 11, 0.9);
        }

        .toast.info {
            background: rgba(59, 130, 246, 0.9);
        }

        /* 加载状态样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9998;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: white;
            margin-top: 16px;
            font-size: 14px;
        }

        /* 网络状态指示器 */
        .network-status {
            position: fixed;
            top: 44px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(239, 68, 68, 0.9);
            color: white;
            padding: 8px 16px;
            border-radius: 0 0 8px 8px;
            font-size: 12px;
            z-index: 9997;
            display: none;
            animation: slideDown 0.3s ease;
        }

        .network-status.online {
            background: rgba(34, 197, 94, 0.9);
        }

        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); }
            to { transform: translateX(-50%) translateY(0); }
        }

        /* 重试按钮样式 */
        .retry-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            margin-left: 8px;
            transition: background 0.2s ease;
        }

        .retry-button:hover {
            background: #2563eb;
        }

        .retry-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }

        /* 移动端触摸滚动优化 */
        .search-suggestions {
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .search-suggestion-item {
            padding: 6px 0; /* 减半内边距，从12px减少到6px */
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 6px;
            margin: 2px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-suggestion-item:hover {
            background-color: #f9fafb;
            transform: translateX(2px);
            border-left: 3px solid #3b82f6;
            padding-left: 8px;
        }

        .search-suggestion-item:last-child {
            border-bottom: none;
        }

        .search-suggestion-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .search-suggestion-title {
            font-weight: 500;
            color: #374151;
            font-size: 14px;
            line-height: 1.3;
            margin-bottom: 2px;
        }

        .search-suggestion-category {
            font-size: 12px;
            color: #6b7280;
            line-height: 1.2;
        }

        /* 搜索空状态 */
        .search-empty-state {
            text-align: center;
            color: #9ca3af;
            font-size: 14px;
            margin-top: 40px;
        }

        .share-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .share-option:hover {
            background: #f8fafc;
        }

        .share-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 24px;
            color: white;
        }

        /* 缓存弹窗样式 - 简化版本 */
        .cache-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000; /* 确保在所有弹窗之上 */
        }

        .cache-modal-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            width: 280px;
            text-align: center;
            position: relative;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .cache-modal-close {
            position: absolute;
            top: 12px;
            right: 16px;
            background: none;
            border: none;
            font-size: 24px;
            color: #9ca3af;
            cursor: pointer;
            padding: 4px;
            line-height: 1;
        }

        .cache-modal-close:hover {
            color: #6b7280;
        }

        .cache-video-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
            padding-right: 20px;
        }

        .cache-download-btn {
            width: 100%;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cache-download-btn:hover {
            background: linear-gradient(135deg, #2563eb, #1e40af);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .cache-download-btn i {
            margin-right: 8px;
        }

        /* 缓存状态样式 - 紧挨着标题显示 */
        .cache-status {
            font-size: 12px;
            color: #6b7280;
            font-weight: 400;
            margin-left: 0;
        }

        .cache-status.downloading {
            color: #3b82f6;
            font-weight: 500;
        }

        .cache-status.success {
            color: #10b981;
            font-weight: 500;
        }

        .cache-status.cached {
            color: #10b981;
            font-weight: 400;
        }
    </style>
</head>
<body class="bg-gray-100 badge-style-mixed">
<!-- 徽章样式容器 - 默认使用混合版 -->
<div class="badge-style-mixed">
<div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar-component">
                <span class="status-time-component">9:41</span>
                <span class="status-icons-component">
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 顶部用户信息 -->
                <div class="top-nav-component">
                    <div class="nav-left-component">
                        <div class="logo-component logo-sm logo-mr">
                            <i class="fas fa-water"></i>
                        </div>
                        <h1 class="nav-title-component">水幕</h1>
                    </div>
                    <div class="nav-right-component">
                        <button class="search-button-component" onclick="showSearchModal()">
                            <i class="fas fa-search" style="color: #6b7280; font-size: 20px;"></i>
                        </button>
                        <button class="share-button-component" onclick="showShareModal()">
                            <i class="fas fa-share-alt blue-green-gradient-enhanced"></i>
                        </button>
                        <p class="nav-user-component" onclick="goToMyPage()">张三</p>
                    </div>
                </div>

                <!-- 免费精品系列 -->
                <div class="series-card free" onclick="handleSeriesClick(event, this, '免费精品系列', '')">
                    <div class="series-header-component">
                        <div class="series-header-left">
                            <i class="fas fa-play-circle series-icon-component text-green-500"></i>
                            <h3 class="series-title-component">免费精品系列</h3>
                        </div>
                        <i class="fas fa-chevron-down toggle-arrow-component arrow-large series-arrow arrow-expanded"></i>
                    </div>

                    <!-- 免费分类 -->
                    <div class="mt-4 space-y-2 series-content">
                        <div class="category-card" onclick="handleCategoryClick(event, this, '约会技巧', '')">
                            <div class="category-header-component">
                                <span class="category-title-component">约会技巧</span>
                                <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow arrow-expanded"></i>
                            </div>
                            <div class="series-progress-component">
                                <div class="progress-component progress-medium progress-flex">
                                    <div class="progress-fill-component animated" style="width: 100%;"></div>
                                </div>
                                <div class="progress-info-component">
                                    <span class="progress-percentage-component">100%</span>
                                    <span class="badge-component badge-level-5">×5</span>
                                </div>
                            </div>
                            <div class="category-content expanded">
                                <div class="mt-2 space-y-1" id="dating-skills-videos">
                                    <!-- 视频项将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <div class="category-card" onclick="handleCategoryClick(event, this, '搭讪技术', '')">
                            <div class="category-header-component">
                                <span class="category-title-component">搭讪技术</span>
                                <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow"></i>
                            </div>
                            <div class="series-progress-component">
                                <div class="progress-component progress-medium progress-flex">
                                    <div class="progress-fill-component animated" style="width: 100%;"></div>
                                </div>
                                <div class="progress-info-component">
                                    <span class="progress-percentage-component">100%</span>
                                    <span class="badge-component badge-level-4">×4</span>
                                </div>
                            </div>
                            <div class="category-content">
                                <div class="mt-2 space-y-1" id="pickup-skills-videos">
                                    <!-- 视频项将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 道：恋爱宝典系列 -->
                <div class="series-card" onclick="handleSeriesClick(event, this, '道：恋爱宝典系列', '¥600')">
                    <div class="series-header-component">
                        <div class="series-header-left">
                            <i class="fas fa-heart series-icon-component text-red-500"></i>
                            <h3 class="series-title-component">道：恋爱宝典系列 <span class="series-price-component">(¥600)</span></h3>
                        </div>
                        <i class="fas fa-chevron-down toggle-arrow-component arrow-large series-arrow"></i>
                    </div>

                    <!-- 分类列表 -->
                    <div class="mt-4 space-y-2 series-content" style="display: none;">
                        <div class="category-card" onclick="handleCategoryClick(event, this, '道：恋爱宝典系列·恋爱宝典1', '')">
                            <div class="category-header-component">
                                <span class="category-title-component">恋爱宝典1</span>
                                <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow"></i>
                            </div>
                            <div class="series-progress-component">
                                <div class="progress-component progress-medium progress-flex">
                                    <div class="progress-fill-component animated" style="width: 75%;"></div>
                                </div>
                                <div class="progress-info-component">
                                    <span class="progress-percentage-component">75%</span>
                                    <span class="badge-component badge-level-3">×3</span>
                                </div>
                            </div>
                            <div class="category-content expanded">
                                <div class="mt-2 space-y-1" id="love-guide-1-videos">
                                    <!-- 视频项将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <div id="love-guide-2-card" class="category-card" onclick="handleCategoryClick(event, this, '道：恋爱宝典系列·恋爱宝典2', '¥100')">
                            <div class="category-header-component">
                                <span class="category-title-component">恋爱宝典2 <span class="category-price-component">(¥100)</span></span>
                                <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow"></i>
                            </div>
                            <div class="category-content">
                                <div class="mt-2 space-y-1" id="love-guide-2-videos">
                                    <!-- 视频项将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <div class="category-card" onclick="handleCategoryClick(event, this, '道：恋爱宝典系列·恋爱宝典3', '¥100')">
                            <div class="category-header-component">
                                <span class="category-title-component">恋爱宝典3 <span class="category-price-component">(¥100)</span></span>
                                <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow"></i>
                            </div>
                            <div class="category-content">
                                <div class="mt-2 space-y-1" id="love-guide-3-videos">
                                    <!-- 视频项将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 术：聊天技术系列 -->
                <div class="series-card" onclick="handleSeriesClick(event, this, '术：聊天技术系列', '¥1000')">
                    <div class="series-header-component">
                        <div class="series-header-left">
                            <i class="fas fa-comments series-icon-component text-pink-500"></i>
                            <h3 class="series-title-component">术：聊天技术系列 <span class="series-price-component">(¥1000)</span></h3>
                        </div>
                        <i class="fas fa-chevron-down toggle-arrow-component arrow-large series-arrow"></i>
                    </div>

                    <!-- 分类列表 -->
                    <div class="mt-4 space-y-2 series-content" style="display: none;">
                        <div class="category-card" onclick="handleCategoryClick(event, this, '术：聊天技术系列·聊天技术1', '¥350')">
                            <div class="category-header-component">
                                <span class="category-title-component">聊天技术1 <span class="category-price-component">(¥350)</span></span>
                                <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow"></i>
                            </div>
                            <div class="category-content">
                                <div class="mt-2 space-y-1" id="chat-tech-1-videos">
                                    <!-- 视频项将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <div class="category-card" onclick="handleCategoryClick(event, this, '术：聊天技术系列·聊天技术2', '¥350')">
                            <div class="category-header-component">
                                <span class="category-title-component">聊天技术2 <span class="category-price-component">(¥350)</span></span>
                                <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow"></i>
                            </div>
                            <div class="category-content">
                                <div class="mt-2 space-y-1" id="chat-tech-2-videos">
                                    <!-- 视频项将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>

                        <div class="category-card" onclick="handleCategoryClick(event, this, '术：聊天技术系列·聊天技术3', '¥300')">
                            <div class="category-header-component">
                                <span class="category-title-component">聊天技术3 <span class="category-price-component">(¥300)</span></span>
                                <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow"></i>
                            </div>
                            <div class="category-content">
                                <div class="mt-2 space-y-1" id="chat-tech-3-videos">
                                    <!-- 视频项将通过JavaScript动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 全套优惠 -->
                <div class="series-card" onclick="showPayment(event, '全套课程', '¥1600')" style="background: white; color: #374151;">
                    <div class="series-header-component">
                        <div class="series-header-left">
                            <i class="fas fa-crown series-icon-component text-blue-500"></i>
                            <h3 class="series-title-component">全套课程 <span class="series-price-component">(¥1600)</span></h3>
                        </div>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>

            <!-- 搜索弹窗 -->
            <div class="search-modal" id="searchModal" onclick="event.stopPropagation()">
                <div class="modal-header-component">
                    <h3 class="modal-title-component">搜索课程</h3>
                    <button class="modal-close-component" onclick="closeSearchModal()">×</button>
                </div>

                <!-- 搜索输入框 -->
                <div class="search-input-container">
                    <input type="text" class="search-input" id="searchInput" placeholder="搜索视频标题..." autocomplete="off" oninput="handleSearchInput()" onkeypress="handleSearchKeypress(event)" onfocus="showSearchDropdown()" onblur="handleSearchBlur()">
                    <button class="search-clear-btn" id="searchClearBtn" onclick="clearSearch()">×</button>

                    <!-- 搜索下拉框 -->
                    <div class="search-dropdown" id="searchDropdown">
                        <div class="search-dropdown-header">
                            <span>搜索历史</span>
                            <span class="search-dropdown-clear" onclick="clearSearchHistory(event)">清空</span>
                        </div>
                        <div id="searchDropdownList">
                            <!-- 历史记录项将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 搜索历史 -->
                <div class="search-history-section" id="searchHistorySection">
                    <div class="search-section-title">搜索历史</div>
                    <div class="search-history-list" id="searchHistoryList">
                        <div class="search-history-item" onclick="searchKeyword('恋爱技巧')">恋爱技巧</div>
                        <div class="search-history-item" onclick="searchKeyword('聊天方法')">聊天方法</div>
                        <div class="search-history-item" onclick="searchKeyword('约会攻略')">约会攻略</div>
                        <div class="search-history-item" onclick="searchKeyword('表白技巧')">表白技巧</div>
                    </div>
                </div>

                <!-- 搜索建议/结果 -->
                <div class="search-suggestions" id="searchSuggestions">
                    <div class="search-empty-state" id="searchEmptyState">
                        输入关键词开始搜索
                    </div>
                </div>
            </div>

                        <!-- 支付弹窗 -->
            <div class="modal-component modal-small payment-modal" id="paymentModal">
                <div class="modal-header-component" style="padding-top: 0px;">
                    <button class="modal-close-component" onclick="closePayment()">×</button>
                </div>
                <div class="mb-1" style="margin-top: -10px;">
                    <h3 class="text-base font-medium text-blue-600 text-left" id="paymentTitle" style="color: #667eea;">系列名·分类名</h3>
                </div>
                <div class="text-center mb-2" style="margin-top: 8px;">
                    <p class="text-3xl font-bold text-red-500" id="paymentPrice">¥100</p>
                    <p class="text-xs text-gray-500 mt-1">购买后可享受30%分享分成</p>
                </div>
                <div class="flex justify-center" style="margin-top: 5px;">
                    <button class="w-4/5 bg-green-500 text-white py-3 rounded-lg font-semibold text-lg" style="background-color: #4caf50;" onclick="goToPaymentPage()">
                        立即支付
                    </button>
                </div>
            </div>

            <!-- 分享弹窗 -->
            <div class="modal-component modal-medium share-modal" id="shareModal">
                <div class="modal-header-component">
                    <h3 class="modal-title-component">分享推广计划</h3>
                    <button class="modal-close-component" onclick="closeShareModal()">×</button>
                </div>

                <div class="text-center mb-4">
                    <p class="text-sm text-green-600 font-medium">分享成功购买可获得30%收益</p>
                    <p class="text-xs text-gray-500 mt-1">邀请好友一起学习，共同成长</p>
                </div>

                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="share-option-component" onclick="shareToWechat()">
                        <div class="share-icon-component share-icon-wechat">
                            <i class="fab fa-weixin"></i>
                        </div>
                        <span class="share-label-component">微信好友</span>
                    </div>

                    <div class="share-option-component" onclick="shareToMoments()">
                        <div class="share-icon-component share-icon-wechat">
                            <i class="fas fa-users"></i>
                        </div>
                        <span class="share-label-component">朋友圈</span>
                    </div>

                    <div class="share-option-component" onclick="copyShareLink()">
                        <div class="share-icon-component share-icon-link">
                            <i class="fas fa-link"></i>
                        </div>
                        <span class="share-label-component">复制链接</span>
                    </div>
                </div>

                <div class="text-center">
                    <button class="w-full bg-gray-100 text-gray-600 py-3 rounded-lg font-medium" onclick="closeShareModal()">
                        取消
                    </button>
                </div>
            </div>

            <!-- 缓存提示弹窗 -->
            <div class="cache-modal-overlay" id="cacheModal" style="display: none;" onclick="event.stopPropagation()">
                <div class="cache-modal-content">
                    <button class="cache-modal-close" onclick="closeCacheModal()">×</button>
                    <p class="cache-video-title" id="cacheVideoTitle">视频标题</p>
                    <button class="cache-download-btn" onclick="startVideoCache()">
                        <i class="fas fa-download"></i>
                        缓存到本地
                    </button>
                </div>
            </div>


            <!-- 底部导航 -->
            <div id="bottomNavigation"></div>
        </div>
    </div>

    <!-- Toast提示容器 -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div style="text-align: center;">
            <div class="loading-spinner"></div>
            <div class="loading-text" id="loadingText">加载中...</div>
        </div>
    </div>

    <!-- 网络状态指示器 -->
    <div class="network-status" id="networkStatus">
        <span id="networkStatusText">网络连接异常</span>
        <button class="retry-button" id="retryButton" onclick="retryConnection()">重试</button>
    </div>

    <script>
        // ========== 底部导航初始化 ==========
        function initBottomNav() {
            if (window.ComponentTemplates) {
                // 初始化导航功能
                ComponentTemplates.initBottomNavigation();
                
                // 渲染导航组件
                const navContainer = document.getElementById('bottomNavigation');
                if (navContainer) {
                    navContainer.innerHTML = ComponentTemplates.createBottomNavigation({
                        currentPage: 'home'
                    });
                }
            }
        }

        // ========== 错误处理和状态管理系统 ==========

        // Toast提示系统
        const ToastManager = {
            container: null,

            init() {
                this.container = document.getElementById('toastContainer');
                if (!this.container) {
                    console.warn('Toast container not found');
                }
            },

            show(message, type = 'info', duration = 3000) {
                if (!this.container) {
                    console.warn('Toast container not initialized');
                    return;
                }

                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.textContent = message;

                this.container.appendChild(toast);

                // 触发显示动画
                requestAnimationFrame(() => {
                    toast.classList.add('show');
                });

                // 自动移除
                setTimeout(() => {
                    this.remove(toast);
                }, duration);

                return toast;
            },

            remove(toast) {
                if (toast && toast.parentNode) {
                    toast.classList.remove('show');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }
            },

            success(message, duration) {
                return this.show(message, 'success', duration);
            },

            error(message, duration) {
                return this.show(message, 'error', duration);
            },

            warning(message, duration) {
                return this.show(message, 'warning', duration);
            },

            info(message, duration) {
                return this.show(message, 'info', duration);
            }
        };

        // 加载状态管理
        const LoadingManager = {
            overlay: null,
            loadingText: null,
            isLoading: false,

            init() {
                this.overlay = document.getElementById('loadingOverlay');
                this.loadingText = document.getElementById('loadingText');
                if (!this.overlay || !this.loadingText) {
                    console.warn('Loading elements not found');
                }
            },

            show(text = '加载中...') {
                if (!this.overlay || !this.loadingText) return;

                this.isLoading = true;
                this.loadingText.textContent = text;
                this.overlay.style.display = 'flex';

                // 防止长时间加载
                setTimeout(() => {
                    if (this.isLoading) {
                        this.hide();
                        ToastManager.error('加载超时，请重试');
                    }
                }, 30000); // 30秒超时
            },

            hide() {
                if (!this.overlay) return;

                this.isLoading = false;
                this.overlay.style.display = 'none';
            },

            updateText(text) {
                if (this.loadingText) {
                    this.loadingText.textContent = text;
                }
            }
        };

        // 网络状态管理
        const NetworkManager = {
            statusElement: null,
            statusText: null,
            retryButton: null,
            isOnline: navigator.onLine,
            retryCount: 0,
            maxRetries: 3,

            init() {
                this.statusElement = document.getElementById('networkStatus');
                this.statusText = document.getElementById('networkStatusText');
                this.retryButton = document.getElementById('retryButton');

                if (!this.statusElement || !this.statusText || !this.retryButton) {
                    console.warn('Network status elements not found');
                    return;
                }

                // 监听网络状态变化
                window.addEventListener('online', () => this.handleOnline());
                window.addEventListener('offline', () => this.handleOffline());

                // 初始状态检查
                this.updateStatus();
            },

            handleOnline() {
                this.isOnline = true;
                this.retryCount = 0;
                this.updateStatus();
                ToastManager.success('网络连接已恢复');
            },

            handleOffline() {
                this.isOnline = false;
                this.updateStatus();
                ToastManager.error('网络连接已断开');
            },

            updateStatus() {
                if (!this.statusElement || !this.statusText) return;

                if (this.isOnline) {
                    this.statusElement.style.display = 'none';
                } else {
                    this.statusText.textContent = '网络连接异常';
                    this.statusElement.className = 'network-status';
                    this.statusElement.style.display = 'block';
                }
            },

            showRetryStatus() {
                if (!this.statusElement || !this.statusText) return;

                this.statusText.textContent = `连接失败，正在重试... (${this.retryCount}/${this.maxRetries})`;
                this.statusElement.style.display = 'block';
            },

            async testConnection() {
                try {
                    const response = await fetch('/', {
                        method: 'HEAD',
                        cache: 'no-cache',
                        signal: AbortSignal.timeout(5000)
                    });
                    return response.ok;
                } catch (error) {
                    console.warn('Connection test failed:', error);
                    return false;
                }
            }
        };

        // API请求管理
        const ApiManager = {
            baseUrl: '', // 实际项目中设置API基础URL

            async request(url, options = {}) {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

                try {
                    // 检查网络状态
                    if (!navigator.onLine) {
                        throw new Error('网络连接不可用');
                    }

                    const response = await fetch(url, {
                        ...options,
                        signal: controller.signal,
                        headers: {
                            'Content-Type': 'application/json',
                            ...options.headers
                        }
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    return await response.json();
                } catch (error) {
                    clearTimeout(timeoutId);

                    if (error.name === 'AbortError') {
                        throw new Error('请求超时，请重试');
                    }

                    throw error;
                }
            },

            async get(url, options = {}) {
                return this.request(url, { ...options, method: 'GET' });
            },

            async post(url, data, options = {}) {
                return this.request(url, {
                    ...options,
                    method: 'POST',
                    body: JSON.stringify(data)
                });
            }
        };

        // 操作重试管理
        const RetryManager = {
            async withRetry(operation, maxRetries = 3, delay = 1000) {
                let lastError;

                for (let i = 0; i <= maxRetries; i++) {
                    try {
                        return await operation();
                    } catch (error) {
                        lastError = error;

                        if (i === maxRetries) {
                            break;
                        }

                        // 显示重试状态
                        NetworkManager.retryCount = i + 1;
                        NetworkManager.showRetryStatus();

                        // 等待后重试
                        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
                    }
                }

                throw lastError;
            }
        };

        // 全局重试连接函数
        async function retryConnection() {
            const retryButton = document.getElementById('retryButton');
            if (retryButton) {
                retryButton.disabled = true;
                retryButton.textContent = '重试中...';
            }

            try {
                LoadingManager.show('正在重新连接...');

                const isConnected = await NetworkManager.testConnection();

                if (isConnected) {
                    NetworkManager.isOnline = true;
                    NetworkManager.retryCount = 0;
                    NetworkManager.updateStatus();
                    ToastManager.success('连接成功');
                } else {
                    throw new Error('连接测试失败');
                }
            } catch (error) {
                console.error('Retry connection failed:', error);
                ToastManager.error('重新连接失败，请检查网络设置');
            } finally {
                LoadingManager.hide();

                if (retryButton) {
                    retryButton.disabled = false;
                    retryButton.textContent = '重试';
                }
            }
        }

        // ========== 原有代码开始 ==========

        // 动态徽章生成系统
        function getBadgeLevel(watchCount) {
            if (watchCount >= 10) {
                return 'badge-level-legendary';  // 10次及以上使用传奇样式
            } else if (watchCount >= 0) {
                return `badge-level-${watchCount}`;  // 0-9次使用对应等级
            }
            return 'badge-level-0';  // 默认
        }

        // 动态生成徽章HTML
        function createBadge(watchCount) {
            const level = getBadgeLevel(watchCount);
            return `<span class="badge-component ${level}">×${watchCount}</span>`;
        }

        // 批量更新页面中的徽章
        function updateAllBadges() {
            // 这个函数可以用于动态更新页面中的所有徽章
            // 实际使用时可以从服务器获取最新的观看次数数据
            const badges = document.querySelectorAll('.badge-component');
            badges.forEach(badge => {
                const text = badge.textContent;
                const match = text.match(/×(\d+)/);
                if (match) {
                    const count = parseInt(match[1]);
                    const newLevel = getBadgeLevel(count);
                    // 移除所有旧的等级类
                    badge.className = badge.className.replace(/badge-level-\w+/g, '');
                    // 添加新的等级类
                    badge.classList.add(newLevel);
                }
            });
        }

        // 统一状态管理（基于数据的购买状态）
        const AppState = {
            // 检查是否已购买 - 基于数据中的购买状态
            isPurchased(title, price) {
                // 优先使用新的数据管理系统
                if (window.DataManager && window.DataManager.initialized) {
                    return DataManager.isPurchased(title);
                }
                
                // 回退逻辑：检查数据中的购买状态
                try {
                    if (window.AppData && AppData.getCategory) {
                        const category = AppData.getCategory(title);
                        if (category) {
                            // 免费内容总是已购买
                            if (category.isFree) {
                                return true;
                            }
                            // 根据数据中的购买状态判断
                            return category.isPurchased || false;
                        }
                    }
                    
                    // 如果找不到数据，检查是否包含"免费"关键字
                    return title.includes('免费');
                } catch (error) {
                    console.error('Error checking purchase status in AppState:', error);
                    // 出错时，免费内容返回true，其他返回false
                    return title.includes('免费');
                }
            },

            // 购买内容（更新数据中的购买状态）
            addPurchased(title) {
                try {
                    // 同步到新的数据管理系统
                    if (window.DataManager && window.DataManager.initialized) {
                        DataManager.purchaseContent(title, '');
                    }
                    
                    // 更新数据中的购买状态
                    if (window.AppData && AppData.getCategory) {
                        const category = AppData.getCategory(title);
                        if (category) {
                            category.isPurchased = true;
                            console.log(`已购买: ${title}`);
                            
                            // 触发UI更新
                            setTimeout(() => {
                                updateCategoryVideoLockStatus(title);
                            }, 100);
                        }
                    }
                } catch (error) {
                    console.error('Error adding purchased item:', error);
                }
            }
        };

        // 视频缓存管理
        const VideoCacheManager = {
            cachedVideos: new Set(), // 已缓存的视频
            downloadingVideos: new Map(), // 正在下载的视频 {videoId: progress}
            currentVideoInfo: null, // 当前要缓存的视频信息

            async init() {
                // 初始化下载队列（用于管理正在下载的视频）
                this.downloadingVideos = new Map();

                // 检查localStorage环境
                this.checkLocalStorageEnvironment();

                // 从存储恢复缓存状态
                await this.loadCacheStateFromStorage();

                // 暴露调试工具到全局
                this.exposeDebugTools();
            },

            // 从存储加载缓存状态（支持localStorage和文件存储）
            async loadCacheStateFromStorage() {
                try {
                    // 方法1：首先从AppData中读取初始缓存状态（app-data.js文件中的基础状态）
                    let appDataCachedCount = 0;
                    if (window.AppData && AppData.categories) {
                        Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                            if (categoryData.videos) {
                                categoryData.videos.forEach(video => {
                                    if (video.isCached === true) {
                                        appDataCachedCount++;
                                        console.log(`🎬 AppData中的已缓存视频: ${video.title} (${video.id}) - ${categoryKey}`);
                                    }
                                });
                            }
                        });
                    }
                    console.log(`🎬 AppData中总共有 ${appDataCachedCount} 个已缓存视频`);

                    // 方法2：从localStorage加载最新缓存状态（优先级更高，会覆盖AppData中的状态）
                    const savedCacheState = localStorage.getItem('videoCacheState');
                    console.log(`🎬 localStorage中的缓存状态:`, savedCacheState);
                    if (savedCacheState) {
                        const cacheState = JSON.parse(savedCacheState);

                        // 先清除AppData中所有的缓存状态
                        if (window.AppData && AppData.categories) {
                            Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                                if (categoryData.videos) {
                                    categoryData.videos.forEach(video => {
                                        video.isCached = false; // 先全部设为false
                                    });
                                }
                            });
                        }

                        // 然后应用localStorage中的最新状态
                        let localStorageCachedCount = 0;
                        Object.keys(cacheState).forEach(videoId => {
                            if (cacheState[videoId]) {
                                // 尝试两种格式：真实视频ID和旧格式ID
                                let foundVideo = false;
                                
                                // 方法1：直接用videoId作为真实视频ID查找
                                for (const [categoryKey, categoryData] of Object.entries(AppData.categories)) {
                                    if (categoryData.videos) {
                                        const video = categoryData.videos.find(v => v.id === videoId);
                                        if (video) {
                                            video.isCached = true;
                                            localStorageCachedCount++;
                                            foundVideo = true;
                                            console.log(`🎬 从localStorage恢复缓存状态 (真实ID): ${video.title} - ${categoryKey}`);
                                            break;
                                        }
                                    }
                                }
                                
                                // 方法2：如果没找到，尝试解析旧格式 "category|||title"
                                if (!foundVideo && videoId.includes('|||')) {
                                    const parts = videoId.split('|||');
                                    if (parts.length === 2) {
                                        const category = parts[0];
                                        const title = parts[1];

                                        const categoryData = AppData.categories[category];
                                        if (categoryData && categoryData.videos) {
                                            const video = categoryData.videos.find(v => v.title === title);
                                            if (video) {
                                                video.isCached = true;
                                                localStorageCachedCount++;
                                                console.log(`🎬 从localStorage恢复缓存状态 (旧格式): ${title} - ${category}`);
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        // 检查是否有新缓存的视频
                        if (localStorageCachedCount > appDataCachedCount) {
                            // 静默处理，不显示提示信息
                        }
                    }

                    // 方法3：尝试从独立的缓存文件加载（为安卓app准备）
                    try {
                        const response = await fetch('data/cache-state.json');
                        if (response.ok) {
                            const fileCacheState = await response.json();
                            // 这里可以作为备用方案
                        }
                    } catch (fileError) {
                        // 文件不存在是正常情况，不需要提示
                    }

                } catch (error) {
                    console.error('❌ 加载缓存状态失败:', error);
                }
            },

            // 保存缓存状态到存储（支持localStorage和文件存储）
            async saveCacheStateToStorage() {
                try {
                    const cacheState = {};

                    // 从AppData中收集所有缓存状态
                    if (window.AppData && AppData.categories) {
                        Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                            if (categoryData.videos) {
                                categoryData.videos.forEach(video => {
                                    if (video.isCached) {
                                        // 构建完整的分类路径
                                        let fullCategoryPath = categoryKey;

                                        // 查找该分类所属的系列
                                        if (window.AppData.series) {
                                            Object.entries(AppData.series).forEach(([seriesKey, seriesData]) => {
                                                if (seriesData.categories && seriesData.categories[categoryKey]) {
                                                    fullCategoryPath = `${seriesKey}·${categoryKey}`;
                                                }
                                            });
                                        }

                                        // 优先使用真实的视频ID，fallback到旧格式以保持兼容性
                                        const videoId = video.id || `${categoryKey}|||${video.title}`;
                                        cacheState[videoId] = true;
                                    }
                                });
                            }
                        });
                    }

                    // 检查localStorage是否可用
                    if (typeof(Storage) === "undefined") {
                        console.error('❌ localStorage不支持');
                        return false;
                    }

                    // 保存到localStorage（Web版本）
                    try {
                        const jsonString = JSON.stringify(cacheState);
                        localStorage.setItem('videoCacheState', jsonString);

                        // 立即验证保存是否成功
                        const savedData = localStorage.getItem('videoCacheState');
                        if (savedData !== jsonString) {
                            console.error('❌ localStorage保存验证失败');
                            return false;
                        }
                    } catch (localStorageError) {
                        console.error('❌ localStorage保存失败:', localStorageError);
                        return false;
                    }

                    // 尝试保存到文件（安卓app版本）
                    try {
                        // 注意：在Web环境中这会失败，但在安卓app中可以实现
                        await this.saveCacheStateToFile(cacheState);
                    } catch (fileError) {
                        // Web环境中文件保存失败是正常的，不需要提示
                    }

                    return true;
                } catch (error) {
                    console.error('❌ 保存缓存状态失败:', error);
                    return false;
                }
            },

            // 获取localStorage使用情况
            getLocalStorageInfo() {
                try {
                    let totalSize = 0;
                    let itemCount = 0;

                    for (let key in localStorage) {
                        if (localStorage.hasOwnProperty(key)) {
                            totalSize += localStorage[key].length + key.length;
                            itemCount++;
                        }
                    }

                    return {
                        itemCount: itemCount,
                        totalSize: totalSize,
                        totalSizeKB: (totalSize / 1024).toFixed(2),
                        videoCacheStateExists: localStorage.getItem('videoCacheState') !== null,
                        videoCacheStateSize: localStorage.getItem('videoCacheState')?.length || 0
                    };
                } catch (error) {
                    return { error: error.message };
                }
            },

            // 保存缓存状态到文件（为安卓app准备）
            async saveCacheStateToFile(cacheState) {
                // 在Web环境中，这个方法会失败
                // 在安卓app中，可以通过WebView接口实现文件写入
                if (window.AndroidInterface && window.AndroidInterface.saveCacheState) {
                    // 安卓app接口
                    window.AndroidInterface.saveCacheState(JSON.stringify(cacheState));
                } else {
                    // Web环境的模拟实现（实际不会写入文件）
                    // 在实际的安卓app中，这里会调用原生方法写入文件
                }

                // 同时将缓存状态写入app-data.js（为安卓app兼容性准备）
                await this.updateAppDataWithCacheState();
            },

            // 将缓存状态更新到app-data.js中（为安卓app准备）
            async updateAppDataWithCacheState() {
                try {
                    if (!window.AppData || !AppData.categories) {
                        console.warn('AppData不可用，跳过app-data.js更新');
                        return;
                    }

                    // 使用当前的AppData（已经包含最新的缓存状态）
                    const updatedAppData = AppData;

                    // 生成新的app-data.js文件内容（会自动清理冗余属性并保持缓存状态）
                    const fileContent = this.generateAppDataFileContent(updatedAppData);

                    // 在安卓app中，这里会实际写入文件
                    if (window.AndroidInterface && window.AndroidInterface.saveAppData) {
                        window.AndroidInterface.saveAppData(fileContent);
                    } else {
                        // Web环境中的模拟实现
                        // 实际不会写入文件
                    }

                } catch (error) {
                    console.error('❌ 更新app-data.js失败:', error);
                }
            },

            // 生成app-data.js文件内容
            generateAppDataFileContent(appData) {
                // 深度清理AppData，移除冗余属性并添加缓存状态
                const cleanedAppData = JSON.parse(JSON.stringify(appData));

                // 清理系列数据中的冗余属性
                Object.entries(cleanedAppData.series).forEach(([seriesKey, seriesData]) => {
                    // 系列级别保留isFree属性，因为它是必要的
                });

                // 清理分类数据中的冗余属性
                Object.entries(cleanedAppData.categories).forEach(([categoryKey, categoryData]) => {
                    // 分类级别保留isFree属性，因为它是必要的

                    if (categoryData.videos) {
                        categoryData.videos.forEach(video => {
                            // 删除视频级别的免费属性，因为分类级别已经控制了
                            delete video.isFree;

                            // 确保每个视频都有isCached属性（默认为false）
                            if (video.isCached === undefined) {
                                video.isCached = false;
                            }
                        });
                    }
                });

                // 生成JavaScript文件内容
                const jsContent = `// 水幕应用数据 - 自动生成于 ${new Date().toISOString()}
// 注意：此文件由系统自动维护，包含用户的缓存状态

const AppData = ${JSON.stringify(cleanedAppData, null, 4)};

// 导出数据（兼容不同的模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppData;
}
if (typeof window !== 'undefined') {
    window.AppData = AppData;
}`;

                return jsContent;
            },

            // 检查localStorage环境
            checkLocalStorageEnvironment() {
                try {
                    // 检查localStorage是否可用
                    if (typeof(Storage) === "undefined") {
                        console.error('❌ 浏览器不支持localStorage');
                        return;
                    }

                    // 测试localStorage读写
                    const testKey = 'localStorage_test_' + Date.now();
                    const testValue = 'test_value';

                    localStorage.setItem(testKey, testValue);
                    const retrievedValue = localStorage.getItem(testKey);
                    localStorage.removeItem(testKey);

                    if (retrievedValue !== testValue) {
                        console.error('❌ localStorage读写测试失败');
                    }

                } catch (error) {
                    console.error('❌ localStorage环境检查失败:', error);
                }
            },

            // 获取存储配额信息
            async getStorageQuota() {
                try {
                    if ('storage' in navigator && 'estimate' in navigator.storage) {
                        const estimate = await navigator.storage.estimate();
                        return {
                            quota: estimate.quota,
                            usage: estimate.usage,
                            quotaMB: (estimate.quota / 1024 / 1024).toFixed(2),
                            usageMB: (estimate.usage / 1024 / 1024).toFixed(2)
                        };
                    }
                    return 'API不可用';
                } catch (error) {
                    return 'API调用失败: ' + error.message;
                }
            },

            // 暴露调试工具到全局
            exposeDebugTools() {
                // 缓存诊断工具
                window.diagnoseCacheStorage = () => {
                    console.log('🔧 缓存存储诊断工具');
                    console.log('==================');

                    // localStorage基本信息
                    console.log('📊 localStorage信息:', this.getLocalStorageInfo());

                    // 当前缓存状态
                    const currentCache = localStorage.getItem('videoCacheState');
                    if (currentCache) {
                        try {
                            const parsed = JSON.parse(currentCache);
                            console.log('💾 当前缓存状态:', parsed);
                            console.log('📈 缓存视频数量:', Object.keys(parsed).length);
                        } catch (e) {
                            console.error('❌ 缓存数据解析失败:', e);
                        }
                    } else {
                        console.log('📭 没有找到缓存数据');
                    }

                    // AppData中的缓存状态
                    if (window.AppData && AppData.categories) {
                        let cachedCount = 0;
                        Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                            if (categoryData.videos) {
                                categoryData.videos.forEach(video => {
                                    if (video.isCached) {
                                        cachedCount++;
                                        console.log(`✅ AppData中已缓存: ${video.title}`);
                                    }
                                });
                            }
                        });
                        console.log(`📊 AppData中缓存视频总数: ${cachedCount}`);
                    }

                    console.log('==================');
                };

                // 强制保存缓存状态
                window.forceSaveCacheState = async () => {
                    console.log('🔄 强制保存缓存状态...');
                    const result = await this.saveCacheStateToStorage();
                    if (result) {
                        console.log('✅ 强制保存成功');
                    } else {
                        console.error('❌ 强制保存失败');
                    }
                    return result;
                };

                // 清除所有缓存状态
                window.clearAllCacheState = () => {
                    console.log('🗑️ 清除所有缓存状态...');
                    try {
                        localStorage.removeItem('videoCacheState');
                        console.log('✅ localStorage缓存已清除');

                        // 清除AppData中的缓存状态
                        if (window.AppData && AppData.categories) {
                            Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                                if (categoryData.videos) {
                                    categoryData.videos.forEach(video => {
                                        video.isCached = false;
                                    });
                                }
                            });
                            console.log('✅ AppData缓存状态已清除');
                        }

                        // 刷新页面显示
                        if (window.refreshAllCategories) {
                            refreshAllCategories();
                        }

                    } catch (error) {
                        console.error('❌ 清除缓存失败:', error);
                    }
                };

                // 调试工具已加载，但不显示详细信息
            },







            // 根据标题和分类查找视频ID
            getVideoId(title, category) {
                // 使用DataManager的统一方法
                if (window.DataManager && DataManager.getVideoId) {
                    return DataManager.getVideoId(title, category);
                }
                
                // 回退到原有逻辑
                if (!window.AppData || !AppData.categories) {
                    console.warn('AppData未加载，使用fallback方法生成ID');
                    return `${category}|||${title}`;
                }

                // 在指定分类中查找视频
                if (category && AppData.categories[category] && AppData.categories[category].videos) {
                    const video = AppData.categories[category].videos.find(v => v.title === title);
                    if (video && video.id) {
                        return video.id;
                    }
                }

                // 如果在指定分类中没找到，在所有分类中查找
                for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                    if (categoryData.videos) {
                        const video = categoryData.videos.find(v => v.title === title);
                        if (video && video.id) {
                            return video.id;
                        }
                    }
                }

                console.warn(`未找到视频ID: ${title} in ${category}，使用fallback方法`);
                return `${category}|||${title}`;
            },

            // 检查视频是否已缓存（基于视频ID）
            isCached(title, category) {
                // 使用DataManager的统一方法
                if (window.DataManager && DataManager.isCached) {
                    return DataManager.isCached(title, category);
                }
                
                // 回退到原有逻辑
                // 首先通过ID查找视频
                const videoId = this.getVideoId(title, category);
                
                // 如果获取到真实ID，通过ID查找
                if (videoId && !videoId.includes('|||') && !videoId.includes('_')) {
                    // 这是真实的视频ID，在AppData中查找
                    for (const [cat, categoryData] of Object.entries(window.AppData?.categories || {})) {
                        if (categoryData.videos) {
                            const video = categoryData.videos.find(v => v.id === videoId);
                            if (video) {
                                console.log(`🏠 缓存检查(ID): ${title} - ${category} = ${!!video.isCached}`, video);
                                return !!video.isCached;
                            }
                        }
                    }
                }
                
                // 如果没有找到真实ID，回退到标题匹配
                const categoryData = window.AppData?.categories?.[category];
                if (categoryData && categoryData.videos) {
                    const video = categoryData.videos.find(v => v.title === title);
                    const result = video ? !!video.isCached : false;
                    console.log(`🏠 缓存检查(标题): ${title} - ${category} = ${result}`, video);
                    return result;
                }
                
                console.log(`🏠 缓存检查: ${title} - ${category} = false (未找到)`);
                return false;
            },

            // 检查视频是否正在下载
            isDownloading(title, category) {
                const videoId = this.getVideoId(title, category);
                return this.downloadingVideos.has(videoId);
            },

            // 显示缓存弹窗
            showCacheModal(title, category, fromSearch = false) {
                // 显示缓存弹窗时关闭可能存在的支付弹窗
                if (typeof closePayment === 'function') {
                    closePayment();
                }
                
                this.currentVideoInfo = { title, category, fromSearch };
                const modal = document.getElementById('cacheModal');
                const titleElement = document.getElementById('cacheVideoTitle');

                if (modal && titleElement) {
                    titleElement.textContent = title;
                    modal.style.display = 'flex';
                }
            },

            // 关闭缓存弹窗
            closeCacheModal() {
                const modal = document.getElementById('cacheModal');
                if (modal) {
                    modal.style.display = 'none';
                }
                this.currentVideoInfo = null;
            },

            // 开始缓存视频
            async startCache() {
                if (!this.currentVideoInfo) return;

                const { title, category, fromSearch } = this.currentVideoInfo;
                const videoId = this.getVideoId(title, category);

                try {
                    // 关闭弹窗
                    this.closeCacheModal();

                    // 开始下载
                    this.downloadingVideos.set(videoId, 0);
                    this.updateVideoStatus(title, category);
                    
                    // 如果来自搜索，更新搜索结果状态
                    if (fromSearch) {
                        this.updateSearchResultStatus(title, category);
                    }

                    // 模拟下载进度
                    await this.simulateDownload(videoId, title, category, fromSearch);

                    // 下载完成
                    this.downloadingVideos.delete(videoId);
                    
                    // 更新数据源中的缓存状态
                    this.updateAppDataCacheStatus(title, category, true);

                    // 保存缓存状态到localStorage
                    this.saveCacheStateToStorage();
                    
                    // 重新生成该分类的视频项，确保缓存状态正确显示
                    if (categoryData) {
                        generateCategoryVideos(category, categoryData);
                    }
                    
                    // 如果来自搜索，更新搜索结果状态
                    if (fromSearch) {
                        this.updateSearchResultStatus(title, category);
                    }

                    // 缓存完成后不自动跳转，用户可以继续缓存其他视频
                    // 用户需要再次点击已缓存的视频才会跳转到播放页

                } catch (error) {
                    console.error('Cache failed:', error);
                    this.downloadingVideos.delete(videoId);
                    this.updateVideoStatus(title, category);
                    
                    // 如果来自搜索，更新搜索结果状态
                    if (fromSearch) {
                        this.updateSearchResultStatus(title, category);
                    }
                    
                    // 缓存失败，静默处理
                }
            },

            // 模拟下载进度
            async simulateDownload(videoId, title, category, fromSearch = false) {
                return new Promise((resolve) => {
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += Math.random() * 15 + 5; // 每次增加5-20%
                        if (progress >= 100) {
                            progress = 100;
                            clearInterval(interval);
                            resolve();
                        }

                        this.downloadingVideos.set(videoId, Math.floor(progress));
                        this.updateVideoStatus(title, category);
                        
                        // 如果来自搜索，同时更新搜索结果状态
                        if (fromSearch) {
                            this.updateSearchResultStatus(title, category);
                        }
                    }, 200); // 每200ms更新一次
                });
            },

            // 更新搜索结果中的视频状态显示
            updateSearchResultStatus(title, category) {
                const videoId = this.getVideoId(title, category);
                
                // 查找搜索结果中对应的视频元素
                const searchItems = document.querySelectorAll('.search-suggestion-item');
                searchItems.forEach(item => {
                    const itemTitle = item.getAttribute('data-title');
                    const itemCategory = item.getAttribute('data-category');
                    
                    if (itemTitle === title && itemCategory === category) {
                        const titleElement = item.querySelector('.search-suggestion-title');
                        if (titleElement) {
                            // 确定新的状态文本
                            let statusText = '';
                            let statusClass = '';

                            if (this.isDownloading(title, category)) {
                                const progress = this.downloadingVideos.get(videoId);
                                statusText = `(${progress}%)`;
                                statusClass = 'downloading';
                            } else if (this.isCached(title, category)) {
                                statusText = '(已缓存)';
                                statusClass = 'cached';
                            }

                            // 更新元素文本内容
                            if (statusText) {
                                titleElement.innerHTML = `${title}<span class="cache-status ${statusClass}">${statusText}</span>`;
                            } else {
                                titleElement.textContent = title;
                            }
                        }
                    }
                });
            },

            // 更新单个视频状态显示
            updateVideoStatus(title, category) {
                // 检查该分类是否已购买或免费
                const categoryData = window.AppData?.categories?.[category];
                if (!categoryData || (!categoryData.isFree && !categoryData.isPurchased)) {
                    // 未购买的分类，不显示缓存状态
                    return;
                }
                
                const videoId = this.getVideoId(title, category);

                // 查找对应的视频元素
                const videoElements = document.querySelectorAll('.video-title-text');
                videoElements.forEach(element => {
                    // 获取原始标题（去除可能已存在的状态信息）
                    const originalText = element.textContent.replace(/\s*\([^)]*\)\s*$/, '').trim();

                    if (originalText === title) {
                        // 确定新的状态文本
                        let statusText = '';
                        let statusClass = '';

                        if (this.isDownloading(title, category)) {
                            const progress = this.downloadingVideos.get(videoId);
                            statusText = `(${progress}%)`;
                            statusClass = 'downloading';
                        } else if (this.isCached(title, category)) {
                            statusText = '(已缓存)';
                            statusClass = 'cached';
                        }

                        // 更新元素文本内容
                        if (statusText) {
                            element.innerHTML = `${title}<span class="cache-status ${statusClass}">${statusText}</span>`;
                        } else {
                            element.textContent = title;
                        }
                    }
                });
            },

            // 更新所有视频状态
            updateAllVideoStatus() {
                const videoElements = document.querySelectorAll('.video-title-text');
                videoElements.forEach(element => {
                    const title = element.textContent.trim();
                    // 通过DOM结构推断category（这里简化处理）
                    const category = this.inferCategoryFromElement(element);
                    if (category) {
                        // 检查该分类是否已购买或免费，只有已购买/免费的视频才显示缓存状态
                        const categoryData = window.AppData?.categories?.[category];
                        if (categoryData && (categoryData.isFree || categoryData.isPurchased)) {
                            this.updateVideoStatus(title, category);
                        } else {
                            // 未购买的视频，确保不显示缓存状态
                            const originalText = element.textContent.replace(/\s*\([^)]*\)\s*$/, '').trim();
                            element.textContent = originalText;
                        }
                    }
                });
            },

            // 从DOM元素推断分类（改进版本）
            inferCategoryFromElement(element) {
                const seriesCard = element.closest('.series-card');
                if (seriesCard) {
                    const seriesTitle = seriesCard.querySelector('.series-title-component');
                    const categoryCard = element.closest('.category-card');
                    if (categoryCard && seriesTitle) {
                        const categoryTitle = categoryCard.querySelector('.category-title-component');
                        if (categoryTitle) {
                            // 清理标题文本，移除价格信息
                            const seriesTitleText = seriesTitle.textContent.replace(/\s*\(¥\d+\).*$/, '').trim();
                            const categoryTitleText = categoryTitle.textContent.replace(/\s*\(¥\d+\).*$/, '').trim();
                            return `${seriesTitleText}·${categoryTitleText}`;
                        }
                    }
                }
                return null;
            },



            // 播放视频
            playVideo(title, category) {
                // 跳转到播放页面
                window.location.href = `02-视频播放页.html?video=${encodeURIComponent(title)}&category=${encodeURIComponent(category)}&cached=true`;
            },

            // 更新AppData中的缓存状态（基于ID优先，标题备用）
            updateAppDataCacheStatus(title, category, isCached) {
                if (!window.AppData || !AppData.categories) {
                    console.warn('AppData不可用，无法更新缓存状态');
                    return;
                }

                // 首先尝试通过ID查找
                const videoId = this.getVideoId(title, category);
                
                if (videoId && !videoId.includes('|||') && !videoId.includes('_')) {
                    // 这是真实的视频ID，通过ID查找并更新
                    for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                        if (categoryData.videos) {
                            const video = categoryData.videos.find(v => v.id === videoId);
                            if (video) {
                                video.isCached = isCached;
                                console.log(`🏠 已更新AppData缓存状态(ID): ${title} - ${isCached} (ID: ${videoId})`);
                                return;
                            }
                        }
                    }
                }

                // 如果通过ID没找到，回退到标题匹配
                // 在指定分类中查找视频
                if (category && AppData.categories[category] && AppData.categories[category].videos) {
                    const video = AppData.categories[category].videos.find(v => v.title === title);
                    if (video) {
                        video.isCached = isCached;
                        console.log(`🏠 已更新AppData缓存状态(标题): ${title} - ${isCached}`);
                        return;
                    }
                }

                // 如果在指定分类中没找到，在所有分类中查找
                for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                    if (categoryData.videos) {
                        const video = categoryData.videos.find(v => v.title === title);
                        if (video) {
                            video.isCached = isCached;
                            console.log(`🏠 已更新AppData缓存状态(标题): ${title} - ${isCached} (在分类 ${cat} 中找到)`);
                            return;
                        }
                    }
                }

                console.warn(`🏠 未在AppData中找到视频: ${title} in ${category}`);
            },

            // 从缓存中移除视频
            removeFromCache(title, category) {
                const videoId = this.getVideoId(title, category);
                
                // 如果正在下载，停止下载
                this.downloadingVideos.delete(videoId);
                
                // 更新数据源中的缓存状态
                this.updateAppDataCacheStatus(title, category, false);

                // 保存缓存状态到localStorage
                this.saveCacheStateToStorage();

                // 重新生成该分类的视频项
                if (categoryData) {
                    generateCategoryVideos(category, categoryData);
                }
            },

            // 获取已缓存视频列表
            getCachedVideosList() {
                const videoList = [];
                if (window.AppData && AppData.categories) {
                    Object.values(AppData.categories).forEach(category => {
                        if (category.videos) {
                            category.videos.forEach(video => {
                                if (video.isCached) {
                                    videoList.push({ 
                                        title: video.title, 
                                        category: category.title,
                                        duration: video.duration 
                                    });
                                }
                            });
                        }
                    });
                }
                return videoList;
            },

            // 调试方法：清理localStorage中的缓存状态
            clearLocalStorageCache() {
                try {
                    localStorage.removeItem('videoCacheState');
                    console.log('🏠 已清理localStorage中的缓存状态');
                    
                    // 重新加载缓存状态（只保留AppData中的基础状态）
                    this.loadCacheStateFromStorage().then(() => {
                        // 重新生成所有分类的视频项
                        if (window.AppData && AppData.categories) {
                            Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                                generateCategoryVideos(categoryKey, categoryData);
                            });
                        }
                        console.log('🏠 缓存状态已重置，页面已刷新');
                    });
                } catch (error) {
                    console.error('🏠 清理缓存状态失败:', error);
                }
            },

            // 调试方法：显示当前缓存状态统计
            showCacheStats() {
                let appDataCount = 0;
                let localStorageCount = 0;
                
                // 统计AppData中的缓存状态
                if (window.AppData && AppData.categories) {
                    Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                        if (categoryData.videos) {
                            categoryData.videos.forEach(video => {
                                if (video.isCached) {
                                    appDataCount++;
                                }
                            });
                        }
                    });
                }
                
                // 统计localStorage中的缓存状态
                try {
                    const savedCacheState = localStorage.getItem('videoCacheState');
                    if (savedCacheState) {
                        const cacheState = JSON.parse(savedCacheState);
                        localStorageCount = Object.keys(cacheState).filter(key => cacheState[key]).length;
                    }
                } catch (error) {
                    console.error('读取localStorage失败:', error);
                }
                
                console.log('🏠 缓存状态统计:');
                console.log(`  AppData中已缓存视频: ${appDataCount} 个`);
                console.log(`  localStorage中已缓存视频: ${localStorageCount} 个`);
                console.log(`  当前显示的缓存状态基于: ${localStorageCount > 0 ? 'localStorage' : 'AppData'}`);
                
                return { appDataCount, localStorageCount };
            }
        };

        // 安全的DOM操作辅助函数
        function safeGetElement(id) {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`Element with id '${id}' not found`);
            }
            return element;
        }

        function safeSetText(id, text) {
            const element = safeGetElement(id);
            if (element) {
                element.textContent = text;
            }
        }

        function safeSetDisplay(id, display) {
            const element = safeGetElement(id);
            if (element) {
                element.style.display = display;
            }
        }

        // 切换系列展开/折叠
        function toggleSeries(element) {
            const content = element.querySelector('.series-content');
            const arrow = element.querySelector('.toggle-arrow-component.arrow-large');

            if (!content || !arrow) {
                console.warn('Series content or arrow not found');
                return;
            }

            // 获取系列标题
            const titleElement = element.querySelector('.series-title-component');
            const seriesTitle = titleElement ? titleElement.textContent.trim().split(' (')[0] : '';

            // 修复状态判断逻辑：考虑初始显示状态和style.display设置
            const computedStyle = window.getComputedStyle(content);
            const isCurrentlyVisible = computedStyle.display !== 'none';
            
            if (isCurrentlyVisible) {
                // 收起
                content.style.display = 'none';
                arrow.classList.remove('arrow-expanded');
                // 记录状态到DataManager
                if (window.DataManager && DataManager.initialized) {
                    DataManager.setSeriesExpanded(seriesTitle, false);
                }
            } else {
                // 展开
                content.style.display = 'block';
                arrow.classList.add('arrow-expanded');
                // 记录状态到DataManager
                if (window.DataManager && DataManager.initialized) {
                    DataManager.setSeriesExpanded(seriesTitle, true);
                }
            }
        }

        // 切换分类展开/折叠 - 修正状态判断逻辑
        function toggleCategory(event, element) {
            event.stopPropagation();
            const content = element.querySelector('.category-content');
            const arrow = element.querySelector('.toggle-arrow-component.arrow-small');

            if (!content || !arrow) {
                console.warn('Category content or arrow not found');
                return;
            }

            // 获取分类标题
            const titleElement = element.querySelector('.category-title-component');
            const categoryTitle = titleElement ? titleElement.textContent.trim().split(' (')[0] : '';

            // 简化状态判断：只检查CSS类
            const isCurrentlyExpanded = content.classList.contains('expanded');

            if (isCurrentlyExpanded) {
                // 当前是展开状态，执行收起
                content.classList.remove('expanded');
                arrow.classList.remove('arrow-expanded');
                // 记录状态到DataManager
                if (window.DataManager && DataManager.initialized) {
                    DataManager.setCategoryExpanded(categoryTitle, false);
                }
            } else {
                // 当前是收起状态，执行展开
                content.classList.add('expanded');
                arrow.classList.add('arrow-expanded');
                // 记录状态到DataManager
                if (window.DataManager && DataManager.initialized) {
                    DataManager.setCategoryExpanded(categoryTitle, true);
                }
            }
        }

        // 处理系列卡片点击 - 使用统一状态管理
        function handleSeriesClick(event, element, title, price) {
            event.stopPropagation(); // 防止事件冒泡

            // 使用统一状态管理检查购买状态
            const isPurchased = AppState.isPurchased(title, price);

            if (!isPurchased) {
                // 未购买：弹出支付框
                showPayment(event, title, price);
            } else {
                // 已购买：关闭支付框（如果当前有显示）
                closePayment();
            }
            // 无论是否购买，都切换展开状态
            toggleSeries(element);
        }

        // 处理分类卡片点击 - 免费分类直接展开，付费分类检查购买状态
        function handleCategoryClick(event, element, title, price) {
            event.stopPropagation(); // 防止事件冒泡

            // 检查是否为免费分类
            const isFree = title.includes('免费') || !price || price === '';
            
            if (isFree) {
                // 免费分类：直接切换展开状态，关闭支付框
                closePayment();
                toggleCategory(event, element);
            } else {
                // 付费分类：使用新的数据结构检查购买状态
                const categoryData = AppData.categories[title];
                const isPurchased = categoryData ? categoryData.isPurchased : false;
                
                if (!isPurchased) {
                    // 未购买：弹出支付框
                    showPayment(event, title, price);
                } else {
                    // 已购买：关闭支付框（如果当前有显示）
                    closePayment();
                }
                // 无论是否购买，都切换展开状态
                toggleCategory(event, element);
            }
        }

        // 显示支付弹窗 - 添加错误处理
        function showPayment(event, title, price) {
            event.stopPropagation();

            // 安全设置文本内容
            safeSetText('paymentTitle', title);
            safeSetText('paymentPrice', price);
            safeSetDisplay('paymentModal', 'block');
        }

        // 跳转到全屏支付页面 - 改进错误处理
        function goToPaymentPage() {
            try {
                LoadingManager.show('正在跳转支付页面...');

                // 获取当前选择的课程信息
                const titleElement = safeGetElement('paymentTitle');
                const priceElement = safeGetElement('paymentPrice');

                if (!titleElement || !priceElement) {
                    throw new Error('支付信息获取失败');
                }

                const title = titleElement.textContent;
                const price = priceElement.textContent;

                // 模拟网络检查
                if (!navigator.onLine) {
                    throw new Error('网络连接不可用，请检查网络后重试');
                }

                // 构建跳转URL，传递课程信息
                const params = new URLSearchParams({
                    title: title,
                    price: price
                });

                // 模拟短暂延迟以显示加载状态
                setTimeout(() => {
                    LoadingManager.hide();
                    // 直接跳转到支付页面
                    window.location.href = `04-支付页面.html?${params.toString()}`;
                }, 500);

            } catch (error) {
                LoadingManager.hide();
                console.error('Error navigating to payment page:', error);
                ToastManager.error(error.message || '跳转支付页面失败，请重试');
            }
        }

        // 关闭支付弹窗
        function closePayment() {
            try {
                safeSetDisplay('paymentModal', 'none');
                // 移除不必要的取消支付提示
            } catch (error) {
                console.error('Error closing payment modal:', error);
            }
        }

        // 获取视频详细信息
        function getVideoData(videoTitle, categoryTitle, videoElement = null) {
            try {
                // 优先使用AppData获取视频信息
                if (window.AppData && AppData.categories) {
                    const categoryData = AppData.categories[categoryTitle];
                    if (categoryData && categoryData.videos) {
                        const video = categoryData.videos.find(v => v.title === videoTitle);
                        if (video) {
                            return {
                                title: video.title,
                                category: categoryTitle,
                                series: categoryData.series || categoryTitle.split('·')[0],
                                duration: video.duration || '15:30',
                                description: video.description || '精彩视频内容',
                                progress: video.defaultProgress || 0,
                                watchCount: video.defaultWatchCount || 0,
                                badgeLevel: Math.min(video.defaultWatchCount || 0, 10),
                                playCount: video.playCount || '1.2万',
                                isFavorite: video.isFavorite || false,
                                isPurchased: categoryData.isFree || categoryData.isPurchased || false,
                                isCached: video.isCached || false,
                                // 添加分类信息
                                categoryPrice: categoryData.price || '',
                                categoryIsFree: categoryData.isFree || false,
                                categoryIsPurchased: categoryData.isPurchased || false
                            };
                        }
                    }
                }

                // 使用新的数据管理系统获取视频信息（备用）
                if (window.DataManager) {
                    const videoInfo = DataManager.getVideoInfo(videoTitle);
                    if (videoInfo) {
                        return {
                            title: videoInfo.title,
                            category: videoInfo.category,
                            series: videoInfo.series,
                            duration: videoInfo.duration || '15:30',
                            description: videoInfo.description || '精彩视频内容',
                            progress: videoInfo.progress || 0,
                            watchCount: videoInfo.watchCount || 0,
                            badgeLevel: videoInfo.badgeLevel || 0,
                            playCount: videoInfo.playCount || '1.2万',
                            isFavorite: videoInfo.isFavorite || false,
                            isPurchased: videoInfo.isPurchased || false,
                            isCached: videoInfo.isCached || false
                        };
                    }
                }
                
                // 回退到DOM元素数据获取方式
                if (!videoElement) {
                    // 如果没有传入videoElement，尝试通过标题查找对应的DOM元素
                    const videoElements = document.querySelectorAll('.video-item-component');
                    for (const element of videoElements) {
                        const titleElement = element.querySelector('.video-title-text');
                        if (titleElement && titleElement.textContent.trim() === videoTitle) {
                            videoElement = element;
                            break;
                        }
                    }
                }
                
                if (videoElement) {
                    const progressElement = videoElement.querySelector('.progress-fill-component');
                    const percentageElement = videoElement.querySelector('.progress-percentage-component');
                    const badgeElement = videoElement.querySelector('.badge-component');
                    
                    const progress = progressElement ? parseInt(progressElement.style.width) || 0 : 0;
                    const watchCountText = badgeElement ? badgeElement.textContent.replace('×', '').trim() : '0';
                    const watchCount = parseInt(watchCountText) || 0;
                    
                    return {
                        title: videoTitle,
                        category: categoryTitle,
                        series: categoryTitle.split('·')[0] || '免费精品系列',
                        duration: '15:30',
                        description: '精彩视频内容',
                        progress: progress,
                        watchCount: watchCount,
                        badgeLevel: Math.min(watchCount, 10),
                        playCount: '1.2万',
                        isFavorite: false,
                        isPurchased: categoryTitle.includes('免费') || AppState.isPurchased(categoryTitle),
                        isCached: VideoCacheManager.isCached(videoTitle, categoryTitle)
                    };
                }
                
                // 最后的回退方案：返回默认数据
                return {
                    title: videoTitle,
                    category: categoryTitle,
                    series: categoryTitle.split('·')[0] || '免费精品系列',
                    duration: '15:30',
                    description: '精彩视频内容',
                    progress: 0,
                    watchCount: 0,
                    badgeLevel: 0,
                    playCount: '1.2万',
                    isFavorite: false,
                    isPurchased: categoryTitle.includes('免费') || AppState.isPurchased(categoryTitle),
                    isCached: VideoCacheManager.isCached(videoTitle, categoryTitle)
                };
            } catch (error) {
                console.error('Error getting video data:', error);
                return {
                    title: videoTitle,
                    category: categoryTitle,
                    series: '免费精品系列',
                    duration: '15:30',
                    description: '精彩视频内容',
                    progress: 0,
                    watchCount: 0,
                    badgeLevel: 0,
                    playCount: '1.2万',
                    isFavorite: false,
                    isPurchased: false,
                    isCached: false
                };
            }
        }



        // 智能视频点击处理函数 - 统一缓存检查逻辑
        function handleVideoClick(event, videoTitle, categoryTitle, categoryPrice, isPurchased = false) {
            event.stopPropagation();

            try {
                // 检查是否为免费课程或已购买
                const isFree = categoryTitle.includes('免费');
                // 使用新的数据结构检查购买状态
                const categoryData = AppData.categories[categoryTitle];
                const actuallyPurchased = categoryData ? categoryData.isPurchased : false;

                if (isFree || actuallyPurchased) {
                    // 免费或已购买：关闭支付框并直接跳转到视频播放页
                    closePayment();
                    
                    // 直接跳转到视频播放页，不再检查缓存状态
                    LoadingManager.show('正在加载视频...');
                    
                    // 保存当前滚动位置
                    if (window.DataManager && DataManager.initialized) {
                        DataManager.saveScrollPosition(window.pageYOffset || document.documentElement.scrollTop);
                    }
                    
                    // 获取视频详细信息，传递当前点击的视频元素
                    const videoElement = event.currentTarget;
                    const videoInfo = getVideoData(videoTitle, categoryTitle, videoElement);
                    
                    // 将视频信息存储到 sessionStorage 中，供播放页使用
                    sessionStorage.setItem('currentVideoInfo', JSON.stringify(videoInfo));
                    
                    setTimeout(() => {
                        LoadingManager.hide();
                        // 使用导航管理器跳转到视频播放页
                        if (window.goToVideo) {
                            goToVideo(videoTitle, categoryTitle);
                        } else {
                            window.location.href = '02-视频播放页.html';
                        }
                    }, 500);
                } else {
                    // 未购买：显示支付弹窗
                    showPayment(event, categoryTitle, categoryPrice);
                }
            } catch (error) {
                LoadingManager.hide();
                console.error('Error handling video click:', error);
                ToastManager.error(error.message || '操作失败，请重试');
            }
        }

        // 分享功能
        function showShareModal() {
            try {
                safeSetDisplay('shareModal', 'block');
                // 移除不必要的选择分享方式提示
            } catch (error) {
                console.error('Error showing share modal:', error);
                ToastManager.error('分享功能暂时不可用');
            }
        }

        function closeShareModal() {
            try {
                safeSetDisplay('shareModal', 'none');
            } catch (error) {
                console.error('Error closing share modal:', error);
            }
        }

        function shareToWechat() {
            try {
                LoadingManager.show('正在调用微信分享...');

                const shareUrl = generateShareLink();

                // 模拟分享操作
                setTimeout(() => {
                    LoadingManager.hide();
                    closeShareModal();
                    // 分享成功，静默处理
                }, 1000);

            } catch (error) {
                LoadingManager.hide();
                console.error('Error sharing to WeChat:', error);
                ToastManager.error('微信分享失败，请重试');
            }
        }

        function shareToMoments() {
            try {
                LoadingManager.show('正在分享到朋友圈...');

                const shareUrl = generateShareLink();

                // 模拟分享操作
                setTimeout(() => {
                    LoadingManager.hide();
                    closeShareModal();
                    // 分享成功，静默处理
                }, 1000);

            } catch (error) {
                LoadingManager.hide();
                console.error('Error sharing to moments:', error);
                ToastManager.error('朋友圈分享失败，请重试');
            }
        }

        function copyShareLink() {
            try {
                const shareUrl = generateShareLink();

                // 改进的剪贴板操作，添加错误处理
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    navigator.clipboard.writeText(shareUrl).then(() => {
                        closeShareModal();
                        ToastManager.success('链接已复制');
                    }).catch((error) => {
                        console.error('Failed to copy link:', error);
                        fallbackCopyText(shareUrl);
                    });
                } else {
                    fallbackCopyText(shareUrl);
                }
            } catch (error) {
                console.error('Error copying share link:', error);
                ToastManager.error('复制链接失败，请重试');
            }
        }

        // 兼容性复制文本方法
        function fallbackCopyText(text) {
            try {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                closeShareModal();
                ToastManager.success('链接已复制');
            } catch (error) {
                console.error('Failed to copy link with fallback method:', error);
                ToastManager.error('复制失败，请手动复制');
            }
        }

        function generateShareLink() {
            try {
                // 生成带有用户标识的分享链接
                const userId = 'user123'; // 实际应用中从用户信息获取
                const baseUrl = window.location.origin + window.location.pathname;
                return `${baseUrl}?ref=${userId}&utm_source=share`;
            } catch (error) {
                console.error('Error generating share link:', error);
                return window.location.href; // 返回当前页面URL作为备选
            }
        }

        // 跳转到我的页面
        function goToMyPage() {
            try {
                LoadingManager.show('正在跳转...');

                // 模拟网络检查
                if (!navigator.onLine) {
                    throw new Error('网络连接不可用');
                }

                // 模拟加载延迟
                setTimeout(() => {
                    LoadingManager.hide();
                    window.location.href = '03-我的页面.html';
                }, 500);

            } catch (error) {
                LoadingManager.hide();
                console.error('Error navigating to my page:', error);
                ToastManager.error(error.message || '页面跳转失败，请重试');
            }
        }

        // 动态进度条动画控制
        function animateProgress() {
            const progressBars = document.querySelectorAll('.progress-fill-component.animated');
            progressBars.forEach(bar => {
                const originalWidth = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = originalWidth;
                }, 100);
            });
        }

        // 缓存相关全局函数
        function closeCacheModal() {
            try {
                VideoCacheManager.closeCacheModal();
            } catch (error) {
                console.error('Error closing cache modal:', error);
            }
        }

        function startVideoCache() {
            try {
                VideoCacheManager.startCache();
            } catch (error) {
                console.error('Error starting video cache:', error);
                ToastManager.error('缓存失败，请重试');
            }
        }

        // 通用动态组件生成器
        function generateDynamicComponents() {
            try {
                console.log('开始生成动态组件...');
                
                if (!window.AppData || !AppData.categories) {
                    console.error('AppData 不可用，无法生成动态组件');
                    return;
                }
                
                // 遍历所有分类，自动生成对应的视频项
                Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
                    generateCategoryVideos(categoryKey, categoryData);
                });
                
                console.log('动态组件生成完成');
            } catch (error) {
                console.error('Error generating dynamic components:', error);
            }
        }
        
        // 为单个分类生成视频项（通用函数）
        function generateCategoryVideos(categoryKey, categoryData) {
            try {
                // 根据分类ID查找对应的容器
                const containerId = findContainerIdByCategory(categoryKey, categoryData);
                if (!containerId) {
                    console.log(`跳过分类 ${categoryData.title}：未找到对应容器`);
                    return;
                }
                
                const container = document.getElementById(containerId);
                if (!container) {
                    console.warn(`容器不存在: ${containerId}`);
                    return;
                }
                
                // 判断分类状态
                const isAccessible = categoryData.isFree || categoryData.isPurchased;
                
                // 注意：不在页面加载时清除缓存状态，只在购买状态真正变化时清除
                // 这个逻辑已移动到setupDataChangeListener中的购买状态变化处理
                
                let html = '';
                
                // 生成视频项 - 适配新的单一数据源结构
                if (categoryData.videos && categoryData.videos.length > 0) {
                    categoryData.videos.forEach((videoData, index) => {
                        // 新数据结构中，videoData已经是完整的视频对象
                        const videoTitle = videoData.title;
                        
                        // 获取缓存状态（只在已购买时显示）- 使用统一的缓存判断逻辑
                        let cacheStatus = '';
                        if (isAccessible) {
                            // 使用VideoCacheManager的统一缓存判断逻辑
                            if (window.VideoCacheManager && VideoCacheManager.isCached) {
                                if (VideoCacheManager.isCached(videoTitle, categoryKey)) {
                                    cacheStatus = '(已缓存)';
                                }
                            } else {
                                // 如果VideoCacheManager不可用，回退到直接读取
                                if (videoData.isCached) {
                                    cacheStatus = '(已缓存)';
                                }
                            }
                            
                            // 检查是否正在下载（优先级最高）
                            if (window.VideoCacheManager && VideoCacheManager.isDownloading) {
                                if (VideoCacheManager.isDownloading(videoTitle, categoryKey)) {
                                    const videoId = VideoCacheManager.getVideoId(videoTitle, categoryKey);
                                    const progress = VideoCacheManager.downloadingVideos.get(videoId) || 0;
                                    cacheStatus = `(${progress}%)`;
                                }
                            }
                        }
                        
                        if (isAccessible) {
                            // 已购买或免费：使用已购买模板
                            html += ComponentTemplates.createPurchasedVideoItem({
                                title: videoTitle,
                                category: categoryKey,
                                progress: videoData.defaultProgress || 0,
                                watchCount: videoData.defaultWatchCount || 0,
                                cacheStatus: cacheStatus,
                                isLastWatched: videoData.isLastWatched || false,
                                onClick: `handleVideoClick(event, '${videoTitle}', '${categoryKey}', '${categoryData.price || ''}', true)`
                            });
                        } else {
                            // 未购买：使用未购买模板（不显示缓存状态）
                            html += ComponentTemplates.createUnpurchasedVideoItem({
                                title: videoTitle,
                                category: categoryKey,
                                onClick: `handleVideoClick(event, '${videoTitle}', '${categoryKey}', '${categoryData.price || ''}', false)`
                            });
                        }
                    });
                }
                
                container.innerHTML = html;
                console.log(`分类 ${categoryData.title} 生成完成，状态: ${isAccessible ? '可访问' : '需购买'}`);
                
            } catch (error) {
                console.error(`生成分类 ${categoryData.title} 视频项失败:`, error);
            }
        }
        
        // 根据分类查找对应的容器ID（通用映射函数）
        function findContainerIdByCategory(categoryKey, categoryData) {
            // 首先尝试根据分类ID查找
            if (categoryData.id) {
                const containerById = document.getElementById(`${categoryData.id}-videos`);
                if (containerById) {
                    return `${categoryData.id}-videos`;
                }
            }
            
            // 然后尝试根据分类标题查找
            const titleToIdMap = {
                '约会技巧': 'dating-skills-videos',
                '搭讪技术': 'pickup-skills-videos',
                '恋爱宝典1': 'love-guide-1-videos',
                '恋爱宝典2': 'love-guide-2-videos',
                '恋爱宝典3': 'love-guide-3-videos',
                '聊天技术1': 'chat-tech-1-videos',
                '聊天技术2': 'chat-tech-2-videos',
                '聊天技术3': 'chat-tech-3-videos'
            };
            
            const containerId = titleToIdMap[categoryData.title];
            if (containerId && document.getElementById(containerId)) {
                return containerId;
            }
            
            // 最后尝试根据分类键名生成ID
            const generatedId = categoryKey.toLowerCase()
                .replace(/[：·]/g, '-')
                .replace(/[^a-z0-9\-]/g, '')
                .replace(/\-+/g, '-')
                .replace(/^\-|\-$/g, '') + '-videos';
            
            if (document.getElementById(generatedId)) {
                return generatedId;
            }
            
            return null;
        }

        // 页面初始化函数 - 避免重复初始化
        async function initializePage() {
            try {
                // 初始化数据管理器（必须最先初始化）
                if (window.DataManager) {
                    DataManager.init();
                }
                
                // 将徽章函数暴露到全局作用域，供组件模板使用
                window.getBadgeClass = SearchManager.getBadgeClass;
                window.getBadgeText = SearchManager.getBadgeText;

                // 初始化所有管理器
                ToastManager.init();
                LoadingManager.init();
                NetworkManager.init();
                SearchManager.init();
                await VideoCacheManager.init();

                // 设置数据变化监听器
                setupDataChangeListener();

                // 生成动态组件
                generateDynamicComponents();
                
                // 恢复首页状态
                restoreHomePageState();
                
                // 更新分类价格显示
                updateCategoryPriceDisplay();
                
                // 监听视频状态变化事件
                setupVideoStatusListener();

                // 使用requestAnimationFrame优化布局操作
                requestAnimationFrame(() => {
                    // 强制重新计算所有flex容器的布局
                    const flexContainers = document.querySelectorAll('.series-header-component');
                    flexContainers.forEach(container => {
                        container.style.display = 'flex';
                        container.style.alignItems = 'center';
                    });

                    // 播放进度条动画
                    animateProgress();
                    
                    // 恢复滚动位置
                    restoreScrollPosition();
                    
                    // 更新所有视频的锁定状态显示
                    // updateAllVideoLockStatus(); // 暂时禁用，避免干扰组件模板生成的正确状态
                });

                console.log('页面初始化完成');
                
                // 显示缓存调试信息
                setTimeout(() => {
                    console.log('💡 缓存状态调试方法:');
                    console.log('  VideoCacheManager.showCacheStats() - 显示缓存状态统计');
                    console.log('  VideoCacheManager.clearLocalStorageCache() - 清理localStorage缓存状态');
                    VideoCacheManager.showCacheStats();
                }, 1000);
            } catch (error) {
                console.error('Error during page initialization:', error);
                // 只在严重初始化失败时显示错误提示
                setTimeout(() => {
                    if (ToastManager && ToastManager.container) {
                        ToastManager.error('页面加载异常，请刷新重试');
                    }
                }, 500);
            }
        }
        
        // 设置数据变化监听器（通用自动更新机制）
        function setupDataChangeListener() {
            try {
                // 创建全局数据变化通知函数
                window.notifyDataChange = function(changeType, categoryKey, oldValue, newValue) {
                    console.log(`数据变化通知: ${changeType} - ${categoryKey}`, { oldValue, newValue });
                    
                    if (changeType === 'purchaseStatus') {
                        // 购买状态变化：重新生成视频项和更新价格显示
                        const categoryData = AppData.categories[categoryKey];
                        if (categoryData) {
                            // 如果从已购买变为未购买，需要额外清除缓存状态
                            if (oldValue === true && newValue === false) {
                                console.log(`检测到退款操作: ${categoryData.title}，将清除缓存状态`);

                                // 清除该分类下所有视频的缓存状态
                                if (categoryData.videos && window.VideoCacheManager) {
                                    categoryData.videos.forEach(videoData => {
                                        const videoTitle = videoData.title;
                                        const videoId = VideoCacheManager.getVideoId(videoTitle, categoryKey);

                                        // 从AppData中清除缓存状态
                                        videoData.isCached = false;

                                        // 从VideoCacheManager中清除缓存状态
                                        if (VideoCacheManager.cachedVideos) {
                                            VideoCacheManager.cachedVideos.delete(videoId);
                                        }
                                        if (VideoCacheManager.downloadingVideos) {
                                            VideoCacheManager.downloadingVideos.delete(videoId);
                                        }
                                    });

                                    // 保存更新后的缓存状态到localStorage
                                    VideoCacheManager.saveCacheStateToStorage();
                                    console.log(`已清除分类 ${categoryData.title} 的所有视频缓存状态`);
                                }
                            }
                            
                            generateCategoryVideos(categoryKey, categoryData);
                            updateCategoryPriceDisplay();
                            
                            // 显示状态变化提示
                            const statusText = newValue ? '已购买' : '未购买';
                            if (window.ToastManager) {
                                ToastManager.success(`${categoryData.title} 状态已更新为${statusText}`);
                            }
                        }
                    }
                };
                
                // 如果AppData存在，为其添加变化监听
                if (window.AppData && AppData.categories) {
                    // 为每个分类的购买状态添加监听
                    Object.keys(AppData.categories).forEach(categoryKey => {
                        const category = AppData.categories[categoryKey];
                        let currentPurchaseStatus = category.isPurchased;
                        
                        // 使用Object.defineProperty监听isPurchased变化
                        Object.defineProperty(category, '_isPurchased', {
                            value: currentPurchaseStatus,
                            writable: true
                        });
                        
                        Object.defineProperty(category, 'isPurchased', {
                            get: function() {
                                return this._isPurchased;
                            },
                            set: function(newValue) {
                                const oldValue = this._isPurchased;
                                this._isPurchased = newValue;
                                
                                // 触发变化通知
                                if (oldValue !== newValue && window.notifyDataChange) {
                                    window.notifyDataChange('purchaseStatus', categoryKey, oldValue, newValue);
                                }
                            }
                        });
                    });
                }
                
                console.log('数据变化监听器已设置');
            } catch (error) {
                console.error('设置数据变化监听器失败:', error);
            }
        }
        
        // 手动触发全量更新（供外部调用）
        window.refreshAllCategories = function() {
            try {
                console.log('开始刷新所有分类...');
                generateDynamicComponents();
                updateCategoryPriceDisplay();
                console.log('所有分类刷新完成');
                
                if (window.ToastManager) {
                    ToastManager.success('页面已刷新');
                }
            } catch (error) {
                console.error('刷新所有分类失败:', error);
                if (window.ToastManager) {
                    ToastManager.error('刷新失败，请重试');
                }
            }
        };
        
        // 更新分类价格显示
        function updateCategoryPriceDisplay() {
            try {
                if (!window.AppData || !AppData.categories) {
                    console.warn('AppData 或 categories 数据不可用');
                    return;
                }
                
                console.log('开始更新分类价格显示...');
                
                // 获取所有分类卡片
                const categoryCards = document.querySelectorAll('.category-card');
                
                categoryCards.forEach(categoryCard => {
                    const titleElement = categoryCard.querySelector('.category-title-component');
                    if (!titleElement) return;
                    
                    // 获取分类名称（去掉可能存在的价格部分）
                    const fullText = titleElement.textContent.trim();
                    const categoryName = fullText.split(' (')[0]; // 去掉价格部分
                    
                    // 查找对应的分类数据
                    let categoryData = null;
                    for (const [key, data] of Object.entries(AppData.categories)) {
                        if (data.title === categoryName || key.includes(categoryName)) {
                            categoryData = data;
                            break;
                        }
                    }
                    
                    if (!categoryData) {
                        console.warn(`未找到分类数据: ${categoryName}`);
                        return;
                    }
                    
                    // 判断是否应该显示价格
                    const shouldShowPrice = !categoryData.isFree && !categoryData.isPurchased;
                    
                    if (shouldShowPrice && categoryData.price) {
                        // 显示价格：如果还没有价格标签，则添加
                        if (!titleElement.querySelector('.category-price-component')) {
                            const priceSpan = document.createElement('span');
                            priceSpan.className = 'category-price-component';
                            priceSpan.textContent = ` (${categoryData.price})`;
                            titleElement.appendChild(priceSpan);
                        }
                    } else {
                        // 不显示价格：移除价格标签
                        const priceElement = titleElement.querySelector('.category-price-component');
                        if (priceElement) {
                            priceElement.remove();
                        }
                        // 更新文本内容，确保只显示分类名称
                        titleElement.textContent = categoryName;
                    }
                });
                
                console.log('分类价格显示更新完成');
            } catch (error) {
                console.error('Error updating category price display:', error);
            }
        }
        
        // 设置视频状态变化监听器
        function setupVideoStatusListener() {
            try {
                window.addEventListener('videoStatusChanged', function(event) {
                    const categoryTitle = event.detail.categoryTitle;
                    console.log(`收到视频状态变化事件: ${categoryTitle}`);
                    
                    // 更新该分类下所有视频的UI显示
                    updateCategoryVideoLockStatus(categoryTitle);
                });
                
                console.log('视频状态监听器已设置');
            } catch (error) {
                console.error('Error setting up video status listener:', error);
            }
        }
        
        // 更新所有视频的锁定状态显示
        function updateAllVideoLockStatus() {
            try {
                if (!window.DataManager || !DataManager.initialized) {
                    return;
                }
                
                console.log('更新所有视频的锁定状态显示...');
                
                // 获取所有视频元素
                const videoElements = document.querySelectorAll('.video-item-component');
                
                videoElements.forEach(videoElement => {
                    const titleElement = videoElement.querySelector('.video-title-text');
                    if (titleElement) {
                        const videoTitle = titleElement.textContent.trim();
                        updateVideoLockStatus(videoElement, videoTitle);
                    }
                });
                
                console.log('所有视频锁定状态显示已更新');
            } catch (error) {
                console.error('Error updating all video lock status:', error);
            }
        }
        
        // 更新特定分类下所有视频的锁定状态显示
        function updateCategoryVideoLockStatus(categoryTitle) {
            try {
                if (!window.DataManager || !DataManager.initialized) {
                    return;
                }
                
                console.log(`更新分类 "${categoryTitle}" 下视频的锁定状态显示...`);
                
                // 查找该分类下的所有视频元素
                const categoryElements = document.querySelectorAll('.category-card');
                
                categoryElements.forEach(categoryElement => {
                    const categoryTitleElement = categoryElement.querySelector('.category-title-component');
                    if (categoryTitleElement && categoryTitleElement.textContent.trim().split(' (')[0] === categoryTitle) {
                        // 找到对应的分类，更新其下的所有视频
                        const videoElements = categoryElement.querySelectorAll('.video-item-component');
                        
                        videoElements.forEach(videoElement => {
                            const titleElement = videoElement.querySelector('.video-title-text');
                            if (titleElement) {
                                const videoTitle = titleElement.textContent.trim();
                                updateVideoLockStatus(videoElement, videoTitle);
                            }
                        });
                    }
                });
                
                console.log(`分类 "${categoryTitle}" 下视频锁定状态显示已更新`);
            } catch (error) {
                console.error('Error updating category video lock status:', error);
            }
        }
        
        // 更新单个视频的锁定状态显示
        function updateVideoLockStatus(videoElement, videoTitle) {
            try {
                if (!window.AppData || !AppData.getVideo) {
                    return;
                }
                
                const video = AppData.getVideo(videoTitle);
                if (!video) {
                    return;
                }
                
                // 检查视频是否应该被锁定
                let shouldBeLocked = false;
                
                if (video.isFree) {
                    // 免费视频不锁定
                    shouldBeLocked = false;
                } else {
                    // 检查分类购买状态
                    const category = AppData.getCategory(video.category);
                    if (category) {
                        if (category.isFree || category.isPurchased) {
                            // 免费分类或已购买分类不锁定
                            shouldBeLocked = false;
                        } else {
                            // 检查UserState中的购买状态
                            if (window.UserState && window.UserState.isPurchased) {
                                shouldBeLocked = !UserState.isPurchased(video.category);
                            } else {
                                // 如果无法获取购买状态，默认锁定付费视频
                                shouldBeLocked = true;
                            }
                        }
                    } else {
                        // 找不到分类信息，默认锁定
                        shouldBeLocked = true;
                    }
                }
                
                // 查找锁定图标（右边的锁图标）
                let lockIcon = videoElement.querySelector('.video-lock-component');
                
                if (shouldBeLocked) {
                    // 视频应该被锁定：显示右边的锁定图标，隐藏进度条和徽章
                    if (!lockIcon) {
                        lockIcon = document.createElement('span');
                        lockIcon.className = 'video-lock-component';
                        lockIcon.textContent = '🔒';
                        
                        // 将锁图标添加到视频项的右边，与搜索结果保持一致
                        videoElement.appendChild(lockIcon);
                    }
                    
                    // 隐藏进度条和徽章
                    const progressRow = videoElement.querySelector('.video-progress-row-component');
                    if (progressRow) progressRow.style.display = 'none';
                    
                } else {
                    // 视频应该解锁：移除锁定图标，显示进度条和徽章
                    if (lockIcon) {
                        lockIcon.remove();
                    }
                    
                    // 显示进度条和徽章
                    const progressRow = videoElement.querySelector('.video-progress-row-component');
                    if (progressRow) progressRow.style.display = '';
                }
                
            } catch (error) {
                console.error('Error updating video lock status:', error);
            }
        }

        // 恢复首页状态
        function restoreHomePageState() {
            try {
                if (!window.DataManager || !DataManager.initialized) {
                    return;
                }

                // 恢复系列展开状态
                const seriesCards = document.querySelectorAll('.series-card');
                seriesCards.forEach(card => {
                    const titleElement = card.querySelector('.series-title-component');
                    if (titleElement) {
                        const seriesTitle = titleElement.textContent.trim().split(' (')[0];
                        const isExpanded = DataManager.isSeriesExpanded(seriesTitle);
                        
                        const content = card.querySelector('.series-content');
                        const arrow = card.querySelector('.toggle-arrow-component.arrow-large');
                        
                        if (content && arrow) {
                            if (isExpanded) {
                                content.style.display = 'block';
                                arrow.classList.add('arrow-expanded');
                            } else {
                                content.style.display = 'none';
                                arrow.classList.remove('arrow-expanded');
                            }
                        }
                    }
                });

                // 恢复分类展开状态
                const categoryCards = document.querySelectorAll('.category-card');
                categoryCards.forEach(card => {
                    const titleElement = card.querySelector('.category-title-component');
                    if (titleElement) {
                        const categoryTitle = titleElement.textContent.trim().split(' (')[0];
                        const isExpanded = DataManager.isCategoryExpanded(categoryTitle);
                        
                        const content = card.querySelector('.category-content');
                        const arrow = card.querySelector('.toggle-arrow-component.arrow-small');
                        
                        if (content && arrow) {
                            if (isExpanded) {
                                content.classList.add('expanded');
                                arrow.classList.add('arrow-expanded');
                            } else {
                                content.classList.remove('expanded');
                                arrow.classList.remove('arrow-expanded');
                            }
                        }
                    }
                });

                // 免费系列默认展开（如果没有保存状态）
                const freeSeriesContent = document.querySelector('.series-card.free .series-content');
                const freeSeriesArrow = document.querySelector('.series-card.free .toggle-arrow-component.arrow-large');
                if (freeSeriesContent && freeSeriesArrow && !DataManager.isSeriesExpanded('免费精品系列')) {
                    freeSeriesContent.style.display = 'block';
                    freeSeriesArrow.classList.add('arrow-expanded');
                    DataManager.setSeriesExpanded('免费精品系列', true);
                }

                console.log('首页状态已恢复');
            } catch (error) {
                console.error('Error restoring home page state:', error);
            }
        }

        // 恢复滚动位置
        function restoreScrollPosition() {
            try {
                if (!window.DataManager || !DataManager.initialized) {
                    return;
                }

                const scrollPosition = DataManager.getScrollPosition();
                if (scrollPosition > 0) {
                    window.scrollTo(0, scrollPosition);
                    console.log('滚动位置已恢复:', scrollPosition);
                }
            } catch (error) {
                console.error('Error restoring scroll position:', error);
            }
        }

        // 添加全局错误处理
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);

            // 显示用户友好的错误提示
            if (ToastManager.container) {
                ToastManager.error('页面出现错误，请刷新重试');
            }

            // 可以添加错误上报逻辑
            // reportError(event.error);
        });

        // 添加未处理的Promise错误处理
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);

            // 显示用户友好的错误提示
            if (ToastManager.container) {
                ToastManager.error('操作失败，请重试');
            }

            // 可以添加错误上报逻辑
            // reportError(event.reason);
        });

        // 页面可见性变化处理
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                // 页面重新可见时检查网络状态
                if (NetworkManager.isOnline !== navigator.onLine) {
                    NetworkManager.isOnline = navigator.onLine;
                    NetworkManager.updateStatus();

                    if (navigator.onLine) {
                        ToastManager.success('网络连接已恢复');
                    } else {
                        ToastManager.error('网络连接已断开');
                    }
                }
            } else {
                // 页面隐藏时保存滚动位置
                if (window.DataManager && DataManager.initialized) {
                    DataManager.saveScrollPosition(window.pageYOffset || document.documentElement.scrollTop);
                }
            }
        });

        // 页面卸载时保存状态
        window.addEventListener('beforeunload', function() {
            if (window.DataManager && DataManager.initialized) {
                // 保存当前滚动位置
                DataManager.saveScrollPosition(window.pageYOffset || document.documentElement.scrollTop);
            }
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            // ESC键关闭弹窗
            if (event.key === 'Escape') {
                const paymentModal = document.getElementById('paymentModal');
                const shareModal = document.getElementById('shareModal');

                if (paymentModal && paymentModal.style.display === 'block') {
                    closePayment();
                } else if (shareModal && shareModal.style.display === 'block') {
                    closeShareModal();
                }
            }

            // Ctrl+R 或 F5 刷新页面时显示加载状态
            if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
                LoadingManager.show('正在刷新页面...');
            }
        });

        // 安全的DOM操作函数
        function safeSetDisplay(elementId, displayValue) {
            try {
                const element = document.getElementById(elementId);
                if (element) {
                    element.style.display = displayValue;
                    return true;
                }
                console.warn(`Element with id '${elementId}' not found`);
                return false;
            } catch (error) {
                console.error(`Error setting display for element '${elementId}':`, error);
                return false;
            }
        }

        // 安全的文本设置函数
        function safeSetText(id, text) {
            const element = safeGetElement(id);
            if (element) {
                element.textContent = text;
            }
        }

        // 安全的元素获取函数
        function safeGetElement(id) {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`Element with id '${id}' not found`);
            }
            return element;
        }



        // 搜索功能管理
        const SearchManager = {
            modal: null,
            input: null,
            clearBtn: null,
            historySection: null,
            historyList: null,
            suggestions: null,
            emptyState: null,
            dropdown: null,
            dropdownList: null,
            currentKeyword: '',
            searchHistory: ['恋爱技巧', '聊天方法', '约会攻略', '表白技巧'],
            dropdownVisible: false,
            isVisible: false,
            isDeleting: false,

            init() {
                try {
                    this.modal = document.getElementById('searchModal');
                    this.input = document.getElementById('searchInput');
                    this.clearBtn = document.getElementById('searchClearBtn');
                    this.historySection = document.getElementById('searchHistorySection');
                    this.historyList = document.getElementById('searchHistoryList');
                    this.suggestions = document.getElementById('searchSuggestions');
                    this.emptyState = document.getElementById('searchEmptyState');
                    this.dropdown = document.getElementById('searchDropdown');
                    this.dropdownList = document.getElementById('searchDropdownList');

                    if (!this.modal || !this.input) {
                        console.warn('Search elements not found, search functionality will be limited');
                        return; // 提前返回，避免后续操作null元素
                    }

                    // 从localStorage加载搜索历史
                    this.loadSearchHistory();
                    
                    // 添加全局点击监听器
                    this.addGlobalClickListener();
                    
                    console.log('SearchManager initialized successfully');
                } catch (error) {
                    console.error('Error initializing SearchManager:', error);
                }
            },

            loadSearchHistory() {
                try {
                    const saved = localStorage.getItem('searchHistory');
                    if (saved) {
                        this.searchHistory = JSON.parse(saved);
                    }
                } catch (error) {
                    console.warn('Failed to load search history:', error);
                }
            },

            saveSearchHistory() {
                try {
                    localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
                } catch (error) {
                    console.warn('Failed to save search history:', error);
                }
            },

            showDropdown() {
                if (!this.dropdown || !this.dropdownList) return;

                // 总是更新并显示下拉框，无论当前状态如何
                this.updateDropdownList();
                this.dropdown.classList.add('show');
                this.dropdownVisible = true;
            },

            hideDropdown() {
                if (!this.dropdown) return;

                // 延迟隐藏，允许点击下拉项
                setTimeout(() => {
                    this.dropdown.classList.remove('show');
                    this.dropdownVisible = false;

                    // 不隐藏历史记录区域，保持显示
                }, 150);
            },

            addGlobalClickListener() {
                // 添加全局点击和触摸监听器
                document.addEventListener('click', (event) => {
                    this.handleGlobalClick(event);
                });
                
                // 移动设备触摸支持
                document.addEventListener('touchstart', (event) => {
                    this.handleGlobalClick(event);
                });
            },

            handleGlobalClick(event) {
                // 如果搜索弹窗没有显示，不需要处理
                if (!this.isVisible) return;

                // 检查点击的元素是否在搜索弹窗内
                if (this.modal && this.modal.contains(event.target)) {
                    return; // 点击在弹窗内，不关闭
                }

                // 检查是否点击了搜索按钮（避免点击搜索按钮时立即关闭）
                const searchButton = event.target.closest('.search-button-component');
                if (searchButton) {
                    return; // 点击的是搜索按钮，不关闭
                }

                // 检查是否点击了缓存弹窗或其内容（避免缓存弹窗操作时关闭搜索弹窗）
                const cacheModal = document.getElementById('cacheModal');
                if (cacheModal && cacheModal.style.display === 'flex' && cacheModal.contains(event.target)) {
                    return; // 点击在缓存弹窗内，不关闭搜索弹窗
                }

                // 检查缓存弹窗是否显示（如果显示则不关闭搜索弹窗）
                if (cacheModal && cacheModal.style.display === 'flex') {
                    return; // 缓存弹窗正在显示，不关闭搜索弹窗
                }

                // 点击在弹窗外，关闭搜索弹窗
                this.hide();
            },

            updateDropdownList() {
                if (!this.dropdownList) return;

                if (this.searchHistory.length === 0) {
                    this.dropdownList.innerHTML = '<div style="padding: 16px; text-align: center; color: #9ca3af; font-size: 14px;">暂无搜索历史</div>';
                    return;
                }

                let html = '';
                this.searchHistory.forEach((keyword, index) => {
                    html += `
                        <div class="search-dropdown-item" onclick="selectDropdownItem('${keyword}')">
                            <i class="fas fa-clock search-dropdown-icon"></i>
                            <span class="search-dropdown-text">${keyword}</span>
                            <i class="fas fa-times search-dropdown-delete" onclick="deleteHistoryItem(${index}, event)" title="删除此记录"></i>
                        </div>
                    `;
                });

                this.dropdownList.innerHTML = html;
            },

            deleteHistoryItem(index) {
                this.isDeleting = true;
                this.searchHistory.splice(index, 1);
                this.saveSearchHistory();
                this.updateDropdownList();
                this.updateHistoryDisplay();
                
                // 删除后保持下拉框显示状态
                this.showDropdown();
                
                // 重置删除标志
                setTimeout(() => {
                    this.isDeleting = false;
                }, 200);
            },

            clearAllHistory() {
                this.isDeleting = true;
                this.searchHistory = [];
                this.saveSearchHistory();
                this.updateDropdownList();
                this.updateHistoryDisplay();
                
                // 清空后保持下拉框显示，显示"暂无搜索历史"
                this.showDropdown();
                
                // 重置删除标志
                setTimeout(() => {
                    this.isDeleting = false;
                }, 200);
            },

            show() {
                if (!this.modal) return;
                this.modal.classList.add('show');
                this.modal.style.display = 'flex';
                this.isVisible = true;

                // 不自动聚焦输入框，让用户手动点击
                // setTimeout(() => {
                //     if (this.input) {
                //         this.input.focus();
                //     }
                // }, 100);

                this.updateHistoryDisplay();
                this.showDefaultState();
                // 不自动显示下拉框
            },

            hide() {
                if (!this.modal) return;
                this.modal.classList.remove('show');
                this.modal.style.display = 'none';
                this.isVisible = false;
                this.clearInput();
            },

            clearInput() {
                if (this.input) {
                    this.input.value = '';
                    this.currentKeyword = '';
                }
                if (this.clearBtn) {
                    this.clearBtn.style.display = 'none';
                }
                this.showDefaultState();
            },

            updateHistoryDisplay() {
                if (!this.historyList) return;

                this.historyList.innerHTML = '';
                this.searchHistory.forEach(keyword => {
                    const item = document.createElement('div');
                    item.className = 'search-history-item';
                    item.textContent = keyword;
                    item.onclick = () => this.searchKeyword(keyword);
                    this.historyList.appendChild(item);
                });
            },

            showDefaultState() {
                if (this.historySection) {
                    this.historySection.style.display = 'block'; // 默认显示历史记录区域
                }
                if (this.suggestions) {
                    this.suggestions.classList.remove('full-height');
                    this.suggestions.innerHTML = '<div class="search-empty-state">输入关键词开始搜索</div>';
                }
            },

            showSearchResults(keyword) {
                if (!this.suggestions) return;

                // 隐藏历史记录
                if (this.historySection) {
                    this.historySection.style.display = 'none';
                }

                // 给搜索建议区域更多空间
                this.suggestions.classList.add('full-height');

                // 模拟搜索结果
                const mockResults = this.getMockSearchResults(keyword);

                if (mockResults.length === 0) {
                    this.suggestions.innerHTML = '<div class="search-empty-state">未找到相关课程</div>';
                    return;
                }

                let resultsHtml = '';
                mockResults.forEach((result, index) => {
                    // 检查缓存状态
                    let cacheStatus = '';

                    if (VideoCacheManager.isDownloading(result.title, result.category)) {
                        const videoId = VideoCacheManager.getVideoId(result.title, result.category);
                        const progress = VideoCacheManager.downloadingVideos.get(videoId);
                        cacheStatus = `(${progress}%)`;
                    } else if (VideoCacheManager.isCached(result.title, result.category)) {
                        cacheStatus = '(已缓存)';
                    }

                    // 检查是否为免费课程或已购买
                    const isFree = result.categoryIsFree || result.category.includes('免费');
                    const isPurchased = result.categoryIsPurchased || AppState.isPurchased(result.category);
                    const showProgress = isFree || isPurchased;

                    // 获取视频数据 - 优先使用搜索结果中的数据
                    const videoData = {
                        progress: result.defaultProgress || this.getVideoData(result.title, result.category).progress,
                        watchCount: result.defaultWatchCount || this.getVideoData(result.title, result.category).watchCount
                    };

                    // 使用组件模板生成搜索项
                    if (showProgress) {
                        // 已购买或免费课程
                        resultsHtml += ComponentTemplates.createPurchasedSearchItem({
                            title: result.title,
                            category: result.category,
                            progress: videoData.progress,
                            watchCount: videoData.watchCount,
                            cacheStatus: cacheStatus,
                            dataTitle: result.title,
                            dataCategory: result.category,
                            dataIndex: index,
                            onClick: `handleSearchVideoClick('${result.title}', '${result.category}', event)`
                        });
                    } else {
                        // 未购买课程
                        resultsHtml += ComponentTemplates.createUnpurchasedSearchItem({
                            title: result.title,
                            category: result.category,
                            dataTitle: result.title,
                            dataCategory: result.category,
                            dataIndex: index,
                            onClick: `handleSearchVideoClick('${result.title}', '${result.category}', event)`
                        });
                    }
                });

                this.suggestions.innerHTML = resultsHtml;
            },

            getMockSearchResults(keyword) {
                // 优先使用新的数据结构
                if (window.AppData && AppData.utils && AppData.utils.searchVideos) {
                    try {
                        const searchResults = AppData.utils.searchVideos(keyword);
                        console.log('搜索结果:', searchResults); // 调试信息
                        // 转换数据格式以匹配旧的接口
                        return searchResults.map(result => ({
                            title: result.title,
                            category: result.category,
                            categoryTitle: result.categoryTitle,
                            series: result.series,
                            categoryPrice: result.categoryPrice,
                            categoryIsFree: result.categoryIsFree,
                            categoryIsPurchased: result.categoryIsPurchased,
                            // 添加视频的其他属性
                            defaultProgress: result.defaultProgress || 0,
                            defaultWatchCount: result.defaultWatchCount || 0,
                            isCached: result.isCached || false
                        }));
                    } catch (error) {
                        console.error('搜索出错:', error);
                    }
                }

                // 回退到旧的搜索逻辑（保持不变作为备用）
                const allCourses = [
                    // 免费精品系列·约会技巧
                    { title: '01. 约会前的准备工作', category: '免费精品系列·约会技巧' },
                    { title: '02. 约会地点的选择', category: '免费精品系列·约会技巧' },

                    // 免费精品系列·搭讪技术
                    { title: '01. 搭讪基础心态', category: '免费精品系列·搭讪技术' },
                    { title: '02. 自然开场技巧', category: '免费精品系列·搭讪技术' },
                    { title: '03. 克服紧张情绪', category: '免费精品系列·搭讪技术' },

                    // 道：恋爱宝典系列·恋爱宝典1
                    { title: '01. 初识吸引力法则', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '02. 建立自信的方法', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '03. 第一印象的重要性', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '04. 肢体语言解读', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '05. 情感表达技巧', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '06. 深度沟通艺术', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '07. 约会策略制定', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '08. 魅力提升秘诀', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '09. 关系升级技巧', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '10. 长期关系维护', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '11. 冲突化解智慧', category: '道：恋爱宝典系列·恋爱宝典1' },
                    { title: '12. 恋爱大师之路', category: '道：恋爱宝典系列·恋爱宝典1' },

                    // 道：恋爱宝典系列·恋爱宝典2
                    { title: '01. 深度沟通技巧', category: '道：恋爱宝典系列·恋爱宝典2' },
                    { title: '02. 情感表达方式', category: '道：恋爱宝典系列·恋爱宝典2' },
                    { title: '03. 冲突处理艺术', category: '道：恋爱宝典系列·恋爱宝典2' },

                    // 道：恋爱宝典系列·恋爱宝典3
                    { title: '01. 长期关系维护', category: '道：恋爱宝典系列·恋爱宝典3' },
                    { title: '02. 信任建立方法', category: '道：恋爱宝典系列·恋爱宝典3' },
                    { title: '03. 未来规划讨论', category: '道：恋爱宝典系列·恋爱宝典3' },

                    // 术：聊天技术系列·聊天技术1
                    { title: '01. 开场白技巧', category: '术：聊天技术系列·聊天技术1' },
                    { title: '02. 话题延续方法', category: '术：聊天技术系列·聊天技术1' },
                    { title: '03. 幽默感培养', category: '术：聊天技术系列·聊天技术1' },

                    // 术：聊天技术系列·聊天技术2
                    { title: '01. 情绪调动技巧', category: '术：聊天技术系列·聊天技术2' },
                    { title: '02. 深度话题引导', category: '术：聊天技术系列·聊天技术2' },
                    { title: '03. 暧昧升级方法', category: '术：聊天技术系列·聊天技术2' },

                    // 术：聊天技术系列·聊天技术3
                    { title: '01. 微信聊天技巧', category: '术：聊天技术系列·聊天技术3' },
                    { title: '02. 语音通话技巧', category: '术：聊天技术系列·聊天技术3' },
                    { title: '03. 视频聊天技巧', category: '术：聊天技术系列·聊天技术3' }
                ];

                const lowerKeyword = keyword.toLowerCase();
                return allCourses.filter(course =>
                    course.title.toLowerCase().includes(lowerKeyword)
                );
            },

            addToHistory(keyword) {
                if (!keyword || this.searchHistory.includes(keyword)) return;

                // 优先使用新的用户状态管理系统
                if (window.UserState && window.UserState.addSearchHistory) {
                    UserState.addSearchHistory(keyword);
                    // 同步到本地搜索历史
                    this.searchHistory = UserState.getSearchHistory().slice(0, 8);
                } else {
                    // 回退到旧逻辑
                    this.searchHistory.unshift(keyword);
                    if (this.searchHistory.length > 8) {
                        this.searchHistory = this.searchHistory.slice(0, 8);
                    }
                    this.saveSearchHistory();
                }
            },

            // 获取视频数据（进度、观看次数等）
            getVideoData(title, category) {
                // 优先使用新的数据结构
                if (window.AppData && AppData.utils) {
                    const video = AppData.utils.getVideoById(title);
                    if (video) {
                        return {
                            progress: video.defaultProgress || 0,
                            watchCount: video.defaultWatchCount || 0
                        };
                    }
                }
                
                // 回退到旧的数据映射
                const videoDataMap = {
                    // 道：恋爱宝典系列·恋爱宝典1
                    '01. 初识吸引力法则': { progress: 0, watchCount: 0 },
                    '02. 建立自信的方法': { progress: 20, watchCount: 1 },
                    '03. 第一印象的重要性': { progress: 45, watchCount: 2 },
                    '04. 肢体语言解读': { progress: 60, watchCount: 3 },
                    '05. 情感表达技巧': { progress: 75, watchCount: 4 },
                    '06. 深度沟通艺术': { progress: 85, watchCount: 6 },
                    '07. 约会策略制定': { progress: 100, watchCount: 6 },
                    '08. 魅力提升秘诀': { progress: 100, watchCount: 7 },
                    '09. 关系升级技巧': { progress: 100, watchCount: 8 },
                    '10. 长期关系维护': { progress: 100, watchCount: 9 },
                    '11. 冲突化解智慧': { progress: 100, watchCount: 10 },
                    '12. 恋爱大师之路': { progress: 100, watchCount: 11 },
                    
                    // 其他系列的默认数据
                    '01. 约会前的准备工作': { progress: 30, watchCount: 2 },
                    '02. 约会地点的选择': { progress: 60, watchCount: 3 },
                    '01. 搭讪基础心态': { progress: 80, watchCount: 4 },
                    '02. 自然开场技巧': { progress: 40, watchCount: 2 },
                    '03. 克服紧张情绪': { progress: 90, watchCount: 5 },
                    '01. 深度沟通技巧': { progress: 70, watchCount: 3 },
                    '02. 情感表达方式': { progress: 50, watchCount: 2 },
                    '03. 冲突处理艺术': { progress: 85, watchCount: 4 },
                    '01. 开场白技巧': { progress: 95, watchCount: 6 },
                    '02. 话题延续方法': { progress: 65, watchCount: 3 },
                    '03. 幽默感培养': { progress: 75, watchCount: 4 }
                };

                return videoDataMap[title] || { progress: 0, watchCount: 0 };
            },

            // 获取徽章样式类
            getBadgeClass(watchCount) {
                if (watchCount >= 10) {
                    return 'badge-level-legendary';
                }
                return `badge-level-${watchCount}`;
            },

            // 获取徽章文字
            getBadgeText(watchCount) {
                return `×${watchCount}`;
            },

            searchKeyword(keyword) {
                if (!keyword.trim()) return;

                this.currentKeyword = keyword;
                if (this.input) {
                    this.input.value = keyword;
                }

                this.addToHistory(keyword);
                this.showSearchResults(keyword);

                // 显示清除按钮
                if (this.clearBtn) {
                    this.clearBtn.style.display = 'block';
                }

                // 搜索后隐藏下拉框（正常行为）
                this.hideDropdown();
            }
        };

        // 搜索相关函数
        function showSearchModal() {
            try {
                SearchManager.show();
            } catch (error) {
                console.error('Error showing search modal:', error);
                ToastManager.error('搜索功能暂时不可用');
            }
        }

        function closeSearchModal() {
            try {
                SearchManager.hide();
            } catch (error) {
                console.error('Error closing search modal:', error);
            }
        }



        function searchKeyword(keyword) {
            try {
                SearchManager.searchKeyword(keyword);
            } catch (error) {
                console.error('Error searching keyword:', error);
                ToastManager.error('搜索失败，请重试');
            }
        }



        // 搜索结果视频项点击处理函数
        function handleSearchVideoClick(title, category, event) {
            try {
                // 阻止事件冒泡，防止触发全局点击事件
                if (event) {
                    event.stopPropagation();
                }

                // 检查是否为免费课程或已购买 - 使用AppData进行准确检查
                const isFree = category.includes('免费');
                let isPurchased = false;

                // 优先使用AppData检查购买状态
                if (window.AppData && AppData.categories) {
                    const categoryData = AppData.categories[category];
                    isPurchased = categoryData ? categoryData.isPurchased : false;
                } else {
                    // 回退到AppState检查
                    isPurchased = AppState.isPurchased(category);
                }

                if (isFree || isPurchased) {
                    // 免费或已购买：先关闭可能存在的支付弹窗，然后检查缓存状态
                    closePayment();

                    if (VideoCacheManager.isCached(title, category)) {
                        // 已缓存，关闭搜索弹窗并跳转到视频播放页
                        closeSearchModal();
                        LoadingManager.show('正在加载视频...');

                        // 获取视频详细信息
                        const videoInfo = getVideoData(title, category);

                        // 将视频信息存储到 sessionStorage 中，供播放页使用
                        sessionStorage.setItem('currentVideoInfo', JSON.stringify(videoInfo));

                        setTimeout(() => {
                            LoadingManager.hide();
                            // 跳转到视频播放页
                            window.location.href = '02-视频播放页.html';
                        }, 500);
                    } else {
                        // 未缓存，显示缓存弹窗，保持搜索弹窗打开
                        VideoCacheManager.showCacheModal(title, category, true); // 传入true表示来自搜索
                    }
                } else {
                    // 未购买：显示支付弹窗，保持搜索弹窗打开
                    // 获取正确的价格信息
                    let categoryPrice = '¥100'; // 默认价格
                    if (window.AppData && AppData.categories) {
                        const categoryData = AppData.categories[category];
                        if (categoryData && categoryData.price) {
                            categoryPrice = categoryData.price;
                        }
                    }

                    const mockEvent = new Event('click');
                    showPayment(mockEvent, category, categoryPrice);
                }
            } catch (error) {
                LoadingManager.hide();
                console.error('Error handling search video click:', error);
                ToastManager.error('操作失败，请重试');
            }
        }

        function selectSearchResult(title, category) {
            try {
                // 检查是否为免费课程或已购买
                const isFree = category.includes('免费');
                const isPurchased = AppState.isPurchased(category);

                if (isFree || isPurchased) {
                    // 免费或已购买：先关闭可能存在的支付弹窗，然后检查缓存状态
                    closePayment();
                    
                    if (VideoCacheManager.isCached(title, category)) {
                        // 已缓存，关闭搜索弹窗并跳转到视频播放页
                        closeSearchModal();
                        LoadingManager.show('正在加载视频...');
                        
                        // 获取视频详细信息
                        const videoInfo = getVideoData(title, category);
                        
                        // 将视频信息存储到 sessionStorage 中，供播放页使用
                        sessionStorage.setItem('currentVideoInfo', JSON.stringify(videoInfo));
                        
                        setTimeout(() => {
                            LoadingManager.hide();
                            // 跳转到视频播放页
                            window.location.href = '02-视频播放页.html';
                        }, 500);
                    } else if (VideoCacheManager.isDownloading(title, category)) {
                        // 正在下载，提示用户
                        // 视频正在缓存中，静默处理
                    } else {
                        // 未缓存，显示缓存弹窗，保持搜索弹窗打开
                        VideoCacheManager.showCacheModal(title, category, true); // fromSearch = true
                    }
                } else {
                    // 未购买：显示支付弹窗，保持搜索弹窗打开
                    const mockEvent = new Event('click');
                    showPayment(mockEvent, category, '¥100');
                }
            } catch (error) {
                LoadingManager.hide();
                console.error('Error selecting search result:', error);
                ToastManager.error('操作失败，请重试');
            }
        }

        // 下拉框相关函数
        function showSearchDropdown() {
            try {
                // 总是显示下拉框，无论是否有搜索结果
                SearchManager.showDropdown();
            } catch (error) {
                console.error('Error showing search dropdown:', error);
            }
        }

        function hideSearchDropdown() {
            try {
                SearchManager.hideDropdown();
            } catch (error) {
                console.error('Error hiding search dropdown:', error);
            }
        }

        function handleSearchBlur() {
            try {
                // 延迟隐藏下拉框，给删除操作留出时间
                setTimeout(() => {
                    // 检查是否有删除操作正在进行
                    if (!SearchManager.isDeleting) {
                        SearchManager.hideDropdown();
                    }
                }, 150);
            } catch (error) {
                console.error('Error handling search blur:', error);
            }
        }

        function selectDropdownItem(keyword) {
            try {
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.value = keyword;
                    SearchManager.currentKeyword = keyword;
                    SearchManager.showSearchResults(keyword);
                    
                    // 立即隐藏下拉框，不使用延迟
                    if (SearchManager.dropdown) {
                        SearchManager.dropdown.classList.remove('show');
                        SearchManager.dropdownVisible = false;
                    }
                    searchInput.blur();
                    
                    // 显示清除按钮
                    const clearBtn = document.getElementById('searchClearBtn');
                    if (clearBtn) {
                        clearBtn.style.display = 'block';
                    }
                }
            } catch (error) {
                console.error('Error selecting dropdown item:', error);
            }
        }

        // 处理搜索输入
        function handleSearchInput() {
            try {
                const searchInput = document.getElementById('searchInput');
                const clearBtn = document.getElementById('searchClearBtn');
                
                if (!searchInput) return;
                
                const keyword = searchInput.value.trim();
                SearchManager.currentKeyword = keyword;
                
                // 控制清除按钮显示
                if (clearBtn) {
                    clearBtn.style.display = keyword ? 'block' : 'none';
                }
                
                if (keyword) {
                    SearchManager.showSearchResults(keyword);
                    // 搜索后不自动隐藏下拉框，让用户可以继续看到搜索历史
                } else {
                    SearchManager.showDefaultState();
                    // 没有搜索内容时显示下拉框
                    SearchManager.showDropdown();
                }
            } catch (error) {
                console.error('Error handling search input:', error);
            }
        }

        // 处理搜索按键
        function handleSearchKeypress(event) {
            try {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    const keyword = event.target.value.trim();
                    if (keyword) {
                        SearchManager.searchKeyword(keyword);
                        // 搜索后让输入框失焦，隐藏下拉框（正常行为）
                        const searchInput = document.getElementById('searchInput');
                        if (searchInput) {
                            searchInput.blur();
                        }
                    }
                }
            } catch (error) {
                console.error('Error handling search keypress:', error);
            }
        }

        // 清除搜索
        function clearSearch() {
            try {
                const searchInput = document.getElementById('searchInput');
                const clearBtn = document.getElementById('searchClearBtn');
                
                if (searchInput) {
                    searchInput.value = '';
                    searchInput.focus();
                }
                
                if (clearBtn) {
                    clearBtn.style.display = 'none';
                }
                
                SearchManager.currentKeyword = '';
                SearchManager.showDefaultState();
                SearchManager.showDropdown();
            } catch (error) {
                console.error('Error clearing search:', error);
            }
        }

        function deleteHistoryItem(index, event) {
            try {
                event.stopPropagation(); // 防止触发父元素的点击事件
                event.preventDefault(); // 防止默认行为
                
                // 阻止输入框失焦
                const searchInput = document.getElementById('searchInput');
                if (searchInput) {
                    searchInput.focus();
                }
                
                SearchManager.deleteHistoryItem(index);
            } catch (error) {
                console.error('Error deleting history item:', error);
            }
        }

        function clearSearchHistory(event) {
            try {
                if (event) {
                    event.stopPropagation();
                    event.preventDefault();
                    
                    // 阻止输入框失焦
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
                
                SearchManager.clearAllHistory();
                // 搜索历史已清空（移除提示，通过界面更新反馈）
                console.log('搜索历史已清空');
            } catch (error) {
                console.error('Error clearing search history:', error);
            }
        }

        // 搜索相关全局函数
        function showSearchModal() {
            try {
                SearchManager.show();
            } catch (error) {
                console.error('Error showing search modal:', error);
            }
        }

        function closeSearchModal() {
            try {
                SearchManager.hide();
            } catch (error) {
                console.error('Error closing search modal:', error);
            }
        }

        // 全局错误处理器
        class ErrorHandler {
            static init() {
                // 捕获JavaScript运行时错误
                window.addEventListener('error', function(event) {
                    console.error('全局错误:', event.error);
                    ErrorHandler.handleError('应用出现异常', event.error?.message || '未知错误');
                });

                // 捕获Promise未处理的拒绝
                window.addEventListener('unhandledrejection', function(event) {
                    console.error('未处理的Promise拒绝:', event.reason);
                    ErrorHandler.handleError('网络请求失败', '请检查网络连接后重试');
                    event.preventDefault(); // 阻止默认的错误提示
                });

                // 捕获资源加载错误
                document.addEventListener('error', function(event) {
                    if (event.target !== window && (event.target.tagName === 'IMG' || event.target.tagName === 'LINK' || event.target.tagName === 'SCRIPT')) {
                        console.error('资源加载失败:', event.target.src || event.target.href);
                        ErrorHandler.handleError('资源加载失败', '部分内容可能无法正常显示');
                    }
                }, true);
            }

            static handleError(title, message) {
                // 显示用户友好的错误提示
                ToastManager.error(`${title}: ${message}`);

                // 记录错误信息
                this.logError(title, message);
            }

            static logError(title, message) {
                // 在控制台记录详细错误信息
                console.log('错误记录:', {
                    title,
                    message,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                });
            }
        }

        // 徽章样式切换功能
        class BadgeStyleManager {
            static currentStyle = 'mixed'; // 默认混合版
            
            static switchToOriginal() {
                const container = document.querySelector('.badge-style-mixed, .badge-style-original');
                if (container) {
                    container.className = 'badge-style-original';
                    this.currentStyle = 'original';
                    ToastManager.success('已切换到原版11级钻石徽章');
                }
            }
            
            static switchToMixed() {
                const container = document.querySelector('.badge-style-mixed, .badge-style-original');
                if (container) {
                    container.className = 'badge-style-mixed';
                    this.currentStyle = 'mixed';
                    ToastManager.success('已切换到混合10级图标徽章');
                }
            }
            
            static getCurrentStyle() {
                return this.currentStyle;
            }
            
            static toggle() {
                if (this.currentStyle === 'mixed') {
                    this.switchToOriginal();
                } else {
                    this.switchToMixed();
                }
            }
        }

        // 更新购买状态（支持三种状态：购买/取消购买/免费）
        function updatePurchaseStatus(seriesOrCategoryName, type = 'category', status = 'purchased') {
            try {
                if (!window.AppData || !AppData.categories) {
                    console.error('AppData 不可用');
                    return false;
                }
                
                // status 可以是: 'purchased', 'unpurchased', 'free'
                let action = '';
                let isPurchased = false;
                let isFree = false;
                
                switch (status) {
                    case 'purchased':
                        action = '购买';
                        isPurchased = true;
                        isFree = false;
                        break;
                    case 'free':
                        action = '设为免费';
                        isPurchased = false;
                        isFree = true;
                        break;
                    case 'unpurchased':
                    default:
                        action = '设为需购买';
                        isPurchased = false;
                        isFree = false;
                        break;
                }
                
                console.log(`更新状态: ${seriesOrCategoryName} (${type}) - ${action}`);
                
                let updatedCategories = [];
                
                if (type === 'category') {
                    // 更新单个分类
                    for (const [key, data] of Object.entries(AppData.categories)) {
                        if (data.title === seriesOrCategoryName || key === seriesOrCategoryName || key.includes(seriesOrCategoryName)) {
                            data.isPurchased = isPurchased;
                            data.isFree = isFree;
                            updatedCategories.push(key);
                            console.log(`分类 "${seriesOrCategoryName}" 状态已更新: isPurchased=${isPurchased}, isFree=${isFree}`);
                            break;
                        }
                    }
                } else if (type === 'series') {
                    // 更新整个系列
                    for (const [key, data] of Object.entries(AppData.categories)) {
                        if (data.series === seriesOrCategoryName || key.startsWith(seriesOrCategoryName)) {
                            data.isPurchased = isPurchased;
                            data.isFree = isFree;
                            updatedCategories.push(key);
                            console.log(`系列 "${seriesOrCategoryName}" 下的分类 "${data.title}" 状态已更新: isPurchased=${isPurchased}, isFree=${isFree}`);
                        }
                    }
                }
                
                // 更新UI显示
                updateCategoryPriceDisplay();
                
                // 更新所有相关视频的UI状态
                updatedCategories.forEach(categoryKey => {
                    if (isPurchased || isFree) {
                        // 购买或免费：添加进度条、徽章、点击事件等
                        updateCategoryVideoUI(categoryKey);
                    } else {
                        // 需购买：移除进度条、添加锁图标
                        revertCategoryVideoUI(categoryKey);
                    }
                });
                
                // 显示提示
                if (window.ToastManager) {
                    ToastManager.success(`${seriesOrCategoryName} ${action}成功！`);
                }
                
                return true;
            } catch (error) {
                console.error('Error updating purchase status:', error);
                return false;
            }
        }
        
        // 恢复分类下所有视频的锁定状态（从可用状态变为锁定状态）
        function revertCategoryVideoUI(categoryKey) {
            try {
                const categoryData = AppData.categories[categoryKey];
                if (!categoryData) return;
                
                console.log(`恢复分类 "${categoryData.title}" 下视频的锁定状态...`);
                
                // 查找该分类的DOM元素
                const categoryCards = document.querySelectorAll('.category-card');
                let targetCategoryCard = null;
                
                categoryCards.forEach(card => {
                    const titleElement = card.querySelector('.category-title-component');
                    if (titleElement && titleElement.textContent.includes(categoryData.title)) {
                        targetCategoryCard = card;
                    }
                });
                
                if (!targetCategoryCard) {
                    console.warn(`未找到分类 "${categoryData.title}" 的DOM元素`);
                    return;
                }
                
                // 获取该分类下的所有视频元素
                const videoElements = targetCategoryCard.querySelectorAll('.video-item-component');
                
                videoElements.forEach(videoElement => {
                    const titleElement = videoElement.querySelector('.video-title-text');
                    if (!titleElement) return;
                    
                    const videoTitle = titleElement.textContent.trim();
                    
                    // 移除进度条和徽章
                    const progressRow = videoElement.querySelector('.video-progress-row-component');
                    if (progressRow) {
                        progressRow.remove();
                    }
                    
                    // 添加锁定图标（如果还没有）
                    let lockIcon = videoElement.querySelector('.video-lock-component');
                    if (!lockIcon) {
                        lockIcon = document.createElement('span');
                        lockIcon.className = 'video-lock-component';
                        lockIcon.textContent = '🔒';
                        
                        // 将锁图标添加到视频项的右边，与搜索结果保持一致
                        videoElement.appendChild(lockIcon);
                    }
                    
                    // 更新点击事件，移除缓存功能
                    updateVideoClickEventToLocked(videoElement, videoTitle, categoryKey);
                });
                
                console.log(`分类 "${categoryData.title}" 下视频锁定状态恢复完成`);
            } catch (error) {
                console.error('Error reverting category video UI:', error);
            }
        }
        
        // 更新视频点击事件为锁定状态
        function updateVideoClickEventToLocked(videoElement, videoTitle, categoryKey) {
            try {
                const categoryData = AppData.categories[categoryKey];
                if (!categoryData) return;
                
                // 移除旧的点击事件
                videoElement.onclick = null;
                
                // 添加锁定状态的点击事件
                videoElement.onclick = function(event) {
                    handleVideoClick(event, videoTitle, categoryKey, categoryData.price || '', false);
                };
                
                console.log(`视频 "${videoTitle}" 点击事件已更新为锁定状态`);
            } catch (error) {
                console.error('Error updating video click event to locked:', error);
            }
        }
        
        // 更新分类下所有视频的UI状态（从锁定状态变为可用状态）
        function updateCategoryVideoUI(categoryKey) {
            try {
                const categoryData = AppData.categories[categoryKey];
                if (!categoryData) return;
                
                console.log(`更新分类 "${categoryData.title}" 下视频的UI状态...`);
                
                // 查找该分类的DOM元素
                const categoryCards = document.querySelectorAll('.category-card');
                let targetCategoryCard = null;
                
                categoryCards.forEach(card => {
                    const titleElement = card.querySelector('.category-title-component');
                    if (titleElement && titleElement.textContent.includes(categoryData.title)) {
                        targetCategoryCard = card;
                    }
                });
                
                if (!targetCategoryCard) {
                    console.warn(`未找到分类 "${categoryData.title}" 的DOM元素`);
                    return;
                }
                
                // 获取该分类下的所有视频元素
                const videoElements = targetCategoryCard.querySelectorAll('.video-item-component');
                
                videoElements.forEach(videoElement => {
                    const titleElement = videoElement.querySelector('.video-title-text');
                    if (!titleElement) return;
                    
                    const videoTitle = titleElement.textContent.trim();
                    
                    // 移除锁定图标
                    const lockIcon = videoElement.querySelector('.video-lock-component');
                    if (lockIcon) {
                        lockIcon.remove();
                    }
                    
                    // 添加进度条和徽章（如果还没有）
                    let progressRow = videoElement.querySelector('.video-progress-row-component');
                    if (!progressRow) {
                        progressRow = createProgressRowComponent(videoTitle, categoryKey);
                        const contentWrapper = videoElement.querySelector('.video-content-wrapper');
                        if (contentWrapper) {
                            contentWrapper.appendChild(progressRow);
                        }
                    }
                    
                    // 更新点击事件，支持缓存功能
                    updateVideoClickEvent(videoElement, videoTitle, categoryKey);
                });
                
                console.log(`分类 "${categoryData.title}" 下视频UI状态更新完成`);
            } catch (error) {
                console.error('Error updating category video UI:', error);
            }
        }
        
        // 创建进度条组件
        function createProgressRowComponent(videoTitle, categoryKey) {
            const progressRow = document.createElement('div');
            progressRow.className = 'video-progress-row-component';
            
            // 获取视频的默认进度和观看次数
            const videoData = getVideoDefaultData(videoTitle, categoryKey);
            
            progressRow.innerHTML = `
                <div class="progress-component progress-thin progress-flex">
                    <div class="progress-fill-component ${videoData.progress > 0 ? 'animated' : ''}" style="width: ${videoData.progress}%;"></div>
                </div>
                <div class="progress-info-component">
                    <span class="progress-percentage-component">${videoData.progress}%</span>
                    <span class="badge-component ${getBadgeClass(videoData.watchCount)}">${getBadgeText(videoData.watchCount)}</span>
                </div>
            `;
            
            return progressRow;
        }
        
        // 获取视频的默认数据
        function getVideoDefaultData(videoTitle, categoryKey) {
            // 优先使用DataManager
            if (window.DataManager && DataManager.getVideoInfo) {
                const videoInfo = DataManager.getVideoInfo(videoTitle);
                if (videoInfo) {
                    return {
                        progress: videoInfo.progress || 0,
                        watchCount: videoInfo.watchCount || 0
                    };
                }
            }
            
            // 回退到AppData（适配新的单一数据源结构）
            if (window.AppData && AppData.utils) {
                const video = AppData.utils.getVideoById(videoTitle);
                if (video) {
                    return {
                        progress: video.defaultProgress || 0,
                        watchCount: video.defaultWatchCount || 0
                    };
                }
            }
            
            // 默认值
            return { progress: 0, watchCount: 0 };
        }
        
        // 更新视频点击事件
        function updateVideoClickEvent(videoElement, videoTitle, categoryKey) {
            try {
                const categoryData = AppData.categories[categoryKey];
                if (!categoryData) return;
                
                // 移除旧的点击事件
                videoElement.onclick = null;
                
                // 添加新的点击事件，支持缓存功能
                videoElement.onclick = function(event) {
                    handleVideoClick(event, videoTitle, categoryKey, categoryData.price || '', true);
                };
                
                console.log(`视频 "${videoTitle}" 点击事件已更新，支持缓存功能`);
            } catch (error) {
                console.error('Error updating video click event:', error);
            }
        }
        
        // 全局函数供控制台调用
        function switchBadgeStyle(style) {
            if (style === 'original') {
                BadgeStyleManager.switchToOriginal();
            } else if (style === 'mixed') {
                BadgeStyleManager.switchToMixed();
            } else {
                console.log('可用样式: "original" (原版11级钻石) 或 "mixed" (混合10级图标)');
                console.log('当前样式:', BadgeStyleManager.getCurrentStyle());
            }
        }

        function toggleBadgeStyle() {
            BadgeStyleManager.toggle();
        }
        
        // 测试购买功能（供控制台调用）
        function testPurchase(categoryName) {
            console.log(`测试购买: ${categoryName}`);
            
            // 模拟购买成功
            if (window.DataManager) {
                const category = AppData.categories[categoryName];
                if (category) {
                    DataManager.purchaseContent(categoryName, category.price);
                } else {
                    console.error(`未找到分类: ${categoryName}`);
                }
            } else {
                console.error('DataManager 未初始化');
            }
        }
        
        // 提供一些快捷测试函数
        function buyLoveGuide3() {
            testPurchase('道：恋爱宝典系列·恋爱宝典3');
        }
        
        function buyChatTech1() {
            testPurchase('术：聊天技术系列·聊天技术1');
        }
        
        function buyChatTech2() {
            testPurchase('术：聊天技术系列·聊天技术2');
        }
        
        function buyChatTech3() {
            testPurchase('术：聊天技术系列·聊天技术3');
        }
        
        // 通用购买函数（直接修改数据，触发自动更新）
        window.purchaseCategory = function(categoryKey) {
            if (window.AppData && AppData.categories[categoryKey]) {
                const categoryData = AppData.categories[categoryKey];
                console.log(`购买分类: ${categoryData.title}`);
                AppData.categories[categoryKey].isPurchased = true;
                return true;
            }
            console.error(`未找到分类: ${categoryKey}`);
            return false;
        };
        
        // 通用退款函数（直接修改数据，触发自动更新）
        window.refundCategory = function(categoryKey) {
            if (window.AppData && AppData.categories[categoryKey]) {
                const categoryData = AppData.categories[categoryKey];
                console.log(`退款分类: ${categoryData.title}`);
                AppData.categories[categoryKey].isPurchased = false;
                return true;
            }
            console.error(`未找到分类: ${categoryKey}`);
            return false;
        };
        
        // 简化的测试函数
        window.testBuy = function(categoryName) {
            // 支持简化的分类名称
            const categoryMap = {
                '恋爱宝典2': '道：恋爱宝典系列·恋爱宝典2',
                '恋爱宝典3': '道：恋爱宝典系列·恋爱宝典3',
                '聊天技术1': '术：聊天技术系列·聊天技术1',
                '聊天技术2': '术：聊天技术系列·聊天技术2',
                '聊天技术3': '术：聊天技术系列·聊天技术3'
            };
            
            const fullCategoryKey = categoryMap[categoryName] || categoryName;
            return purchaseCategory(fullCategoryKey);
        };
        
        window.testRefund = function(categoryName) {
            // 支持简化的分类名称
            const categoryMap = {
                '恋爱宝典2': '道：恋爱宝典系列·恋爱宝典2',
                '恋爱宝典3': '道：恋爱宝典系列·恋爱宝典3',
                '聊天技术1': '术：聊天技术系列·聊天技术1',
                '聊天技术2': '术：聊天技术系列·聊天技术2',
                '聊天技术3': '术：聊天技术系列·聊天技术3'
            };
            
            const fullCategoryKey = categoryMap[categoryName] || categoryName;
            return refundCategory(fullCategoryKey);
        };

        // 购买成功后的回调函数
        window.onContentPurchased = function(categoryName, price) {
            console.log(`购买成功回调: ${categoryName} (${price})`);
            
            try {
                // 1. 更新AppData中的购买状态
                if (window.AppData && AppData.categories && AppData.categories[categoryName]) {
                    AppData.categories[categoryName].isPurchased = true;
                    console.log(`已更新AppData中 ${categoryName} 的购买状态`);
                }
                
                // 2. 重新生成该分类的视频项
                regenerateCategoryVideos(categoryName);
                
                // 3. 更新分类标题（移除价格显示）
                updateCategoryTitle(categoryName);
                
                // 4. 显示成功提示
                if (window.ToastManager) {
                    ToastManager.success(`${categoryName.split('·')[1] || categoryName} 购买成功！`);
                }
                
            } catch (error) {
                console.error('购买成功回调处理失败:', error);
            }
        };
        
        // 重新生成分类的视频项（通用函数）
        function regenerateCategoryVideos(categoryName) {
            try {
                // 获取分类数据
                const categoryData = AppData.categories[categoryName];
                if (!categoryData) {
                    console.warn(`未找到分类数据: ${categoryName}`);
                    return;
                }
                
                // 使用通用函数重新生成
                generateCategoryVideos(categoryName, categoryData);
                console.log(`已重新生成 ${categoryData.title} 的视频项`);
                
            } catch (error) {
                console.error('重新生成分类视频项失败:', error);
            }
        }
        
        // 更新分类标题（移除价格显示）
        function updateCategoryTitle(categoryName) {
            try {
                const categoryCards = document.querySelectorAll('.category-card');
                
                categoryCards.forEach(card => {
                    const titleElement = card.querySelector('.category-title-component');
                    if (titleElement) {
                        const titleText = titleElement.textContent;
                        
                        // 检查是否是目标分类
                        if (titleText.includes(categoryName.split('·')[1] || categoryName)) {
                            // 移除价格部分
                            const priceElement = titleElement.querySelector('.category-price-component');
                            if (priceElement) {
                                priceElement.remove();
                                console.log(`已移除 ${categoryName} 的价格显示`);
                            }
                        }
                    }
                });
                
            } catch (error) {
                console.error('更新分类标题失败:', error);
            }
        }

        // 页面加载完成后初始化 - 统一入口
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('水幕首页开始加载');

            // 初始化全局错误处理
            ErrorHandler.init();

            // 调用统一的初始化函数
            await initializePage();

            // 添加底部导航
            addBottomNavigation();

            // 模拟加载状态
            LoadingManager.show('正在加载课程...');
            setTimeout(() => {
                LoadingManager.hide();
                ToastManager.success('欢迎来到水幕！');

                // 延迟更新缓存状态，确保DOM完全加载
                // 注意：使用增强组件系统后，缓存状态已在组件生成时处理，无需额外更新
                // setTimeout(() => {
                //     VideoCacheManager.updateAllVideoStatus();
                // }, 500);
                
                // 显示使用提示
                setTimeout(() => {
                    console.log('💡 徽章样式切换方法:');
                    console.log('  switchBadgeStyle("original") - 切换到原版11级钻石徽章');
                    console.log('  switchBadgeStyle("mixed") - 切换到混合10级图标徽章');
                    console.log('  toggleBadgeStyle() - 在两种样式间切换');
                    console.log('  当前样式:', BadgeStyleManager.getCurrentStyle());
                    console.log('');
                    console.log('🛒 购买/退款测试方法:');
                    console.log('  testBuy("恋爱宝典2") - 购买恋爱宝典2');
                    console.log('  testBuy("恋爱宝典3") - 购买恋爱宝典3');
                    console.log('  testBuy("聊天技术1") - 购买聊天技术1');
                    console.log('  testRefund("恋爱宝典2") - 退款恋爱宝典2');
                    console.log('  refreshAllCategories() - 手动刷新所有分类');
                    console.log('');
                    console.log('🔄 系统特性:');
                    console.log('  ✅ 完全通用：支持任意数量的系列和分类');
                    console.log('  ✅ 自动更新：数据变化时自动更新UI');
                    console.log('  ✅ 智能映射：自动查找对应的HTML容器');
                    console.log('  ✅ 状态同步：购买状态、价格显示、锁图标完全同步');
                }, 2000);
            }, 1000);
        });

        // 添加底部导航函数
        function addBottomNavigation() {
            try {
                // 检查是否已经存在底部导航
                if (document.querySelector('.bottom-nav')) {
                    console.log('底部导航已存在，跳过添加');
                    return;
                }

                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加底部导航');
                    return;
                }

                // 创建底部导航HTML
                const bottomNavHtml = ComponentTemplates.createBottomNavigation({
                    currentPage: 'home'
                });

                // 添加到页面底部
                document.body.insertAdjacentHTML('beforeend', bottomNavHtml);

                // 初始化底部导航功能
                ComponentTemplates.initBottomNavigation();

                // 调整内容区域高度，避免被底部导航遮挡
                const contentElement = document.querySelector('.content');
                if (contentElement) {
                    contentElement.style.paddingBottom = '60px';
                }

                console.log('底部导航添加成功');
            } catch (error) {
                console.error('添加底部导航失败:', error);
            }
        }
    </script>
</div> <!-- 结束徽章样式容器 -->
</body>
</html>