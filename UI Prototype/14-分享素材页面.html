<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 分享素材</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <script src="data/component-templates-with-styles.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
        }
        .content::-webkit-scrollbar { display: none; }
        .share-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 24px;
            margin: 16px 0;
            color: white;
            text-align: center;
        }
        .material-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .material-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .material-item:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .material-item.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }
        .copy-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
        }
        .share-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            margin-left: 8px;
        }
        .tab-container {
            display: flex;
            background: #f3f4f6;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 16px;
        }
        .tab-btn {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .tab-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .preview-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .preview-content {
            background: white;
            border-radius: 16px;
            padding: 24px;
            max-width: 300px;
            width: 90%;
            text-align: center;
        }
        .qr-code {
            width: 200px;
            height: 200px;
            background: #f3f4f6;
            border-radius: 12px;
            margin: 16px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #6b7280;
        }
        .link-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            background: #f8fafc;
            margin: 8px 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px 0;
        }
        .stat-item {
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 分享头部 -->
                <div class="share-header">
                    <h2 class="text-lg font-semibold mb-2">我的专属分享</h2>
                    <p class="text-white text-opacity-80 mb-4">分享给好友，获得30%收益分成</p>
                    
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="text-lg font-bold">156</div>
                            <div class="text-sm opacity-80">分享人数</div>
                        </div>
                        <div class="stat-item">
                            <div class="text-lg font-bold">¥2,468</div>
                            <div class="text-sm opacity-80">累计收益</div>
                        </div>
                    </div>
                </div>

                <!-- 选项卡 -->
                <div class="tab-container">
                    <div class="tab-btn active" onclick="switchTab('text')">文案素材</div>
                    <div class="tab-btn" onclick="switchTab('image')">图片素材</div>
                    <div class="tab-btn" onclick="switchTab('link')">分享链接</div>
                </div>

                <!-- 文案素材 -->
                <div id="textTab" class="material-card">
                    <h3 class="font-semibold text-gray-800 mb-4">精选文案</h3>
                    
                    <div class="material-item" onclick="selectMaterial(this)">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-medium text-gray-800">恋爱必修课</h4>
                            <div>
                                <button class="copy-btn" onclick="copyText(event, 'text1')">复制</button>
                                <button class="share-btn" onclick="shareText(event, 'text1')">分享</button>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 leading-relaxed" id="text1">
                            🔥 想要脱单？想要恋爱技巧？<br>
                            💕 水幕App - 专业恋爱学习平台<br>
                            ✨ 系统化课程，实战技巧分享<br>
                            🎯 让你在恋爱路上更自信！<br>
                            👇 点击链接立即体验
                        </p>
                    </div>

                    <div class="material-item" onclick="selectMaterial(this)">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-medium text-gray-800">聊天技巧</h4>
                            <div>
                                <button class="copy-btn" onclick="copyText(event, 'text2')">复制</button>
                                <button class="share-btn" onclick="shareText(event, 'text2')">分享</button>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 leading-relaxed" id="text2">
                            💬 还在为聊天没话题发愁？<br>
                            🎯 水幕App教你聊天技巧<br>
                            📚 从破冰到深入，全套聊天攻略<br>
                            💝 让你成为聊天高手！<br>
                            🔗 马上学习，告别尬聊
                        </p>
                    </div>

                    <div class="material-item" onclick="selectMaterial(this)">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-medium text-gray-800">约会指南</h4>
                            <div>
                                <button class="copy-btn" onclick="copyText(event, 'text3')">复制</button>
                                <button class="share-btn" onclick="shareText(event, 'text3')">分享</button>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 leading-relaxed" id="text3">
                            🌹 第一次约会不知道怎么办？<br>
                            📍 水幕App约会攻略大全<br>
                            🎪 从地点选择到话题准备<br>
                            💯 让每次约会都完美！<br>
                            ⬇️ 点击学习约会秘籍
                        </p>
                    </div>

                    <div class="material-item" onclick="selectMaterial(this)">
                        <div class="flex justify-between items-start mb-3">
                            <h4 class="font-medium text-gray-800">情感挽回</h4>
                            <div>
                                <button class="copy-btn" onclick="copyText(event, 'text4')">复制</button>
                                <button class="share-btn" onclick="shareText(event, 'text4')">分享</button>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 leading-relaxed" id="text4">
                            💔 感情出现问题？想要挽回？<br>
                            🔄 水幕App情感修复课程<br>
                            🧠 心理学角度分析问题<br>
                            💪 科学方法重燃爱火！<br>
                            🚀 立即学习挽回技巧
                        </p>
                    </div>
                </div>

                <!-- 图片素材 -->
                <div id="imageTab" class="material-card" style="display: none;">
                    <h3 class="font-semibold text-gray-800 mb-4">精美海报</h3>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="material-item text-center">
                            <img src="https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?w=150&h=200&fit=crop" 
                                 class="w-full h-32 object-cover rounded-lg mb-3" alt="海报1">
                            <p class="text-sm font-medium text-gray-800 mb-2">恋爱宝典</p>
                            <div>
                                <button class="copy-btn text-xs" onclick="copyImage(event, 'image1')">保存</button>
                                <button class="share-btn text-xs" onclick="shareImage(event, 'image1')">分享</button>
                            </div>
                        </div>

                        <div class="material-item text-center">
                            <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=150&h=200&fit=crop" 
                                 class="w-full h-32 object-cover rounded-lg mb-3" alt="海报2">
                            <p class="text-sm font-medium text-gray-800 mb-2">聊天技巧</p>
                            <div>
                                <button class="copy-btn text-xs" onclick="copyImage(event, 'image2')">保存</button>
                                <button class="share-btn text-xs" onclick="shareImage(event, 'image2')">分享</button>
                            </div>
                        </div>

                        <div class="material-item text-center">
                            <img src="https://images.unsplash.com/photo-1518621012382-9e7706a0f2eb?w=150&h=200&fit=crop" 
                                 class="w-full h-32 object-cover rounded-lg mb-3" alt="海报3">
                            <p class="text-sm font-medium text-gray-800 mb-2">约会指南</p>
                            <div>
                                <button class="copy-btn text-xs" onclick="copyImage(event, 'image3')">保存</button>
                                <button class="share-btn text-xs" onclick="shareImage(event, 'image3')">分享</button>
                            </div>
                        </div>

                        <div class="material-item text-center">
                            <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=150&h=200&fit=crop" 
                                 class="w-full h-32 object-cover rounded-lg mb-3" alt="海报4">
                            <p class="text-sm font-medium text-gray-800 mb-2">情感挽回</p>
                            <div>
                                <button class="copy-btn text-xs" onclick="copyImage(event, 'image4')">保存</button>
                                <button class="share-btn text-xs" onclick="shareImage(event, 'image4')">分享</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分享链接 -->
                <div id="linkTab" class="material-card" style="display: none;">
                    <h3 class="font-semibold text-gray-800 mb-4">我的专属链接</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">分享链接</label>
                            <div class="flex">
                                <input type="text" class="link-input flex-1" 
                                       value="https://shuimu.com/share?code=ABC123" readonly>
                                <button class="copy-btn ml-2" onclick="copyLink()">复制</button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">邀请码</label>
                            <div class="flex">
                                <input type="text" class="link-input flex-1" 
                                       value="ABC123" readonly>
                                <button class="copy-btn ml-2" onclick="copyCode()">复制</button>
                            </div>
                        </div>

                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-medium text-blue-800 mb-2">
                                <i class="fas fa-info-circle mr-2"></i>分享说明
                            </h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• 好友通过您的链接注册并购买课程</li>
                                <li>• 您将获得订单金额30%的分成收益</li>
                                <li>• 收益将在订单完成后24小时内到账</li>
                                <li>• 可在"分享收益"页面查看详细记录</li>
                            </ul>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <button class="w-full bg-green-500 text-white py-3 rounded-lg font-medium" 
                                    onclick="shareToWechat()">
                                <i class="fab fa-weixin mr-2"></i>分享到微信
                            </button>
                            <button class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium" 
                                    onclick="shareToQQ()">
                                <i class="fab fa-qq mr-2"></i>分享到QQ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 分享统计 -->
                <div class="material-card">
                    <h3 class="font-semibold text-gray-800 mb-4">分享统计</h3>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-4 bg-blue-50 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">156</div>
                            <div class="text-sm text-blue-500">今日分享</div>
                        </div>
                        <div class="text-center p-4 bg-green-50 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">23</div>
                            <div class="text-sm text-green-500">今日注册</div>
                        </div>
                        <div class="text-center p-4 bg-yellow-50 rounded-lg">
                            <div class="text-2xl font-bold text-yellow-600">8</div>
                            <div class="text-sm text-yellow-500">今日购买</div>
                        </div>
                        <div class="text-center p-4 bg-purple-50 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600">¥234</div>
                            <div class="text-sm text-purple-500">今日收益</div>
                        </div>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>
        </div>
    </div>

    <!-- 预览弹窗 -->
    <div id="previewModal" class="preview-modal">
        <div class="preview-content">
            <h3 class="font-semibold text-gray-800 mb-4">扫码分享</h3>
            <div class="qr-code">
                <i class="fas fa-qrcode"></i>
            </div>
            <p class="text-sm text-gray-600 mb-4">扫描二维码分享给好友</p>
            <button class="w-full bg-gray-500 text-white py-2 rounded-lg" onclick="hidePreview()">
                关闭
            </button>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTopNavigation();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '分享素材'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        function goBack() {
            if (window.navigateTo) {
                navigateTo('13-分享排行页面.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '13-分享排行页面.html';
            }
        }

        // showShareModal函数已经在顶部导航组件中自动包含

        function switchTab(tab) {
            // 隐藏所有标签页
            document.getElementById('textTab').style.display = 'none';
            document.getElementById('imageTab').style.display = 'none';
            document.getElementById('linkTab').style.display = 'none';
            
            // 移除所有按钮的active类
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 显示选中的标签页
            if (tab === 'text') {
                document.getElementById('textTab').style.display = 'block';
                document.querySelectorAll('.tab-btn')[0].classList.add('active');
            } else if (tab === 'image') {
                document.getElementById('imageTab').style.display = 'block';
                document.querySelectorAll('.tab-btn')[1].classList.add('active');
            } else if (tab === 'link') {
                document.getElementById('linkTab').style.display = 'block';
                document.querySelectorAll('.tab-btn')[2].classList.add('active');
            }
        }

        function selectMaterial(element) {
            // 移除其他选中状态
            document.querySelectorAll('.material-item').forEach(item => {
                item.classList.remove('selected');
            });
            // 添加选中状态
            element.classList.add('selected');
        }

        function copyText(event, textId) {
            event.stopPropagation();
            const text = document.getElementById(textId).innerText;
            navigator.clipboard.writeText(text).then(() => {
                alert('文案已复制到剪贴板！');
            });
        }

        function shareText(event, textId) {
            event.stopPropagation();
            alert('分享功能已触发！');
        }

        function copyImage(event, imageId) {
            event.stopPropagation();
            alert('图片已保存到相册！');
        }

        function shareImage(event, imageId) {
            event.stopPropagation();
            alert('图片分享功能已触发！');
        }

        function copyLink() {
            const link = document.querySelector('input[value*="shuimu.com"]');
            navigator.clipboard.writeText(link.value).then(() => {
                alert('链接已复制到剪贴板！');
            });
        }

        function copyCode() {
            const code = document.querySelector('input[value="ABC123"]');
            navigator.clipboard.writeText(code.value).then(() => {
                alert('邀请码已复制到剪贴板！');
            });
        }

        function shareToWechat() {
            alert('正在打开微信分享...');
        }

        function shareToQQ() {
            alert('正在打开QQ分享...');
        }

        function showPreview() {
            document.getElementById('previewModal').style.display = 'flex';
        }

        function hidePreview() {
            document.getElementById('previewModal').style.display = 'none';
        }
    </script>
</body>
</html> 