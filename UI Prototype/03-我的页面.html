<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 我的</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/component-templates-with-styles.js"></script>
    <script src="data/navigation-manager.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .content {
            height: calc(100% - 44px);
            overflow-y: auto;
            padding: 0 16px;
            padding-bottom: 60px;
        }
        .content::-webkit-scrollbar { display: none; }
        .user-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 24px;
            margin: 16px 0;
            color: white;
            position: relative;
            overflow: hidden;
        }
        .user-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            pointer-events: none;
        }
        .stats-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .stat-item {
            text-align: center;
            flex: 1;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 12px;
            color: #6b7280;
        }
        .menu-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: between;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }
        .progress-ring {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: conic-gradient(#667eea 0deg 216deg, #e5e7eb 216deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }
        .progress-ring::before {
            content: '';
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }
        .progress-text {
            position: relative;
            z-index: 1;
            font-size: 12px;
            font-weight: bold;
            color: #667eea;
        }
        .share-highlight {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border: 2px solid #f59e0b;
        }
        .share-percentage {
            color: #dc2626;
            font-size: 20px;
            font-weight: bold;
        }
        
        /* ========== 组件系统 ========== */
        
        /* 分享按钮组件 */
        .share-button-component {
            padding: 8px;
            color: #6b7280;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .share-button-component:hover {
            background: #f3f4f6;
        }
        
        .share-button-component i {
            font-size: 20px;
        }
        
        /* 分享按钮白色变体 */
        .share-button-white {
            color: white;
        }
        
        .share-button-white:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        /* 自定义橙紫渐变 */
        .orange-purple-gradient {
            background: linear-gradient(90deg, #f97316, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
        }
        
        /* 增强版本 - 带悬停效果和发光 */
        .orange-purple-gradient-enhanced {
            background: linear-gradient(90deg, #f97316, #9333ea);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 2px rgba(249, 115, 22, 0.3));
        }

        .orange-purple-gradient-enhanced:hover {
            background: linear-gradient(90deg, #fb923c, #a855f7);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 6px rgba(249, 115, 22, 0.6));
            animation: gradient-pulse 2s ease-in-out infinite;
        }

        /* 亮色版本 - 深色背景用 */
        .orange-purple-gradient-bright {
            background: linear-gradient(90deg, #fbbf24, #c084fc);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 3px rgba(251, 191, 36, 0.4));
        }

        .orange-purple-gradient-bright:hover {
            background: linear-gradient(90deg, #fcd34d, #ddd6fe);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.8));
            animation: gradient-pulse-bright 2s ease-in-out infinite;
        }

        /* 动画关键帧 */
        @keyframes gradient-pulse {
            0%, 100% { 
                filter: drop-shadow(0 0 6px rgba(249, 115, 22, 0.6));
            }
            50% { 
                filter: drop-shadow(0 0 10px rgba(147, 51, 234, 0.8));
            }
        }

        @keyframes gradient-pulse-bright {
            0%, 100% { 
                filter: drop-shadow(0 0 8px rgba(251, 191, 36, 0.8));
            }
            50% { 
                filter: drop-shadow(0 0 12px rgba(192, 132, 252, 1));
            }
        }


    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 用户信息头部 -->
                <div class="user-header">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face"
                                 class="w-16 h-16 rounded-full mr-4 border-2 border-white border-opacity-30" alt="用户头像">
                            <div>
                                <h2 class="text-xl font-bold">张三</h2>
                                <p class="text-white text-opacity-80 text-sm">会员用户</p>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- 学习统计 -->
                <div class="stats-card">
                    <h3 class="font-semibold text-gray-800 mb-4">学习统计</h3>
                    <div class="flex justify-between">
                        <div class="stat-item">
                            <div class="stat-number">3</div>
                            <div class="stat-label">已购课程</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">24.5h</div>
                            <div class="stat-label">学习时长</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">68%</div>
                            <div class="stat-label">完成进度</div>
                        </div>
                    </div>
                </div>

                <!-- 分享数据统计 -->
                <div class="stats-card">
                    <h3 class="font-semibold text-gray-800 mb-4">分享数据</h3>
                    <div class="flex justify-between">
                        <div class="stat-item">
                            <div class="stat-number">12</div>
                            <div class="stat-label">分享次数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">3</div>
                            <div class="stat-label">转化人数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">¥180</div>
                            <div class="stat-label">累计收益</div>
                        </div>
                    </div>
                </div>

                <!-- 分享分成说明 -->
                <div class="stats-card share-highlight">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-1">分享分成机制</h3>
                            <p class="text-sm text-gray-600">分享App给好友，每成交一单获得</p>
                        </div>
                        <div class="text-center">
                            <div class="share-percentage">30%</div>
                            <div class="text-xs text-gray-600">分成收益</div>
                        </div>
                    </div>
                </div>

                <!-- 客服微信 -->
                <div class="stats-card">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-1">客服微信</h3>
                            <p class="text-lg font-mono text-blue-600">shuimu_kf001</p>
                            <p class="text-xs text-gray-500">添加微信验证身份后拉入专属学习群</p>
                        </div>
                        <button class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm">
                            点击复制
                        </button>
                    </div>
                </div>

                <!-- 最近学习 -->
                <div class="stats-card">
                    <h3 class="font-semibold text-gray-800 mb-4">最近学习</h3>
                    <div class="flex items-center">
                        <div class="progress-ring mr-4">
                            <div class="progress-text">4/6</div>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">恋爱宝典1</p>
                            <p class="text-sm text-gray-500">道：恋爱宝典系列</p>
                            <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 67%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="space-y-2">
                    <div class="menu-item" onclick="goToOrders()">
                        <div class="menu-icon" style="background: #10b981;">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">购买记录</p>
                            <p class="text-sm text-gray-500">查看购买历史</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="menu-item" onclick="goToCache()">
                        <div class="menu-icon" style="background: #3b82f6;">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">缓存管理</p>
                            <p class="text-sm text-gray-500">管理本地视频</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="menu-item" onclick="goToReport()">
                        <div class="menu-icon" style="background: #8b5cf6;">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">学习报告</p>
                            <p class="text-sm text-gray-500">详细学习数据</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="menu-item" onclick="goToEarnings()">
                        <div class="menu-icon" style="background: #f59e0b;">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">分享收益</p>
                            <p class="text-sm text-gray-500">查看分成明细</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="menu-item" onclick="goToRanking()">
                        <div class="menu-icon" style="background: #ef4444;">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">分享排行</p>
                            <p class="text-sm text-gray-500">排行榜与奖励</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="menu-item" onclick="goToMaterials()">
                        <div class="menu-icon" style="background: #06b6d4;">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">分享素材</p>
                            <p class="text-sm text-gray-500">文案与配图</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="menu-item" onclick="goToSettings()">
                        <div class="menu-icon" style="background: #6b7280;">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">设置</p>
                            <p class="text-sm text-gray-500">个性化配置</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="menu-item" onclick="goToHelp()">
                        <div class="menu-icon" style="background: #84cc16;">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">帮助与反馈</p>
                            <p class="text-sm text-gray-500">常见问题</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>

                    <div class="menu-item" onclick="goToAbout()">
                        <div class="menu-icon" style="background: #64748b;">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-800">关于我们</p>
                            <p class="text-sm text-gray-500">版本信息</p>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>


        </div>
    </div>

    <!-- 分享弹窗 -->
    <div class="share-modal" id="shareModal" style="display: none;">
        <div class="modal-header">
            <h3 class="text-lg font-semibold text-gray-800">分享推广分成</h3>
            <button onclick="closeShareModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <div class="share-content">
            <p class="text-gray-600 mb-6 text-center">分享给好友，每成交一单获得30%分成收益</p>

            <div class="share-options">
                <div class="share-option" onclick="shareToWechat()">
                    <div class="share-icon" style="background: #07c160;">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <span>微信好友</span>
                </div>

                <div class="share-option" onclick="shareToMoments()">
                    <div class="share-icon" style="background: #1aad19;">
                        <i class="fas fa-camera"></i>
                    </div>
                    <span>朋友圈</span>
                </div>

                <div class="share-option" onclick="copyShareLink()">
                    <div class="share-icon" style="background: #3b82f6;">
                        <i class="fas fa-link"></i>
                    </div>
                    <span>复制链接</span>
                </div>

                <div class="share-option" onclick="shareToQQ()">
                    <div class="share-icon" style="background: #12b7f5;">
                        <i class="fab fa-qq"></i>
                    </div>
                    <span>QQ好友</span>
                </div>
            </div>

            <div class="share-stats">
                <div class="stat-item">
                    <div class="stat-number">12</div>
                    <div class="stat-label">分享次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">转化人数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">¥180</div>
                    <div class="stat-label">累计收益</div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .share-modal {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 50%;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 20px;
            z-index: 9999;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f3f4f6;
        }

        .share-content {
            height: calc(100% - 60px);
            overflow-y: auto;
        }

        .share-options {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .share-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .share-option:hover {
            transform: scale(1.05);
        }

        .share-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 8px;
        }

        .share-option span {
            font-size: 12px;
            color: #6b7280;
            text-align: center;
        }

        .share-stats {
            display: flex;
            justify-content: space-around;
            background: #f9fafb;
            border-radius: 12px;
            padding: 16px;
        }

        .share-stats .stat-item {
            text-align: center;
        }

        .share-stats .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 4px;
        }

        .share-stats .stat-label {
            font-size: 11px;
            color: #6b7280;
        }
    </style>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加顶部导航
            addTopNavigation();
            // 添加底部导航
            addBottomNavigation();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '我的'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        // 添加底部导航函数
        function addBottomNavigation() {
            try {
                // 检查是否已经存在底部导航
                if (document.querySelector('.bottom-nav')) {
                    console.log('底部导航已存在，跳过添加');
                    return;
                }

                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加底部导航');
                    return;
                }

                // 创建底部导航HTML
                const bottomNavHtml = ComponentTemplates.createBottomNavigation({
                    currentPage: 'profile'
                });

                // 添加到页面底部
                document.body.insertAdjacentHTML('beforeend', bottomNavHtml);

                // 初始化底部导航功能
                ComponentTemplates.initBottomNavigation();

                console.log('底部导航添加成功');
            } catch (error) {
                console.error('添加底部导航失败:', error);
            }
        }

        // 返回函数
        function goBack() {
            if (window.navigateTo) {
                navigateTo('01-首页.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '01-首页.html';
            }
        }

        // 导航函数
        function goToOrders() {
            if (window.navigateTo) {
                navigateTo('07-购买记录页面.html');
            } else {
                window.location.href = '07-购买记录页面.html';
            }
        }

        function goToCache() {
            if (window.navigateTo) {
                navigateTo('06-缓存管理页面.html');
            } else {
                window.location.href = '06-缓存管理页面.html';
            }
        }

        function goToReport() {
            if (window.navigateTo) {
                navigateTo('08-学习报告页面.html');
            } else {
                window.location.href = '08-学习报告页面.html';
            }
        }

        function goToEarnings() {
            if (window.navigateTo) {
                navigateTo('12-分享收益页面.html');
            } else {
                window.location.href = '12-分享收益页面.html';
            }
        }

        function goToRanking() {
            if (window.navigateTo) {
                navigateTo('13-分享排行页面.html');
            } else {
                window.location.href = '13-分享排行页面.html';
            }
        }

        function goToMaterials() {
            if (window.navigateTo) {
                navigateTo('14-分享素材页面.html');
            } else {
                window.location.href = '14-分享素材页面.html';
            }
        }

        function goToSettings() {
            if (window.navigateTo) {
                navigateTo('09-设置页面.html');
            } else {
                window.location.href = '09-设置页面.html';
            }
        }

        function goToHelp() {
            if (window.navigateTo) {
                navigateTo('10-帮助与反馈页面.html');
            } else {
                window.location.href = '10-帮助与反馈页面.html';
            }
        }

        function goToAbout() {
            if (window.navigateTo) {
                navigateTo('11-关于我们页面.html');
            } else {
                window.location.href = '11-关于我们页面.html';
            }
        }

        // 分享相关函数
        function showShareModal() {
            const shareModal = document.getElementById('shareModal');
            if (shareModal) {
                shareModal.style.display = 'block';
            }
        }

        function closeShareModal() {
            const shareModal = document.getElementById('shareModal');
            if (shareModal) {
                shareModal.style.display = 'none';
            }
        }

        function shareToWechat() {
            showToast('正在打开微信分享...');
            setTimeout(() => {
                showToast('分享成功！');
                closeShareModal();
            }, 1000);
        }

        function shareToMoments() {
            showToast('正在打开朋友圈分享...');
            setTimeout(() => {
                showToast('分享成功！');
                closeShareModal();
            }, 1000);
        }

        function copyShareLink() {
            const shareLink = 'https://shuimu.app/share?ref=user123';

            if (navigator.clipboard) {
                navigator.clipboard.writeText(shareLink).then(() => {
                    showToast('分享链接已复制到剪贴板');
                }).catch(() => {
                    showToast('复制失败，请手动复制：' + shareLink);
                });
            } else {
                const textArea = document.createElement('textarea');
                textArea.value = shareLink;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    showToast('分享链接已复制到剪贴板');
                } catch (err) {
                    showToast('复制失败，请手动复制：' + shareLink);
                }
                document.body.removeChild(textArea);
            }
            closeShareModal();
        }

        function shareToQQ() {
            showToast('正在打开QQ分享...');
            setTimeout(() => {
                showToast('分享成功！');
                closeShareModal();
            }, 1000);
        }

        // Toast提示函数
        function showToast(message) {
            const toast = document.createElement('div');
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            document.body.appendChild(toast);

            setTimeout(() => toast.style.opacity = '1', 10);
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => toast.remove(), 300);
            }, 2000);
        }
    </script>
</body>
</html>