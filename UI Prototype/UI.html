<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕App - UI设计预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .page-selector {
            transition: all 0.3s ease;
        }
        .page-selector:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .page-selector.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        iframe {
            border: none;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .preview-container {
            background: white;
            border-radius: 25px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-7xl mx-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">水幕App UI设计</h1>
            <p class="text-white/80 text-lg">恋爱学习视频课程平台 - 完整UI设计预览</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- 页面选择器 -->
            <div class="lg:col-span-1">
                <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
                    <h2 class="text-xl font-bold text-white mb-4">页面导航</h2>
                    <div class="space-y-2">
                        <!-- 核心页面 -->
                        <div class="mb-4">
                            <h3 class="text-white/80 text-sm font-medium mb-2">核心页面</h3>
                            <button onclick="loadPage('01-首页.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all">
                                <i class="fas fa-home mr-2"></i>首页
                            </button>
                            <button onclick="loadPage('02-视频播放页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-play mr-2"></i>视频播放
                            </button>
                            <button onclick="loadPage('03-我的页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-user mr-2"></i>我的页面
                            </button>
                            <button onclick="loadPage('04-支付页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-credit-card mr-2"></i>支付页面
                            </button>
                        </div>

                        <!-- 功能页面 -->
                        <div>
                            <h3 class="text-white/80 text-sm font-medium mb-2">功能页面</h3>
                            <button onclick="loadPage('05-登录页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all">
                                <i class="fas fa-sign-in-alt mr-2"></i>登录页面
                            </button>
                            <button onclick="loadPage('06-缓存管理页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-download mr-2"></i>缓存管理
                            </button>
                            <button onclick="loadPage('07-购买记录页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-receipt mr-2"></i>购买记录
                            </button>
                            <button onclick="loadPage('08-学习报告页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-chart-line mr-2"></i>学习报告
                            </button>
                            <button onclick="loadPage('09-设置页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-cog mr-2"></i>设置页面
                            </button>
                            <button onclick="loadPage('10-帮助与反馈页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-question-circle mr-2"></i>帮助反馈
                            </button>
                            <button onclick="loadPage('11-关于我们页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-info-circle mr-2"></i>关于我们
                            </button>
                            <button onclick="loadPage('12-分享收益页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-coins mr-2"></i>分享收益
                            </button>
                            <button onclick="loadPage('13-分享排行页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-trophy mr-2"></i>分享排行
                            </button>
                            <button onclick="loadPage('14-分享素材页面.html')" class="page-selector w-full text-left p-3 bg-white/20 rounded-lg text-white hover:bg-white/30 transition-all mt-2">
                                <i class="fas fa-share-alt mr-2"></i>分享素材
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 页面信息 -->
                <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6 mt-6">
                    <h3 class="text-lg font-bold text-white mb-3">当前页面</h3>
                    <div id="pageInfo" class="text-white/80 text-sm">
                        <p class="mb-2"><strong>页面：</strong><span id="currentPageName">请选择页面</span></p>
                        <p class="mb-2"><strong>尺寸：</strong>375 × 812px</p>
                        <p class="mb-2"><strong>设备：</strong>iPhone 13 Pro</p>
                        <p><strong>状态：</strong><span id="loadStatus">待加载</span></p>
                    </div>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="lg:col-span-3">
                <div class="preview-container">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-bold text-gray-800">页面预览</h2>
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <i class="fas fa-mobile-alt"></i>
                            <span>375 × 812px</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-center">
                        <div class="relative">
                            <!-- iframe容器 -->
                            <div class="relative bg-white rounded-[1.5rem] overflow-hidden shadow-2xl">
                                <iframe 
                                    id="pagePreview" 
                                    src="01-首页.html" 
                                    width="375" 
                                    height="812"
                                    class="block">
                                </iframe>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="text-center mt-8 text-white/60">
            <p>水幕App UI设计 - 共14个页面 | 设计规范：375×812px | 技术栈：TailwindCSS + FontAwesome</p>
        </div>
    </div>

    <script>
        function loadPage(pageName) {
            const iframe = document.getElementById('pagePreview');
            const currentPageName = document.getElementById('currentPageName');
            const loadStatus = document.getElementById('loadStatus');
            
            // 更新页面信息
            currentPageName.textContent = pageName.replace('.html', '');
            loadStatus.textContent = '加载中...';
            
            // 加载页面
            iframe.src = pageName;
            
            // 更新按钮状态
            document.querySelectorAll('.page-selector').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 模拟加载完成
            setTimeout(() => {
                loadStatus.textContent = '加载完成';
            }, 500);
        }

        // 页面加载完成后默认选中首页
        window.addEventListener('load', function() {
            document.querySelector('.page-selector').classList.add('active');
            document.getElementById('currentPageName').textContent = '01-首页';
            document.getElementById('loadStatus').textContent = '加载完成';
        });
    </script>
</body>
</html> 