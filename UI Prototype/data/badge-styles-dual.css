/* ========== 水幕徽章系统样式 - 双套方案 ========== */
/* 观看次数徽章组件 - 支持两套样式方案切换 */

/* 徽章基础组件样式 */
.badge-component {
    font-size: 11px;
    padding: 0px 6px;
    border-radius: 10px;
    margin-left: 6px;
    font-weight: 500;
    flex-shrink: 0;
    display: inline-block;
    position: relative;
    overflow: visible;
    transition: all 0.3s ease;
}

/* ========== 方案一：原版11级钻石系统 ========== */
/* 使用方式：添加 .badge-style-original 类到容器 */

.badge-style-original .badge-component {
    transition: all 0.3s ease; /* 原版有transition */
}

/* 等级0：静态显示，更浅的绿色背景 */
.badge-style-original .badge-level-0 {
    background: #f7fef8;
    color: #166534;
    /* 0次观看无动画效果 */
}

/* 等级1-2：仅闪动效果 */
.badge-style-original .badge-level-1 {
    background: #e6f9ea;
    color: #166534;
    animation: breathe-level-1 1.7s infinite;
}
.badge-style-original .badge-level-2 {
    background: #d1f2db;
    color: #166534;
    animation: breathe-level-2 1.5s infinite;
}

/* 等级3-5：闪动 + 1个钻石，钻石大小递增 */
.badge-style-original .badge-level-3 {
    background: #b8ebc5;
    color: #166534;
    animation: breathe-level-3 3.1s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-original .badge-level-3::before {
    content: '💎';
    position: absolute;
    top: -6px;
    left: -6px;
    font-size: 8px;
    animation: diamond-sparkle-left 2.8s infinite;
    pointer-events: none;
    z-index: 1;
}

.badge-style-original .badge-level-4 {
    background: #9ee4af;
    color: #166534;
    animation: breathe-level-4 2.9s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-original .badge-level-4::before {
    content: '💎';
    position: absolute;
    top: -9px;
    left: -9px;
    font-size: 11px;
    animation: diamond-sparkle-left 2.6s infinite;
    pointer-events: none;
    z-index: 1;
}

.badge-style-original .badge-level-5 {
    background: #84dd99;
    color: #ffffff;
    animation: breathe-level-5 2.7s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-original .badge-level-5::before {
    content: '💎';
    position: absolute;
    top: -12px;
    left: -12px;
    font-size: 14px;
    animation: diamond-sparkle-left 2.4s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级6-9：闪动 + 2个钻石，钻石大小递增 */
.badge-style-original .badge-level-6 {
    background: #6ad683;
    color: #ffffff;
    animation: breathe-level-6 2.5s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-original .badge-level-6::before {
    content: '💎';
    position: absolute;
    top: -6px;
    left: -6px;
    font-size: 8px;
    animation: diamond-sparkle-left 2.2s infinite;
    pointer-events: none;
    z-index: 1;
}
.badge-style-original .badge-level-6::after {
    content: '💎';
    position: absolute;
    top: -6px;
    right: -6px;
    font-size: 8px;
    animation: diamond-sparkle-right 2.0s infinite;
    pointer-events: none;
    z-index: 1;
}

.badge-style-original .badge-level-7 {
    background: #50cf6d;
    color: #ffffff;
    animation: breathe-level-7 2.4s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-original .badge-level-7::before {
    content: '💎';
    position: absolute;
    top: -9px;
    left: -9px;
    font-size: 11px;
    animation: diamond-sparkle-left 2.1s infinite;
    pointer-events: none;
    z-index: 1;
}
.badge-style-original .badge-level-7::after {
    content: '💎';
    position: absolute;
    top: -9px;
    right: -9px;
    font-size: 11px;
    animation: diamond-sparkle-right 1.9s infinite;
    pointer-events: none;
    z-index: 1;
}

.badge-style-original .badge-level-8 {
    background: #36c857;
    color: #ffffff;
    animation: breathe-level-8 2.3s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-original .badge-level-8::before {
    content: '💎';
    position: absolute;
    top: -12px;
    left: -12px;
    font-size: 14px;
    animation: diamond-sparkle-left 2.0s infinite;
    pointer-events: none;
    z-index: 1;
}
.badge-style-original .badge-level-8::after {
    content: '💎';
    position: absolute;
    top: -12px;
    right: -12px;
    font-size: 14px;
    animation: diamond-sparkle-right 1.8s infinite;
    pointer-events: none;
    z-index: 1;
}

.badge-style-original .badge-level-9 {
    background: #2cc157;
    color: #ffffff;
    animation: breathe-level-9 2.2s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-original .badge-level-9::before {
    content: '💎';
    position: absolute;
    top: -15px;
    left: -15px;
    font-size: 17px;
    animation: diamond-sparkle-left 1.9s infinite;
    pointer-events: none;
    z-index: 1;
}
.badge-style-original .badge-level-9::after {
    content: '💎';
    position: absolute;
    top: -15px;
    right: -15px;
    font-size: 17px;
    animation: diamond-sparkle-right 1.7s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级10及以上：传奇徽章 */
.badge-style-original .badge-level-10,
.badge-style-original .badge-level-11,
.badge-style-original .badge-level-12,
.badge-style-original .badge-level-13,
.badge-style-original .badge-level-14,
.badge-style-original .badge-level-15,
.badge-style-original .badge-level-16,
.badge-style-original .badge-level-17,
.badge-style-original .badge-level-18,
.badge-style-original .badge-level-19,
.badge-style-original .badge-level-20,
.badge-style-original .badge-legendary {
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #92400e);
    background-size: 300% 300%;
    color: #ffffff;
    font-weight: bold;
    font-size: 12px;
    box-shadow: 0 0 15px rgba(251, 191, 36, 0.6), 0 0 30px rgba(251, 191, 36, 0.3);
    animation: pulse-legendary 0.8s infinite, breathe-legendary 1.5s infinite, gradient-shift 3s infinite;
    position: relative;
    overflow: visible;
    border: 1px solid rgba(255, 255, 255, 0.3);
}
.badge-style-original .badge-level-10::before,
.badge-style-original .badge-level-11::before,
.badge-style-original .badge-level-12::before,
.badge-style-original .badge-level-13::before,
.badge-style-original .badge-level-14::before,
.badge-style-original .badge-level-15::before,
.badge-style-original .badge-level-16::before,
.badge-style-original .badge-level-17::before,
.badge-style-original .badge-level-18::before,
.badge-style-original .badge-level-19::before,
.badge-style-original .badge-level-20::before,
.badge-style-original .badge-legendary::before {
    content: '👑';
    position: absolute;
    top: -20px;
    left: -12px;
    font-size: 20px;
    animation: crown-sparkle 1.2s infinite;
    pointer-events: none;
    z-index: 2;
}
.badge-style-original .badge-level-10::after,
.badge-style-original .badge-level-11::after,
.badge-style-original .badge-level-12::after,
.badge-style-original .badge-level-13::after,
.badge-style-original .badge-level-14::after,
.badge-style-original .badge-level-15::after,
.badge-style-original .badge-level-16::after,
.badge-style-original .badge-level-17::after,
.badge-style-original .badge-level-18::after,
.badge-style-original .badge-level-19::after,
.badge-style-original .badge-level-20::after,
.badge-style-original .badge-legendary::after {
    content: '👧';
    position: absolute;
    top: -20px;
    right: -10px;
    font-size: 18px;
    animation: girl-sparkle 1.2s infinite;
    pointer-events: none;
    z-index: 2;
}

/* ========== 方案二：混合10级图标系统 ========== */
/* 使用方式：添加 .badge-style-mixed 类到容器 */

.badge-style-mixed .badge-component {
    transition: none; /* 混合版本无transition */
}

/* 等级0：静态显示，更浅的绿色背景 */
.badge-style-mixed .badge-level-0 {
    background: #f7fef8;
    color: #166534;
    /* 0次观看无动画效果 */
}

/* 等级1：1个海星 */
.badge-style-mixed .badge-level-1 {
    background: #f0fdf4;
    color: #166534;
    animation: starfish-grow 2.5s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-mixed .badge-level-1::before {
    content: '⭐';
    position: absolute;
    top: -9px;
    left: -9px;
    font-size: 11px;
    animation: starfish-twinkle 2.8s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级2：2个海星 */
.badge-style-mixed .badge-level-2 {
    background: #e6f9ea;
    color: #166534;
    animation: starfish-grow 2.3s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-mixed .badge-level-2::before {
    content: '⭐';
    position: absolute;
    top: -7px;
    left: -10px;
    font-size: 11px;
    animation: starfish-twinkle 2.6s infinite;
    pointer-events: none;
    z-index: 1;
}
.badge-style-mixed .badge-level-2::after {
    content: '⭐';
    position: absolute;
    top: -7px;
    right: -10px;
    font-size: 11px;
    animation: starfish-twinkle 2.4s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级3：1个闪耀 */
.badge-style-mixed .badge-level-3 {
    background: #b8ebc5;
    color: #166534;
    animation: sparkle-grow 2.0s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-mixed .badge-level-3::before {
    content: '✨';
    position: absolute;
    top: -14px;
    left: -14px;
    font-size: 16px;
    animation: sparkle-twinkle 2.2s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级4：2个闪耀 */
.badge-style-mixed .badge-level-4 {
    background: #9ee4af;
    color: #166534;
    animation: sparkle-grow 1.8s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-mixed .badge-level-4::before {
    content: '✨';
    position: absolute;
    top: -12px;
    left: -15px;
    font-size: 16px;
    animation: sparkle-twinkle 2.0s infinite;
    pointer-events: none;
    z-index: 1;
}
.badge-style-mixed .badge-level-4::after {
    content: '✨';
    position: absolute;
    top: -12px;
    right: -15px;
    font-size: 16px;
    animation: sparkle-twinkle 1.8s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级5：1个钻石 */
.badge-style-mixed .badge-level-5 {
    background: #84dd99;
    color: #ffffff;
    animation: diamond-grow 2.2s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-mixed .badge-level-5::before {
    content: '💎';
    position: absolute;
    top: -14px;
    left: -14px;
    font-size: 16px;
    animation: diamond-sparkle 2.4s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级6：2个钻石 */
.badge-style-mixed .badge-level-6 {
    background: #6ad683;
    color: #ffffff;
    animation: diamond-grow 2.0s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-mixed .badge-level-6::before {
    content: '💎';
    position: absolute;
    top: -12px;
    left: -15px;
    font-size: 16px;
    animation: diamond-sparkle 2.2s infinite;
    pointer-events: none;
    z-index: 1;
}
.badge-style-mixed .badge-level-6::after {
    content: '💎';
    position: absolute;
    top: -12px;
    right: -15px;
    font-size: 16px;
    animation: diamond-sparkle 2.0s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级7：1个粉钻 */
.badge-style-mixed .badge-level-7 {
    background: #50cf6d;
    color: #ffffff;
    animation: pink-diamond-grow 1.8s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-mixed .badge-level-7::before {
    content: '💎';
    position: absolute;
    top: -14px;
    left: -14px;
    font-size: 16px;
    animation: pink-diamond-sparkle 2.0s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级8：2个粉钻 */
.badge-style-mixed .badge-level-8 {
    background: #36c857;
    color: #ffffff;
    animation: pink-diamond-grow 1.6s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-mixed .badge-level-8::before {
    content: '💎';
    position: absolute;
    top: -12px;
    left: -15px;
    font-size: 16px;
    animation: pink-diamond-sparkle-clockwise 1.8s infinite;
    pointer-events: none;
    z-index: 1;
}
.badge-style-mixed .badge-level-8::after {
    content: '💎';
    position: absolute;
    top: -12px;
    right: -15px;
    font-size: 16px;
    animation: pink-diamond-sparkle-counter 1.6s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级9：1个皇冠 */
.badge-style-mixed .badge-level-9 {
    background: #2cc157;
    color: #ffffff;
    animation: crown-grow 0.8s infinite;
    position: relative;
    overflow: visible;
}
.badge-style-mixed .badge-level-9::before {
    content: '👑';
    position: absolute;
    top: -19px;
    left: -16px;
    font-size: 20px;
    animation: crown-sparkle 2.0s infinite;
    pointer-events: none;
    z-index: 1;
}

/* 等级10及以上：传奇徽章（皇冠+头像） */
.badge-style-mixed .badge-level-10,
.badge-style-mixed .badge-level-11,
.badge-style-mixed .badge-level-12,
.badge-style-mixed .badge-level-13,
.badge-style-mixed .badge-level-14,
.badge-style-mixed .badge-level-15,
.badge-style-mixed .badge-level-16,
.badge-style-mixed .badge-level-17,
.badge-style-mixed .badge-level-18,
.badge-style-mixed .badge-level-19,
.badge-style-mixed .badge-level-20,
.badge-style-mixed .badge-legendary {
    background: linear-gradient(45deg, #fbbf24, #f59e0b, #d97706, #92400e);
    background-size: 300% 300%;
    color: #ffffff;
    font-weight: bold;
    font-size: 12px;
    box-shadow: 0 0 15px rgba(251, 191, 36, 0.6), 0 0 30px rgba(251, 191, 36, 0.3);
    animation: pulse-legendary 0.8s infinite, breathe-legendary 1.5s infinite, gradient-shift 3s infinite;
    position: relative;
    overflow: visible;
    border: 1px solid rgba(255, 255, 255, 0.3);
}
.badge-style-mixed .badge-level-10::before,
.badge-style-mixed .badge-level-11::before,
.badge-style-mixed .badge-level-12::before,
.badge-style-mixed .badge-level-13::before,
.badge-style-mixed .badge-level-14::before,
.badge-style-mixed .badge-level-15::before,
.badge-style-mixed .badge-level-16::before,
.badge-style-mixed .badge-level-17::before,
.badge-style-mixed .badge-level-18::before,
.badge-style-mixed .badge-level-19::before,
.badge-style-mixed .badge-level-20::before,
.badge-style-mixed .badge-legendary::before {
    content: '👑';
    position: absolute;
    top: -20px;
    left: -12px;
    font-size: 20px;
    animation: crown-sparkle 2s infinite;
    pointer-events: none;
    z-index: 2;
}
.badge-style-mixed .badge-level-10::after,
.badge-style-mixed .badge-level-11::after,
.badge-style-mixed .badge-level-12::after,
.badge-style-mixed .badge-level-13::after,
.badge-style-mixed .badge-level-14::after,
.badge-style-mixed .badge-level-15::after,
.badge-style-mixed .badge-level-16::after,
.badge-style-mixed .badge-level-17::after,
.badge-style-mixed .badge-level-18::after,
.badge-style-mixed .badge-level-19::after,
.badge-style-mixed .badge-level-20::after,
.badge-style-mixed .badge-legendary::after {
    content: '👧';
    position: absolute;
    top: -20px;
    right: -10px;
    font-size: 18px;
    animation: girl-sparkle 2s infinite;
    pointer-events: none;
    z-index: 2;
}

/* ========== 原版动画关键帧 ========== */

/* 原版呼吸动画 */
@keyframes breathe-level-1 {
    0%, 100% { 
        background: #e6f9ea; 
        box-shadow: 0 0 5px rgba(230, 249, 234, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #dcfce7; 
        box-shadow: 0 0 10px rgba(230, 249, 234, 0.6); 
        transform: scale(1.02);
    }
}

@keyframes breathe-level-2 {
    0%, 100% { 
        background: #d1f2db; 
        box-shadow: 0 0 5px rgba(209, 242, 219, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #bbf7d0; 
        box-shadow: 0 0 10px rgba(209, 242, 219, 0.6); 
        transform: scale(1.02);
    }
}

@keyframes breathe-level-3 {
    0%, 100% { 
        background: #b8ebc5; 
        box-shadow: 0 0 6px rgba(184, 235, 197, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #9ee4af; 
        box-shadow: 0 0 12px rgba(184, 235, 197, 0.6); 
        transform: scale(1.03);
    }
}

@keyframes breathe-level-4 {
    0%, 100% { 
        background: #9ee4af; 
        box-shadow: 0 0 6px rgba(158, 228, 175, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #84dd99; 
        box-shadow: 0 0 12px rgba(158, 228, 175, 0.6); 
        transform: scale(1.03);
    }
}

@keyframes breathe-level-5 {
    0%, 100% { 
        background: #84dd99; 
        box-shadow: 0 0 7px rgba(132, 221, 153, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #6ad683; 
        box-shadow: 0 0 14px rgba(132, 221, 153, 0.6); 
        transform: scale(1.04);
    }
}

@keyframes breathe-level-6 {
    0%, 100% { 
        background: #6ad683; 
        box-shadow: 0 0 8px rgba(106, 214, 131, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #50cf6d; 
        box-shadow: 0 0 16px rgba(106, 214, 131, 0.6); 
        transform: scale(1.05);
    }
}

@keyframes breathe-level-7 {
    0%, 100% { 
        background: #50cf6d; 
        box-shadow: 0 0 9px rgba(80, 207, 109, 0.5); 
        transform: scale(1);
    }
    50% { 
        background: #36c857; 
        box-shadow: 0 0 18px rgba(80, 207, 109, 0.7); 
        transform: scale(1.06);
    }
}

@keyframes breathe-level-8 {
    0%, 100% { 
        background: #36c857; 
        box-shadow: 0 0 10px rgba(54, 200, 87, 0.5); 
        transform: scale(1);
    }
    50% { 
        background: #2cc157; 
        box-shadow: 0 0 20px rgba(54, 200, 87, 0.7); 
        transform: scale(1.07);
    }
}

@keyframes breathe-level-9 {
    0%, 100% { 
        background: #2cc157; 
        box-shadow: 0 0 12px rgba(44, 193, 87, 0.6); 
        transform: scale(1);
    }
    50% { 
        background: #22c55e; 
        box-shadow: 0 0 24px rgba(44, 193, 87, 0.8); 
        transform: scale(1.08);
    }
}

/* 原版钻石动画 */
@keyframes diamond-sparkle-left {
    0%, 100% {
        opacity: 0.3;
        transform: rotate(0deg) scale(0.8);
    }
    20% {
        opacity: 1;
        transform: rotate(72deg) scale(1.1);
    }
    40% {
        opacity: 0.6;
        transform: rotate(144deg) scale(0.9);
    }
    60% {
        opacity: 1;
        transform: rotate(216deg) scale(1.2);
    }
    80% {
        opacity: 0.7;
        transform: rotate(288deg) scale(1);
    }
}

@keyframes diamond-sparkle-right {
    0%, 100% {
        opacity: 0.3;
        transform: rotate(0deg) scale(0.8);
    }
    20% {
        opacity: 1;
        transform: rotate(-72deg) scale(1.1);
    }
    40% {
        opacity: 0.6;
        transform: rotate(-144deg) scale(0.9);
    }
    60% {
        opacity: 1;
        transform: rotate(-216deg) scale(1.2);
    }
    80% {
        opacity: 0.7;
        transform: rotate(-288deg) scale(1);
    }
}

/* ========== 混合版动画关键帧 ========== */

/* 海星生长动画 */
@keyframes starfish-grow {
    0%, 100% { 
        background: #f0fdf4; 
        box-shadow: 0 0 5px rgba(240, 253, 244, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #dcfce7; 
        box-shadow: 0 0 10px rgba(240, 253, 244, 0.6); 
        transform: scale(1.02);
    }
}

/* 闪耀生长动画 */
@keyframes sparkle-grow {
    0%, 100% { 
        background: #f0fdf4; 
        box-shadow: 0 0 5px rgba(240, 253, 244, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #dcfce7; 
        box-shadow: 0 0 10px rgba(240, 253, 244, 0.6); 
        transform: scale(1.02);
    }
}

/* 樱花生长动画 */
@keyframes blossom-grow {
    0%, 100% { 
        background: #b8ebc5; 
        box-shadow: 0 0 6px rgba(184, 235, 197, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #9ee4af; 
        box-shadow: 0 0 12px rgba(184, 235, 197, 0.6); 
        transform: scale(1.03);
    }
}

/* 钻石生长动画 */
@keyframes diamond-grow {
    0%, 100% { 
        background: #84dd99; 
        box-shadow: 0 0 7px rgba(132, 221, 153, 0.4); 
        transform: scale(1);
    }
    50% { 
        background: #6ad683; 
        box-shadow: 0 0 14px rgba(132, 221, 153, 0.6); 
        transform: scale(1.04);
    }
}

/* 粉钻生长动画 */
@keyframes pink-diamond-grow {
    0%, 100% { 
        background: #50cf6d; 
        box-shadow: 0 0 10px rgba(80, 207, 109, 0.5); 
        transform: scale(1);
    }
    50% { 
        background: #36c857; 
        box-shadow: 0 0 15px rgba(80, 207, 109, 0.7); 
        transform: scale(1.05);
    }
}

/* 皇冠生长动画 */
@keyframes crown-grow {
    0%, 100% { 
        background: #2cc157; 
        box-shadow: 0 0 10px rgba(44, 193, 87, 0.5); 
        transform: scale(1);
    }
    50% { 
        background: #22c55e; 
        box-shadow: 0 0 15px rgba(44, 193, 87, 0.7); 
        transform: scale(1.05);
    }
}

/* 海星装饰动画 - 正常黄色效果 */
@keyframes starfish-twinkle {
    0%, 100% {
        opacity: 0.4;
        transform: rotate(0deg) scale(0.8) translateY(0px);
        filter: brightness(1.1) saturate(1.2);
    }
    25% {
        opacity: 0.9;
        transform: rotate(15deg) scale(1.1) translateY(-1px);
        filter: brightness(1.1) saturate(1.2);
    }
    50% {
        opacity: 1;
        transform: rotate(0deg) scale(1.2) translateY(-2px);
        filter: brightness(1.1) saturate(1.2);
    }
    75% {
        opacity: 0.8;
        transform: rotate(-15deg) scale(1) translateY(-1px);
        filter: brightness(1.1) saturate(1.2);
    }
}

/* 闪耀装饰动画 */
@keyframes sparkle-twinkle {
    0%, 100% {
        opacity: 0.4;
        transform: rotate(0deg) scale(0.8);
    }
    25% {
        opacity: 1;
        transform: rotate(90deg) scale(1.2);
    }
    50% {
        opacity: 0.7;
        transform: rotate(180deg) scale(1);
    }
    75% {
        opacity: 1;
        transform: rotate(270deg) scale(1.1);
    }
}

/* 樱花装饰动画 - 深金黄色效果 */
@keyframes blossom-sparkle {
    0%, 100% {
        opacity: 0.4;
        transform: rotate(0deg) scale(0.8) translateY(0px);
        filter: hue-rotate(45deg) saturate(1.6) brightness(1.0);
    }
    25% {
        opacity: 0.9;
        transform: rotate(10deg) scale(1.1) translateY(-1px);
        filter: hue-rotate(50deg) saturate(1.7) brightness(1.05);
    }
    50% {
        opacity: 1;
        transform: rotate(0deg) scale(1.2) translateY(-2px);
        filter: hue-rotate(55deg) saturate(1.8) brightness(1.1);
    }
    75% {
        opacity: 0.8;
        transform: rotate(-10deg) scale(1) translateY(-1px);
        filter: hue-rotate(50deg) saturate(1.7) brightness(1.05);
    }
}

/* 钻石装饰动画 */
@keyframes diamond-sparkle {
    0%, 100% {
        opacity: 0.3;
        transform: rotate(0deg) scale(0.8);
    }
    20% {
        opacity: 1;
        transform: rotate(72deg) scale(1.1);
    }
    40% {
        opacity: 0.6;
        transform: rotate(144deg) scale(0.9);
    }
    60% {
        opacity: 1;
        transform: rotate(216deg) scale(1.2);
    }
    80% {
        opacity: 0.7;
        transform: rotate(288deg) scale(1);
    }
}

/* 粉钻装饰动画 - 粉色渐变效果 */
@keyframes pink-diamond-sparkle {
    0%, 100% {
        opacity: 0.3;
        transform: rotate(0deg) scale(0.8);
        filter: brightness(1.3) contrast(1.2) hue-rotate(70deg);
    }
    20% {
        opacity: 1;
        transform: rotate(72deg) scale(1.1);
        filter: brightness(1.8) contrast(1.5) hue-rotate(85deg);
    }
    40% {
        opacity: 0.6;
        transform: rotate(144deg) scale(0.9);
        filter: brightness(1.4) contrast(1.3) hue-rotate(100deg);
    }
    60% {
        opacity: 1;
        transform: rotate(216deg) scale(1.2);
        filter: brightness(2) contrast(1.6) hue-rotate(120deg);
    }
    80% {
        opacity: 0.7;
        transform: rotate(288deg) scale(1);
        filter: brightness(1.5) contrast(1.4) hue-rotate(105deg);
    }
}

/* 粉钻装饰动画 - 顺时针版本 */
@keyframes pink-diamond-sparkle-clockwise {
    0%, 100% {
        opacity: 0.3;
        transform: rotate(0deg) scale(0.8);
        filter: brightness(1.3) contrast(1.2) hue-rotate(70deg);
    }
    20% {
        opacity: 1;
        transform: rotate(72deg) scale(1.1);
        filter: brightness(1.8) contrast(1.5) hue-rotate(85deg);
    }
    40% {
        opacity: 0.6;
        transform: rotate(144deg) scale(0.9);
        filter: brightness(1.4) contrast(1.3) hue-rotate(100deg);
    }
    60% {
        opacity: 1;
        transform: rotate(216deg) scale(1.2);
        filter: brightness(2) contrast(1.6) hue-rotate(120deg);
    }
    80% {
        opacity: 0.7;
        transform: rotate(288deg) scale(1);
        filter: brightness(1.5) contrast(1.4) hue-rotate(105deg);
    }
}

/* 粉钻装饰动画 - 逆时针版本 */
@keyframes pink-diamond-sparkle-counter {
    0%, 100% {
        opacity: 0.3;
        transform: rotate(0deg) scale(0.8);
        filter: brightness(1.3) contrast(1.2) hue-rotate(70deg);
    }
    20% {
        opacity: 1;
        transform: rotate(-72deg) scale(1.1);
        filter: brightness(1.8) contrast(1.5) hue-rotate(85deg);
    }
    40% {
        opacity: 0.6;
        transform: rotate(-144deg) scale(0.9);
        filter: brightness(1.4) contrast(1.3) hue-rotate(100deg);
    }
    60% {
        opacity: 1;
        transform: rotate(-216deg) scale(1.2);
        filter: brightness(2) contrast(1.6) hue-rotate(120deg);
    }
    80% {
        opacity: 0.7;
        transform: rotate(-288deg) scale(1);
        filter: brightness(1.5) contrast(1.4) hue-rotate(105deg);
    }
}

/* ========== 共用动画关键帧 ========== */

/* 皇冠装饰动画 */
@keyframes crown-sparkle {
    0%, 100% {
        opacity: 0.9;
        transform: rotate(0deg) scale(0.9);
    }
    25% {
        opacity: 1;
        transform: rotate(10deg) scale(1.1);
    }
    50% {
        opacity: 0.95;
        transform: rotate(0deg) scale(1);
    }
    75% {
        opacity: 1;
        transform: rotate(-10deg) scale(1.1);
    }
}

/* 传奇等级动画 */
@keyframes pulse-legendary {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes breathe-legendary {
    0%, 100% {
        box-shadow: 0 0 15px rgba(251, 191, 36, 0.6), 0 0 30px rgba(251, 191, 36, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(251, 191, 36, 0.8), 0 0 40px rgba(251, 191, 36, 0.4);
    }
}

/* 女孩头像动画 */
@keyframes girl-sparkle {
    0%, 100% {
        opacity: 0.9;
        transform: scale(0.9) rotate(0deg);
    }
    25% {
        opacity: 1;
        transform: scale(1.1) rotate(-8deg);
    }
    50% {
        opacity: 0.95;
        transform: scale(1) rotate(0deg);
    }
    75% {
        opacity: 1;
        transform: scale(1.1) rotate(8deg);
    }
}

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* ========== 使用说明 ========== */
/*
使用方法：

1. 原版11级钻石系统：
   在容器元素上添加 .badge-style-original 类
   <div class="badge-style-original">
       <span class="badge-component badge-level-3">×3</span>
   </div>

2. 混合10级图标系统（已更新）：
   在容器元素上添加 .badge-style-mixed 类
   <div class="badge-style-mixed">
       <span class="badge-component badge-level-3">×3</span>
   </div>
   
   图标配置：
   - 等级1-2：⭐ 海星
   - 等级3-4：✨ 闪耀
   - 等级5-6：💎 钻石
   - 等级7-8：💎 粉钻（粉色滤镜）
   - 等级9：👑 皇冠
   - 等级10+：👑 皇冠 + 👧 女孩头像

3. JavaScript控制切换：
   function setBadgeStyle(style) {
       const container = document.querySelector('.badge-container');
       container.className = 'badge-container badge-style-' + style;
   }
   
   setBadgeStyle('original'); // 使用原版样式
   setBadgeStyle('mixed');    // 使用混合样式
*/ 