// 数据管理器文件
// 统一管理所有数据模块的初始化和交互

const DataManager = {
    // 初始化状态
    initialized: false,
    
    // 初始化所有数据模块
    init: function() {
        try {
            console.log('初始化数据管理器...');
            
            // 初始化各个模块
            if (window.AppData) {
                AppData.init();
            }
            
            if (window.UserState) {
                UserState.init();
            }
            
            if (window.CacheManager) {
                CacheManager.init();
            }
            
            // 只在首次访问或长时间未访问时执行清理检查
            this.performCleanupCheckIfNeeded();
            
            this.initialized = true;
            console.log('数据管理器初始化完成');
            
        } catch (error) {
            console.error('数据管理器初始化失败:', error);
        }
    },
    
    // ==================== 统一的ID判断逻辑 ====================
    
    // 根据标题和分类获取视频ID
    getVideoId: function(title, category) {
        if (!window.AppData || !AppData.categories) {
            console.warn('AppData未加载，使用fallback方法生成ID');
            return `${category}|||${title}`;
        }

        // 在指定分类中查找视频
        if (category && AppData.categories[category] && AppData.categories[category].videos) {
            const video = AppData.categories[category].videos.find(v => v.title === title);
            if (video && video.id) {
                return video.id;
            }
        }

        // 如果在指定分类中没找到，在所有分类中查找
        for (const [cat, categoryData] of Object.entries(AppData.categories)) {
            if (categoryData.videos) {
                const video = categoryData.videos.find(v => v.title === title);
                if (video && video.id) {
                    return video.id;
                }
            }
        }

        console.warn(`未找到视频ID: ${title} in ${category}，使用fallback方法`);
        return `${category}|||${title}`;
    },
    
    // 根据ID或标题查找视频对象（统一查找方法）
    findVideoByIdOrTitle: function(videoId, title, category) {
        if (!window.AppData || !AppData.categories) {
            console.warn('AppData未加载');
            return null;
        }

        // 首先尝试通过ID查找（如果ID不是fallback格式）
        if (videoId && !videoId.includes('|||')) {
            for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                if (categoryData.videos) {
                    const video = categoryData.videos.find(v => v.id === videoId);
                    if (video) {
                        return { video, category: cat };
                    }
                }
            }
        }

        // 如果通过ID没找到，回退到标题查找
        if (title) {
            // 在指定分类中查找
            if (category && AppData.categories[category] && AppData.categories[category].videos) {
                const video = AppData.categories[category].videos.find(v => v.title === title);
                if (video) {
                    return { video, category };
                }
            }

            // 在所有分类中查找
            for (const [cat, categoryData] of Object.entries(AppData.categories)) {
                if (categoryData.videos) {
                    const video = categoryData.videos.find(v => v.title === title);
                    if (video) {
                        return { video, category: cat };
                    }
                }
            }
        }

        return null;
    },

    // ==================== 动态数据获取功能 ====================
    
    // 获取当前播放的视频信息（动态）
    getCurrentVideo: function() {
        // 从sessionStorage或其他状态管理中获取当前视频
        try {
            const currentVideoInfo = sessionStorage.getItem('currentVideoInfo');
            if (currentVideoInfo) {
                return JSON.parse(currentVideoInfo);
            }
        } catch (error) {
            console.warn('获取当前视频信息失败:', error);
        }
        
        // 如果没有存储的信息，返回默认的第一个视频
        return this.getDefaultCurrentVideo();
    },
    
    // 获取默认当前视频（从AppData动态获取）
    getDefaultCurrentVideo: function() {
        if (!window.AppData || !AppData.categories) {
            return {
                title: '06. 深度沟通艺术',
                category: '道：恋爱宝典系列·恋爱宝典1'
            };
        }
        
        // 查找第一个已购买分类的第一个视频
        for (const [categoryKey, categoryData] of Object.entries(AppData.categories)) {
            if (categoryData.isPurchased && categoryData.videos && categoryData.videos.length > 0) {
                // 查找进度最高的视频作为当前视频
                let currentVideo = categoryData.videos[0];
                let maxProgress = currentVideo.defaultProgress || 0;
                
                categoryData.videos.forEach(video => {
                    const progress = video.defaultProgress || 0;
                    if (progress > maxProgress) {
                        maxProgress = progress;
                        currentVideo = video;
                    }
                });
                
                return {
                    title: currentVideo.title,
                    category: categoryKey
                };
            }
        }
        
        // 如果没有已购买的分类，返回第一个免费视频
        for (const [categoryKey, categoryData] of Object.entries(AppData.categories)) {
            if (categoryData.isFree && categoryData.videos && categoryData.videos.length > 0) {
                return {
                    title: categoryData.videos[0].title,
                    category: categoryKey
                };
            }
        }
        
        // 最后的回退
        return {
            title: '06. 深度沟通艺术',
            category: '道：恋爱宝典系列·恋爱宝典1'
        };
    },
    
    // 设置当前视频
    setCurrentVideo: function(title, category) {
        const videoInfo = {
            title: title,
            category: category,
            timestamp: Date.now()
        };
        
        try {
            sessionStorage.setItem('currentVideoInfo', JSON.stringify(videoInfo));
            console.log(`设置当前视频: ${title} - ${category}`);
        } catch (error) {
            console.warn('设置当前视频失败:', error);
        }
    },
    
    // 检查是否为当前播放的视频
    isCurrentVideo: function(title, category) {
        const currentVideo = this.getCurrentVideo();
        return currentVideo.title === title && currentVideo.category === category;
    },

    // ==================== 原有功能保持不变 ====================
    
    // 智能清理检查 - 只在需要时执行
    performCleanupCheckIfNeeded: function() {
        if (!window.CacheManager) return;
        
        try {
            // 检查是否为首次访问
            const lastVisit = localStorage.getItem('shuimu_last_visit');
            const now = Date.now();
            
            if (!lastVisit) {
                // 首次访问，不执行清理
                localStorage.setItem('shuimu_last_visit', now.toString());
                console.log('首次访问，跳过清理检查');
                return;
            }
            
            const lastVisitTime = parseInt(lastVisit);
            const timeSinceLastVisit = now - lastVisitTime;
            const oneDayInMs = 24 * 60 * 60 * 1000;
            
            // 只有超过1天未访问且CacheManager认为需要清理时才执行清理
            if (timeSinceLastVisit > oneDayInMs && CacheManager.shouldCleanup()) {
                console.log('执行定期缓存清理...');
                const cleanedCount = CacheManager.cleanupUnpurchasedVideoCache();
                if (cleanedCount > 0) {
                    console.log(`清理了 ${cleanedCount} 个未购买视频的缓存`);
                }
            } else {
                console.log('跳过清理检查 - 最近访问过或未到清理时间');
            }
            
            // 更新最后访问时间
            localStorage.setItem('shuimu_last_visit', now.toString());
            
        } catch (error) {
            console.error('清理检查失败:', error);
        }
    },
    
    // 执行清理检查（保留原方法供手动调用）
    performCleanupCheck: function() {
        if (!window.CacheManager) return;
        
        // 检查是否需要清理
        if (CacheManager.shouldCleanup()) {
            console.log('执行定期缓存清理...');
            CacheManager.cleanupUnpurchasedVideoCache();
        }
    },
    
    // 获取视频完整信息（包含用户状态）
    getVideoInfo: function(videoTitle, category = null) {
        if (!window.AppData) return null;
        
        // 使用统一查找方法
        const videoId = this.getVideoId(videoTitle, category);
        const result = this.findVideoByIdOrTitle(videoId, videoTitle, category);
        
        if (!result || !result.video) {
            return null;
        }
        
        const videoData = result.video;
        const videoCategory = result.category;
        
        // 添加用户状态信息
        const videoInfo = { ...videoData, category: videoCategory };
        
        if (window.UserState) {
            videoInfo.progress = UserState.getVideoProgress(videoTitle);
            videoInfo.watchCount = UserState.getWatchCount(videoTitle);
            videoInfo.isFavorite = UserState.isFavorite(videoTitle);
            videoInfo.badgeLevel = UserState.getWatchBadgeLevel(videoTitle);
            videoInfo.isPurchased = UserState.isPurchased(videoCategory);
        }
        
        if (window.CacheManager) {
            videoInfo.cacheStatus = CacheManager.getCacheStatus(videoTitle);
            videoInfo.isCached = CacheManager.isCached(videoTitle);
            videoInfo.cacheProgress = CacheManager.getCacheProgress(videoTitle);
        }
        
        return videoInfo;
    },
    
    // 获取分类完整信息（包含用户状态）
    getCategoryInfo: function(categoryName) {
        if (!window.AppData) return null;
        
        const categoryData = AppData.categories[categoryName];
        if (!categoryData) return null;
        
        const categoryInfo = { ...categoryData };
        
        if (window.UserState) {
            categoryInfo.isPurchased = UserState.isPurchased(categoryName);
        }
        
        // 计算分类的整体进度和统计
        const videos = categoryData.videos || [];
        if (videos.length > 0 && window.UserState) {
            let totalProgress = 0;
            let totalWatchCount = 0;
            let completedVideos = 0;
            
            videos.forEach(video => {
                const progress = UserState.getVideoProgress(video.title);
                const watchCount = UserState.getWatchCount(video.title);
                
                totalProgress += progress;
                totalWatchCount += watchCount;
                
                if (progress >= 100) {
                    completedVideos++;
                }
            });
            
            categoryInfo.averageProgress = Math.round(totalProgress / videos.length);
            categoryInfo.totalWatchCount = totalWatchCount;
            categoryInfo.completedVideos = completedVideos;
            categoryInfo.completionRate = Math.round((completedVideos / videos.length) * 100);
        }
        
        return categoryInfo;
    },
    
    // 获取系列完整信息（包含用户状态）
    getSeriesInfo: function(seriesName) {
        if (!window.AppData) return null;
        
        const seriesData = AppData.series[seriesName];
        if (!seriesData) return null;
        
        const seriesInfo = { ...seriesData };
        
        if (window.UserState) {
            seriesInfo.isPurchased = UserState.isPurchased(seriesName);
        }
        
        // 计算系列的整体统计
        const categories = seriesData.categories || [];
        let allVideos = [];
        
        categories.forEach(categoryName => {
            const categoryData = AppData.categories[categoryName];
            if (categoryData && categoryData.videos) {
                allVideos = allVideos.concat(categoryData.videos);
            }
        });
        
        if (allVideos.length > 0 && window.UserState) {
            let totalProgress = 0;
            let totalWatchCount = 0;
            let completedVideos = 0;
            
            allVideos.forEach(video => {
                const progress = UserState.getVideoProgress(video.title);
                const watchCount = UserState.getWatchCount(video.title);
                
                totalProgress += progress;
                totalWatchCount += watchCount;
                
                if (progress >= 100) {
                    completedVideos++;
                }
            });
            
            seriesInfo.averageProgress = Math.round(totalProgress / allVideos.length);
            seriesInfo.totalWatchCount = totalWatchCount;
            seriesInfo.completedVideos = completedVideos;
            seriesInfo.completionRate = Math.round((completedVideos / allVideos.length) * 100);
        }
        
        return seriesInfo;
    },
    
    // 搜索视频（使用统一的ID判断）
    searchVideos: function(query, options = {}) {
        if (!window.AppData || !query) return [];
        
        const {
            includeTitle = true,
            includeDescription = true,
            includeCategory = true,
            maxResults = 50
        } = options;
        
        const results = [];
        const queryLower = query.toLowerCase();
        
        // 遍历所有分类的视频
        Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
            if (categoryData.videos) {
                categoryData.videos.forEach(video => {
                    let matches = false;
                    let relevance = 0;
                    
                    // 标题匹配
                    if (includeTitle && video.title.toLowerCase().includes(queryLower)) {
                        matches = true;
                        relevance += video.title.toLowerCase().indexOf(queryLower) === 0 ? 10 : 5;
                    }
                    
                    // 描述匹配
                    if (includeDescription && video.description && video.description.toLowerCase().includes(queryLower)) {
                        matches = true;
                        relevance += 3;
                    }
                    
                    // 分类匹配
                    if (includeCategory && categoryKey.toLowerCase().includes(queryLower)) {
                        matches = true;
                        relevance += 2;
                    }
                    
                    if (matches) {
                        const videoInfo = this.getVideoInfo(video.title, categoryKey);
                        if (videoInfo) {
                            results.push({
                                ...videoInfo,
                                relevance: relevance
                            });
                        }
                    }
                });
            }
        });
        
        // 按相关性排序
        results.sort((a, b) => b.relevance - a.relevance);
        
        // 限制结果数量
        return results.slice(0, maxResults);
    },
    
    // 播放视频（更新相关状态）
    playVideo: function(videoTitle, category) {
        console.log(`播放视频: ${videoTitle}`);
        
        // 设置为当前视频
        this.setCurrentVideo(videoTitle, category);
        
        if (window.UserState) {
            // 增加观看次数
            UserState.incrementWatchCount(videoTitle);
            
            // 添加到最近观看
            UserState.addRecentWatched(videoTitle, category);
            
            // 更新学习统计
            UserState.updateLearningStats();
        }
        
        // 触发播放事件
        if (typeof window !== 'undefined' && window.onVideoPlay) {
            window.onVideoPlay(videoTitle, category);
        }
        
        return true;
    },
    
    // 更新视频进度
    updateVideoProgress: function(videoTitle, progress) {
        if (window.UserState) {
            UserState.updateVideoProgress(videoTitle, progress);
            
            // 如果完成了视频，更新学习统计
            if (progress >= 100) {
                UserState.updateLearningStats(1); // 假设每个视频1小时
            }
        }
        
        return true;
    },
    
    // 切换收藏状态
    toggleFavorite: function(videoTitle) {
        if (window.UserState) {
            const isFavorite = UserState.toggleFavorite(videoTitle);
            console.log(`${isFavorite ? '添加到' : '从'}收藏列表${isFavorite ? '' : '移除'}: ${videoTitle}`);
            return isFavorite;
        }
        return false;
    },
    
    // 购买内容
    purchaseContent: function(seriesOrCategory, price) {
        if (window.UserState) {
            UserState.addPurchasedItem(seriesOrCategory);
            console.log(`购买成功: ${seriesOrCategory} (${price})`);
            
            // 触发购买事件
            if (typeof window !== 'undefined' && window.onContentPurchased) {
                window.onContentPurchased(seriesOrCategory, price);
            }
            
            return true;
        }
        return false;
    },
    
    // 开始缓存视频
    startCaching: function(videoTitle, category) {
        if (window.CacheManager) {
            return CacheManager.startCaching(videoTitle, category);
        }
        return false;
    },
    
    // 检查内容是否已购买
    isPurchased: function(seriesOrCategory) {
        if (window.UserState) {
            return UserState.isPurchased(seriesOrCategory);
        }
        
        // 回退到AppData检查
        if (window.AppData && AppData.categories && AppData.categories[seriesOrCategory]) {
            return AppData.categories[seriesOrCategory].isPurchased || false;
        }
        
        return false;
    },
    
    // 检查视频是否已缓存
    isCached: function(videoTitle, category = null) {
        if (window.CacheManager) {
            return CacheManager.isCached(videoTitle);
        }
        
        // 回退到AppData检查
        const videoId = this.getVideoId(videoTitle, category);
        const result = this.findVideoByIdOrTitle(videoId, videoTitle, category);
        
        if (result && result.video) {
            return result.video.isCached || false;
        }
        
        return false;
    },
    
    // 获取用户学习统计
    getLearningStats: function() {
        if (window.UserState) {
            return UserState.getLearningStats();
        }
        return null;
    },
    
    // 获取缓存统计
    getCacheStats: function() {
        if (window.CacheManager) {
            return CacheManager.getCacheStats();
        }
        return null;
    },
    
    // 导出所有数据
    exportAllData: function() {
        const exportData = {
            exportDate: new Date().toISOString(),
            version: '1.0.0'
        };
        
        if (window.UserState) {
            exportData.userData = UserState.exportUserData();
        }
        
        if (window.CacheManager) {
            exportData.cacheData = CacheManager.exportCacheData();
        }
        
        return exportData;
    },
    
    // 导入所有数据
    importAllData: function(data) {
        try {
            if (data.userData && window.UserState) {
                UserState.importUserData(data.userData);
                console.log('用户数据导入成功');
            }
            
            if (data.cacheData && window.CacheManager) {
                CacheManager.importCacheData(data.cacheData);
                console.log('缓存数据导入成功');
            }
            
            return true;
        } catch (error) {
            console.error('数据导入失败:', error);
            return false;
        }
    },
    
    // 清除所有数据
    clearAllData: function() {
        if (window.UserState) {
            UserState.clearAllData();
        }
        
        if (window.CacheManager) {
            CacheManager.clearAllCache();
        }
        
        console.log('所有数据已清除');
    },
    
    // 获取推荐视频
    getRecommendedVideos: function(limit = 10) {
        if (!window.AppData || !window.UserState) return [];
        
        const recentWatched = UserState.getRecentWatched();
        const favorites = UserState.getFavorites();
        const purchasedItems = UserState.getPurchasedItems();
        
        const recommendations = [];
        
        // 基于最近观看推荐同分类视频
        recentWatched.slice(0, 3).forEach(item => {
            const categoryData = AppData.categories[item.category];
            if (categoryData && categoryData.videos) {
                categoryData.videos.forEach(video => {
                    if (video.title !== item.title && !recommendations.find(r => r.title === video.title)) {
                        const videoInfo = this.getVideoInfo(video.title, item.category);
                        if (videoInfo && (videoInfo.isFree || videoInfo.isPurchased)) {
                            recommendations.push({
                                ...videoInfo,
                                reason: '基于最近观看'
                            });
                        }
                    }
                });
            }
        });
        
        // 推荐已购买但未观看的视频
        purchasedItems.forEach(item => {
            const categoryData = AppData.categories[item];
            if (categoryData && categoryData.videos) {
                categoryData.videos.forEach(video => {
                    const progress = UserState.getVideoProgress(video.title);
                    if (progress < 100 && !recommendations.find(r => r.title === video.title)) {
                        const videoInfo = this.getVideoInfo(video.title, item);
                        if (videoInfo) {
                            recommendations.push({
                                ...videoInfo,
                                reason: '已购买未完成'
                            });
                        }
                    }
                });
            }
        });
        
        // 推荐免费视频
        Object.entries(AppData.categories).forEach(([categoryKey, categoryData]) => {
            if (categoryData.isFree && categoryData.videos) {
                categoryData.videos.forEach(video => {
                    if (!recommendations.find(r => r.title === video.title)) {
                        const videoInfo = this.getVideoInfo(video.title, categoryKey);
                        if (videoInfo) {
                            recommendations.push({
                                ...videoInfo,
                                reason: '免费精品'
                            });
                        }
                    }
                });
            }
        });
        
        return recommendations.slice(0, limit);
    }
};

// 自动初始化（当所有依赖加载完成后）
if (typeof window !== 'undefined') {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => DataManager.init(), 100);
        });
    } else {
        setTimeout(() => DataManager.init(), 100);
    }
    
    // 导出到全局
    window.DataManager = DataManager;
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DataManager;
} 