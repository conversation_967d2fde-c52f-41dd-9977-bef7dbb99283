// 导航管理器 - 处理页面间的跳转和返回功能
class NavigationManager {
    constructor() {
        this.history = [];
        this.currentPage = '';
        this.init();
    }

    init() {
        // 获取当前页面信息
        this.currentPage = this.getCurrentPageName();
        console.log('当前页面:', this.currentPage);
        
        // 从sessionStorage恢复历史记录
        this.loadHistoryFromStorage();
        
        // 监听页面加载
        window.addEventListener('load', () => {
            this.addToHistory(this.currentPage);
        });
        
        // 监听浏览器前进后退
        window.addEventListener('popstate', (event) => {
            console.log('浏览器后退/前进事件');
            this.currentPage = this.getCurrentPageName();
        });
    }

    // 获取当前页面名称
    getCurrentPageName() {
        const path = window.location.pathname;
        const filename = path.split('/').pop();
        return filename || 'index.html';
    }

    // 添加到历史记录
    addToHistory(pageName) {
        if (this.history[this.history.length - 1] !== pageName) {
            this.history.push(pageName);
            // 保存到sessionStorage
            this.saveHistoryToStorage();
        }
        console.log('导航历史:', this.history);
    }

    // 保存历史记录到sessionStorage
    saveHistoryToStorage() {
        try {
            sessionStorage.setItem('navigationHistory', JSON.stringify(this.history));
        } catch (error) {
            console.warn('无法保存导航历史到sessionStorage:', error);
        }
    }

    // 从sessionStorage加载历史记录
    loadHistoryFromStorage() {
        try {
            const savedHistory = sessionStorage.getItem('navigationHistory');
            if (savedHistory) {
                this.history = JSON.parse(savedHistory);
                console.log('从sessionStorage恢复导航历史:', this.history);
            }
        } catch (error) {
            console.warn('无法从sessionStorage加载导航历史:', error);
            this.history = [];
        }
    }

    // 页面跳转
    navigateTo(targetPage, params = {}) {
        console.log('跳转到:', targetPage, '参数:', params);
        
        // 构建URL
        let url = targetPage;
        if (Object.keys(params).length > 0) {
            const searchParams = new URLSearchParams(params);
            url += '?' + searchParams.toString();
        }
        
        // 记录当前页面到历史（在跳转前）
        this.addToHistory(this.currentPage);
        
        // 跳转
        window.location.href = url;
    }

    // 返回上一页
    goBack() {
        console.log('返回上一页，当前历史:', this.history);
        
        if (this.history.length > 1) {
            // 移除当前页面
            this.history.pop();
            // 获取上一页
            const previousPage = this.history[this.history.length - 1];
            console.log('返回到:', previousPage);
            
            // 确保页面名称是完整的文件名
            let targetPage = previousPage;
            if (!targetPage.includes('.html')) {
                // 如果不是完整文件名，尝试从映射表中查找
                const mappings = this.getPageMappings();
                const foundMapping = Object.entries(mappings).find(([key, value]) => 
                    value.includes(targetPage) || key === targetPage
                );
                if (foundMapping) {
                    targetPage = foundMapping[1];
                } else {
                    targetPage = '01-首页.html'; // 默认返回首页
                }
            }
            
            window.location.href = targetPage;
        } else {
            // 没有历史记录，返回首页
            console.log('没有历史记录，返回首页');
            window.location.href = '01-首页.html';
        }
    }

    // 返回首页
    goHome() {
        console.log('返回首页');
        window.location.href = '01-首页.html';
    }

    // 获取URL参数
    getUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        const params = {};
        for (const [key, value] of urlParams) {
            params[key] = value;
        }
        return params;
    }

    // 页面映射表
    getPageMappings() {
        return {
            'home': '01-首页.html',
            'video': '02-视频播放页.html',
            'profile': '03-我的页面.html',
            'payment': '04-支付页面.html',
            'login': '05-登录页面.html',
            'cache': '06-缓存管理页面.html',
            'orders': '07-购买记录页面.html',
            'report': '08-学习报告页面.html',
            'settings': '09-设置页面.html',
            'help': '10-帮助与反馈页面.html',
            'about': '11-关于我们页面.html',
            'earnings': '12-分享收益页面.html',
            'ranking': '13-分享排行页面.html',
            'materials': '14-分享素材页面.html'
        };
    }

    // 根据页面别名跳转
    navigateToPage(pageAlias, params = {}) {
        const mappings = this.getPageMappings();
        const targetPage = mappings[pageAlias];
        
        if (targetPage) {
            this.navigateTo(targetPage, params);
        } else {
            console.error('未找到页面映射:', pageAlias);
        }
    }
}

// 创建全局导航管理器实例
window.NavigationManager = new NavigationManager();

// 全局导航函数
window.navigateTo = (page, params) => window.NavigationManager.navigateTo(page, params);
window.navigateToPage = (alias, params) => window.NavigationManager.navigateToPage(alias, params);
window.goBack = () => window.NavigationManager.goBack();
window.goHome = () => window.NavigationManager.goHome();
window.getUrlParams = () => window.NavigationManager.getUrlParams();

// 页面特定的导航函数
window.goToVideo = (videoId, title) => {
    window.NavigationManager.navigateTo('02-视频播放页.html', { 
        videoId: videoId, 
        title: title 
    });
};

window.goToPayment = (title, price) => {
    window.NavigationManager.navigateTo('04-支付页面.html', { 
        title: title, 
        price: price 
    });
};

window.goToProfile = () => {
    window.NavigationManager.navigateTo('03-我的页面.html');
};

window.goToOrders = () => {
    window.NavigationManager.navigateTo('07-购买记录页面.html');
};

window.goToCache = () => {
    window.NavigationManager.navigateTo('06-缓存管理页面.html');
};

window.goToReport = () => {
    window.NavigationManager.navigateTo('08-学习报告页面.html');
};

window.goToEarnings = () => {
    window.NavigationManager.navigateTo('12-分享收益页面.html');
};

window.goToRanking = () => {
    window.NavigationManager.navigateTo('13-分享排行页面.html');
};

window.goToMaterials = () => {
    window.NavigationManager.navigateTo('14-分享素材页面.html');
};

window.goToSettings = () => {
    window.NavigationManager.navigateTo('09-设置页面.html');
};

window.goToHelp = () => {
    window.NavigationManager.navigateTo('10-帮助与反馈页面.html');
};

window.goToAbout = () => {
    window.NavigationManager.navigateTo('11-关于我们页面.html');
};

console.log('导航管理器已加载'); 