// 用户状态管理文件
// 管理用户的购买记录、收藏列表、观看进度、观看次数等

const UserState = {
    // 存储键名
    STORAGE_KEYS: {
        PURCHASED_ITEMS: 'shuimu_purchased_items',
        FAVORITES: 'shuimu_favorites',
        VIDEO_PROGRESS: 'shuimu_video_progress',
        WATCH_COUNT: 'shuimu_watch_count',
        SEARCH_HISTORY: 'shuimu_search_history',
        RECENT_WATCHED: 'shuimu_recent_watched',
        LEARNING_STATS: 'shuimu_learning_stats'
    },

    // 初始化用户状态
    init: function() {
        this.initPurchasedItems();
        this.initFavorites();
        this.initVideoProgress();
        this.initWatchCount();
        this.initSearchHistory();
        this.initRecentWatched();
        this.initLearningStats();
    },

    // 初始化已购买项目
    initPurchasedItems: function() {
        const defaultPurchased = [
            '免费精品系列',
            '道：恋爱宝典系列·恋爱宝典1',       
            '道：恋爱宝典系列·恋爱宝典3'
        ];
        
        if (!localStorage.getItem(this.STORAGE_KEYS.PURCHASED_ITEMS)) {
            this.setPurchasedItems(defaultPurchased);
        }
    },

    // 初始化收藏列表
    initFavorites: function() {
        if (!localStorage.getItem(this.STORAGE_KEYS.FAVORITES)) {
            this.setFavorites([]);
        }
    },

    // 初始化视频进度
    initVideoProgress: function() {
        if (!localStorage.getItem(this.STORAGE_KEYS.VIDEO_PROGRESS)) {
            const defaultProgress = {};
            
            // 设置免费视频的默认进度
            if (window.AppData) {
                Object.keys(AppData.videos).forEach(videoTitle => {
                    const video = AppData.videos[videoTitle];
                    if (video.isFree) {
                        defaultProgress[videoTitle] = video.defaultProgress || 100;
                    } else if (video.category === '道：恋爱宝典系列·恋爱宝典1') {
                        // 已购买的分类设置默认进度
                        defaultProgress[videoTitle] = video.defaultProgress || 0;
                    }
                });
            }
            
            this.setVideoProgress(defaultProgress);
        }
    },

    // 初始化观看次数
    initWatchCount: function() {
        if (!localStorage.getItem(this.STORAGE_KEYS.WATCH_COUNT)) {
            const defaultWatchCount = {};
            
            // 设置默认观看次数
            if (window.AppData) {
                Object.keys(AppData.videos).forEach(videoTitle => {
                    const video = AppData.videos[videoTitle];
                    defaultWatchCount[videoTitle] = video.defaultWatchCount || 0;
                });
            }
            
            this.setWatchCount(defaultWatchCount);
        }
    },

    // 初始化搜索历史
    initSearchHistory: function() {
        if (!localStorage.getItem(this.STORAGE_KEYS.SEARCH_HISTORY)) {
            this.setSearchHistory([]);
        }
    },

    // 初始化最近观看
    initRecentWatched: function() {
        if (!localStorage.getItem(this.STORAGE_KEYS.RECENT_WATCHED)) {
            this.setRecentWatched([]);
        }
    },

    // 初始化学习统计
    initLearningStats: function() {
        if (!localStorage.getItem(this.STORAGE_KEYS.LEARNING_STATS)) {
            this.setLearningStats({
                totalWatchTime: 0,
                totalVideosWatched: 0,
                totalDaysActive: 0,
                lastActiveDate: new Date().toDateString(),
                streakDays: 0
            });
        }
    },

    // 购买相关方法
    getPurchasedItems: function() {
        const items = localStorage.getItem(this.STORAGE_KEYS.PURCHASED_ITEMS);
        return items ? JSON.parse(items) : [];
    },

    setPurchasedItems: function(items) {
        localStorage.setItem(this.STORAGE_KEYS.PURCHASED_ITEMS, JSON.stringify(items));
    },

    isPurchased: function(seriesOrCategory, price = null) {
        const purchasedItems = this.getPurchasedItems();
        
        // 检查是否为免费内容
        if (window.AppData && AppData.isFreeContent(seriesOrCategory, seriesOrCategory)) {
            return true;
        }
        
        return purchasedItems.includes(seriesOrCategory);
    },

    addPurchasedItem: function(item) {
        const items = this.getPurchasedItems();
        if (!items.includes(item)) {
            items.push(item);
            this.setPurchasedItems(items);
        }
    },

    // 收藏相关方法
    getFavorites: function() {
        const favorites = localStorage.getItem(this.STORAGE_KEYS.FAVORITES);
        return favorites ? JSON.parse(favorites) : [];
    },

    setFavorites: function(favorites) {
        localStorage.setItem(this.STORAGE_KEYS.FAVORITES, JSON.stringify(favorites));
    },

    isFavorite: function(videoTitle) {
        const favorites = this.getFavorites();
        return favorites.includes(videoTitle);
    },

    toggleFavorite: function(videoTitle) {
        const favorites = this.getFavorites();
        const index = favorites.indexOf(videoTitle);
        
        if (index > -1) {
            favorites.splice(index, 1);
        } else {
            favorites.push(videoTitle);
        }
        
        this.setFavorites(favorites);
        return !this.isFavorite(videoTitle);
    },

    // 视频进度相关方法
    getVideoProgress: function(videoTitle = null) {
        const progress = localStorage.getItem(this.STORAGE_KEYS.VIDEO_PROGRESS);
        const progressData = progress ? JSON.parse(progress) : {};
        
        if (videoTitle) {
            return progressData[videoTitle] || 0;
        }
        
        return progressData;
    },

    setVideoProgress: function(progressData) {
        localStorage.setItem(this.STORAGE_KEYS.VIDEO_PROGRESS, JSON.stringify(progressData));
    },

    updateVideoProgress: function(videoTitle, progress) {
        const progressData = this.getVideoProgress();
        progressData[videoTitle] = Math.max(0, Math.min(100, progress));
        this.setVideoProgress(progressData);
    },

    // 观看次数相关方法
    getWatchCount: function(videoTitle = null) {
        const watchCount = localStorage.getItem(this.STORAGE_KEYS.WATCH_COUNT);
        const watchData = watchCount ? JSON.parse(watchCount) : {};
        
        if (videoTitle) {
            return watchData[videoTitle] || 0;
        }
        
        return watchData;
    },

    setWatchCount: function(watchData) {
        localStorage.setItem(this.STORAGE_KEYS.WATCH_COUNT, JSON.stringify(watchData));
    },

    incrementWatchCount: function(videoTitle) {
        const watchData = this.getWatchCount();
        watchData[videoTitle] = (watchData[videoTitle] || 0) + 1;
        this.setWatchCount(watchData);
        return watchData[videoTitle];
    },

    // 获取观看徽章等级
    getWatchBadgeLevel: function(videoTitle) {
        const count = this.getWatchCount(videoTitle);
        if (count >= 10) return 'legendary';
        return Math.min(count, 9);
    },

    // 搜索历史相关方法
    getSearchHistory: function() {
        const history = localStorage.getItem(this.STORAGE_KEYS.SEARCH_HISTORY);
        return history ? JSON.parse(history) : [];
    },

    setSearchHistory: function(history) {
        localStorage.setItem(this.STORAGE_KEYS.SEARCH_HISTORY, JSON.stringify(history));
    },

    addSearchHistory: function(query) {
        if (!query || query.trim() === '') return;
        
        const history = this.getSearchHistory();
        const trimmedQuery = query.trim();
        
        // 移除重复项
        const index = history.indexOf(trimmedQuery);
        if (index > -1) {
            history.splice(index, 1);
        }
        
        // 添加到开头
        history.unshift(trimmedQuery);
        
        // 限制历史记录数量
        if (history.length > 20) {
            history.splice(20);
        }
        
        this.setSearchHistory(history);
    },

    clearSearchHistory: function() {
        this.setSearchHistory([]);
    },

    // 最近观看相关方法
    getRecentWatched: function() {
        const recent = localStorage.getItem(this.STORAGE_KEYS.RECENT_WATCHED);
        return recent ? JSON.parse(recent) : [];
    },

    setRecentWatched: function(recent) {
        localStorage.setItem(this.STORAGE_KEYS.RECENT_WATCHED, JSON.stringify(recent));
    },

    addRecentWatched: function(videoTitle, category) {
        const recent = this.getRecentWatched();
        const videoInfo = {
            title: videoTitle,
            category: category,
            timestamp: Date.now()
        };
        
        // 移除重复项
        const index = recent.findIndex(item => item.title === videoTitle);
        if (index > -1) {
            recent.splice(index, 1);
        }
        
        // 添加到开头
        recent.unshift(videoInfo);
        
        // 限制最近观看数量
        if (recent.length > 50) {
            recent.splice(50);
        }
        
        this.setRecentWatched(recent);
    },

    // 学习统计相关方法
    getLearningStats: function() {
        const stats = localStorage.getItem(this.STORAGE_KEYS.LEARNING_STATS);
        return stats ? JSON.parse(stats) : {
            totalWatchTime: 0,
            totalVideosWatched: 0,
            totalDaysActive: 0,
            lastActiveDate: new Date().toDateString(),
            streakDays: 0
        };
    },

    setLearningStats: function(stats) {
        localStorage.setItem(this.STORAGE_KEYS.LEARNING_STATS, JSON.stringify(stats));
    },

    updateLearningStats: function(watchTime = 0) {
        const stats = this.getLearningStats();
        const today = new Date().toDateString();
        
        // 更新观看时间
        stats.totalWatchTime += watchTime;
        
        // 检查是否是新的一天
        if (stats.lastActiveDate !== today) {
            const lastDate = new Date(stats.lastActiveDate);
            const currentDate = new Date(today);
            const daysDiff = Math.floor((currentDate - lastDate) / (1000 * 60 * 60 * 24));
            
            if (daysDiff === 1) {
                // 连续学习
                stats.streakDays += 1;
            } else if (daysDiff > 1) {
                // 中断了连续学习
                stats.streakDays = 1;
            }
            
            stats.totalDaysActive += 1;
            stats.lastActiveDate = today;
        }
        
        this.setLearningStats(stats);
    },

    // 数据导出和导入
    exportUserData: function() {
        return {
            purchasedItems: this.getPurchasedItems(),
            favorites: this.getFavorites(),
            videoProgress: this.getVideoProgress(),
            watchCount: this.getWatchCount(),
            searchHistory: this.getSearchHistory(),
            recentWatched: this.getRecentWatched(),
            learningStats: this.getLearningStats(),
            exportDate: new Date().toISOString()
        };
    },

    importUserData: function(data) {
        if (data.purchasedItems) this.setPurchasedItems(data.purchasedItems);
        if (data.favorites) this.setFavorites(data.favorites);
        if (data.videoProgress) this.setVideoProgress(data.videoProgress);
        if (data.watchCount) this.setWatchCount(data.watchCount);
        if (data.searchHistory) this.setSearchHistory(data.searchHistory);
        if (data.recentWatched) this.setRecentWatched(data.recentWatched);
        if (data.learningStats) this.setLearningStats(data.learningStats);
    },

    // 清除所有用户数据
    clearAllData: function() {
        Object.values(this.STORAGE_KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
        this.init();
    }
};

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserState;
} else if (typeof window !== 'undefined') {
    window.UserState = UserState;
} 