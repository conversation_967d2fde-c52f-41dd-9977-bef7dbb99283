// 应用主数据文件
// 包含所有系列、分类、视频的完整信息

const AppData = {
    // 系列数据
    series: {
        '免费精品系列': {
            id: 'free-series',
            title: '免费精品系列',
            icon: 'fas fa-play-circle',
            iconColor: 'text-green-500',
            price: '',
            isFree: true,
            totalVideos: 5,
            categories: ['免费精品系列·约会技巧', '免费精品系列·搭讪技术']
        },
        '道：恋爱宝典系列': {
            id: 'love-guide-series',
            title: '道：恋爱宝典系列',
            icon: 'fas fa-heart',
            iconColor: 'text-red-500',
            price: '¥600',
            isFree: false,
            totalVideos: 18,
            categories: ['道：恋爱宝典系列·恋爱宝典1', '道：恋爱宝典系列·恋爱宝典2', '道：恋爱宝典系列·恋爱宝典3']
        },
        '术：聊天技术系列': {
            id: 'chat-tech-series',
            title: '术：聊天技术系列',
            icon: 'fas fa-comments',
            iconColor: 'text-blue-500',
            price: '¥1000',
            isFree: false,
            totalVideos: 9,
            categories: ['术：聊天技术系列·聊天技术1', '术：聊天技术系列·聊天技术2', '术：聊天技术系列·聊天技术3']
        },
        '全套课程': {
            id: 'complete-course',
            title: '全套课程',
            icon: 'fas fa-graduation-cap',
            iconColor: 'text-purple-500',
            price: '¥1600',
            isFree: false,
            totalVideos: 32,
            categories: [] // 包含所有付费系列
        }
    },

    // 分类数据（包含完整视频信息）
    categories: {
        // 免费精品系列分类
        '免费精品系列·约会技巧': {
            id: 'free-dating-tips',
            title: '约会技巧',
            series: '免费精品系列',
            price: '',
            isFree: true,
            totalVideos: 2,
            videos: [
                {
                    id: 'free-dating-prep',
                    title: '01. 约会前的准备工作，学习约会前的各种准备工作，包括形象打理',
                    duration: '15:30',
                    description: '学习约会前的各种准备工作，包括形象打理、心理准备等',
                    defaultProgress: 100,
                    defaultWatchCount: 3,
                    playCount: '1.2万',
                    isCached: false
                },
                {
                    id: 'free-dating-location',
                    title: '02. 约会地点的选择，测试',
                    duration: '12:45',
                    description: '如何选择合适的约会地点，营造浪漫氛围',
                    defaultProgress: 100,
                    defaultWatchCount: 2,
                    playCount: '8.5千',
                    isCached: false
                }
            ]
        },
        '免费精品系列·搭讪技术': {
            id: 'free-pickup-skills',
            title: '搭讪技术',
            series: '免费精品系列',
            price: '',
            isFree: true,
            totalVideos: 3,
            videos: [
                {
                    id: 'free-pickup-mindset',
                    title: '01. 搭讪基础心态',
                    duration: '18:20',
                    description: '建立正确的搭讪心态，克服内心恐惧',
                    defaultProgress: 100,
                    defaultWatchCount: 2,
                    playCount: '2.1万',
                    isCached: false
                },
                {
                    id: 'free-pickup-opening',
                    title: '02. 自然开场技巧',
                    duration: '16:15',
                    description: '学习自然而然的开场白技巧',
                    defaultProgress: 100,
                    defaultWatchCount: 1,
                    playCount: '1.8万',
                    isCached: false
                },
                {
                    id: 'free-pickup-anxiety',
                    title: '03. 克服紧张情绪',
                    duration: '14:30',
                    description: '有效方法帮助你克服搭讪时的紧张情绪',
                    defaultProgress: 100,
                    defaultWatchCount: 1,
                    playCount: '1.5万',
                    isCached: false
                }
            ]
        },

        // 道：恋爱宝典系列分类
        '道：恋爱宝典系列·恋爱宝典1': {
            id: 'love-guide-1',
            title: '恋爱宝典1',
            series: '道：恋爱宝典系列',
            price: '¥100',
            isFree: false,
            isPurchased: true,
            totalVideos: 12,
            videos: [
                {
                    id: 'love-guide-1-attraction',
                    title: '01. 初识吸引力法则',
                    duration: '22:30',
                    description: '深入了解吸引力的本质和运作原理',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '5.2万',
                    isCached: false
                },
                {
                    id: 'love-guide-1-confidence',
                    title: '02. 建立自信的方法',
                    duration: '19:45',
                    description: '系统性方法帮助你建立内在自信',
                    defaultProgress: 20,
                    defaultWatchCount: 1,
                    playCount: '4.8万',
                    isCached: false
                },
                {
                    id: 'love-guide-1-first-impression',
                    title: '03. 第一印象的重要性',
                    duration: '17:20',
                    description: '如何在初次见面时留下深刻的好印象',
                    defaultProgress: 40,
                    defaultWatchCount: 2,
                    playCount: '4.1万',
                    isCached: true
                },
                {
                    id: 'love-guide-1-body-language',
                    title: '04. 肢体语言解读',
                    duration: '21:10',
                    description: '学会读懂对方的肢体语言信号',
                    defaultProgress: 60,
                    defaultWatchCount: 3,
                    playCount: '3.9万',
                    isCached: true
                },
                {
                    id: 'love-guide-1-emotion-expression',
                    title: '05. 情感表达技巧',
                    duration: '18:55',
                    description: '掌握恰当的情感表达方式和技巧',
                    defaultProgress: 80,
                    defaultWatchCount: 4,
                    playCount: '3.7万',
                    isCached: false
                },
                {
                    id: 'love-guide-1-deep-communication',
                    title: '06. 深度沟通艺术',
                    duration: '24:15',
                    description: '学习深层次的沟通技巧和艺术',
                    defaultProgress: 100,
                    defaultWatchCount: 5,
                    playCount: '3.5万',
                    isCached: true
                },
                {
                    id: 'love-guide-1-dating-strategy',
                    title: '07. 约会策略制定',
                    duration: '20:40',
                    description: '制定有效的约会策略和计划',
                    defaultProgress: 100,
                    defaultWatchCount: 6,
                    playCount: '3.3万',
                    isCached: true
                },
                {
                    id: 'love-guide-1-charm-enhancement',
                    title: '08. 魅力提升秘诀',
                    duration: '23:25',
                    description: '全面提升个人魅力的实用秘诀',
                    defaultProgress: 100,
                    defaultWatchCount: 7,
                    playCount: '3.1万',
                    isCached: false
                },
                {
                    id: 'love-guide-1-relationship-upgrade',
                    title: '09. 关系升级技巧',
                    duration: '19:30',
                    description: '如何自然地推进关系发展',
                    defaultProgress: 100,
                    defaultWatchCount: 8,
                    playCount: '2.9万',
                    isCached: false
                },
                {
                    id: 'love-guide-1-long-term-relationship',
                    title: '10. 长期关系维护',
                    duration: '25:10',
                    description: '维护长期稳定关系的核心要素',
                    defaultProgress: 100,
                    defaultWatchCount: 9,
                    playCount: '2.7万',
                    isCached: false
                },
                {
                    id: 'love-guide-1-conflict-resolution',
                    title: '11. 冲突化解智慧',
                    duration: '22:45',
                    description: '智慧地处理和化解关系中的冲突',
                    defaultProgress: 100,
                    defaultWatchCount: 10,
                    playCount: '2.5万',
                    isCached: false
                },
                {
                    id: 'love-guide-1-master-path',
                    title: '12. 恋爱大师之路',
                    duration: '28:20',
                    description: '成为恋爱大师的完整成长路径',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '2.3万',
                    isCached: false
                }
            ]
        },
        '道：恋爱宝典系列·恋爱宝典2': {
            id: 'love-guide-2',
            title: '恋爱宝典2',
            series: '道：恋爱宝典系列',
            price: '¥100',
            isFree: false,
            isPurchased: false,
            totalVideos: 3,
            videos: [
                {
                    id: 'love-guide-2-deep-communication',
                    title: '01. 深度沟通技巧',
                    duration: '26:15',
                    description: '进阶的深度沟通技巧和方法',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '1.8万',
                    isCached: false
                },
                {
                    id: 'love-guide-2-emotion-expression',
                    title: '02. 情感表达方式',
                    duration: '23:40',
                    description: '多样化的情感表达方式和技巧',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '1.6万',
                    isCached: false
                },
                {
                    id: 'love-guide-2-conflict-handling',
                    title: '03. 冲突处理艺术',
                    duration: '24:55',
                    description: '高级的冲突处理艺术和智慧',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '1.4万',
                    isCached: false
                }
            ]
        },
        '道：恋爱宝典系列·恋爱宝典3': {
            id: 'love-guide-3',
            title: '恋爱宝典3',
            series: '道：恋爱宝典系列',
            price: '¥100',
            isFree: false,
            isPurchased: true,
            totalVideos: 4,
            videos: [
                {
                    id: 'love-guide-3-long-term-maintenance',
                    title: '01. 长期关系维护',
                    duration: '27:30',
                    description: '长期关系的深度维护策略',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '1.2万',
                    isCached: true
                },
                {
                    id: 'love-guide-3-trust-building',
                    title: '02. 信任建立方法',
                    duration: '25:20',
                    description: '建立和维护关系中信任的方法',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '1.1万',
                    isCached: true
                },
                {
                    id: 'love-guide-3-future-planning',
                    title: '03. 未来规划讨论',
                    duration: '29:45',
                    description: '如何与伴侣讨论和规划共同未来',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '9.8千',
                    isCached: false
                },
                {
                    id: 'love-guide-3-crisis-handling',
                    title: '04. 危机处理策略',
                    duration: '26:30',
                    description: '处理和化解关系中的危机和挑战',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '9.6千',
                    isCached: false
                }
            ]
        },

        // 术：聊天技术系列分类
        '术：聊天技术系列·聊天技术1': {
            id: 'chat-tech-1',
            title: '聊天技术1',
            series: '术：聊天技术系列',
            price: '¥350',
            isFree: false,
            isPurchased: false,
            totalVideos: 3,
            videos: [
                {
                    id: 'chat-tech-1-opening',
                    title: '01. 开场白技巧',
                    duration: '20:15',
                    description: '掌握各种场合的开场白技巧',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '3.2万',
                    isCached: false
                },
                {
                    id: 'chat-tech-1-topic-continuation',
                    title: '02. 话题延续方法',
                    duration: '22:30',
                    description: '学习如何自然延续和发展话题',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '2.8万',
                    isCached: false
                },
                {
                    id: 'chat-tech-1-humor-development',
                    title: '03. 幽默感培养',
                    duration: '18:45',
                    description: '培养和运用幽默感的实用技巧',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '2.5万',
                    isCached: false
                }
            ]
        },
        '术：聊天技术系列·聊天技术2': {
            id: 'chat-tech-2',
            title: '聊天技术2',
            series: '术：聊天技术系列',
            price: '¥350',
            isFree: false,
            isPurchased: false,
            totalVideos: 3,
            videos: [
                {
                    id: 'chat-tech-2-emotion-mobilization',
                    title: '01. 情绪调动技巧',
                    duration: '24:20',
                    description: '学习如何调动和引导对方情绪',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '2.3万',
                    isCached: false
                },
                {
                    id: 'chat-tech-2-deep-topic-guidance',
                    title: '02. 深度话题引导',
                    duration: '26:15',
                    description: '引导深层次话题的技巧和方法',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '2.1万',
                    isCached: false
                },
                {
                    id: 'chat-tech-2-ambiguous-escalation',
                    title: '03. 暧昧升级方法',
                    duration: '21:40',
                    description: '自然升级暧昧关系的方法',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '1.9万',
                    isCached: false
                }
            ]
        },
        '术：聊天技术系列·聊天技术3': {
            id: 'chat-tech-3',
            title: '聊天技术3',
            series: '术：聊天技术系列',
            price: '¥300',
            isFree: false,
            isPurchased: false,
            totalVideos: 3,
            videos: [
                {
                    id: 'chat-tech-3-wechat-skills',
                    title: '01. 微信聊天技巧',
                    duration: '19:30',
                    description: '微信聊天的专业技巧和策略',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '1.7万',
                    isCached: false
                },
                {
                    id: 'chat-tech-3-voice-call-skills',
                    title: '02. 语音通话技巧',
                    duration: '17:25',
                    description: '语音通话中的沟通技巧',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '1.5万',
                    isCached: false
                },
                {
                    id: 'chat-tech-3-video-chat-skills',
                    title: '03. 视频聊天技巧',
                    duration: '20:10',
                    description: '视频聊天的高级技巧和注意事项',
                    defaultProgress: 0,
                    defaultWatchCount: 0,
                    playCount: '1.3万',
                    isCached: false
                }
            ]
        }
    },

    // 工具函数
    utils: {
        // 根据分类获取视频列表
        getVideosByCategory(categoryKey) {
            const category = this.categories[categoryKey];
            return category ? category.videos : [];
        },

        // 根据视频ID查找视频信息
        getVideoById(videoId) {
            for (const categoryKey in this.categories) {
                const category = this.categories[categoryKey];
                const video = category.videos.find(v => v.id === videoId);
                if (video) {
                    return {
                        ...video,
                        category: categoryKey,
                        series: category.series
                    };
                }
            }
            return null;
        },

        // 根据系列获取所有分类
        getCategoriesBySeries(seriesName) {
            const result = [];
            for (const categoryKey in this.categories) {
                const category = this.categories[categoryKey];
                if (category.series === seriesName) {
                    result.push({
                        key: categoryKey,
                        ...category
                    });
                }
            }
            return result;
        },

        // 获取所有免费视频
        getFreeVideos() {
            const freeVideos = [];
            for (const categoryKey in this.categories) {
                const category = this.categories[categoryKey];
                if (category.isFree) {
                    category.videos.forEach(video => {
                        freeVideos.push({
                            ...video,
                            category: categoryKey,
                            series: category.series
                        });
                    });
                }
            }
            return freeVideos;
        },

        // 搜索视频（只搜索视频标题）
        searchVideos(keyword) {
            const results = [];
            const lowerKeyword = keyword.toLowerCase();

            // 使用AppData而不是this，避免this引用问题
            for (const categoryKey in AppData.categories) {
                const category = AppData.categories[categoryKey];
                if (category.videos && Array.isArray(category.videos)) {
                    category.videos.forEach(video => {
                        // 只搜索视频标题
                        if (video.title && video.title.toLowerCase().includes(lowerKeyword)) {
                            results.push({
                                ...video,
                                category: categoryKey,
                                categoryTitle: category.title,
                                series: category.series,
                                categoryPrice: category.price,
                                categoryIsFree: category.isFree,
                                categoryIsPurchased: category.isPurchased
                            });
                        }
                    });
                }
            }
            return results;
        }
    }
};

// 向后兼容的全局访问方式
window.AppData = AppData; 