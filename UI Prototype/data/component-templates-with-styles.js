// ========== 水幕组件模板库 - 包含样式版本 ==========
// 包含6个核心组件的完整实现，自动引入必要的CSS样式

// 自动引入样式文件和注入必要样式
(function() {
    // 检查是否已经处理过样式
    if (document.getElementById('component-templates-styles')) {
        return;
    }

    // 1. 动态引入徽章样式文件
    const badgeStylesLink = document.createElement('link');
    badgeStylesLink.rel = 'stylesheet';
    
    // 智能检测路径：如果当前页面在data文件夹内，使用相对路径；否则使用data/路径
    const currentPath = window.location.pathname;
    const isInDataFolder = currentPath.includes('/data/');
    badgeStylesLink.href = isInDataFolder ? 'badge-styles-dual.css' : 'data/badge-styles-dual.css';
    
    badgeStylesLink.id = 'badge-styles-dual';
    document.head.appendChild(badgeStylesLink);

    // 2. 注入进度条和锁图标样式
    const styleElement = document.createElement('style');
    styleElement.id = 'component-templates-styles';
    styleElement.textContent = `
        /* ========== 进度条样式 ========== */
        .progress-component {
            background: #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }

        .progress-thin {
            height: 4px;
        }

        .progress-medium {
            height: 6px;
        }

        .progress-flex {
            flex: 1;
        }

        .progress-fill-component {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            border-radius: inherit;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .progress-fill-component.animated::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: progress-shine 2s infinite;
        }

        .progress-info-component {
            display: flex;
            align-items: center;
            margin-left: 8px;
            flex-shrink: 0;
        }

        .progress-percentage-component {
            font-size: 11px;
            color: #6b7280;
            font-weight: 500;
            min-width: 28px;
        }

        .video-progress-row-component {
            display: flex;
            align-items: center;
            margin-top: 4px;
        }

        /* ========== 锁图标样式 ========== */
        .video-lock-component {
            font-size: 16px;
            color: #F59E0B;
            margin-left: auto;
            flex-shrink: 0;
            padding-left: 8px;
            padding-right: 10px;
            display: flex;
            align-items: center;
            z-index: 2;
        }

        /* ========== 进度条动画 ========== */
        @keyframes progress-shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* ========== 缓存状态样式 ========== */
        .cache-status {
            font-size: 12px;
            color: #6b7280;
            font-weight: 400;
            margin-left: 0;
        }

        .cache-status.downloading {
            color: #3b82f6;
            font-weight: 500;
        }

        .cache-status.success {
            color: #10b981;
            font-weight: 500;
        }

        .cache-status.cached {
            color: #10b981;
            font-weight: 400;
        }

        /* ========== 顶部导航样式 ========== */
        .top-nav {
            background: white;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f3f4f6;
            position: sticky;
            top: 0;
            z-index: 100;
            height: 45px;
        }

        .top-nav-spacer {
            flex: 1;
        }

        .top-nav-title {
            flex: 1;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
        }

        /* ========== 分享弹窗样式 ========== */
        .share-modal {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 50%;
            background: white;
            border-radius: 16px 16px 0 0;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.15);
            padding: 20px;
            z-index: 9999;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #f3f4f6;
        }

        .share-content {
            height: calc(100% - 60px);
            overflow-y: auto;
        }

        /* 隐藏分享弹窗的滚动条 */
        .share-content::-webkit-scrollbar {
            display: none;
        }

        .share-content {
            -ms-overflow-style: none;  /* IE and Edge */
            scrollbar-width: none;  /* Firefox */
        }

        .share-options {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .share-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .share-option:hover {
            transform: scale(1.05);
        }

        .share-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 8px;
        }

        .share-option span {
            font-size: 12px;
            color: #6b7280;
            text-align: center;
        }

        .share-stats {
            display: flex;
            justify-content: space-around;
            background: #f9fafb;
            border-radius: 12px;
            padding: 16px;
        }

        .share-stats .stat-item {
            text-align: center;
        }

        .share-stats .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 4px;
        }

        .share-stats .stat-label {
            font-size: 11px;
            color: #6b7280;
        }

        .top-nav-action {
            background: transparent;
            border: none;
            padding: 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #6b7280;
        }

        .top-nav-action:hover {
            background: #f3f4f6;
        }

        .top-nav-action i {
            font-size: 20px;
        }

        /* 蓝绿渐变 - 水幕主题色 */
        .blue-green-gradient-enhanced {
            background: linear-gradient(90deg, #3b82f6, #10b981);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            transition: all 0.3s ease;
            filter: drop-shadow(0 0 2px rgba(59, 130, 246, 0.3));
        }

        .blue-green-gradient-enhanced:hover {
            background: linear-gradient(90deg, #60a5fa, #34d399);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6));
            animation: gradient-pulse-blue-green 2s ease-in-out infinite;
        }

        @keyframes gradient-pulse-blue-green {
            0%, 100% {
                filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.6));
            }
            50% {
                filter: drop-shadow(0 0 10px rgba(16, 185, 129, 0.8));
            }
        }

        /* ========== 分享按钮完整样式（与首页一致） ========== */
        .share-button-component {
            padding: 8px;
            color: #6b7280;
            background: transparent;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: visible;
        }

        .share-button-component:hover {
            transform: scale(1.1);
        }

        .share-button-component i,
        .share-button-component i.blue-green-gradient-enhanced {
            font-size: 30px;
            transition: none !important;
        }

        /* 分享按钮右上角的金币动画 */
        .share-button-component::after {
            content: '💰';
            position: absolute;
            right: -8px;
            font-size: 18px;
            animation: coin-scale-pulse 3s ease-in-out infinite;
            pointer-events: none;
            z-index: 2;
            transform-origin: center;
        }

        /* 金币由小变大的缩放动画 */
        @keyframes coin-scale-pulse {
            0% {
                transform: scale(0.8);
                opacity: 0.7;
            }
            50% {
                transform: scale(1.2);
                opacity: 1;
            }
            100% {
                transform: scale(0.8);
                opacity: 0.7;
            }
        }



        /* ========== 底部导航样式 ========== */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 359px;
            height: 60px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-around;
            z-index: 1000;
            padding: 6px 0;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 8px;
            min-width: 60px;
        }

        .nav-item:hover {
            background: #f3f4f6;
        }

        .nav-item.active {
            background: #dbeafe;
            border-radius: 8px;
        }

        .nav-item.active .nav-icon {
            color: #3b82f6;
        }

        .nav-item.active .nav-label {
            color: #3b82f6;
            font-weight: 500;
        }



        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
            color: #6b7280;
            transition: all 0.2s ease;
            line-height: 1;
        }

        /* emoji图标特殊处理 */
        .emoji-icon {
            display: inline-block;
            line-height: 1;
            vertical-align: middle;
        }

        .nav-label {
            font-size: 10px;
            color: #6b7280;
            transition: color 0.2s ease;
            text-align: center;
            line-height: 1.2;
        }
    `;

    // 将样式添加到页面头部
    document.head.appendChild(styleElement);
})();

// 组件模板对象
const ComponentTemplates = {
    // ========== 核心组件方法 ==========
    
    // 1. 已购买/免费状态的视频项
    createPurchasedVideoItem: function(options = {}) {
        const {
            title = '',
            category = '',
            progress = 0,
            watchCount = 0,
            cacheStatus = '',
            isLastWatched = false,
            onClick = '',
            customClass = ''
        } = options;

        const badgeLevel = this.getBadgeLevel(watchCount);
        const badgeClass = this.getBadgeClass(badgeLevel);
        const badgeText = this.getBadgeText(watchCount);
        const animatedClass = progress > 0 ? 'animated' : '';
        const lastWatchedClass = isLastWatched ? 'last-watched' : '';
        
        // 处理缓存状态显示
        let titleWithCache = title;
        if (cacheStatus) {
            // 解析缓存状态，确定CSS类
            let statusClass = 'cached';
            if (cacheStatus.includes('%')) {
                statusClass = 'downloading';
            }
            const cacheStatusHtml = `<span class="cache-status ${statusClass}">${cacheStatus}</span>`;
            titleWithCache = `${title}${cacheStatusHtml}`;
        }

        return `
            <div class="video-item-component ${lastWatchedClass} ${customClass}" onclick="${onClick}">
                <i class="fas fa-play video-play-icon"></i>
                <div class="video-content-wrapper">
                    <div class="video-title-component">
                        <span class="video-title-text">${titleWithCache}</span>
                    </div>
                    <div class="video-progress-row-component">
                        <div class="progress-component progress-thin progress-flex">
                            <div class="progress-fill-component ${animatedClass}" style="width: ${progress}%;"></div>
                        </div>
                        <div class="progress-info-component">
                            <span class="progress-percentage-component">${progress}%</span>
                            <span class="badge-component ${badgeClass}">${badgeText}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // 2. 未购买状态的视频项
    createUnpurchasedVideoItem: function(options = {}) {
        const {
            title = '',
            category = '',
            onClick = '',
            customClass = ''
        } = options;

        return `
            <div class="video-item-component ${customClass}" onclick="${onClick}">
                <i class="fas fa-play video-play-icon"></i>
                <div class="video-content-wrapper">
                    <div class="video-title-component">
                        <span class="video-title-text">${title}</span>
                    </div>
                </div>
                <span class="video-lock-component">🔒</span>
            </div>
        `;
    },

    // 3. 已购买/免费状态的分类卡片
    createPurchasedCategoryCard: function(options = {}) {
        const {
            title = '',
            progress = 0,
            watchCount = 0,
            isExpanded = false,
            videos = [],
            onClick = '',
            customClass = ''
        } = options;

        const badgeLevel = this.getBadgeLevel(watchCount);
        const badgeClass = this.getBadgeClass(badgeLevel);
        const badgeText = this.getBadgeText(watchCount);
        const animatedClass = progress > 0 ? 'animated' : '';
        const expandedClass = isExpanded ? 'expanded' : '';

        let videosHtml = '';
        videos.forEach(video => {
            if (video.isPurchased || video.isFree) {
                videosHtml += this.createPurchasedVideoItem(video);
            } else {
                videosHtml += this.createUnpurchasedVideoItem(video);
            }
        });

        return `
            <div class="category-card ${customClass}" onclick="${onClick}">
                <div class="category-header-component">
                    <span class="category-title-component">${title}</span>
                    <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow"></i>
                </div>
                <div class="series-progress-component">
                    <div class="progress-component progress-medium progress-flex">
                        <div class="progress-fill-component ${animatedClass}" style="width: ${progress}%;"></div>
                    </div>
                    <div class="progress-info-component">
                        <span class="progress-percentage-component">${progress}%</span>
                        <span class="badge-component ${badgeClass}">${badgeText}</span>
                    </div>
                </div>
                <div class="category-content ${expandedClass}">
                    <div class="mt-2 space-y-1">
                        ${videosHtml}
                    </div>
                </div>
            </div>
        `;
    },

    // 4. 未购买状态的分类卡片
    createUnpurchasedCategoryCard: function(options = {}) {
        const {
            title = '',
            price = '',
            videos = [],
            onClick = '',
            customClass = ''
        } = options;

        let videosHtml = '';
        videos.forEach(video => {
            videosHtml += this.createUnpurchasedVideoItem(video);
        });

        const titleWithPrice = price ? `${title} <span class="category-price-component">(${price})</span>` : title;

        return `
            <div class="category-card ${customClass}" onclick="${onClick}">
                <div class="category-header-component">
                    <span class="category-title-component">${titleWithPrice}</span>
                    <i class="fas fa-chevron-down toggle-arrow-component arrow-small category-arrow"></i>
                </div>
                <div class="category-content">
                    <div class="mt-2 space-y-1">
                        ${videosHtml}
                    </div>
                </div>
            </div>
        `;
    },

    // 5. 已购买/免费状态的搜索视频项
    createPurchasedSearchItem: function(options = {}) {
        const {
            title = '',
            category = '',
            progress = 0,
            watchCount = 0,
            cacheStatus = '',
            dataTitle = '',
            dataCategory = '',
            dataIndex = 0,
            onClick = '',
            customClass = ''
        } = options;

        const badgeLevel = this.getBadgeLevel(watchCount);
        const badgeClass = this.getBadgeClass(badgeLevel);
        const badgeText = this.getBadgeText(watchCount);
        const animatedClass = progress > 0 ? 'animated' : '';
        
        // 处理缓存状态显示
        let titleWithCache = title;
        if (cacheStatus) {
            // 解析缓存状态，确定CSS类
            let statusClass = 'cached';
            if (cacheStatus.includes('%')) {
                statusClass = 'downloading';
            }
            const cacheStatusHtml = `<span class="cache-status ${statusClass}">${cacheStatus}</span>`;
            titleWithCache = `${title}${cacheStatusHtml}`;
        }

        return `
            <div class="search-suggestion-item video-item-component ${customClass}" 
                 data-title="${dataTitle}" 
                 data-category="${dataCategory}" 
                 data-index="${dataIndex}" 
                 onclick="${onClick}">
                <i class="fas fa-play video-play-icon"></i>
                <div class="video-content-wrapper">
                    <div class="video-title-component">
                        <span class="video-title-text">${titleWithCache}</span>
                    </div>
                    <div class="video-progress-row-component">
                        <div class="progress-component progress-thin progress-flex">
                            <div class="progress-fill-component ${animatedClass}" style="width: ${progress}%;"></div>
                        </div>
                        <div class="progress-info-component">
                            <span class="progress-percentage-component">${progress}%</span>
                            <span class="badge-component ${badgeClass}">${badgeText}</span>
                        </div>
                    </div>
                    <div class="search-suggestion-category">${category}</div>
                </div>
            </div>
        `;
    },

    // 6. 未购买状态的搜索视频项
    createUnpurchasedSearchItem: function(options = {}) {
        const {
            title = '',
            category = '',
            dataTitle = '',
            dataCategory = '',
            dataIndex = 0,
            onClick = '',
            customClass = ''
        } = options;

        return `
            <div class="search-suggestion-item video-item-component ${customClass}" 
                 data-title="${dataTitle}" 
                 data-category="${dataCategory}" 
                 data-index="${dataIndex}" 
                 onclick="${onClick}">
                <i class="fas fa-play video-play-icon"></i>
                <div class="video-content-wrapper">
                    <div class="video-title-component">
                        <span class="video-title-text">${title}</span>
                    </div>
                    <div class="search-suggestion-category">${category}</div>
                </div>
                <span class="video-lock-component">🔒</span>
            </div>
        `;
    },

    // 7. 分享弹窗组件
    createShareModal: function(options = {}) {
        const {
            customClass = ''
        } = options;

        return `
            <div class="share-modal" id="shareModal" style="display: none;">
                <div class="modal-header">
                    <h3 class="text-lg font-semibold text-gray-800">分享推广分成</h3>
                    <button onclick="closeShareModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="share-content">
                    <p class="text-gray-600 mb-6 text-center">分享给好友，每成交一单获得30%分成收益</p>

                    <div class="share-options">
                        <div class="share-option" onclick="shareToWechat()">
                            <div class="share-icon" style="background: #07c160;">
                                <i class="fab fa-weixin"></i>
                            </div>
                            <span>微信好友</span>
                        </div>

                        <div class="share-option" onclick="shareToMoments()">
                            <div class="share-icon" style="background: #1aad19;">
                                <i class="fas fa-camera"></i>
                            </div>
                            <span>朋友圈</span>
                        </div>

                        <div class="share-option" onclick="copyShareLink()">
                            <div class="share-icon" style="background: #3b82f6;">
                                <i class="fas fa-link"></i>
                            </div>
                            <span>复制链接</span>
                        </div>

                        <div class="share-option" onclick="shareToQQ()">
                            <div class="share-icon" style="background: #12b7f5;">
                                <i class="fab fa-qq"></i>
                            </div>
                            <span>QQ好友</span>
                        </div>
                    </div>

                    <div class="share-stats">
                        <div class="stat-item">
                            <div class="stat-number">12</div>
                            <div class="stat-label">分享次数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">3</div>
                            <div class="stat-label">转化人数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">¥180</div>
                            <div class="stat-label">累计收益</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // 8. 顶部导航组件（包含分享弹窗）
    createTopNavigation: function(options = {}) {
        const {
            title = '',
            onBackClick = 'goBack()',
            onShareClick = 'showShareModal()',
            customClass = ''
        } = options;

        // 自动初始化分享功能
        setTimeout(() => {
            if (typeof window !== 'undefined' && !window.shareModalInitialized) {
                this.initShareFunctions();
                window.shareModalInitialized = true;
            }
        }, 0);

        return `
            <div class="top-nav ${customClass}">
                <button class="top-nav-action" onclick="${onBackClick}">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="top-nav-title">${title}</div>
                <button class="share-button-component" onclick="${onShareClick}">
                    <i class="fas fa-share-alt blue-green-gradient-enhanced"></i>
                </button>
            </div>
            ${this.createShareModal()}
        `;
    },



    // 9. 底部导航组件
    createBottomNavigation: function(options = {}) {
        const {
            currentPage = 'home', // 'home', 'learning', 'share', 'profile'
            customClass = ''
        } = options;

        const navItems = [
            {
                id: 'home',
                icon: 'fas fa-home',
                label: '首页',
                url: '01-首页.html',
                alias: 'home'
            },
            {
                id: 'learning',
                icon: 'fas fa-book-open',
                label: '学习',
                url: '08-学习报告页面.html',
                alias: 'report'
            },
            {
                id: 'share',
                icon: '💰',
                label: '分享推广分成',
                url: '12-分享收益页面.html',
                alias: 'earnings'
            },
            {
                id: 'profile',
                icon: 'fas fa-user',
                label: '我',
                url: '03-我的页面.html',
                alias: 'profile'
            }
        ];

        let navItemsHtml = '';
        navItems.forEach(item => {
            const activeClass = currentPage === item.id ? 'active' : '';
            // 判断是否为emoji图标
            const isEmoji = !item.icon.startsWith('fas ') && !item.icon.startsWith('far ') && !item.icon.startsWith('fab ');
            const emojiClass = isEmoji ? 'emoji-nav-item' : '';
            const iconHtml = isEmoji
                ? `<span class="nav-icon emoji-icon">${item.icon}</span>`
                : `<i class="${item.icon} nav-icon"></i>`;
            
            navItemsHtml += `
                <div class="nav-item ${activeClass} ${emojiClass}" onclick="navigateTo('${item.url}')">
                    ${iconHtml}
                    <span class="nav-label">${item.label}</span>
                </div>
            `;
        });

        return `
            <div class="bottom-nav ${customClass}">
                ${navItemsHtml}
            </div>
        `;
    },

    // 分享功能JavaScript
    initShareFunctions: function() {
        // 分享相关函数
        window.showShareModal = function() {
            const shareModal = document.getElementById('shareModal');
            if (shareModal) {
                shareModal.style.display = 'block';
            }
        };

        window.closeShareModal = function() {
            const shareModal = document.getElementById('shareModal');
            if (shareModal) {
                shareModal.style.display = 'none';
            }
        };

        window.shareToWechat = function() {
            showToast('正在打开微信分享...');
            setTimeout(() => {
                showToast('分享成功！');
                closeShareModal();
            }, 1000);
        };

        window.shareToMoments = function() {
            showToast('正在打开朋友圈分享...');
            setTimeout(() => {
                showToast('分享成功！');
                closeShareModal();
            }, 1000);
        };

        window.copyShareLink = function() {
            const shareLink = 'https://shuimu.app/share?ref=user123';

            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(shareLink).then(() => {
                    showToast('分享链接已复制到剪贴板');
                }).catch(() => {
                    fallbackCopyTextToClipboard(shareLink);
                });
            } else {
                fallbackCopyTextToClipboard(shareLink);
            }

            function fallbackCopyTextToClipboard(text) {
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    showToast('分享链接已复制到剪贴板');
                } catch (err) {
                    showToast('复制失败，请手动复制：' + text);
                }
                document.body.removeChild(textArea);
            }
            closeShareModal();
        };

        window.shareToQQ = function() {
            showToast('正在打开QQ分享...');
            setTimeout(() => {
                showToast('分享成功！');
                closeShareModal();
            }, 1000);
        };

        // Toast提示函数
        window.showToast = function(message) {
            // 移除现有的toast
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            // 创建新的toast
            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 14px;
                z-index: 10000;
                animation: fadeInOut 2s ease-in-out;
            `;

            // 添加动画样式
            if (!document.querySelector('#toast-style')) {
                const style = document.createElement('style');
                style.id = 'toast-style';
                style.textContent = `
                    @keyframes fadeInOut {
                        0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                        20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                        80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                        100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(toast);

            // 2秒后自动移除
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 2000);
        };
    },

    // 底部导航JavaScript功能
    initBottomNavigation: function() {
        // 底部导航功能已经在navigation-manager.js中定义
        // 这里只需要确保导航功能正常工作
        console.log('底部导航功能已初始化');

        // 确保navigateToPage函数存在
        if (typeof window.navigateToPage !== 'function') {
            console.warn('navigateToPage函数未找到，使用备用导航方式');
            window.navigateToPage = function(pageUrl) {
                try {
                    window.location.href = pageUrl;
                } catch (error) {
                    console.error('Navigation error:', error);
                    alert('页面跳转失败，请重试');
                }
            };
        }
    },

    // ========== 工具方法 ==========
    
    // 获取徽章等级
    getBadgeLevel: function(watchCount) {
        if (watchCount >= 10) return 10;
        return Math.min(Math.max(watchCount, 0), 9);
    },

    // 获取徽章CSS类
    getBadgeClass: function(level) {
        if (level >= 10) return 'badge-level-10';
        return `badge-level-${level}`;
    },

    // 获取徽章文本
    getBadgeText: function(watchCount) {
        return `×${watchCount}`;
    }
};

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ComponentTemplates;
} else if (typeof window !== 'undefined') {
    window.ComponentTemplates = ComponentTemplates;
} 