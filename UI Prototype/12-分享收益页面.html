<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 分享收益</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <script src="data/component-templates-with-styles.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
            padding-bottom: 60px;
        }
        .content::-webkit-scrollbar { display: none; }
        .earnings-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 24px;
            margin: 16px 0;
            color: white;
            text-align: center;
        }
        .earnings-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .stat-item {
            text-align: center;
            padding: 16px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            margin: 0 4px;
        }
        .withdraw-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            margin-top: 16px;
        }
        .earnings-item {
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 16px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        .earnings-item:last-child {
            border-bottom: none;
        }
        .earnings-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: white;
        }
        .chart-container {
            height: 200px;
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }
        .bar-chart {
            display: flex;
            align-items: end;
            height: 120px;
            gap: 8px;
            padding: 0 16px;
        }
        .bar {
            flex: 1;
            background: linear-gradient(to top, #10b981, #34d399);
            border-radius: 4px 4px 0 0;
            position: relative;
            min-height: 20px;
        }
        .bar-label {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: #6b7280;
        }
        .time-filter {
            display: flex;
            background: #f3f4f6;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 16px;
        }
        .filter-btn {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .filter-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        .status-processing {
            background: #dbeafe;
            color: #1e40af;
        }


    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 收益概览 -->
                <div class="earnings-header">
                    <h2 class="text-lg font-semibold mb-2">总收益</h2>
                    <div class="text-3xl font-bold mb-4">¥2,468.50</div>
                    
                    <div class="grid grid-cols-3 gap-2">
                        <div class="stat-item">
                            <div class="text-lg font-bold">¥1,234.50</div>
                            <div class="text-sm opacity-80">可提现</div>
                        </div>
                        <div class="stat-item">
                            <div class="text-lg font-bold">¥1,234.00</div>
                            <div class="text-sm opacity-80">已提现</div>
                        </div>
                        <div class="stat-item">
                            <div class="text-lg font-bold">156</div>
                            <div class="text-sm opacity-80">分享人数</div>
                        </div>
                    </div>
                    
                    <button class="withdraw-btn w-full">
                        <i class="fas fa-wallet mr-2"></i>
                        立即提现
                    </button>
                </div>

                <!-- 收益趋势 -->
                <div class="earnings-card">
                    <h3 class="font-semibold text-gray-800 mb-4">收益趋势</h3>
                    
                    <!-- 时间筛选 -->
                    <div class="time-filter">
                        <div class="filter-btn">本周</div>
                        <div class="filter-btn active">本月</div>
                        <div class="filter-btn">本年</div>
                    </div>

                    <div class="chart-container">
                        <div class="bar-chart">
                            <div class="bar" style="height: 60%;">
                                <div class="bar-label">1月</div>
                            </div>
                            <div class="bar" style="height: 80%;">
                                <div class="bar-label">2月</div>
                            </div>
                            <div class="bar" style="height: 40%;">
                                <div class="bar-label">3月</div>
                            </div>
                            <div class="bar" style="height: 90%;">
                                <div class="bar-label">4月</div>
                            </div>
                            <div class="bar" style="height: 70%;">
                                <div class="bar-label">5月</div>
                            </div>
                            <div class="bar" style="height: 50%;">
                                <div class="bar-label">6月</div>
                            </div>
                        </div>
                        <div class="text-center mt-4 text-sm text-gray-500">
                            本月收益 ¥456.50，比上月增长 23%
                        </div>
                    </div>
                </div>

                <!-- 收益明细 -->
                <div class="earnings-card">
                    <h3 class="font-semibold text-gray-800 mb-4">收益明细</h3>
                    
                    <div class="space-y-0">
                        <div class="earnings-item">
                            <div class="flex items-center">
                                <div class="earnings-icon" style="background: #10b981;">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">分享收益</p>
                                    <p class="text-sm text-gray-500">用户购买：道系列课程</p>
                                    <p class="text-xs text-gray-400">2024-01-15 14:30</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-green-600">+¥89.70</div>
                                <div class="status-badge status-success">已到账</div>
                            </div>
                        </div>

                        <div class="earnings-item">
                            <div class="flex items-center">
                                <div class="earnings-icon" style="background: #3b82f6;">
                                    <i class="fas fa-minus"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">提现</p>
                                    <p class="text-sm text-gray-500">提现到支付宝</p>
                                    <p class="text-xs text-gray-400">2024-01-14 16:20</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-600">-¥500.00</div>
                                <div class="status-badge status-success">已完成</div>
                            </div>
                        </div>

                        <div class="earnings-item">
                            <div class="flex items-center">
                                <div class="earnings-icon" style="background: #10b981;">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">分享收益</p>
                                    <p class="text-sm text-gray-500">用户购买：术系列课程</p>
                                    <p class="text-xs text-gray-400">2024-01-13 10:15</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-green-600">+¥67.50</div>
                                <div class="status-badge status-pending">待结算</div>
                            </div>
                        </div>

                        <div class="earnings-item">
                            <div class="flex items-center">
                                <div class="earnings-icon" style="background: #10b981;">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">分享收益</p>
                                    <p class="text-sm text-gray-500">用户购买：道系列课程</p>
                                    <p class="text-xs text-gray-400">2024-01-12 20:45</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-green-600">+¥89.70</div>
                                <div class="status-badge status-success">已到账</div>
                            </div>
                        </div>

                        <div class="earnings-item">
                            <div class="flex items-center">
                                <div class="earnings-icon" style="background: #10b981;">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">分享收益</p>
                                    <p class="text-sm text-gray-500">用户购买：术系列课程</p>
                                    <p class="text-xs text-gray-400">2024-01-11 15:30</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-green-600">+¥67.50</div>
                                <div class="status-badge status-processing">处理中</div>
                            </div>
                        </div>

                        <div class="earnings-item">
                            <div class="flex items-center">
                                <div class="earnings-icon" style="background: #10b981;">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">分享收益</p>
                                    <p class="text-sm text-gray-500">用户购买：道系列课程</p>
                                    <p class="text-xs text-gray-400">2024-01-10 11:20</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-green-600">+¥89.70</div>
                                <div class="status-badge status-success">已到账</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提现记录 -->
                <div class="earnings-card">
                    <h3 class="font-semibold text-gray-800 mb-4">提现记录</h3>
                    
                    <div class="space-y-0">
                        <div class="earnings-item">
                            <div class="flex items-center">
                                <div class="earnings-icon" style="background: #3b82f6;">
                                    <i class="fab fa-alipay"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">支付宝</p>
                                    <p class="text-sm text-gray-500">138****8888</p>
                                    <p class="text-xs text-gray-400">2024-01-14 16:20</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">¥500.00</div>
                                <div class="status-badge status-success">已到账</div>
                            </div>
                        </div>

                        <div class="earnings-item">
                            <div class="flex items-center">
                                <div class="earnings-icon" style="background: #10b981;">
                                    <i class="fab fa-weixin"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">微信</p>
                                    <p class="text-sm text-gray-500">微信用户</p>
                                    <p class="text-xs text-gray-400">2024-01-08 14:15</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">¥300.00</div>
                                <div class="status-badge status-success">已到账</div>
                            </div>
                        </div>

                        <div class="earnings-item">
                            <div class="flex items-center">
                                <div class="earnings-icon" style="background: #3b82f6;">
                                    <i class="fab fa-alipay"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">支付宝</p>
                                    <p class="text-sm text-gray-500">138****8888</p>
                                    <p class="text-xs text-gray-400">2024-01-01 10:30</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-gray-800">¥434.00</div>
                                <div class="status-badge status-success">已到账</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 收益说明 -->
                <div class="earnings-card">
                    <h3 class="font-semibold text-gray-800 mb-4">收益说明</h3>
                    
                    <div class="space-y-3 text-sm text-gray-600">
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>分享收益为订单金额的30%，订单完成后24小时内到账</p>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>最低提现金额为100元，提现手续费为2%</p>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>提现申请后1-3个工作日内到账</p>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                            <p>如有疑问请联系客服：shuimu_kf001</p>
                        </div>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>


        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTopNavigation();
            // 添加底部导航
            addBottomNavigation();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '分享收益'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        function goBack() {
            if (window.navigateTo) {
                navigateTo('03-我的页面.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '03-我的页面.html';
            }
        }

        // showShareModal函数已经在顶部导航组件中自动包含

        function goToHelp() {
            if (window.navigateTo) {
                navigateTo('10-帮助与反馈页面.html');
            } else {
                window.location.href = '10-帮助与反馈页面.html';
            }
        }

        // 添加底部导航函数
        function addBottomNavigation() {
            try {
                // 检查是否已经存在底部导航
                if (document.querySelector('.bottom-nav')) {
                    console.log('底部导航已存在，跳过添加');
                    return;
                }

                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加底部导航');
                    return;
                }

                // 创建底部导航HTML
                const bottomNavHtml = ComponentTemplates.createBottomNavigation({
                    currentPage: 'share'
                });

                // 添加到页面底部
                document.body.insertAdjacentHTML('beforeend', bottomNavHtml);

                // 初始化底部导航功能
                ComponentTemplates.initBottomNavigation();

                console.log('底部导航添加成功');
            } catch (error) {
                console.error('添加底部导航失败:', error);
            }
        }
    </script>
</body>
</html> 