<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 学习报告</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <script src="data/component-templates-with-styles.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
            padding-bottom: 60px;
        }
        .content::-webkit-scrollbar { display: none; }
        .report-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .chart-container {
            height: 200px;
            background: #f8fafc;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            overflow: hidden;
        }
        .progress-ring {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(#667eea 0deg 245deg, #e5e7eb 245deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin: 0 auto;
        }
        .progress-ring::before {
            content: '';
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: white;
            position: absolute;
        }
        .progress-text {
            position: relative;
            z-index: 1;
            text-align: center;
        }
        .bar-chart {
            display: flex;
            align-items: end;
            height: 120px;
            gap: 8px;
            padding: 0 16px;
        }
        .bar {
            flex: 1;
            background: linear-gradient(to top, #667eea, #764ba2);
            border-radius: 4px 4px 0 0;
            position: relative;
            min-height: 20px;
        }
        .bar-label {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: #6b7280;
        }
        .time-filter {
            display: flex;
            background: #f3f4f6;
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 16px;
        }
        .filter-btn {
            flex: 1;
            padding: 8px 12px;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .filter-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .achievement-badge {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            margin: 4px;
        }


    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 时间筛选 -->
                <div class="time-filter">
                    <div class="filter-btn" onclick="switchTimeFilter(this, 'week')">本周</div>
                    <div class="filter-btn active" onclick="switchTimeFilter(this, 'month')">本月</div>
                    <div class="filter-btn" onclick="switchTimeFilter(this, 'year')">本年</div>
                    <div class="filter-btn" onclick="switchTimeFilter(this, 'all')">全部</div>
                </div>

                <!-- 总体进度 -->
                <div class="report-card" onclick="showProgressDetails()">
                    <h3 class="font-semibold text-gray-800 mb-4">总体学习进度</h3>
                    <div class="progress-ring" onclick="showProgressDetails()">
                        <div class="progress-text">
                            <div class="text-2xl font-bold text-blue-600" id="progressPercent">68%</div>
                            <div class="text-sm text-gray-500">完成度</div>
                        </div>
                    </div>
                    <div class="grid grid-cols-3 gap-4 mt-6">
                        <div class="text-center" onclick="showTimeDetails()">
                            <div class="text-lg font-bold text-gray-800" id="studyTime">24.5h</div>
                            <div class="text-sm text-gray-500">学习时长</div>
                        </div>
                        <div class="text-center" onclick="showVideoDetails()">
                            <div class="text-lg font-bold text-gray-800" id="completedVideos">18</div>
                            <div class="text-sm text-gray-500">完成视频</div>
                        </div>
                        <div class="text-center" onclick="showCourseDetails()">
                            <div class="text-lg font-bold text-gray-800" id="purchasedCourses">3</div>
                            <div class="text-sm text-gray-500">已购课程</div>
                        </div>
                    </div>
                </div>

                <!-- 每日学习时长 -->
                <div class="report-card">
                    <h3 class="font-semibold text-gray-800 mb-4" id="chartTitle">每日学习时长（本月）</h3>
                    <div class="chart-container">
                        <div class="bar-chart" id="barChart">
                            <div class="bar" style="height: 60%;" onclick="showDayDetails(1, '1.2h')" title="第1天: 1.2小时">
                                <div class="bar-label">1</div>
                            </div>
                            <div class="bar" style="height: 80%;" onclick="showDayDetails(2, '1.6h')" title="第2天: 1.6小时">
                                <div class="bar-label">2</div>
                            </div>
                            <div class="bar" style="height: 40%;" onclick="showDayDetails(3, '0.8h')" title="第3天: 0.8小时">
                                <div class="bar-label">3</div>
                            </div>
                            <div class="bar" style="height: 90%;" onclick="showDayDetails(4, '1.8h')" title="第4天: 1.8小时">
                                <div class="bar-label">4</div>
                            </div>
                            <div class="bar" style="height: 70%;" onclick="showDayDetails(5, '1.4h')" title="第5天: 1.4小时">
                                <div class="bar-label">5</div>
                            </div>
                            <div class="bar" style="height: 50%;" onclick="showDayDetails(6, '1.0h')" title="第6天: 1.0小时">
                                <div class="bar-label">6</div>
                            </div>
                            <div class="bar" style="height: 85%;" onclick="showDayDetails(7, '1.7h')" title="第7天: 1.7小时">
                                <div class="bar-label">7</div>
                            </div>
                        </div>
                        <div class="text-center mt-4 text-sm text-gray-500" id="averageTime">
                            平均每日学习 1.2小时
                        </div>
                    </div>
                </div>

                <!-- 课程完成情况 -->
                <div class="report-card">
                    <h3 class="font-semibold text-gray-800 mb-4">课程完成情况</h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between course-item" onclick="showCourseProgress('道：恋爱宝典系列')">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-heart text-green-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">道：恋爱宝典系列</p>
                                    <p class="text-sm text-gray-500">6个课程</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-green-600">100%</div>
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between course-item" onclick="showCourseProgress('术：聊天技术系列')">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-comments text-blue-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">术：聊天技术系列</p>
                                    <p class="text-sm text-gray-500">3个课程</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-blue-600">67%</div>
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 67%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-center justify-between course-item" onclick="showCourseProgress('免费体验')">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-play-circle text-yellow-600"></i>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-800">免费体验</p>
                                    <p class="text-sm text-gray-500">2个课程</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-bold text-yellow-600">100%</div>
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 学习成就 -->
                <div class="report-card">
                    <h3 class="font-semibold text-gray-800 mb-4">学习成就</h3>
                    <div class="flex flex-wrap">
                        <div class="achievement-badge">
                            <i class="fas fa-trophy mr-2"></i>
                            初学者
                        </div>
                        <div class="achievement-badge">
                            <i class="fas fa-fire mr-2"></i>
                            连续学习7天
                        </div>
                        <div class="achievement-badge">
                            <i class="fas fa-star mr-2"></i>
                            完成首个系列
                        </div>
                        <div class="achievement-badge">
                            <i class="fas fa-clock mr-2"></i>
                            学习达人
                        </div>
                        <div class="achievement-badge">
                            <i class="fas fa-share mr-2"></i>
                            分享达人
                        </div>
                    </div>
                </div>

                <!-- 学习建议 -->
                <div class="report-card">
                    <h3 class="font-semibold text-gray-800 mb-4">学习建议</h3>
                    <div class="space-y-3">
                        <div class="flex items-start">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                <i class="fas fa-lightbulb text-blue-600 text-xs"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800">保持学习节奏</p>
                                <p class="text-sm text-gray-500">建议每天学习30-60分钟，保持稳定的学习习惯</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                <i class="fas fa-target text-green-600 text-xs"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800">完成剩余课程</p>
                                <p class="text-sm text-gray-500">还有1个聊天技术课程未完成，建议本周内完成</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center mr-3 mt-1">
                                <i class="fas fa-users text-purple-600 text-xs"></i>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800">实践应用</p>
                                <p class="text-sm text-gray-500">理论学习后要多实践，可在学习群内交流心得</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>


        </div>
    </div>

    <script>
        // 学习报告数据管理器
        class ReportManager {
            constructor() {
                this.currentFilter = 'month';
                this.reportData = this.loadReportData();
                this.init();
            }

            init() {
                this.updateDisplay();
                this.addInteractiveStyles();
            }

            // 加载报告数据
            loadReportData() {
                return {
                    week: {
                        progress: 45,
                        studyTime: '8.5h',
                        completedVideos: 6,
                        purchasedCourses: 3,
                        averageDaily: '1.2h',
                        chartTitle: '每日学习时长（本周）',
                        chartData: [
                            { day: 1, time: '1.2h', height: '60%' },
                            { day: 2, time: '1.6h', height: '80%' },
                            { day: 3, time: '0.8h', height: '40%' },
                            { day: 4, time: '1.8h', height: '90%' },
                            { day: 5, time: '1.4h', height: '70%' },
                            { day: 6, time: '1.0h', height: '50%' },
                            { day: 7, time: '1.7h', height: '85%' }
                        ]
                    },
                    month: {
                        progress: 68,
                        studyTime: '24.5h',
                        completedVideos: 18,
                        purchasedCourses: 3,
                        averageDaily: '1.2h',
                        chartTitle: '每日学习时长（本月）',
                        chartData: [
                            { day: 1, time: '1.2h', height: '60%' },
                            { day: 2, time: '1.6h', height: '80%' },
                            { day: 3, time: '0.8h', height: '40%' },
                            { day: 4, time: '1.8h', height: '90%' },
                            { day: 5, time: '1.4h', height: '70%' },
                            { day: 6, time: '1.0h', height: '50%' },
                            { day: 7, time: '1.7h', height: '85%' }
                        ]
                    },
                    year: {
                        progress: 85,
                        studyTime: '156h',
                        completedVideos: 89,
                        purchasedCourses: 3,
                        averageDaily: '0.9h',
                        chartTitle: '每月学习时长（本年）',
                        chartData: [
                            { day: 1, time: '12h', height: '40%' },
                            { day: 2, time: '18h', height: '60%' },
                            { day: 3, time: '25h', height: '85%' },
                            { day: 4, time: '22h', height: '75%' },
                            { day: 5, time: '28h', height: '95%' },
                            { day: 6, time: '20h', height: '65%' },
                            { day: 7, time: '31h', height: '100%' }
                        ]
                    },
                    all: {
                        progress: 92,
                        studyTime: '298h',
                        completedVideos: 156,
                        purchasedCourses: 3,
                        averageDaily: '0.8h',
                        chartTitle: '总体学习时长分布',
                        chartData: [
                            { day: 1, time: '45h', height: '30%' },
                            { day: 2, time: '62h', height: '42%' },
                            { day: 3, time: '78h', height: '52%' },
                            { day: 4, time: '89h', height: '60%' },
                            { day: 5, time: '95h', height: '64%' },
                            { day: 6, time: '102h', height: '68%' },
                            { day: 7, time: '118h', height: '80%' }
                        ]
                    }
                };
            }

            // 更新显示
            updateDisplay() {
                const data = this.reportData[this.currentFilter];

                // 更新进度数据
                document.getElementById('progressPercent').textContent = data.progress + '%';
                document.getElementById('studyTime').textContent = data.studyTime;
                document.getElementById('completedVideos').textContent = data.completedVideos;
                document.getElementById('purchasedCourses').textContent = data.purchasedCourses;

                // 更新图表
                document.getElementById('chartTitle').textContent = data.chartTitle;
                document.getElementById('averageTime').textContent = `平均每日学习 ${data.averageDaily}`;

                this.updateChart(data.chartData);
            }

            // 更新图表
            updateChart(chartData) {
                const barChart = document.getElementById('barChart');
                barChart.innerHTML = '';

                chartData.forEach((item, index) => {
                    const bar = document.createElement('div');
                    bar.className = 'bar';
                    bar.style.height = item.height;
                    bar.onclick = () => this.showDayDetails(item.day, item.time);
                    bar.title = `第${item.day}${this.getTimeUnit()}: ${item.time}`;

                    const label = document.createElement('div');
                    label.className = 'bar-label';
                    label.textContent = item.day;

                    bar.appendChild(label);
                    barChart.appendChild(bar);
                });
            }

            // 获取时间单位
            getTimeUnit() {
                const units = {
                    week: '天',
                    month: '天',
                    year: '月',
                    all: '阶段'
                };
                return units[this.currentFilter] || '天';
            }

            // 添加交互样式
            addInteractiveStyles() {
                const style = document.createElement('style');
                style.textContent = `
                    .course-item {
                        cursor: pointer;
                        transition: all 0.3s ease;
                        padding: 8px;
                        border-radius: 8px;
                    }
                    .course-item:hover {
                        background-color: #f9fafb;
                        transform: translateX(4px);
                    }
                    .bar {
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .bar:hover {
                        opacity: 0.8;
                        transform: scale(1.05);
                    }
                    .report-card {
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    .report-card:hover {
                        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                    }
                `;
                document.head.appendChild(style);
            }

            // 显示提示
            showToast(message) {
                const toast = document.createElement('div');
                toast.textContent = message;
                toast.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: rgba(0, 0, 0, 0.8);
                    color: white;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 14px;
                    z-index: 10000;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                `;

                document.body.appendChild(toast);
                setTimeout(() => toast.style.opacity = '1', 10);
                setTimeout(() => {
                    toast.style.opacity = '0';
                    setTimeout(() => toast.remove(), 300);
                }, 2000);
            }
        }

        // 全局报告管理器实例
        let reportManager;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTopNavigation();
            reportManager = new ReportManager();
            // 添加底部导航
            addBottomNavigation();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '学习报告'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        function goBack() {
            if (window.navigateTo) {
                navigateTo('03-我的页面.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '03-我的页面.html';
            }
        }

        // showShareModal函数已经在顶部导航组件中自动包含

        // 时间筛选切换
        function switchTimeFilter(element, filterType) {
            // 移除所有active类
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 添加active类到当前元素
            element.classList.add('active');

            // 更新数据
            if (reportManager) {
                reportManager.currentFilter = filterType;
                reportManager.updateDisplay();
                reportManager.showToast(`已切换到${element.textContent}视图`);
            }
        }

        // 显示进度详情
        function showProgressDetails() {
            if (reportManager) {
                const data = reportManager.reportData[reportManager.currentFilter];
                const message = `学习进度详情：\n完成度：${data.progress}%\n学习时长：${data.studyTime}\n完成视频：${data.completedVideos}个\n已购课程：${data.purchasedCourses}个`;
                alert(message);
            }
        }

        // 显示时长详情
        function showTimeDetails() {
            if (reportManager) {
                const data = reportManager.reportData[reportManager.currentFilter];
                reportManager.showToast(`总学习时长：${data.studyTime}`);
            }
        }

        // 显示视频详情
        function showVideoDetails() {
            if (reportManager) {
                const data = reportManager.reportData[reportManager.currentFilter];
                reportManager.showToast(`已完成 ${data.completedVideos} 个视频`);
            }
        }

        // 显示课程详情
        function showCourseDetails() {
            if (reportManager) {
                const data = reportManager.reportData[reportManager.currentFilter];
                reportManager.showToast(`已购买 ${data.purchasedCourses} 个课程系列`);
            }
        }

        // 显示单日详情
        function showDayDetails(day, time) {
            if (reportManager) {
                const unit = reportManager.getTimeUnit();
                reportManager.showToast(`第${day}${unit}学习时长：${time}`);
            }
        }

        // 显示课程进度
        function showCourseProgress(courseName) {
            const progressData = {
                '道：恋爱宝典系列': {
                    progress: 100,
                    completed: 6,
                    total: 6,
                    details: '已完成全部6个课程，包括恋爱宝典1、2、3等'
                },
                '术：聊天技术系列': {
                    progress: 67,
                    completed: 2,
                    total: 3,
                    details: '已完成2个课程，还有1个聊天技术3未完成'
                },
                '免费体验': {
                    progress: 100,
                    completed: 2,
                    total: 2,
                    details: '已完成全部2个免费体验课程'
                }
            };

            const data = progressData[courseName];
            if (data) {
                const message = `${courseName}\n\n进度：${data.progress}%\n完成：${data.completed}/${data.total}个课程\n\n${data.details}`;
                alert(message);
            }
        }

        // 添加底部导航函数
        function addBottomNavigation() {
            try {
                // 检查是否已经存在底部导航
                if (document.querySelector('.bottom-nav')) {
                    console.log('底部导航已存在，跳过添加');
                    return;
                }

                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加底部导航');
                    return;
                }

                // 创建底部导航HTML
                const bottomNavHtml = ComponentTemplates.createBottomNavigation({
                    currentPage: 'learning'
                });

                // 添加到页面底部
                document.body.insertAdjacentHTML('beforeend', bottomNavHtml);

                // 初始化底部导航功能
                ComponentTemplates.initBottomNavigation();

                console.log('底部导航添加成功');
            } catch (error) {
                console.error('添加底部导航失败:', error);
            }
        }
    </script>
</body>
</html> 