<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 缓存管理</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
        }
        .content::-webkit-scrollbar { display: none; }
        .storage-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 12px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        .video-item {
            background: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            justify-content: between;
        }
        .video-info {
            flex: 1;
            margin-right: 12px;
        }
        .video-title {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 4px;
        }
        .video-meta {
            font-size: 12px;
            color: #6b7280;
        }
        .delete-btn {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: #fee2e2;
            color: #dc2626;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .clear-all-btn {
            background: #ef4444;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            border: none;
            cursor: pointer;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>



            <!-- 内容区域 -->
            <div class="content">
                <!-- 存储空间信息 -->
                <div class="storage-card">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-semibold text-gray-800">存储空间</h3>
                        <span class="text-sm text-gray-500">1.2GB / 64GB</span>
                    </div>
                    
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 18%"></div>
                    </div>
                    
                    <div class="flex justify-between text-sm">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                            <span class="text-gray-600">视频缓存 1.0GB</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gray-300 rounded-full mr-2"></div>
                            <span class="text-gray-600">其他文件 0.2GB</span>
                        </div>
                    </div>
                </div>

                <!-- 缓存视频列表 -->
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-3">
                        <h3 class="text-lg font-semibold text-gray-800">已缓存视频 (8个)</h3>
                        <button class="clear-all-btn" onclick="clearAllCache()">
                            清空全部
                        </button>
                    </div>
                    
                    <div class="video-item">
                        <div class="video-info">
                            <div class="video-title">01. 初识吸引力法则 • 道·恋爱宝典1</div>
                            <div class="video-meta">156MB • 已完成 • 12分钟</div>
                        </div>
                        <button class="delete-btn" onclick="deleteVideo(this)">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>

                    <div class="video-item">
                        <div class="video-info">
                            <div class="video-title">02. 建立自信的方法 • 道·恋爱宝典1</div>
                            <div class="video-meta">142MB • 已完成 • 15分钟</div>
                        </div>
                        <button class="delete-btn" onclick="deleteVideo(this)">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>

                    <div class="video-item">
                        <div class="video-info">
                            <div class="video-title">03. 第一印象的重要性 • 道·恋爱宝典1</div>
                            <div class="video-meta">168MB • 已完成 • 18分钟</div>
                        </div>
                        <button class="delete-btn" onclick="deleteVideo(this)">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>

                    <div class="video-item">
                        <div class="video-info">
                            <div class="video-title">04. 肢体语言解读 • 道·恋爱宝典1</div>
                            <div class="video-meta">189MB • 已完成 • 22分钟</div>
                        </div>
                        <button class="delete-btn" onclick="deleteVideo(this)">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>

                    <div class="video-item">
                        <div class="video-info">
                            <div class="video-title">01. 约会前的准备工作 • 免费体验</div>
                            <div class="video-meta">98MB • 已完成 • 8分钟</div>
                        </div>
                        <button class="delete-btn" onclick="deleteVideo(this)">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>

                    <div class="video-item">
                        <div class="video-info">
                            <div class="video-title">02. 约会地点的选择 • 免费体验</div>
                            <div class="video-meta">112MB • 已完成 • 10分钟</div>
                        </div>
                        <button class="delete-btn" onclick="deleteVideo(this)">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>

                    <div class="video-item">
                        <div class="video-info">
                            <div class="video-title">01. 长期关系建立 • 术·聊天技术</div>
                            <div class="video-meta">201MB • 已完成 • 25分钟</div>
                        </div>
                        <button class="delete-btn" onclick="deleteVideo(this)">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>

                    <div class="video-item">
                        <div class="video-info">
                            <div class="video-title">02. 短期速约技巧 • 术·聊天技术</div>
                            <div class="video-meta">178MB • 已完成 • 20分钟</div>
                        </div>
                        <button class="delete-btn" onclick="deleteVideo(this)">
                            <i class="fas fa-trash text-sm"></i>
                        </button>
                    </div>
                </div>

                <!-- 缓存设置 -->
                <div class="storage-card">
                    <h3 class="font-semibold text-gray-800 mb-4">缓存设置</h3>
                    
                    <div class="flex items-center justify-between py-3 border-b border-gray-100">
                        <div>
                            <p class="font-medium text-gray-800">仅WiFi下缓存</p>
                            <p class="text-sm text-gray-500">节省流量消耗</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer" checked>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                    
                    <div class="flex items-center justify-between py-3">
                        <div>
                            <p class="font-medium text-gray-800">自动清理</p>
                            <p class="text-sm text-gray-500">30天未观看自动删除</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <div class="h-20"></div>
            </div>
        </div>
    </div>

    <script src="data/component-templates-with-styles.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addTopNavigation();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '缓存管理'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }



        function goBack() {
            if (window.navigateTo) {
                navigateTo('03-我的页面.html');
            } else if (window.history && window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '03-我的页面.html';
            }
        }

        // showShareModal函数已经在ComponentTemplates.initShareFunctions()中定义

        function deleteVideo(button) {
            if (confirm('确定要删除这个视频缓存吗？')) {
                button.closest('.video-item').remove();
                // 更新统计信息
                updateStorageInfo();
            }
        }
        
        function clearAllCache() {
            if (confirm('确定要清空所有缓存吗？此操作不可恢复。')) {
                // 删除所有视频项
                const videoItems = document.querySelectorAll('.video-item');
                videoItems.forEach(item => item.remove());
                
                // 更新统计信息
                updateStorageInfo();
                
                // 显示成功消息
                alert('所有缓存已清空');
            }
        }
        
        function updateStorageInfo() {
            // 更新存储空间信息的逻辑
            const remainingItems = document.querySelectorAll('.video-item').length;
            const title = document.querySelector('h3');
            if (title && title.textContent.includes('已缓存视频')) {
                title.textContent = `已缓存视频 (${remainingItems}个)`;
            }
            console.log('更新存储空间信息');
        }
    </script>
</body>
</html> 