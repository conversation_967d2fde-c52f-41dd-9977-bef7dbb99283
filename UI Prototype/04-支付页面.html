<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水幕 - 确认支付</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="data/navigation-manager.js"></script>
    <style>
        body { 
            overflow: hidden; 
            font-family: 'PingFang SC', 'Helvetica Neue', sans-serif;
        }
        .phone-mockup {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
            position: relative;
        }
        .screen {
            width: 359px;
            height: 796px;
            background: #f8fafc;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .status-bar {
            height: 44px;
            background: transparent;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }
        .nav-bar {
            height: 44px;
            background: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e5e7eb;
        }
        .content {
            height: calc(100% - 88px);
            overflow-y: auto;
            padding: 0 16px;
        }
        .content::-webkit-scrollbar { display: none; }
        .course-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .payment-method {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            border: 2px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .payment-method.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }
        .payment-method:hover {
            border-color: #667eea;
        }
        .price-section {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .pay-button {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: calc(359px - 32px);
            background: #10b981;
            color: white;
            padding: 16px;
            border-radius: 12px;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        .course-content {
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
        }
        .content-item {
            display: flex;
            align-items: center;
            padding: 4px 0;
            font-size: 14px;
            color: #6b7280;
        }

    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-mockup">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>
                    <i class="fas fa-signal"></i>
                    <i class="fas fa-wifi"></i>
                    <i class="fas fa-battery-three-quarters"></i>
                </span>
            </div>

            <!-- 顶部导航栏 -->
            <div id="topNavContainer"></div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 课程信息 -->
                <div class="course-card">
                    <div class="flex items-start">
                        <img src="https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=80&h=80&fit=crop" 
                             class="w-20 h-20 rounded-lg mr-4" alt="课程封面">
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-gray-800 mb-2" id="courseTitle">恋爱宝典1</h3>
                            <p class="text-sm text-gray-500 mb-2" id="courseSubtitle">道：恋爱宝典系列</p>
                            <div class="flex items-center text-xs text-gray-400">
                                <span><i class="fas fa-video mr-1"></i>6个视频</span>
                                <span class="mx-2">•</span>
                                <span><i class="fas fa-clock mr-1"></i>约2小时</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 课程内容 -->
                    <div class="course-content">
                        <p class="text-sm font-medium text-gray-700 mb-2">包含内容：</p>
                        <div class="content-item">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>01. 初识吸引力法则</span>
                        </div>
                        <div class="content-item">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>02. 建立自信的方法</span>
                        </div>
                        <div class="content-item">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>03. 第一印象的重要性</span>
                        </div>
                        <div class="content-item">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>04. 肢体语言解读</span>
                        </div>
                        <div class="content-item">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>05. 情感表达技巧</span>
                        </div>
                        <div class="content-item">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <span>06. 实战案例分析</span>
                        </div>
                    </div>
                </div>

                <!-- 支付方式 -->
                <div class="mb-4">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">选择支付方式</h3>
                    
                    <div class="payment-method selected" onclick="selectPayment('alipay')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=40&h=40&fit=crop" 
                                     class="w-10 h-10 rounded mr-3" alt="支付宝">
                                <div>
                                    <p class="font-medium text-gray-800">支付宝</p>
                                    <p class="text-sm text-gray-500">推荐使用</p>
                                </div>
                            </div>
                            <div class="w-5 h-5 border-2 border-blue-500 rounded-full bg-blue-500 flex items-center justify-center">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                        </div>
                    </div>

                    <div class="payment-method" onclick="selectPayment('wechat')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <img src="https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=40&h=40&fit=crop" 
                                     class="w-10 h-10 rounded mr-3" alt="微信支付">
                                <div>
                                    <p class="font-medium text-gray-800">微信支付</p>
                                    <p class="text-sm text-gray-500">安全便捷</p>
                                </div>
                            </div>
                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full"></div>
                        </div>
                    </div>
                </div>

                <!-- 价格详情 -->
                <div class="price-section">
                    <h3 class="text-lg font-semibold text-gray-800 mb-3">订单详情</h3>
                    
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">课程原价</span>
                            <span class="text-gray-800" id="originalPrice">¥100.00</span>
                        </div>
                        
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">优惠减免</span>
                            <span class="text-green-500">-¥0.00</span>
                        </div>
                        
                        <div class="border-t pt-3">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-semibold text-gray-800">实付金额</span>
                                <span class="text-2xl font-bold text-red-500" id="finalPrice">¥100.00</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 购买须知 -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-20">
                    <h4 class="font-medium text-yellow-800 mb-2">
                        <i class="fas fa-info-circle mr-2"></i>购买须知
                    </h4>
                    <ul class="text-sm text-yellow-700 space-y-1">
                        <li>• 购买后可永久观看，支持离线缓存</li>
                        <li>• 课程内容受版权保护，禁止录屏分享</li>
                        <li>• 购买成功后请添加客服微信验证身份</li>
                        <li>• 获得分享分成权限，每单可得30%收益</li>
                    </ul>
                </div>
            </div>

            <!-- 支付按钮 -->
            <button class="pay-button" onclick="processPayment()" id="payButton">
                <span id="payButtonText">立即支付 ¥100.00</span>
                <i class="fas fa-spinner fa-spin" id="paySpinner" style="display: none;"></i>
            </button>
        </div>
    </div>

    <!-- 支付结果弹窗 -->
    <div class="modal-overlay" id="paymentResultModal" style="display: none;">
        <div class="result-modal">
            <div class="result-content" id="resultContent">
                <!-- 支付成功内容 -->
                <div class="success-content" id="successContent" style="display: none;">
                    <div class="result-icon success">
                        <i class="fas fa-check"></i>
                    </div>
                    <h3 class="result-title">支付成功！</h3>
                    <p class="result-message">恭喜您成功购买课程</p>
                    <div class="result-details">
                        <p><strong id="successCourseTitle">课程名称</strong></p>
                        <p>支付金额：<span id="successAmount">¥100.00</span></p>
                        <p>订单号：<span id="orderNumber">202312010001</span></p>
                    </div>
                    <div class="result-actions">
                        <button class="btn-secondary" onclick="goToOrders()">查看订单</button>
                        <button class="btn-primary" onclick="goToStudy()">开始学习</button>
                    </div>
                </div>

                <!-- 支付失败内容 -->
                <div class="failure-content" id="failureContent" style="display: none;">
                    <div class="result-icon failure">
                        <i class="fas fa-times"></i>
                    </div>
                    <h3 class="result-title">支付失败</h3>
                    <p class="result-message" id="failureMessage">支付过程中出现问题</p>
                    <div class="result-actions">
                        <button class="btn-secondary" onclick="closeResultModal()">取消</button>
                        <button class="btn-primary" onclick="retryPayment()">重试支付</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .result-modal {
            background: white;
            border-radius: 16px;
            width: 320px;
            max-width: 90vw;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .result-content {
            padding: 40px 30px 30px;
            text-align: center;
        }

        .result-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 24px;
        }

        .result-icon.success {
            background: #10b981;
            color: white;
        }

        .result-icon.failure {
            background: #ef4444;
            color: white;
        }

        .result-title {
            font-size: 20px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }

        .result-message {
            color: #6b7280;
            margin-bottom: 20px;
        }

        .result-details {
            background: #f9fafb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            text-align: left;
        }

        .result-details p {
            margin: 8px 0;
            color: #374151;
            font-size: 14px;
        }

        .result-actions {
            display: flex;
            gap: 12px;
        }

        .btn-primary, .btn-secondary {
            flex: 1;
            padding: 12px 16px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .pay-button:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
        }

        .pay-button:disabled:hover {
            background-color: #9ca3af;
        }
    </style>

    <script src="data/navigation-manager.js"></script>
    <script src="data/component-templates-with-styles.js"></script>
    <script>
        // 支付管理器
        class PaymentManager {
            constructor() {
                this.selectedPaymentMethod = 'alipay';
                this.courseInfo = {};
                this.isProcessing = false;
                this.init();
            }

            init() {
                this.loadCourseInfo();
                this.initPaymentMethods();
            }

            // 加载课程信息
            loadCourseInfo() {
                const urlParams = new URLSearchParams(window.location.search);
                const title = urlParams.get('title');
                const price = urlParams.get('price');

                if (title && price) {
                    this.courseInfo = {
                        title: title,
                        price: price
                    };

                    // 更新页面显示
                    this.updatePageContent();
                } else {
                    // 默认信息
                    this.courseInfo = {
                        title: '课程名称',
                        price: '¥100'
                    };
                }
            }

            // 更新页面内容
            updatePageContent() {
                const { title, price } = this.courseInfo;

                // 更新课程标题
                const courseTitleEl = document.getElementById('courseTitle');
                const courseSubtitleEl = document.getElementById('courseSubtitle');
                if (courseTitleEl) courseTitleEl.textContent = title;
                if (courseSubtitleEl) courseSubtitleEl.textContent = title;

                // 更新价格信息
                const priceText = price.includes('¥') ? price + '.00' : '¥' + price + '.00';
                const originalPriceEl = document.getElementById('originalPrice');
                const finalPriceEl = document.getElementById('finalPrice');
                const payButtonTextEl = document.getElementById('payButtonText');

                if (originalPriceEl) originalPriceEl.textContent = priceText;
                if (finalPriceEl) finalPriceEl.textContent = priceText;
                if (payButtonTextEl) payButtonTextEl.textContent = '立即支付 ' + priceText;
            }

            // 初始化支付方式
            initPaymentMethods() {
                // 默认选择支付宝
                this.selectPaymentMethod('alipay');
            }

            // 选择支付方式
            selectPaymentMethod(method) {
                this.selectedPaymentMethod = method;

                // 更新UI显示
                document.querySelectorAll('.payment-method').forEach(el => {
                    el.classList.remove('selected');
                    const radio = el.querySelector('.w-5.h-5');
                    if (radio) {
                        radio.className = 'w-5 h-5 border-2 border-gray-300 rounded-full';
                        radio.innerHTML = '';
                    }
                });

                // 添加选中状态
                const selectedEl = document.querySelector(`[onclick="selectPayment('${method}')"]`);
                if (selectedEl) {
                    selectedEl.classList.add('selected');
                    const radio = selectedEl.querySelector('.w-5.h-5');
                    if (radio) {
                        radio.className = 'w-5 h-5 border-2 border-blue-500 rounded-full bg-blue-500 flex items-center justify-center';
                        radio.innerHTML = '<i class="fas fa-check text-white text-xs"></i>';
                    }
                }
            }

            // 设置支付按钮状态
            setPaymentLoading(loading) {
                this.isProcessing = loading;
                const payButton = document.getElementById('payButton');
                const payButtonText = document.getElementById('payButtonText');
                const paySpinner = document.getElementById('paySpinner');

                if (loading) {
                    payButton.disabled = true;
                    payButtonText.style.display = 'none';
                    paySpinner.style.display = 'inline-block';
                } else {
                    payButton.disabled = false;
                    payButtonText.style.display = 'inline';
                    paySpinner.style.display = 'none';
                }
            }

            // 处理支付
            async processPayment() {
                if (this.isProcessing) return;

                this.setPaymentLoading(true);

                try {
                    // 模拟支付处理
                    const result = await this.simulatePayment();

                    if (result.success) {
                        this.showPaymentResult(true, result);
                        // 保存购买记录
                        this.savePurchaseRecord(result);
                    } else {
                        this.showPaymentResult(false, result);
                    }
                } catch (error) {
                    this.showPaymentResult(false, {
                        message: error.message || '支付过程中出现错误'
                    });
                } finally {
                    this.setPaymentLoading(false);
                }
            }

            // 模拟支付
            async simulatePayment() {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        // 90% 成功率
                        const success = Math.random() > 0.1;

                        if (success) {
                            resolve({
                                success: true,
                                orderNumber: 'SM' + Date.now(),
                                amount: this.courseInfo.price,
                                courseTitle: this.courseInfo.title,
                                paymentMethod: this.selectedPaymentMethod,
                                timestamp: new Date().toISOString()
                            });
                        } else {
                            resolve({
                                success: false,
                                message: '支付失败，请检查支付方式或稍后重试'
                            });
                        }
                    }, 3000); // 模拟3秒支付处理时间
                });
            }

            // 显示支付结果
            showPaymentResult(success, result) {
                const modal = document.getElementById('paymentResultModal');
                const successContent = document.getElementById('successContent');
                const failureContent = document.getElementById('failureContent');

                // 隐藏所有内容
                successContent.style.display = 'none';
                failureContent.style.display = 'none';

                if (success) {
                    // 显示成功内容
                    document.getElementById('successCourseTitle').textContent = result.courseTitle;
                    document.getElementById('successAmount').textContent = result.amount;
                    document.getElementById('orderNumber').textContent = result.orderNumber;
                    successContent.style.display = 'block';
                } else {
                    // 显示失败内容
                    document.getElementById('failureMessage').textContent = result.message;
                    failureContent.style.display = 'block';
                }

                modal.style.display = 'flex';
            }

            // 保存购买记录
            savePurchaseRecord(result) {
                try {
                    const purchases = JSON.parse(localStorage.getItem('purchaseRecords') || '[]');
                    purchases.unshift({
                        id: result.orderNumber,
                        courseTitle: result.courseTitle,
                        amount: result.amount,
                        paymentMethod: result.paymentMethod,
                        status: 'completed',
                        purchaseDate: result.timestamp
                    });
                    localStorage.setItem('purchaseRecords', JSON.stringify(purchases));
                } catch (error) {
                    console.error('保存购买记录失败:', error);
                }
            }

            // 关闭结果弹窗
            closeResultModal() {
                document.getElementById('paymentResultModal').style.display = 'none';
            }

            // 重试支付
            retryPayment() {
                this.closeResultModal();
                setTimeout(() => {
                    this.processPayment();
                }, 500);
            }
        }

        // 全局支付管理器实例
        let paymentManager;

        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addTopNavigation();
            paymentManager = new PaymentManager();
        });

        // 添加顶部导航函数
        function addTopNavigation() {
            try {
                // 确保组件模板已加载
                if (typeof ComponentTemplates === 'undefined') {
                    console.error('ComponentTemplates 未加载，无法添加顶部导航');
                    return;
                }

                // 创建顶部导航HTML
                const topNavHtml = ComponentTemplates.createTopNavigation({
                    title: '确认支付'
                });

                // 添加到指定容器
                const container = document.getElementById('topNavContainer');
                if (container) {
                    container.innerHTML = topNavHtml;
                    console.log('顶部导航添加成功');
                } else {
                    console.error('找不到顶部导航容器');
                }
            } catch (error) {
                console.error('添加顶部导航失败:', error);
            }
        }

        // 全局函数
        function selectPayment(method) {
            if (paymentManager) {
                paymentManager.selectPaymentMethod(method);
            }
        }

        function processPayment() {
            if (paymentManager) {
                paymentManager.processPayment();
            }
        }

        function closeResultModal() {
            if (paymentManager) {
                paymentManager.closeResultModal();
            }
        }

        function retryPayment() {
            if (paymentManager) {
                paymentManager.retryPayment();
            }
        }

        function goToOrders() {
            if (paymentManager) {
                paymentManager.closeResultModal();
            }
            // 跳转到购买记录页面
            if (window.navigateTo) {
                navigateTo('07-购买记录页面.html');
            } else {
                window.location.href = '07-购买记录页面.html';
            }
        }

        function goToStudy() {
            if (paymentManager) {
                paymentManager.closeResultModal();
            }
            // 跳转到首页开始学习
            if (window.navigateTo) {
                navigateTo('01-首页.html');
            } else {
                window.location.href = '01-首页.html';
            }
        }



        function goBack() {
            // 使用导航管理器返回上一页
            if (window.navigateTo) {
                // 使用导航管理器返回首页
                navigateTo('01-首页.html');
            } else if (window.history && window.history.length > 1) {
                // 使用浏览器历史记录返回
                window.history.back();
            } else {
                // 回退到直接返回首页
                window.location.href = '01-首页.html';
            }
        }

        // showShareModal函数已经在ComponentTemplates.initShareFunctions()中定义
    </script>
</body>
</html> 