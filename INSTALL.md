# 水幕课程管理端 - 安装和使用指南

## 🎯 快速开始

### ✅ 环境已配置完成！
虚拟环境和依赖包已经安装完成，可以直接使用。

### 方法1：一键运行（推荐）
```bash
# Windows
双击 run.bat

# 或者命令行运行
run.bat
```

### 方法2：手动运行
```bash
# 激活虚拟环境
venv\Scripts\activate

# 运行程序
python src\main.py
```

## 🖥️ 系统要求
- ✅ Python 3.9+ (当前: 3.12.3)
- ✅ Windows 10/11
- ✅ 至少 2GB 可用内存
- 🔄 网络连接（用于连接数据库）

## 📦 已安装的依赖包
- ✅ **PyQt6 6.9.1**: 图形界面框架（最新版）
- ✅ **PyMySQL 1.1.1**: MySQL数据库连接
- ✅ **pandas 2.3.0**: 数据处理和分析
- ✅ **matplotlib 3.10.0**: 图表绘制
- ✅ **SQLAlchemy 2.0.36**: 数据库ORM
- ✅ **PyInstaller 6.11.1**: 打包工具

## 🔧 配置数据库
首次运行前，请编辑 `config.ini` 文件：

```ini
[database]
host = your-database-host
port = 3306
user = your-username
password = your-password
database = shuimu_course
```

## 🚀 运行程序
1. **配置数据库**: 编辑 `config.ini` 文件
2. **启动程序**: 双击 `run.bat` 或运行命令
3. **首次连接**: 程序会自动测试数据库连接

## 📦 打包成exe
```bash
# 激活虚拟环境
venv\Scripts\activate

# 打包程序
python build.py
```

## 🎨 界面功能
- 📊 **数据概览**: 实时显示用户、课程、订单统计
- 👥 **用户管理**: 查看和管理用户信息
- 📚 **课程管理**: 管理系列和视频内容
- 💰 **订单管理**: 查看订单和财务数据
- ⚙️ **系统设置**: 配置和系统管理

## ❓ 常见问题

### Q: 程序无法启动
A: 确保：
1. 双击 `run.bat` 启动
2. 或者手动激活虚拟环境后运行

### Q: 无法连接数据库
A: 检查 `config.ini` 中的数据库配置：
- 数据库服务器地址是否正确
- 用户名和密码是否正确
- 数据库是否允许远程连接

### Q: 界面显示异常
A: 所有依赖已正确安装，如有问题请重新运行：
```bash
venv\Scripts\pip install --upgrade PyQt6
```

## 📁 项目结构
```
shuimu-admin/
├── venv/                 # 虚拟环境（已创建）
├── src/                  # 源代码
├── config.ini           # 配置文件
├── requirements.txt     # 依赖列表
├── run.bat             # 启动脚本
├── setup.py            # 环境设置脚本
└── INSTALL.md          # 本文档
```

## 📞 技术支持
环境配置完成，所有依赖包版本兼容，可以正常使用！

如有问题，请检查：
1. ✅ Python版本: 3.12.3 (符合要求)
2. ✅ 虚拟环境: 已创建并激活
3. ✅ 依赖包: 全部安装完成
4. 🔄 数据库配置: 需要手动配置
