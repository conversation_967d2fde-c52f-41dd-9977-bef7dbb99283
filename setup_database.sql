-- 水幕课程管理端 - 数据库初始化脚本
-- 请在MySQL安装完成后运行此脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS shuimu_course 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE shuimu_course;

-- 创建用户 mike（如果不存在）
CREATE USER IF NOT EXISTS 'mike'@'localhost' IDENTIFIED BY 'dyj217';

-- 授予权限
GRANT ALL PRIVILEGES ON shuimu_course.* TO 'mike'@'localhost';

-- 刷新权限
FLUSH PRIVILEGES;

-- 创建基础表结构（示例）
-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 系列表
CREATE TABLE IF NOT EXISTS series (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) DEFAULT 0.00,
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 视频表
CREATE TABLE IF NOT EXISTS videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    series_id INT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    video_url VARCHAR(500),
    duration INT DEFAULT 0,
    order_index INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE
);

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    series_id INT,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (series_id) REFERENCES series(id) ON DELETE CASCADE
);

-- 插入测试数据
-- 测试用户
INSERT IGNORE INTO users (username, email, password_hash) VALUES 
('admin', '<EMAIL>', 'hashed_password_here'),
('test_user', '<EMAIL>', 'hashed_password_here');

-- 测试系列
INSERT IGNORE INTO series (title, description, price, is_published) VALUES 
('Java基础教程', 'Java编程入门到精通', 99.00, TRUE),
('Python数据分析', 'Python数据分析实战课程', 129.00, TRUE),
('前端开发实战', 'HTML/CSS/JavaScript全栈开发', 159.00, FALSE);

-- 测试视频
INSERT IGNORE INTO videos (series_id, title, description, duration, order_index) VALUES 
(1, 'Java环境搭建', 'JDK安装和IDE配置', 1800, 1),
(1, 'Java基础语法', '变量、数据类型、运算符', 2400, 2),
(2, 'Python环境配置', 'Python安装和Jupyter配置', 1500, 1),
(2, 'Pandas基础', '数据读取和基本操作', 3000, 2);

-- 测试订单
INSERT IGNORE INTO orders (user_id, series_id, amount, status) VALUES 
(1, 1, 99.00, 'completed'),
(2, 1, 99.00, 'pending'),
(2, 2, 129.00, 'completed');

-- 显示创建结果
SELECT 'Database setup completed!' as message;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as series_count FROM series;
SELECT COUNT(*) as video_count FROM videos;
SELECT COUNT(*) as order_count FROM orders;
