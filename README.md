# 水幕课程管理端

## 项目简介
水幕视频课程App的本地管理工具，使用Python + PyQt6开发，可以方便地在本地电脑上管理课程数据和用户信息。

## 功能特性
- 📊 数据概览：实时显示用户数量、课程数量、订单统计
- 👥 用户管理：查看用户列表、用户详情、观看记录、购买记录
- 📚 课程管理：管理系列和视频、编辑课程信息、发布控制
- 💰 订单管理：查看订单列表、订单详情、财务统计
- ⚙️ 系统工具：数据库配置、数据导出、日志查看

## 技术栈
- **界面框架**: PyQt6
- **数据库**: PyMySQL + SQLAlchemy
- **数据处理**: pandas + numpy
- **图表**: matplotlib
- **打包**: PyInstaller

## 项目结构
```
shuimu-admin/
├── src/                    # 源代码目录
│   ├── main.py            # 主程序入口
│   ├── ui/                # 界面模块
│   │   ├── __init__.py
│   │   ├── main_window.py # 主窗口
│   │   ├── user_window.py # 用户管理窗口
│   │   ├── course_window.py # 课程管理窗口
│   │   └── order_window.py # 订单管理窗口
│   ├── database/          # 数据库模块
│   │   ├── __init__.py
│   │   ├── connection.py  # 数据库连接
│   │   └── models.py      # 数据模型
│   ├── utils/             # 工具模块
│   │   ├── __init__.py
│   │   ├── config.py      # 配置管理
│   │   └── helpers.py     # 辅助函数
│   └── resources/         # 资源文件
│       ├── icons/         # 图标文件
│       └── styles/        # 样式文件
├── requirements.txt       # 依赖包列表
├── config.ini            # 配置文件
├── build.py              # 打包脚本
└── README.md             # 项目说明
```

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库
编辑 `config.ini` 文件，配置数据库连接信息：
```ini
[database]
host = your-server.com
port = 3306
user = your-username
password = your-password
database = shuimu_course
```

### 3. 运行程序
```bash
python src/main.py
```

### 4. 打包成exe
```bash
python build.py
```

## 开发计划
- [x] 项目结构创建
- [ ] 数据库连接模块
- [ ] 主界面开发
- [ ] 用户管理功能
- [ ] 课程管理功能
- [ ] 订单管理功能
- [ ] 数据导出功能
- [ ] 打包和分发

## 注意事项
- 确保数据库服务器允许远程连接
- 建议使用VPN或安全连接访问生产数据库
- 定期备份重要数据

## 版本历史
- v1.0.0 (计划中) - 基础功能实现
