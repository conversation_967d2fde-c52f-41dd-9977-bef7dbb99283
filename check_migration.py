#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库迁移状态
"""

import pymysql

def check_migration():
    """检查迁移状态"""
    try:
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='shuimu_course',
            charset='utf8mb4'
        )
        cursor = connection.cursor()
        
        print("检查数据库表结构...")
        
        # 检查categories表
        cursor.execute("SHOW TABLES LIKE 'categories'")
        if cursor.fetchone():
            print("✅ categories表存在")
        else:
            print("❌ categories表不存在")
            
        # 检查videos表的category_id字段
        cursor.execute("SHOW COLUMNS FROM videos LIKE 'category_id'")
        if cursor.fetchone():
            print("✅ videos表有category_id字段")
        else:
            print("❌ videos表没有category_id字段")
            
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM categories")
        category_count = cursor.fetchone()[0]
        print(f"分类数量: {category_count}")
        
        cursor.execute("SELECT COUNT(*) FROM videos WHERE category_id IS NOT NULL")
        video_with_category = cursor.fetchone()[0]
        print(f"有分类关联的视频数量: {video_with_category}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_migration()
