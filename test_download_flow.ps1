# 测试下载流程脚本
Write-Host "=== 测试视频下载流程 ===" -ForegroundColor Green

Write-Host "`n1. 检查服务器状态..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "https://api.shuimu.us.kg/api/cache/config" -Method GET
    Write-Host "✅ 服务器运行正常" -ForegroundColor Green
    Write-Host "配置: $($response | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ 服务器连接失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n2. 检查关键代码修改..." -ForegroundColor Yellow

# 检查 CourseApplication 是否正确配置
$applicationCode = Get-Content "app/src/main/java/com/shuimu/course/CourseApplication.kt" -Raw
if ($applicationCode -match "HiltWorkerFactory" -and $applicationCode -match "Configuration.Provider") {
    Write-Host "✅ CourseApplication 已正确配置 HiltWorkerFactory" -ForegroundColor Green
} else {
    Write-Host "❌ CourseApplication 配置不正确" -ForegroundColor Red
}

# 检查 VideoDownloadWorker 是否使用 @HiltWorker
$workerCode = Get-Content "app/src/main/java/com/shuimu/course/data/workers/VideoDownloadWorker.kt" -Raw
if ($workerCode -match "@HiltWorker" -and $workerCode -match "@AssistedInject") {
    Write-Host "✅ VideoDownloadWorker 已正确配置 Hilt 注入" -ForegroundColor Green
} else {
    Write-Host "❌ VideoDownloadWorker Hilt 配置不正确" -ForegroundColor Red
}

Write-Host "`n3. 检查依赖配置..." -ForegroundColor Yellow
$buildGradle = Get-Content "app/build.gradle.kts" -Raw
if ($buildGradle -match "androidx.hilt:hilt-work" -and $buildGradle -match "androidx.work:work-runtime-ktx") {
    Write-Host "✅ WorkManager 和 Hilt-Work 依赖已正确配置" -ForegroundColor Green
} else {
    Write-Host "❌ WorkManager 或 Hilt-Work 依赖配置不正确" -ForegroundColor Red
}

Write-Host "`n=== 测试指南 ===" -ForegroundColor Green
Write-Host "现在请启动应用并按以下步骤测试:" -ForegroundColor White
Write-Host "1. 打开应用，等待数据加载完成" -ForegroundColor Cyan
Write-Host "2. 点击任意视频的'缓存'按钮" -ForegroundColor Cyan
Write-Host "3. 确认缓存对话框" -ForegroundColor Cyan
Write-Host "4. 观察日志标签: CourseApplication, HomeViewModel, DownloadManager, VideoDownloadWorker" -ForegroundColor Cyan

Write-Host "`n=== 常见问题排查 ===" -ForegroundColor Green
Write-Host "如果仍然没有反应，请检查:" -ForegroundColor White
Write-Host "• Logcat 中是否有 HiltWorkerFactory not set 错误" -ForegroundColor Cyan
Write-Host "• Logcat 中是否有 Could not instantiate Worker 错误" -ForegroundColor Cyan
Write-Host "• WorkManager Inspector 中任务状态是否为 FAILED" -ForegroundColor Cyan

Write-Host "`n测试脚本执行完成!" -ForegroundColor Green 