# Claude CLI 使用指南 🎉

## ✅ 安装成功！

恭喜！你已经成功安装了官方的 **Claude Code** CLI工具！

### 🔧 已解决的问题

1. ✅ **WSL网络问题** - 修复了DNS解析，切换到阿里云镜像源
2. ✅ **Node.js环境** - 成功安装了Node.js 18.19.1和npm 9.2.0
3. ✅ **Claude Code安装** - 安装了官方的@anthropic-ai/claude-code v1.0.35
4. ✅ **全局可用性** - 创建了claude.bat，可在任何目录使用

## 🚀 使用方法

### 基本命令

```bash
# 查看版本
claude.bat --version

# 查看帮助
claude.bat --help

# 交互模式（直接对话）
claude.bat

# 非交互模式（获取回复后退出）
claude.bat --print "你的问题"

# 管道输入
echo "Hello Claude" | claude.bat --print
```

### 实际示例

```bash
# 翻译
echo "请翻译：Hello World" | claude.bat --print

# 代码解释
echo "解释这段代码：console.log('Hello')" | claude.bat --print

# 数学计算
echo "计算：2+2*3" | claude.bat --print

# 文本处理
echo "总结一下：人工智能正在改变世界..." | claude.bat --print
```

### 高级功能

```bash
# 指定模型
claude.bat --model sonnet --print "你的问题"

# 继续上次对话
claude.bat --continue

# 恢复特定对话
claude.bat --resume

# 调试模式
claude.bat --debug --print "你的问题"
```

## 🌟 特性

- ✅ **真正的Claude AI** - 连接到Anthropic官方Claude服务
- ✅ **支持中文** - 完美支持中文输入输出
- ✅ **管道操作** - 支持 `echo "text" | claude.bat --print`
- ✅ **交互模式** - 可以进行多轮对话
- ✅ **代码理解** - 自动识别你的项目环境
- ✅ **全局可用** - 在任何目录都能使用

## 🔐 认证说明

Claude Code使用你的Claude Pro订阅进行认证，无需额外的API密钥。
如果遇到认证问题，请确保：

1. 你有有效的Claude Pro订阅
2. 在浏览器中登录了Claude
3. 使用 `claude.bat config` 检查配置

## 🛠️ 故障排除

### 如果claude命令不工作：

1. **检查WSL状态**：
   ```bash
   wsl --list --verbose
   ```

2. **检查Node.js**：
   ```bash
   wsl -e bash -c "node --version"
   ```

3. **重新安装Claude Code**：
   ```bash
   wsl -e bash -c "sudo npm install -g @anthropic-ai/claude-code"
   ```

### 如果网络有问题：

```bash
# 测试网络连接
wsl -e bash -c "ping -c 3 api.anthropic.com"

# 检查DNS设置
wsl -e bash -c "cat /etc/resolv.conf"
```

## 📝 配置选项

```bash
# 查看配置
claude.bat config

# 设置主题
claude.bat config set theme dark

# 设置默认模型
claude.bat config set model sonnet
```

## 🎯 最佳实践

1. **交互模式**：适合复杂讨论和多轮对话
2. **--print模式**：适合脚本化和自动化任务
3. **管道操作**：适合文本处理和批量操作

## 🔗 相关文件

- `claude.bat` - Windows批处理文件，提供全局claude命令
- 官方文档：https://docs.anthropic.com/claude/docs/claude-code

---

**享受使用Claude Code的乐趣！** 🎉 