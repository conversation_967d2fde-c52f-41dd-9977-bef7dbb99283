#!/usr/bin/env python3
"""
简化版本的服务器测试
"""
import sys
import os
sys.path.insert(0, os.getcwd())

from fastapi import FastAPI, Header
from typing import Optional, List
import json
from pathlib import Path

app = FastAPI()

# 简化版本的get_user_purchases
def get_user_purchases_simple(user_id: str) -> List[str]:
    """简化版本的获取用户购买记录"""
    data_dir = Path(__file__).resolve().parent / "src" / "data" / "user_data"
    user_file = data_dir / f"{user_id}.json"
    
    if not user_file.exists():
        return []
    
    with open(user_file, "r", encoding="utf-8") as f:
        data = json.load(f)
    
    purchases = data.get("purchases", {})
    all_purchases = []
    
    # 处理series购买记录
    for item in purchases.get("series", []):
        if isinstance(item, str):
            all_purchases.append(item)
        elif isinstance(item, dict) and "id" in item:
            all_purchases.append(item["id"])
    
    # 处理categories购买记录
    for item in purchases.get("categories", []):
        if isinstance(item, str):
            all_purchases.append(item)
        elif isinstance(item, dict) and "id" in item:
            all_purchases.append(item["id"])
    
    return all_purchases

@app.get("/api/test-purchases")
def test_purchases(x_user_id: Optional[str] = Header("user_001")):
    """测试购买记录获取"""
    try:
        purchases = get_user_purchases_simple(x_user_id)
        return {
            "success": True,
            "user_id": x_user_id,
            "purchases": purchases,
            "count": len(purchases)
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.get("/api/test-series-simple")
def test_series_simple(x_user_id: Optional[str] = Header("user_001")):
    """简化版本的series测试"""
    try:
        # 加载数据
        data_dir = Path(__file__).resolve().parent / "src" / "data"
        
        with open(data_dir / "series.json", "r", encoding="utf-8") as f:
            series_db = json.load(f)
        
        purchases = get_user_purchases_simple(x_user_id)
        
        result = []
        for series in series_db:
            result.append({
                "id": series["id"],
                "title": series["title"],
                "isPurchased": series["id"] in purchases,
                "isFree": series.get("isFree", False),
                "price": series.get("price", 0)
            })
        
        return {
            "success": True,
            "user_purchases": purchases,
            "series": result
        }
    
    except Exception as e:
        import traceback
        return {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8003) 