#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API配置文件 - 统一管理API相关配置
"""

import os
from typing import Dict, Any

class APIConfig:
    """API配置类"""
    
    def __init__(self):
        # 服务端配置
        self.host = os.getenv('API_HOST', '0.0.0.0')
        self.port = int(os.getenv('API_PORT', 8000))  # 默认端口改为8000
        self.base_url = os.getenv('API_BASE_URL', f'http://localhost:{self.port}')
        
        # API版本
        self.api_version = os.getenv('API_VERSION', 'v1')
        self.api_prefix = f'/api/{self.api_version}' if self.api_version != 'v1' else '/api'
        
        # 跨域配置
        self.cors_origins = os.getenv('CORS_ORIGINS', '*').split(',')
        self.cors_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        self.cors_headers = ["*"]
        
        # 认证配置
        self.auth_header_user_id = "X-User-Id"
        self.auth_header_is_admin = "X-Is-Admin"
        self.default_admin_user_id = "admin_001"
        
        # 分页配置
        self.default_page_size = 20
        self.max_page_size = 100
        
        # 文件上传配置
        self.max_file_size = int(os.getenv('MAX_FILE_SIZE', 50 * 1024 * 1024))  # 50MB
        self.allowed_file_types = ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'avi', 'mov']
        
        # 缓存配置
        self.cache_ttl = int(os.getenv('CACHE_TTL', 3600))  # 1小时
        
        # 数据库配置
        self.db_host = os.getenv('DB_HOST', 'localhost')
        self.db_port = int(os.getenv('DB_PORT', 3306))
        self.db_user = os.getenv('DB_USER', 'shuimu_server')
        self.db_password = os.getenv('DB_PASSWORD', 'dyj217')
        self.db_name = os.getenv('DB_NAME', 'shuimu_course_server')
        
    def get_full_url(self, endpoint: str) -> str:
        """获取完整的API URL"""
        if endpoint.startswith('/'):
            endpoint = endpoint[1:]
        return f"{self.base_url}/{endpoint}"
    
    def get_api_url(self, endpoint: str) -> str:
        """获取API端点的完整URL"""
        if endpoint.startswith('/api'):
            return f"{self.base_url}{endpoint}"
        elif endpoint.startswith('/'):
            return f"{self.base_url}{self.api_prefix}{endpoint}"
        else:
            return f"{self.base_url}{self.api_prefix}/{endpoint}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'host': self.host,
            'port': self.port,
            'base_url': self.base_url,
            'api_version': self.api_version,
            'api_prefix': self.api_prefix,
            'cors_origins': self.cors_origins,
            'default_page_size': self.default_page_size,
            'max_page_size': self.max_page_size,
            'max_file_size': self.max_file_size,
            'cache_ttl': self.cache_ttl
        }

# 全局配置实例
api_config = APIConfig()

# 常用的API端点常量
class APIEndpoints:
    """API端点常量"""
    
    # 用户相关
    USERS = "/users"
    USER_BY_ID = "/users/{user_id}"
    USER_PROFILE = "/users/{user_id}/profile"
    USER_PROGRESS = "/users/{user_id}/progress"
    USER_PROGRESS_VIDEO = "/users/{user_id}/progress/{video_id}"
    USER_CACHE = "/users/{user_id}/cache"
    USER_CACHE_VIDEO = "/users/{user_id}/cache/{video_id}"
    USER_FAVORITES = "/users/{user_id}/favorites"
    
    # 管理端用户
    ADMIN_USERS = "/admin/users"
    ADMIN_USER_BY_ID = "/admin/users/{user_id}"
    
    # 系列相关
    SERIES = "/series"
    SERIES_BY_ID = "/series/{series_id}"
    SERIES_CATEGORIES = "/series/{series_id}/categories"
    
    # 管理端系列
    ADMIN_SERIES = "/admin/series"
    ADMIN_SERIES_BY_ID = "/admin/series/{series_id}"
    
    # 分类相关
    CATEGORIES = "/categories"
    CATEGORY_BY_ID = "/categories/{category_id}"
    CATEGORY_VIDEOS = "/categories/{category_id}/videos"
    
    # 管理端分类
    ADMIN_CATEGORIES = "/admin/categories"
    ADMIN_CATEGORY_BY_ID = "/admin/categories/{category_id}"
    
    # 视频相关
    VIDEOS = "/videos"
    VIDEO_BY_ID = "/videos/{video_id}"
    
    # 管理端视频
    ADMIN_VIDEOS = "/admin/videos"
    ADMIN_VIDEO_BY_ID = "/admin/videos/{video_id}"
    
    # 认证相关
    AUTH_LOGIN = "/auth/login"
    AUTH_LOGOUT = "/auth/logout"
    AUTH_REGISTER = "/auth/register"
    AUTH_REFRESH = "/auth/refresh"
    
    # 其他
    SEARCH = "/search"
    ANALYTICS = "/analytics"
    HEALTH = "/health"

# 响应状态码常量
class ResponseCodes:
    """响应状态码常量"""
    SUCCESS = 200
    CREATED = 201
    NO_CONTENT = 204
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    CONFLICT = 409
    INTERNAL_ERROR = 500

# 响应消息常量
class ResponseMessages:
    """响应消息常量"""
    SUCCESS = "操作成功"
    CREATED = "创建成功"
    UPDATED = "更新成功"
    DELETED = "删除成功"
    NOT_FOUND = "资源不存在"
    UNAUTHORIZED = "未授权访问"
    FORBIDDEN = "权限不足"
    BAD_REQUEST = "请求参数错误"
    INTERNAL_ERROR = "服务器内部错误"
    
    # 用户相关
    USER_NOT_FOUND = "用户不存在"
    USER_CREATED = "用户创建成功"
    USER_UPDATED = "用户信息更新成功"
    USER_DELETED = "用户删除成功"
    USER_PROFILE_UPDATED = "用户资料更新成功"
    
    # 进度相关
    PROGRESS_UPDATED = "观看进度更新成功"
    PROGRESS_DELETED = "进度记录删除成功"
    
    # 缓存相关
    CACHE_UPDATED = "缓存状态更新成功"
    CACHE_DELETED = "缓存记录删除成功"
    
    # 收藏相关
    FAVORITES_UPDATED = "收藏更新成功"
