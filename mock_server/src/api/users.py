from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any
from ..database.mysql_manager import mysql_manager

# Mock dependency for getting current user from a token
# In a real app, this would involve token validation
def get_current_user_id():
    return "user001" # For mocking, always return the same user

router = APIRouter()

@router.get("/user/profile")
def get_user_profile(user_id: str = Depends(get_current_user_id)):
    """
    Returns the profile for the current user.
    """
    try:
        users = mysql_manager.execute_query("""
            SELECT * FROM users WHERE id = %s
        """, (user_id,))

        if not users:
            raise HTTPException(status_code=404, detail="User not found")

        return users[0]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get user profile: {str(e)}")

@router.get("/user/purchases")
def get_user_purchases(user_id: str = Depends(get_current_user_id)) -> Dict[str, Any]:
    """
    Returns the purchase history for the current user.
    """
    user_data = load_user_data(user_id)
    purchases = user_data.get("purchases", {})
    
    # 转换为旧格式以保持兼容性
    purchase_history = []
    
    # 添加series购买记录
    for series_id in purchases.get("series", []):
        purchase_history.append({
            "userId": user_id,
            "itemId": series_id,
            "itemType": "series",
            "purchaseDate": "2024-01-01T00:00:00Z"  # 默认日期
        })
    
    # 添加categories购买记录
    for category_id in purchases.get("categories", []):
        purchase_history.append({
            "userId": user_id,
            "itemId": category_id,
            "itemType": "category",
            "purchaseDate": "2024-01-01T00:00:00Z"  # 默认日期
        })
    
    return {"purchases": purchase_history}

@router.get("/user/data")
def get_user_data(user_id: str = Depends(get_current_user_id)) -> Dict[str, Any]:
    """
    Returns all user data including purchases, progress, and cache.
    """
    return load_user_data(user_id)
