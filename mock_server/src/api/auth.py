from fastapi import APIRouter, HTTPException
from typing import Dict
from ..models.user import UserLogin
from ..database.mysql_manager import mysql_manager

router = APIRouter()

@router.post("/auth/login", response_model=Dict[str, str])
def login(user_credentials: UserLogin):
    """
    Authenticates a user and returns a token.
    """
    try:
        # 从MySQL数据库查询用户
        users = mysql_manager.execute_query("""
            SELECT username, password, token FROM users
            WHERE username = %s AND password = %s
        """, (user_credentials.username, user_credentials.password))

        if users:
            user = users[0]
            return {"token": user["token"], "message": "Login successful"}
        else:
            raise HTTPException(status_code=401, detail="Invalid username or password")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Authentication failed: {str(e)}")
