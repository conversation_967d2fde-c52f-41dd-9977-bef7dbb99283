"""
管理端视频API接口
提供视频的CRUD操作 - 使用MySQL数据库
"""

import uuid
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from ..database.mysql_manager import mysql_manager

router = APIRouter()

# 数据模型
class VideoCreate(BaseModel):
    id: Optional[str] = None
    title: str
    description: Optional[str] = ""
    category_id: str
    video_url: Optional[str] = ""
    duration: Optional[int] = 0
    order_index: Optional[int] = 1
    is_free: Optional[bool] = False

class VideoUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    video_url: Optional[str] = None
    duration: Optional[int] = None
    order_index: Optional[int] = None
    is_free: Optional[bool] = None

@router.get("/admin/videos")
def get_videos_admin(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    category_id: Optional[str] = Query(None)
):
    """获取视频列表（管理端）"""
    try:
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []

        if search:
            where_conditions.append("(v.title LIKE %s OR v.description LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        if category_id:
            where_conditions.append("v.category_id = %s")
            params.append(category_id)

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        count_sql = f"""
            SELECT COUNT(*) as total 
            FROM videos v 
            WHERE {where_clause}
        """
        total_result = mysql_manager.execute_query(count_sql, params)
        total = total_result[0]['total'] if total_result else 0

        # 获取数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT v.*, c.title as category_title, s.title as series_title
            FROM videos v
            LEFT JOIN categories c ON v.category_id = c.id
            LEFT JOIN series s ON c.series_id = s.id
            WHERE {where_clause}
            ORDER BY v.created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        videos = mysql_manager.execute_query(data_sql, params)

        return {
            "success": True,
            "data": videos,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": (total + page_size - 1) // page_size
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频列表失败: {str(e)}")

@router.post("/admin/videos")
def create_video_admin(video: VideoCreate):
    """创建视频（管理端）"""
    try:
        # 使用客户端提供的ID，如果没有则生成新ID
        new_id = video.id if video.id else str(uuid.uuid4())

        # 执行插入SQL
        affected_rows = mysql_manager.execute_update("""
            INSERT INTO videos (id, title, description, category_id, video_url, duration, order_index, is_free, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_id,
            video.title,
            video.description,
            video.category_id,
            video.video_url,
            video.duration,
            video.order_index,
            video.is_free
        ))

        if affected_rows > 0:
            # 获取创建的视频
            created_video = mysql_manager.execute_query("""
                SELECT v.*, c.title as category_title, s.title as series_title
                FROM videos v
                LEFT JOIN categories c ON v.category_id = c.id
                LEFT JOIN series s ON c.series_id = s.id
                WHERE v.id = %s
            """, (new_id,))

            return {
                "success": True,
                "message": "视频创建成功",
                "data": created_video[0] if created_video else None
            }
        else:
            raise HTTPException(status_code=500, detail="创建视频失败")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建视频失败: {str(e)}")

@router.put("/admin/videos/{video_id}")
def update_video_admin(video_id: str, video: VideoUpdate):
    """更新视频（管理端）"""
    try:
        # 检查视频是否存在
        existing_video = mysql_manager.execute_query("""
            SELECT * FROM videos WHERE id = %s
        """, (video_id,))

        if not existing_video:
            raise HTTPException(status_code=404, detail="视频不存在")

        # 构建更新SQL
        update_fields = []
        params = []
        
        update_data = video.dict(exclude_unset=True)
        for field, value in update_data.items():
            update_fields.append(f"{field} = %s")
            params.append(value)

        if update_fields:
            update_fields.append("updated_at = NOW()")
            params.append(video_id)
            
            sql = f"""
                UPDATE videos 
                SET {', '.join(update_fields)}
                WHERE id = %s
            """
            
            affected_rows = mysql_manager.execute_update(sql, params)
            
            if affected_rows > 0:
                # 获取更新后的视频
                updated_video = mysql_manager.execute_query("""
                    SELECT v.*, c.title as category_title, s.title as series_title
                    FROM videos v
                    LEFT JOIN categories c ON v.category_id = c.id
                    LEFT JOIN series s ON c.series_id = s.id
                    WHERE v.id = %s
                """, (video_id,))

                return {
                    "success": True,
                    "message": "视频更新成功",
                    "data": updated_video[0] if updated_video else None
                }
            else:
                raise HTTPException(status_code=500, detail="更新视频失败")
        else:
            return {
                "success": True,
                "message": "没有需要更新的字段",
                "data": existing_video[0]
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新视频失败: {str(e)}")

@router.delete("/admin/videos/{video_id}")
def delete_video_admin(video_id: str):
    """删除视频（管理端）"""
    try:
        # 检查视频是否存在
        existing_video = mysql_manager.execute_query("""
            SELECT * FROM videos WHERE id = %s
        """, (video_id,))

        if not existing_video:
            raise HTTPException(status_code=404, detail="视频不存在")

        # 删除视频
        affected_rows = mysql_manager.execute_update("""
            DELETE FROM videos WHERE id = %s
        """, (video_id,))

        if affected_rows > 0:
            return {
                "success": True,
                "message": "视频删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="删除视频失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除视频失败: {str(e)}")

@router.get("/admin/videos/{video_id}")
def get_video_admin(video_id: str):
    """获取单个视频详情（管理端）"""
    try:
        video = mysql_manager.execute_query("""
            SELECT v.*, c.title as category_title, s.title as series_title
            FROM videos v
            LEFT JOIN categories c ON v.category_id = c.id
            LEFT JOIN series s ON c.series_id = s.id
            WHERE v.id = %s
        """, (video_id,))

        if not video:
            raise HTTPException(status_code=404, detail="视频不存在")

        return {
            "success": True,
            "data": video[0]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")
