"""
管理端视频API接口
提供视频的CRUD操作 - 使用MySQL数据库
"""

import uuid
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from ..database.mysql_manager import mysql_manager

router = APIRouter()

def format_duration(seconds):
    """格式化时长显示"""
    if not seconds:
        return "00:00"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes:02d}:{seconds:02d}"

# 数据模型
class VideoCreate(BaseModel):
    title: str
    categoryId: str
    description: Optional[str] = None
    cloudUrl: Optional[str] = None
    duration: int = 0
    order_index: int = 0

class VideoUpdate(BaseModel):
    title: Optional[str] = None
    categoryId: Optional[str] = None
    description: Optional[str] = None
    cloudUrl: Optional[str] = None
    duration: Optional[int] = None
    order_index: Optional[int] = None

# API接口
@router.get("/admin/videos", response_model=Dict[str, Any])
def get_videos_admin(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    category_id: Optional[str] = Query(None),
    series_id: Optional[str] = Query(None)
):
    """获取视频列表（管理端）"""
    try:
        videos_db = load_data("videos.json")
        categories_db = load_data("categories.json")
        series_db = load_data("series.json")
        
        # 创建映射
        category_map = {cat["id"]: cat for cat in categories_db}
        series_map = {series["id"]: series["title"] for series in series_db}
        category_to_series = {cat["id"]: cat.get("seriesId") for cat in categories_db}
        
        # 筛选和搜索
        filtered_videos = []
        for video in videos_db:
            video_category_id = video.get("categoryId")
            video_series_id = category_to_series.get(video_category_id)
            
            # 分类筛选
            if category_id and video_category_id != category_id:
                continue
            
            # 系列筛选
            if series_id and video_series_id != series_id:
                continue
            
            # 搜索筛选
            if search:
                search_lower = search.lower()
                if (search_lower not in video.get("title", "").lower() and
                    search_lower not in video.get("description", "").lower()):
                    continue
            
            # 获取分类和系列信息
            category_info = category_map.get(video_category_id, {})
            
            # 添加额外信息
            video_with_info = {
                "id": video.get("id"),
                "category_id": video_category_id,
                "category_title": category_info.get("title", ""),
                "category_price": float(category_info.get("price", 0)),
                "series_id": video_series_id,
                "series_title": series_map.get(video_series_id, ""),
                "title": video.get("title"),
                "description": video.get("description", ""),
                "video_url": video.get("cloudUrl", ""),
                "duration": video.get("duration", 0),
                "duration_formatted": format_duration(video.get("duration", 0)),
                "order_index": 0,  # 服务端没有排序字段
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00"
            }
            
            filtered_videos.append(video_with_info)
        
        # 排序
        filtered_videos.sort(key=lambda x: (x.get("series_title", ""), x.get("category_title", ""), x.get("title", "")))
        
        # 分页
        total = len(filtered_videos)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        videos_page = filtered_videos[start_idx:end_idx]
        
        # 计算分页信息
        total_pages = (total + page_size - 1) // page_size
        
        return {
            "success": True,
            "data": videos_page,
            "pagination": {
                "current_page": page,
                "page_size": page_size,
                "total_records": total,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频列表失败: {str(e)}")

@router.get("/admin/videos/{video_id}", response_model=Dict[str, Any])
def get_video_admin(video_id: str):
    """获取视频详情（管理端）"""
    try:
        videos_db = load_data("videos.json")
        
        video = next((v for v in videos_db if v["id"] == video_id), None)
        if not video:
            return {
                "success": False,
                "message": "视频不存在",
                "data": None
            }
        
        return {
            "success": True,
            "data": video,
            "message": "获取视频详情成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

@router.post("/admin/videos", response_model=Dict[str, Any])
def create_video_admin(video: VideoCreate):
    """创建视频（管理端）"""
    try:
        videos_db = load_data("videos.json")
        categories_db = load_data("categories.json")
        
        # 生成新ID
        new_id = str(uuid.uuid4())
        
        # 创建视频数据
        new_video = {
            "id": new_id,
            "title": video.title,
            "description": video.description,
            "duration": video.duration,
            "categoryId": video.categoryId,
            "playCount": 0,
            "cloudUrl": video.cloudUrl,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        videos_db.append(new_video)
        save_data("videos.json", videos_db)
        
        # 更新分类的videoIds
        for category in categories_db:
            if category["id"] == video.categoryId:
                if "videoIds" not in category:
                    category["videoIds"] = []
                category["videoIds"].append(new_id)
                break
        
        save_data("categories.json", categories_db)
        
        return {
            "success": True,
            "message": "视频创建成功",
            "data": new_video
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建视频失败: {str(e)}")

@router.put("/admin/videos/{video_id}", response_model=Dict[str, Any])
def update_video_admin(video_id: str, video: VideoUpdate):
    """更新视频（管理端）"""
    try:
        videos_db = load_data("videos.json")
        categories_db = load_data("categories.json")
        
        # 查找视频
        video_index = next((i for i, v in enumerate(videos_db) if v["id"] == video_id), None)
        if video_index is None:
            return {
                "success": False,
                "message": "视频不存在",
                "data": None
            }
        
        # 更新视频数据
        existing_video = videos_db[video_index]
        old_category_id = existing_video.get("categoryId")
        update_data = video.dict(exclude_unset=True)
        
        for key, value in update_data.items():
            if key == "categoryId":
                existing_video["categoryId"] = value
            else:
                existing_video[key] = value
        
        existing_video["updated_at"] = datetime.now().isoformat()
        
        videos_db[video_index] = existing_video
        save_data("videos.json", videos_db)
        
        # 如果分类发生变化，更新分类的videoIds
        new_category_id = existing_video.get("categoryId")
        if old_category_id != new_category_id:
            # 从旧分类中移除
            for category in categories_db:
                if category["id"] == old_category_id:
                    if "videoIds" in category and video_id in category["videoIds"]:
                        category["videoIds"].remove(video_id)
                    break
            
            # 添加到新分类
            for category in categories_db:
                if category["id"] == new_category_id:
                    if "videoIds" not in category:
                        category["videoIds"] = []
                    if video_id not in category["videoIds"]:
                        category["videoIds"].append(video_id)
                    break
            
            save_data("categories.json", categories_db)
        
        return {
            "success": True,
            "message": "视频更新成功",
            "data": existing_video
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新视频失败: {str(e)}")

@router.delete("/admin/videos/{video_id}", response_model=Dict[str, Any])
def delete_video_admin(video_id: str):
    """删除视频（管理端）"""
    try:
        videos_db = load_data("videos.json")
        categories_db = load_data("categories.json")
        
        # 查找视频
        video = next((v for v in videos_db if v["id"] == video_id), None)
        if not video:
            return {
                "success": False,
                "message": "视频不存在"
            }
        
        # 删除视频
        videos_db = [v for v in videos_db if v["id"] != video_id]
        save_data("videos.json", videos_db)
        
        # 从分类的videoIds中移除
        category_id = video.get("categoryId")
        for category in categories_db:
            if category["id"] == category_id:
                if "videoIds" in category and video_id in category["videoIds"]:
                    category["videoIds"].remove(video_id)
                break
        
        save_data("categories.json", categories_db)
        
        return {
            "success": True,
            "message": "视频删除成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除视频失败: {str(e)}")
