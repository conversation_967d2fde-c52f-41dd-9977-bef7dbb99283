"""
管理端系列API接口
提供系列的CRUD操作
"""

import json
import uuid
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from pathlib import Path

router = APIRouter()

# Base directory for JSON data
DATA_DIR = Path(__file__).resolve().parent.parent / "data"

def load_data(file_path):
    """加载JSON数据"""
    try:
        with open(DATA_DIR / file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_data(file_path, data):
    """保存JSON数据"""
    with open(DATA_DIR / file_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

# 数据模型
class SeriesCreate(BaseModel):
    id: Optional[str] = None  # 允许客户端提供ID
    title: str
    description: Optional[str] = None
    is_published: bool = False

class SeriesUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    is_published: Optional[bool] = None

# API接口
@router.get("/admin/series", response_model=Dict[str, Any])
def get_series_admin(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None)
):
    """获取系列列表（管理端）"""
    try:
        series_db = load_data("series.json")
        categories_db = load_data("categories.json")
        videos_db = load_data("videos.json")
        
        # 筛选和搜索
        filtered_series = []
        for series in series_db:
            # 跳过全套课程包
            if series.get("isPackage", False):
                continue
            
            # 搜索筛选
            if search:
                search_lower = search.lower()
                if search_lower not in series.get("title", "").lower():
                    continue
            
            # 计算分类数和视频数
            category_ids = series.get("categoryIds", [])
            category_count = len(category_ids)
            
            # 计算视频数
            video_count = 0
            for category_id in category_ids:
                category = next((cat for cat in categories_db if cat["id"] == category_id), None)
                if category:
                    video_count += len(category.get("videoIds", []))
            
            # 添加额外信息
            series_with_info = {
                "id": series.get("id"),
                "title": series.get("title"),
                "description": "",  # 服务端没有description字段
                "price": float(series.get("price", 0)),
                "is_published": not series.get("isFree", False),
                "category_count": category_count,
                "video_count": video_count,
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00"
            }
            
            filtered_series.append(series_with_info)
        
        # 排序
        filtered_series.sort(key=lambda x: x.get("title", ""))
        
        # 分页
        total = len(filtered_series)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        series_page = filtered_series[start_idx:end_idx]
        
        # 计算分页信息
        total_pages = (total + page_size - 1) // page_size
        
        return {
            "success": True,
            "data": series_page,
            "pagination": {
                "current_page": page,
                "page_size": page_size,
                "total_records": total,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系列列表失败: {str(e)}")

@router.get("/admin/series/{series_id}", response_model=Dict[str, Any])
def get_series_admin(series_id: str):
    """获取系列详情（管理端）"""
    try:
        series_db = load_data("series.json")
        
        series = next((s for s in series_db if s["id"] == series_id), None)
        if not series:
            return {
                "success": False,
                "message": "系列不存在",
                "data": None
            }
        
        return {
            "success": True,
            "data": series,
            "message": "获取系列详情成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系列详情失败: {str(e)}")

@router.post("/admin/series", response_model=Dict[str, Any])
def create_series_admin(series: SeriesCreate):
    """创建系列（管理端）"""
    try:
        print(f"🔍 admin_series: 收到请求")
        print(f"🔍 admin_series: 原始数据={series}")
        print(f"🔍 admin_series: 客户端ID={series.id}")
        print(f"🔍 admin_series: ID类型={type(series.id)}")

        series_db = load_data("series.json")

        # 使用客户端提供的ID，如果没有则生成新ID
        new_id = series.id if series.id else str(uuid.uuid4())
        print(f"🔍 admin_series: 最终ID={new_id}")
        
        # 创建系列数据
        new_series = {
            "id": new_id,
            "title": series.title,
            "price": 0,
            "isFree": not series.is_published,
            "isPackage": False,
            "defaultExpanded": False,
            "categoryIds": [],
            "icon": "play",
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        
        series_db.append(new_series)
        save_data("series.json", series_db)
        
        return {
            "success": True,
            "message": "系列创建成功",
            "data": new_series
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建系列失败: {str(e)}")

@router.put("/admin/series/{series_id}", response_model=Dict[str, Any])
def update_series_admin(series_id: str, series: SeriesUpdate):
    """更新系列（管理端）"""
    try:
        series_db = load_data("series.json")
        
        # 查找系列
        series_index = next((i for i, s in enumerate(series_db) if s["id"] == series_id), None)
        if series_index is None:
            return {
                "success": False,
                "message": "系列不存在",
                "data": None
            }
        
        # 更新系列数据
        existing_series = series_db[series_index]
        update_data = series.dict(exclude_unset=True)
        
        if "title" in update_data:
            existing_series["title"] = update_data["title"]
        if "is_published" in update_data:
            existing_series["isFree"] = not update_data["is_published"]
        
        existing_series["updated_at"] = datetime.now().isoformat()
        
        series_db[series_index] = existing_series
        save_data("series.json", series_db)
        
        return {
            "success": True,
            "message": "系列更新成功",
            "data": existing_series
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新系列失败: {str(e)}")

@router.delete("/admin/series/{series_id}", response_model=Dict[str, Any])
def delete_series_admin(series_id: str):
    """删除系列（管理端）"""
    try:
        series_db = load_data("series.json")
        categories_db = load_data("categories.json")
        
        # 查找系列
        series = next((s for s in series_db if s["id"] == series_id), None)
        if not series:
            return {
                "success": False,
                "message": "系列不存在"
            }
        
        # 检查是否有关联的分类
        category_ids = series.get("categoryIds", [])
        if category_ids:
            return {
                "success": False,
                "message": f"无法删除系列，还有 {len(category_ids)} 个关联分类"
            }
        
        # 删除系列
        series_db = [s for s in series_db if s["id"] != series_id]
        save_data("series.json", series_db)
        
        return {
            "success": True,
            "message": "系列删除成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除系列失败: {str(e)}")
