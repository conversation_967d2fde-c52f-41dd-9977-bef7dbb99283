"""
管理端用户API接口
提供用户的CRUD操作 - 使用MySQL数据库
"""

import uuid
from datetime import datetime
from fastapi import APIRouter, HTTPException, Query
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from ..database.mysql_manager import mysql_manager

router = APIRouter()

# 数据模型
class UserCreate(BaseModel):
    id: Optional[str] = None
    username: str
    email: str
    password: str
    phone: Optional[str] = ""
    avatar: Optional[str] = ""
    is_active: Optional[bool] = True

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    avatar: Optional[str] = None
    is_active: Optional[bool] = None

@router.get("/admin/users")
def get_users_admin(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None)
):
    """获取用户列表（管理端）"""
    try:
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []

        if search:
            where_conditions.append("(username LIKE %s OR email LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        if is_active is not None:
            where_conditions.append("is_active = %s")
            params.append(is_active)

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        count_sql = f"""
            SELECT COUNT(*) as total 
            FROM users 
            WHERE {where_clause}
        """
        total_result = mysql_manager.execute_query(count_sql, params)
        total = total_result[0]['total'] if total_result else 0

        # 获取数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT id, username, email, phone, avatar, is_active, created_at, updated_at
            FROM users
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        users = mysql_manager.execute_query(data_sql, params)

        return {
            "success": True,
            "data": users,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": (total + page_size - 1) // page_size
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

@router.post("/admin/users")
def create_user_admin(user: UserCreate):
    """创建用户（管理端）"""
    try:
        # 使用客户端提供的ID，如果没有则生成新ID
        new_id = user.id if user.id else str(uuid.uuid4())

        # 检查用户名和邮箱是否已存在
        existing_user = mysql_manager.execute_query("""
            SELECT id FROM users WHERE username = %s OR email = %s
        """, (user.username, user.email))

        if existing_user:
            raise HTTPException(status_code=400, detail="用户名或邮箱已存在")

        # 生成token
        token = str(uuid.uuid4())

        # 执行插入SQL
        affected_rows = mysql_manager.execute_update("""
            INSERT INTO users (id, username, email, password, phone, avatar, is_active, token, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_id,
            user.username,
            user.email,
            user.password,
            user.phone,
            user.avatar,
            user.is_active,
            token
        ))

        if affected_rows > 0:
            # 获取创建的用户
            created_user = mysql_manager.execute_query("""
                SELECT id, username, email, phone, avatar, is_active, created_at, updated_at
                FROM users WHERE id = %s
            """, (new_id,))

            return {
                "success": True,
                "message": "用户创建成功",
                "data": created_user[0] if created_user else None
            }
        else:
            raise HTTPException(status_code=500, detail="创建用户失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建用户失败: {str(e)}")

@router.put("/admin/users/{user_id}")
def update_user_admin(user_id: str, user: UserUpdate):
    """更新用户（管理端）"""
    try:
        # 检查用户是否存在
        existing_user = mysql_manager.execute_query("""
            SELECT * FROM users WHERE id = %s
        """, (user_id,))

        if not existing_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 构建更新SQL
        update_fields = []
        params = []
        
        update_data = user.dict(exclude_unset=True)
        for field, value in update_data.items():
            update_fields.append(f"{field} = %s")
            params.append(value)

        if update_fields:
            update_fields.append("updated_at = NOW()")
            params.append(user_id)
            
            sql = f"""
                UPDATE users 
                SET {', '.join(update_fields)}
                WHERE id = %s
            """
            
            affected_rows = mysql_manager.execute_update(sql, params)
            
            if affected_rows > 0:
                # 获取更新后的用户
                updated_user = mysql_manager.execute_query("""
                    SELECT id, username, email, phone, avatar, is_active, created_at, updated_at
                    FROM users WHERE id = %s
                """, (user_id,))

                return {
                    "success": True,
                    "message": "用户更新成功",
                    "data": updated_user[0] if updated_user else None
                }
            else:
                raise HTTPException(status_code=500, detail="更新用户失败")
        else:
            return {
                "success": True,
                "message": "没有需要更新的字段",
                "data": existing_user[0]
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新用户失败: {str(e)}")

@router.delete("/admin/users/{user_id}")
def delete_user_admin(user_id: str):
    """删除用户（管理端）"""
    try:
        # 检查用户是否存在
        existing_user = mysql_manager.execute_query("""
            SELECT * FROM users WHERE id = %s
        """, (user_id,))

        if not existing_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 删除用户
        affected_rows = mysql_manager.execute_update("""
            DELETE FROM users WHERE id = %s
        """, (user_id,))

        if affected_rows > 0:
            return {
                "success": True,
                "message": "用户删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="删除用户失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")

@router.get("/admin/users/{user_id}")
def get_user_admin(user_id: str):
    """获取单个用户详情（管理端）"""
    try:
        user = mysql_manager.execute_query("""
            SELECT id, username, email, phone, avatar, is_active, created_at, updated_at
            FROM users WHERE id = %s
        """, (user_id,))

        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        return {
            "success": True,
            "data": user[0]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户详情失败: {str(e)}")
