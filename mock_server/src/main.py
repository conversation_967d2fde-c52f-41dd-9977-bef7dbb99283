from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from .api import auth, series, categories, videos, payments, users, share, analytics, search, playlist, products, cache, users_crud, deprecated_endpoints, admin_videos, admin_users
# user_data暂时禁用，需要迁移到MySQL

app = FastAPI(
    title="Shuimu Video Course API",
    description="""
    水幕视频课程API服务 - RESTful架构

    ## ⚠️ 重要通知：端点废弃

    以下端点已废弃，将于 **2025-09-30** 移除：

    - `PUT /api/videos/{id}/progress` → `PUT /api/users/{user_id}/progress/{video_id}`
    - `PUT /api/videos/{id}/cache-status` → `PUT /api/users/{user_id}/cache/{video_id}`
    - `PUT /api/videos/{id}/watch-count` → `PUT /api/users/{user_id}/progress/{video_id}`

    ## 🔄 迁移指南

    访问 `GET /api/deprecated/endpoints` 获取完整的废弃端点列表和迁移指导。
    """,
    version="2.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include routers
# 用户数据路由放在最前面，避免被其他路由覆盖
app.include_router(user_data.router, prefix="/api", tags=["User Data"])
app.include_router(users_crud.router, prefix="/api", tags=["Users CRUD"])

# 废弃端点路由
app.include_router(deprecated_endpoints.router, prefix="/api", tags=["Deprecated Endpoints"])

app.include_router(auth.router, prefix="/api", tags=["Authentication"])
app.include_router(series.router, prefix="/api", tags=["Series"])
app.include_router(categories.router, prefix="/api", tags=["Categories"])  # 现在包含管理端API
app.include_router(videos.router, prefix="/api", tags=["Videos"])
app.include_router(payments.router, prefix="/api", tags=["Payments"])
app.include_router(users.router, prefix="/api", tags=["Users"])
app.include_router(share.router, prefix="/api", tags=["Share"])
app.include_router(analytics.router, prefix="/api", tags=["Analytics"])
app.include_router(search.router, prefix="/api", tags=["Search"])
app.include_router(playlist.router, prefix="/api", tags=["Playlist"])
app.include_router(products.router, prefix="/api", tags=["Products"])
app.include_router(cache.router, prefix="/api", tags=["Cache"])
# app.include_router(admin_series.router, prefix="/api", tags=["Admin Series"])  # 临时禁用JSON版本
app.include_router(admin_videos.router, prefix="/api", tags=["Admin Videos"])
app.include_router(admin_users.router, prefix="/api", tags=["Admin Users"])

@app.get("/", tags=["Root"])
def read_root():
    return {"message": "Welcome to the Shuimu Mock API!", "version": "2.0.0", "updated": "2025-06-30", "reload_test": "active"}

@app.get("/api/test/sync-check", tags=["Test"])
def sync_check():
    """检查代码同步状态"""
    return {
        "status": "synced",
        "timestamp": "2025-06-30T12:00:00Z",
        "message": "8000端口代码已同步",
        "new_endpoints_available": True
    }

# To run this server:
# 1. Make sure you have fastapi and uvicorn installed:
#    pip install fastapi "uvicorn[standard]"
# 2. Navigate to the `01-shuimu_01/mock_server` directory in your terminal.
# 3. Run the server:
#    uvicorn src.main:app --reload
# The server will be available at http://localhost:8000
