#!/usr/bin/env python3
"""
单元测试：验证 get_user_purchases() 函数
测试两种格式的购买记录数据
"""
import sys
import os
sys.path.insert(0, os.getcwd())

from src.utils.user_data import get_user_purchases

def test_get_user_purchases():
    """测试get_user_purchases函数是否正确提取ID"""
    print("=== 测试 get_user_purchases() 函数 ===")
    
    # 测试user_001（含对象格式的购买记录）
    try:
        purchases = get_user_purchases('user_001')
        print(f"user_001购买记录: {purchases}")
        print(f"数据类型: {type(purchases)}")
        
        if purchases:
            print(f"第一个元素类型: {type(purchases[0])}")
            print(f"第一个元素值: {purchases[0]}")
            
            # 验证返回的都是字符串
            all_strings = all(isinstance(item, str) for item in purchases)
            print(f"是否全为字符串: {all_strings}")
            
            if all_strings:
                print("✅ user_001测试通过")
            else:
                print("❌ user_001测试失败：包含非字符串元素")
        else:
            print("⚠️ user_001无购买记录")
            
    except Exception as e:
        print(f"❌ user_001测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试user_002（可能没有购买记录）
    try:
        purchases_2 = get_user_purchases('user_002')
        print(f"user_002购买记录: {purchases_2}")
        print(f"✅ user_002测试通过")
    except Exception as e:
        print(f"❌ user_002测试失败: {e}")

if __name__ == "__main__":
    test_get_user_purchases() 