#!/usr/bin/env python3
"""
诊断 series API 的具体错误
"""
import sys
import os
sys.path.insert(0, os.getcwd())

def test_step_by_step():
    """逐步测试每个函数"""
    print("=== 逐步诊断 series API ===")
    
    try:
        # 步骤1：测试基础导入
        print("步骤1：测试基础导入...")
        from src.utils.user_data import get_user_purchases
        print("✅ 导入 user_data 成功")
        
        # 步骤2：测试用户购买数据
        print("步骤2：测试用户购买数据...")
        purchases = get_user_purchases('user_001')
        print(f"✅ 获取购买数据成功: {purchases}")
        
        # 步骤3：测试加载基础数据
        print("步骤3：测试加载基础数据...")
        import json
        from pathlib import Path
        data_dir = Path(__file__).resolve().parent / "src" / "data"
        
        with open(data_dir / "videos.json", "r", encoding="utf-8") as f:
            videos = json.load(f)
        print(f"✅ 加载 videos.json 成功，共 {len(videos)} 个视频")
        
        with open(data_dir / "categories.json", "r", encoding="utf-8") as f:
            categories = json.load(f)
        print(f"✅ 加载 categories.json 成功，共 {len(categories)} 个分类")
        
        with open(data_dir / "series.json", "r", encoding="utf-8") as f:
            series = json.load(f)
        print(f"✅ 加载 series.json 成功，共 {len(series)} 个系列")
        
        # 步骤4：测试模型导入
        print("步骤4：测试模型导入...")
        from src.models.series import Series
        from src.models.category import Category
        from src.models.video import Video
        print("✅ 模型导入成功")
        
        # 步骤5：测试单个函数
        print("步骤5：测试单个函数...")
        sys.path.append('src')
        
        # 不能直接导入，因为有相对导入问题
        # 让我们测试具体的逻辑
        
        # 测试一个具体的分类
        cat_data = categories[0]
        print(f"测试分类: {cat_data['id']}")
        
        # 模拟 get_category_by_id 的逻辑
        category_id = cat_data["id"]
        series_id = cat_data.get("seriesId")
        is_purchased = (
            category_id in purchases or 
            series_id in purchases or
            cat_data.get("isFree", False)
        )
        print(f"分类 {category_id} 是否已购买: {is_purchased}")
        
        print("✅ 所有步骤测试完成")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_step_by_step() 