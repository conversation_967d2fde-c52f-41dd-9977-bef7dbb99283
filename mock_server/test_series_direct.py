#!/usr/bin/env python3
"""
直接测试 series API 核心逻辑
"""
import sys
import os
import json
from pathlib import Path

sys.path.insert(0, os.getcwd())

def test_series_logic():
    """直接测试series API的核心逻辑"""
    print("=== 直接测试 series API 逻辑 ===")
    
    try:
        # 导入必要的函数和数据
        from src.utils.user_data import (
            get_user_purchases, 
            get_video_watch_progress, 
            get_video_cache_status,
            calculate_category_progress,
            calculate_category_watch_count
        )
        from src.models.series import Series
        from src.models.category import Category
        from src.models.video import Video
        
        # 加载数据文件
        data_dir = Path(__file__).resolve().parent / "src" / "data"
        
        with open(data_dir / "videos.json", "r", encoding="utf-8") as f:
            videos_db = json.load(f)
        
        with open(data_dir / "categories.json", "r", encoding="utf-8") as f:
            categories_db = json.load(f)
        
        with open(data_dir / "series.json", "r", encoding="utf-8") as f:
            series_db = json.load(f)
        
        print("✅ 数据加载成功")
        
        # 模拟 get_all_series 的逻辑
        x_user_id = "user_001"
        user_purchases = get_user_purchases(x_user_id)
        print(f"用户购买记录: {user_purchases}")
        
        # 定义内部函数
        def get_video_by_id(video_id, user_id=None):
            """模拟 get_video_by_id"""
            video_data = next((video for video in videos_db if video["id"] == video_id), None)
            if not video_data:
                return None
            
            video_with_user_data = video_data.copy()
            
            if user_id:
                progress_data = get_video_watch_progress(user_id, video_id)
                cache_data = get_video_cache_status(user_id, video_id)
                
                video_with_user_data["watch_count"] = progress_data.get("watchCount", 0)
                video_with_user_data["progress"] = (
                    progress_data.get("position", 0) / progress_data.get("duration", 1) 
                    if progress_data.get("duration", 0) > 0 else 0.0
                )
                video_with_user_data["cache_status"] = (
                    "CACHED" if cache_data.get("isCached", False) else "NOT_CACHED"
                )
            else:
                video_with_user_data["watch_count"] = 0
                video_with_user_data["progress"] = 0.0
                video_with_user_data["cache_status"] = "NOT_CACHED"
            
            return video_with_user_data
        
        def get_category_by_id(category_id, user_purchases, user_id=None):
            """模拟 get_category_by_id"""
            category_data = next((cat for cat in categories_db if cat["id"] == category_id), None)
            if not category_data:
                return None
            
            category_with_user_data = category_data.copy()
            
            # 获取视频列表
            videos = []
            for vid in category_data.get("videoIds", []):
                video = get_video_by_id(vid, user_id)
                if video:
                    videos.append(Video(**video))
            
            category_with_user_data["videos"] = videos
            category_with_user_data["totalVideos"] = len(videos)
            
            # 检查是否已购买
            series_id = category_data.get("seriesId")
            is_purchased = (
                category_id in user_purchases or 
                series_id in user_purchases or
                category_data.get("isFree", False)
            )
            category_with_user_data["isPurchased"] = is_purchased
            
            # 计算进度
            if user_id and (is_purchased or category_data.get("isFree", False)):
                video_ids = category_data.get("videoIds", [])
                category_with_user_data["progress"] = calculate_category_progress(user_id, video_ids)
                category_with_user_data["watchCount"] = calculate_category_watch_count(user_id, video_ids)
            else:
                category_with_user_data["progress"] = 0.0
                category_with_user_data["watchCount"] = 0
            
            return Category(**category_with_user_data)
        
        # 测试第一个分类
        print("测试第一个分类...")
        first_category = categories_db[0]
        cat_result = get_category_by_id(first_category["id"], user_purchases, x_user_id)
        print(f"✅ 分类处理成功: {first_category['id']}")
        
        # 测试第一个系列
        print("测试第一个系列...")
        first_series = series_db[0]
        series_data = first_series.copy()
        series_id = series_data["id"]
        series_data["isPurchased"] = series_id in user_purchases
        
        categories = []
        for cat_id in series_data.get("categoryIds", []):
            cat = get_category_by_id(cat_id, user_purchases, x_user_id)
            if cat:
                categories.append(cat)
        
        series_data["categories"] = categories
        series_data["totalVideos"] = sum(cat.totalVideos for cat in categories)
        
        # 尝试创建Series对象
        series_obj = Series(**series_data)
        print(f"✅ 系列处理成功: {series_id}")
        
        print("✅ 所有核心逻辑测试通过！")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_series_logic() 