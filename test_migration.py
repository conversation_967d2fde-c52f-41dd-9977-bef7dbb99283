#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试迁移脚本
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("🚀 测试迁移脚本...")

try:
    print("📦 导入模块...")
    from database.models import DatabaseManager, Base, Series, Category, Video
    print("✅ 数据库模块导入成功")
    
    from cache.global_data_manager import global_data_manager
    print("✅ 全局数据管理器导入成功")
    
    # 测试数据库连接
    print("\n🔍 测试数据库连接...")
    db_manager = DatabaseManager()
    engine = db_manager.engine
    print(f"✅ 数据库连接成功: {engine}")
    
    # 测试表重建
    print("\n🔄 测试表重建...")
    Base.metadata.drop_all(engine)
    print("✅ 表删除成功")
    
    Base.metadata.create_all(engine)
    print("✅ 表创建成功")
    
    # 测试数据加载
    print("\n📦 测试数据加载...")
    if not global_data_manager.is_data_loaded():
        success = global_data_manager.load_all_data_once()
        print(f"📊 数据加载结果: {success}")
    
    summary = global_data_manager.get_data_summary()
    print(f"📊 内存数据统计: {summary}")
    
    print("\n✅ 测试完成，迁移脚本应该可以正常运行")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
