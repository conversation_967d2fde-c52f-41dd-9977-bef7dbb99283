#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强后的AI-Pipe GUI
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from ui.main_window import MainWindow

def main():
    """主函数"""
    print("🚀 启动AI-Pipe GUI v2.0...")
    print(f"📁 当前项目路径: {os.getcwd()}")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("AI-Pipe GUI v2.0")
    app.setApplicationVersion("2.0")
    
    try:
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        print("✅ GUI启动成功！")
        print("\n🔧 新功能测试清单:")
        print("1. ✅ AI智能助手面板（左下方）")
        print("2. ✅ 性能监控面板（左下方）") 
        print("3. ✅ 智能结果显示面板（右下方）")
        print("4. ✅ 增强的参数面板（执行按钮）")
        print("5. ✅ 进度条和停止按钮")
        print("6. ✅ 三分类输出处理")
        print("7. ✅ 心跳检测机制")
        print("\n请在GUI中测试这些功能...")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ GUI启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 