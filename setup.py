#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水幕课程管理端 - 环境设置脚本
自动创建虚拟环境并安装依赖
"""

import os
import sys
import subprocess
import platform

def run_command(command, description=""):
    """运行命令并显示结果"""
    print(f"🔄 {description}")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"✅ {description} 成功")
        if result.stdout:
            print(f"输出: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print(f"错误: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"🐍 当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("❌ Python版本过低，需要Python 3.9或更高版本")
        print("请升级Python版本后重试")
        return False
    
    print("✅ Python版本符合要求")
    return True

def create_virtual_environment():
    """创建虚拟环境"""
    venv_name = "venv"
    
    if os.path.exists(venv_name):
        print(f"📁 虚拟环境 '{venv_name}' 已存在")
        return True
    
    # 创建虚拟环境
    if not run_command(f"python -m venv {venv_name}", "创建虚拟环境"):
        return False
    
    print(f"✅ 虚拟环境 '{venv_name}' 创建成功")
    return True

def get_activation_command():
    """获取虚拟环境激活命令"""
    system = platform.system().lower()
    
    if system == "windows":
        return "venv\\Scripts\\activate"
    else:
        return "source venv/bin/activate"

def install_dependencies():
    """安装依赖包"""
    system = platform.system().lower()
    
    if system == "windows":
        pip_command = "venv\\Scripts\\pip"
    else:
        pip_command = "venv/bin/pip"
    
    # 升级pip
    if not run_command(f"{pip_command} install --upgrade pip", "升级pip"):
        return False
    
    # 安装依赖
    if not run_command(f"{pip_command} install -r requirements.txt", "安装项目依赖"):
        return False
    
    return True

def create_run_script():
    """创建运行脚本"""
    system = platform.system().lower()
    
    if system == "windows":
        # Windows批处理脚本
        script_content = """@echo off
echo 🚀 启动水幕课程管理端...
call venv\\Scripts\\activate
python src\\main.py
pause
"""
        script_name = "run.bat"
    else:
        # Linux/Mac shell脚本
        script_content = """#!/bin/bash
echo "🚀 启动水幕课程管理端..."
source venv/bin/activate
python src/main.py
"""
        script_name = "run.sh"
    
    with open(script_name, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    if system != "windows":
        # 给shell脚本添加执行权限
        os.chmod(script_name, 0o755)
    
    print(f"✅ 运行脚本 '{script_name}' 创建成功")

def create_install_guide():
    """创建安装指南"""
    system = platform.system().lower()
    activation_cmd = get_activation_command()
    
    guide_content = f"""# 水幕课程管理端 - 安装和使用指南

## 🎯 快速开始

### 方法1：自动安装（推荐）
```bash
python setup.py
```

### 方法2：手动安装
1. **创建虚拟环境**
   ```bash
   python -m venv venv
   ```

2. **激活虚拟环境**
   ```bash
   {activation_cmd}
   ```

3. **安装依赖**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

4. **运行程序**
   ```bash
   python src/main.py
   ```

## 🖥️ 系统要求
- Python 3.9 或更高版本
- Windows 10/11, macOS 10.14+, 或 Linux
- 至少 2GB 可用内存
- 网络连接（用于连接数据库）

## 📦 依赖包说明
- **PyQt6**: 图形界面框架
- **PyMySQL**: MySQL数据库连接
- **pandas**: 数据处理和分析
- **matplotlib**: 图表绘制
- **SQLAlchemy**: 数据库ORM

## 🔧 配置数据库
首次运行前，请编辑 `config.ini` 文件：

```ini
[database]
host = your-database-host
port = 3306
user = your-username
password = your-password
database = shuimu_course
```

## 🚀 运行程序
- **Windows**: 双击 `run.bat`
- **Linux/Mac**: 运行 `./run.sh`
- **手动**: 激活虚拟环境后运行 `python src/main.py`

## 📦 打包成exe
```bash
{activation_cmd}
python build.py
```

## ❓ 常见问题

### Q: 安装依赖时出错
A: 确保Python版本 >= 3.9，并尝试升级pip：
```bash
python -m pip install --upgrade pip
```

### Q: 无法连接数据库
A: 检查config.ini中的数据库配置，确保：
- 数据库服务器地址正确
- 用户名和密码正确
- 数据库允许远程连接

### Q: 界面显示异常
A: 确保安装了完整的PyQt6：
```bash
pip install --upgrade PyQt6
```

## 📞 技术支持
如有问题，请检查：
1. Python版本是否符合要求
2. 虚拟环境是否正确激活
3. 依赖包是否完整安装
4. 数据库配置是否正确
"""
    
    with open("INSTALL.md", 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ 安装指南 'INSTALL.md' 创建成功")

def main():
    """主函数"""
    print("🎯 水幕课程管理端 - 环境设置")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 创建虚拟环境
    if not create_virtual_environment():
        return False
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 创建运行脚本
    create_run_script()
    
    # 创建安装指南
    create_install_guide()
    
    print("\n🎉 环境设置完成！")
    print("=" * 50)
    print("📋 下一步操作：")
    print("1. 编辑 config.ini 配置数据库连接")
    print("2. 运行程序：")
    
    system = platform.system().lower()
    if system == "windows":
        print("   - 双击 run.bat")
        print("   - 或者运行: venv\\Scripts\\activate && python src\\main.py")
    else:
        print("   - 运行: ./run.sh")
        print("   - 或者运行: source venv/bin/activate && python src/main.py")
    
    print("3. 查看 INSTALL.md 了解详细使用说明")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)
