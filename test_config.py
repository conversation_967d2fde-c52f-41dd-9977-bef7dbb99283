#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置管理
"""

from src.utils.config import Config

def test_config():
    """测试配置读取"""
    print("=== 配置管理测试 ===")
    
    config = Config()
    
    # 测试API配置
    api_config = config.get_api_config()
    print("\n📡 API配置:")
    print(f"  base_url: {api_config['base_url']}")
    print(f"  timeout: {api_config['timeout']}")
    print(f"  use_api: {api_config['use_api']}")
    print(f"  retry_count: {api_config['retry_count']}")
    print(f"  retry_delay: {api_config['retry_delay']}")
    
    # 测试应用配置
    app_config = config.get_app_config()
    print("\n🖥️ 应用配置:")
    print(f"  title: {app_config['title']}")
    print(f"  version: {app_config['version']}")
    print(f"  window_width: {app_config['window_width']}")
    print(f"  window_height: {app_config['window_height']}")
    
    # 测试数据库配置
    db_config = config.get_database_config()
    print("\n🗄️ 数据库配置:")
    print(f"  host: {db_config['host']}")
    print(f"  port: {db_config['port']}")
    print(f"  user: {db_config['user']}")
    print(f"  database: {db_config['database']}")
    
    print("\n✅ 配置测试完成")

if __name__ == "__main__":
    test_config()
