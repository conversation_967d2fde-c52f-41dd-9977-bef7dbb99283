---
description: 
globs: 
alwaysApply: true
---
# path-convention-rules.mdc

## Path Writing Convention (ALWAYS APPLIED)

- All tool calls (edit_file, read_file, run_terminal_cmd, list_dir, grep_search, codebase_search, etc.) **must reference files and directories relative to the repository root.**
- Never prepend the project folder name `01-shuimu_01/` to any path.
- If an incoming user instruction, commit message, or existing code contains a path starting with `01-shuimu_01/`, assume it refers to `./` and remove the leading segment when executing a tool.
- When generating commit messages or review comments, explicitly warn whenever such duplicate-folder paths appear.
- When updating `.gitignore`, patterns must likewise start from the repository root (leading `/`) and must not hard-code the duplicated segment.

