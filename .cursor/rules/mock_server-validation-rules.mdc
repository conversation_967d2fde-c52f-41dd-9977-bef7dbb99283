---
description: USE WHEN 用到 mock server、服务器
globs: 
alwaysApply: false
---
# Mock Server 调试与验证统一规则
- 以'Hi，将遵循mock server 检查规则'开头
## 适用场景
- 本地开发启动 mock_server 并进行接口联调 / 健康检查
- CI 脚本在执行自动化测试前需要确保服务可用
- 手动排查后端数据或价格计算逻辑时，快速获取 JSON 数据

## 1. 端口与路由
1. 服务默认监听 `http://localhost:8000`
2. 所有业务接口统一前缀 `/api`
   - 示例：`/api/series`, `/api/videos`, `/api/products`

## 2. PowerShell 调用约定
- **禁止** 直接使用 `curl … | python …`（PowerShell 将 `curl` 映射为 `Invoke-WebRequest`，管道解析失败）
- 推荐两种安全写法：
  1. 原生命令
     ```powershell
     Invoke-WebRequest -Uri "http://localhost:8000/api/series" -UseBasicParsing |
       ConvertFrom-Json | Select-Object -First 1
     ```
  2. 指定真实的 `curl.exe`（例如 Git Bash）
     ```powershell
     & "C:\Program Files\Git\mingw64\bin\curl.exe" -s http://localhost:8000/api/series |
       python -m json.tool
     ```

## 3. 服务启动流程
```powershell
cd mock_server
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
# 等待 log 出现 "Application startup complete."
```

## 4. 服务验证
- 新终端执行第 **2** 条中的请求命令，成功返回 JSON 即表示服务正常运行
- 若脚本自动验证，可加入 `Start-Sleep -Seconds 2` 以等待端口就绪

## 5. 常见错误与排查
| 错误信息 | 常见原因 | 解决方案 |
| -------- | -------- | -------- |
| `无法连接到远程服务器` / `Connection refused` | 端口或前缀写错；未等待服务完全启动 | 确认 `8000` 端口、路径含 `/api`，并等待 `Application startup complete.` |
| `DriveNotFoundException: http` | PowerShell 把 `curl` 解析为驱动器路径 | 按 **2** 条使用 `Invoke-WebRequest` 或真 `curl.exe` |
| `404 Not Found` | 路由缺少 `/api` 或拼写错误 | 检查接口前缀、大小写|
| `[WinError 10013] 以一种访问权限不允许的方式做了一个访问套接字的尝试` | 端口已被占用，服务已在运行 | 直接验证现有服务，无需重启 |

遵守以上规则，可避免因端口、前缀或 PowerShell 管道误用造成的误判与调试时间浪费。



