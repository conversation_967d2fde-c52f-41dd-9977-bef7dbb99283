#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水幕课程管理端 - 主程序入口
"""

import sys
import os
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ui.main_window import MainWindow
from utils.config import Config

def main():
    """主函数"""
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("水幕课程管理端")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("水幕科技")
    
    # 设置高DPI支持 (PyQt6中已默认启用，无需手动设置)
    # app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    # app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    try:
        # 加载配置
        config = Config()
        
        # 创建主窗口
        main_window = MainWindow(config)
        main_window.show()
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except Exception as e:
        # 显示错误信息
        error_msg = QMessageBox()
        error_msg.setIcon(QMessageBox.Icon.Critical)
        error_msg.setWindowTitle("启动错误")
        error_msg.setText(f"程序启动失败：{str(e)}")
        error_msg.exec()
        sys.exit(1)

if __name__ == "__main__":
    main()
