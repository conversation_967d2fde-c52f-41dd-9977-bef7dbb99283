"""
API配置
"""

import os
from typing import Dict, Any

class APIConfig:
    """API配置类"""
    
    def __init__(self):
        # 从环境变量或配置文件读取，默认启用API模式
        self.use_api = os.getenv('USE_API', 'true').lower() == 'true'
        self.api_base_url = os.getenv('API_BASE_URL', 'http://localhost:8000')
        self.api_timeout = int(os.getenv('API_TIMEOUT', '30'))
    
    def get_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return {
            'use_api': self.use_api,
            'base_url': self.api_base_url,
            'timeout': self.api_timeout
        }
    
    def set_api_mode(self, enabled: bool):
        """设置API模式"""
        self.use_api = enabled
    
    def set_api_url(self, url: str):
        """设置API地址"""
        self.api_base_url = url

    def is_api_mode(self) -> bool:
        """是否为API模式"""
        return self.use_api

    def is_local_mode(self) -> bool:
        """是否为本地数据库模式"""
        return not self.use_api

    def get_mode_name(self) -> str:
        """获取当前模式名称"""
        return "服务端API" if self.use_api else "本地数据库"

    def get_endpoint_url(self, endpoint: str, **kwargs) -> str:
        """获取端点的完整URL"""
        url = f"{self.api_base_url}{endpoint}"
        if kwargs:
            url = url.format(**kwargs)
        return url

# API端点常量
class APIEndpoints:
    """API端点常量"""

    # 用户相关
    USERS = "/api/users"
    USER_BY_ID = "/api/users/{user_id}"
    USER_PROFILE = "/api/users/{user_id}/profile"
    USER_PROGRESS = "/api/users/{user_id}/progress"
    USER_PROGRESS_VIDEO = "/api/users/{user_id}/progress/{video_id}"
    USER_CACHE = "/api/users/{user_id}/cache"
    USER_FAVORITES = "/api/users/{user_id}/favorites"

    # 管理端用户
    ADMIN_USERS = "/api/admin/users"
    ADMIN_USER_BY_ID = "/api/admin/users/{user_id}"

    # 系列相关
    SERIES = "/api/series"
    SERIES_BY_ID = "/api/series/{series_id}"
    SERIES_CATEGORIES = "/api/series/{series_id}/categories"

    # 管理端系列
    ADMIN_SERIES = "/api/admin/series"
    ADMIN_SERIES_BY_ID = "/api/admin/series/{series_id}"

    # 分类相关
    CATEGORIES = "/api/categories"
    CATEGORY_BY_ID = "/api/categories/{category_id}"
    CATEGORY_VIDEOS = "/api/categories/{category_id}/videos"

    # 管理端分类
    ADMIN_CATEGORIES = "/api/admin/categories"
    ADMIN_CATEGORY_BY_ID = "/api/admin/categories/{category_id}"

    # 视频相关
    VIDEOS = "/api/videos"
    VIDEO_BY_ID = "/api/videos/{video_id}"

    # 管理端视频
    ADMIN_VIDEOS = "/api/admin/videos"
    ADMIN_VIDEO_BY_ID = "/api/admin/videos/{video_id}"

# 全局配置实例
api_config = APIConfig()
