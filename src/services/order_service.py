#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单业务服务
"""

from typing import List, Optional, Dict, Any, Tuple
from database.dao import OrderDAO
from database.models import DatabaseManager
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class OrderService:
    """订单业务服务类"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def get_order_list(self, page: int = 1, page_size: int = 20, 
                      search: str = None, status: str = None,
                      start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """获取订单列表"""
        try:
            session = self.db_manager.get_session()
            order_dao = OrderDAO(session)
            
            orders, total = order_dao.get_all(page, page_size, search, status)
            
            # 转换为字典格式
            order_data = [order.to_dict() for order in orders]
            
            # 计算分页信息
            total_pages = (total + page_size - 1) // page_size
            
            session.close()
            
            return {
                'success': True,
                'data': order_data,
                'pagination': {
                    'current_page': page,
                    'page_size': page_size,
                    'total_records': total,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        except Exception as e:
            logger.error(f"获取订单列表失败: {e}")
            return {
                'success': False,
                'message': f'获取订单列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }
    
    def get_order_detail(self, order_id: int) -> Dict[str, Any]:
        """获取订单详情"""
        try:
            session = self.db_manager.get_session()
            order_dao = OrderDAO(session)
            
            order = order_dao.get_by_id(order_id)
            if not order:
                return {
                    'success': False,
                    'message': '订单不存在',
                    'data': None
                }
            
            order_data = order.to_dict()
            
            session.close()
            
            return {
                'success': True,
                'data': order_data,
                'message': '获取订单详情成功'
            }
        except Exception as e:
            logger.error(f"获取订单详情失败: {e}")
            return {
                'success': False,
                'message': f'获取订单详情失败: {str(e)}',
                'data': None
            }
    
    def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建订单"""
        try:
            # 数据验证
            validation_result = self._validate_order_data(order_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }
            
            session = self.db_manager.get_session()
            order_dao = OrderDAO(session)
            
            # 创建订单
            order = order_dao.create(order_data)
            
            session.close()
            
            return {
                'success': True,
                'message': '订单创建成功',
                'data': order.to_dict()
            }
        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            return {
                'success': False,
                'message': f'创建订单失败: {str(e)}',
                'data': None
            }
    
    def update_order_status(self, order_id: int, status: str) -> Dict[str, Any]:
        """更新订单状态"""
        try:
            # 验证状态值
            valid_statuses = ['pending', 'completed', 'cancelled']
            if status not in valid_statuses:
                return {
                    'success': False,
                    'message': f'无效的订单状态: {status}'
                }
            
            session = self.db_manager.get_session()
            order_dao = OrderDAO(session)
            
            # 检查订单是否存在
            existing_order = order_dao.get_by_id(order_id)
            if not existing_order:
                session.close()
                return {
                    'success': False,
                    'message': '订单不存在'
                }
            
            # 更新订单状态
            order = order_dao.update(order_id, {'status': status})
            
            session.close()
            
            status_map = {
                'pending': '待支付',
                'completed': '已完成',
                'cancelled': '已取消'
            }
            
            return {
                'success': True,
                'message': f'订单状态已更新为: {status_map[status]}',
                'data': order.to_dict() if order else None
            }
        except Exception as e:
            logger.error(f"更新订单状态失败: {e}")
            return {
                'success': False,
                'message': f'更新订单状态失败: {str(e)}',
                'data': None
            }
    
    def get_revenue_statistics(self) -> Dict[str, Any]:
        """获取收入统计"""
        try:
            session = self.db_manager.get_session()
            order_dao = OrderDAO(session)
            
            stats = order_dao.get_revenue_statistics()
            
            # 获取时间段统计
            today = datetime.now().date()
            
            # 今日收入
            today_start = datetime.combine(today, datetime.min.time())
            today_end = datetime.combine(today, datetime.max.time())
            today_revenue = self._get_revenue_by_date_range(session, today_start, today_end)
            
            # 本月收入
            month_start = today.replace(day=1)
            month_start_dt = datetime.combine(month_start, datetime.min.time())
            month_revenue = self._get_revenue_by_date_range(session, month_start_dt, today_end)
            
            # 本年收入
            year_start = today.replace(month=1, day=1)
            year_start_dt = datetime.combine(year_start, datetime.min.time())
            year_revenue = self._get_revenue_by_date_range(session, year_start_dt, today_end)
            
            stats.update({
                'today_revenue': today_revenue,
                'month_revenue': month_revenue,
                'year_revenue': year_revenue
            })
            
            session.close()
            
            return {
                'success': True,
                'data': stats,
                'message': '获取收入统计成功'
            }
        except Exception as e:
            logger.error(f"获取收入统计失败: {e}")
            return {
                'success': False,
                'message': f'获取收入统计失败: {str(e)}',
                'data': {}
            }
    
    def get_daily_revenue_chart(self, days: int = 30) -> Dict[str, Any]:
        """获取每日收入图表数据"""
        try:
            session = self.db_manager.get_session()
            
            # 计算日期范围
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days-1)
            
            # 获取每日收入数据
            daily_data = []
            current_date = start_date
            
            while current_date <= end_date:
                day_start = datetime.combine(current_date, datetime.min.time())
                day_end = datetime.combine(current_date, datetime.max.time())
                
                revenue = self._get_revenue_by_date_range(session, day_start, day_end)
                
                daily_data.append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'revenue': revenue
                })
                
                current_date += timedelta(days=1)
            
            session.close()
            
            return {
                'success': True,
                'data': daily_data,
                'message': '获取每日收入数据成功'
            }
        except Exception as e:
            logger.error(f"获取每日收入数据失败: {e}")
            return {
                'success': False,
                'message': f'获取每日收入数据失败: {str(e)}',
                'data': []
            }
    
    def get_order_status_distribution(self) -> Dict[str, Any]:
        """获取订单状态分布"""
        try:
            session = self.db_manager.get_session()
            order_dao = OrderDAO(session)
            
            stats = order_dao.get_revenue_statistics()
            
            distribution = [
                {'status': '待支付', 'count': stats['pending_orders'], 'color': '#FFA500'},
                {'status': '已完成', 'count': stats['completed_orders'], 'color': '#32CD32'},
                {'status': '已取消', 'count': stats['cancelled_orders'], 'color': '#FF6347'}
            ]
            
            session.close()
            
            return {
                'success': True,
                'data': distribution,
                'message': '获取订单状态分布成功'
            }
        except Exception as e:
            logger.error(f"获取订单状态分布失败: {e}")
            return {
                'success': False,
                'message': f'获取订单状态分布失败: {str(e)}',
                'data': []
            }
    
    def _get_revenue_by_date_range(self, session, start_date: datetime, end_date: datetime) -> float:
        """获取指定日期范围内的收入"""
        try:
            from sqlalchemy import func, and_
            from database.models import Order
            
            result = session.query(func.sum(Order.amount)).filter(
                and_(
                    Order.status == 'completed',
                    Order.created_at >= start_date,
                    Order.created_at <= end_date
                )
            ).scalar()
            
            return float(result) if result else 0.0
        except Exception as e:
            logger.error(f"获取日期范围收入失败: {e}")
            return 0.0
    
    def _validate_order_data(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证订单数据"""
        required_fields = ['user_id', 'series_id', 'amount']
        for field in required_fields:
            if field not in order_data or order_data[field] is None:
                return {
                    'valid': False,
                    'message': f'{field} 是必填字段'
                }
        
        # 金额验证
        try:
            amount = float(order_data['amount'])
            if amount <= 0:
                return {
                    'valid': False,
                    'message': '订单金额必须大于0'
                }
        except (ValueError, TypeError):
            return {
                'valid': False,
                'message': '订单金额格式不正确'
            }
        
        # 状态验证
        if 'status' in order_data:
            valid_statuses = ['pending', 'completed', 'cancelled']
            if order_data['status'] not in valid_statuses:
                return {
                    'valid': False,
                    'message': '订单状态无效'
                }
        
        return {
            'valid': True,
            'message': '数据验证通过'
        }
