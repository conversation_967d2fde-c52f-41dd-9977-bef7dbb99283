#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户业务服务
"""

import re
import hashlib
from typing import List, Optional, Dict, Any, Tuple
from database.dao import UserDAO
from database.models import DatabaseManager
from cache.data_manager import DataManager
import logging

logger = logging.getLogger(__name__)

class UserService:
    """用户业务服务类"""
    
    def __init__(self, db_manager: DatabaseManager, use_cache: bool = True):
        self.db_manager = db_manager
        self.use_cache = use_cache

        # 初始化数据管理器
        if self.use_cache:
            self.data_manager = DataManager()
            logger.info("用户服务使用缓存优先模式")
        else:
            self.data_manager = None
    
    def get_user_list(self, page: int = 1, page_size: int = 20,
                     search: str = None, is_active: bool = None) -> Dict[str, Any]:
        """获取用户列表"""
        try:
            # 优先使用缓存数据
            if self.use_cache and self.data_manager and self.data_manager._initialized:
                logger.info("使用缓存获取用户列表")
                return self.data_manager.get_data('users', page=page, page_size=page_size,
                                                search=search, is_active=is_active)

            # 使用本地数据库模式
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)

            users, total = user_dao.get_all(page, page_size, search, is_active)

            # 转换为字典格式
            user_list = [user.to_dict() for user in users]

            # 计算分页信息
            total_pages = (total + page_size - 1) // page_size

            session.close()

            return {
                'success': True,
                'data': user_list,
                'pagination': {
                    'current_page': page,
                    'page_size': page_size,
                    'total_records': total,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return {
                'success': False,
                'message': f'获取用户列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }
    
    def get_user_detail(self, user_id: int) -> Dict[str, Any]:
        """获取用户详情"""
        try:
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            user = user_dao.get_by_id(user_id)
            if not user:
                return {
                    'success': False,
                    'message': '用户不存在',
                    'data': None
                }
            
            # 获取用户订单
            orders = user_dao.get_user_orders(user_id)
            order_list = [order.to_dict() for order in orders]
            
            user_data = user.to_dict()
            user_data['orders'] = order_list
            user_data['order_count'] = len(order_list)
            
            # 计算总消费金额
            total_spent = sum(float(order.amount) for order in orders if order.status == 'completed')
            user_data['total_spent'] = total_spent
            
            session.close()
            
            return {
                'success': True,
                'data': user_data,
                'message': '获取用户详情成功'
            }
        except Exception as e:
            logger.error(f"获取用户详情失败: {e}")
            return {
                'success': False,
                'message': f'获取用户详情失败: {str(e)}',
                'data': None
            }
    
    def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建用户"""
        try:
            # 数据验证
            validation_result = self._validate_user_data(user_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }
            
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            # 检查用户名是否已存在
            existing_user = user_dao.get_by_username(user_data['username'])
            if existing_user:
                session.close()
                return {
                    'success': False,
                    'message': '用户名已存在',
                    'data': None
                }
            
            # 检查邮箱是否已存在
            existing_email = user_dao.get_by_email(user_data['email'])
            if existing_email:
                session.close()
                return {
                    'success': False,
                    'message': '邮箱已存在',
                    'data': None
                }
            
            # 密码哈希处理
            if 'password' in user_data:
                user_data['password_hash'] = self._hash_password(user_data['password'])
                del user_data['password']
            
            # 创建用户
            user = user_dao.create(user_data)
            
            session.close()
            
            return {
                'success': True,
                'message': '用户创建成功',
                'data': user.to_dict()
            }
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            return {
                'success': False,
                'message': f'创建用户失败: {str(e)}',
                'data': None
            }
    
    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户"""
        try:
            # 数据验证
            validation_result = self._validate_user_data(user_data, is_update=True)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }
            
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            # 检查用户是否存在
            existing_user = user_dao.get_by_id(user_id)
            if not existing_user:
                session.close()
                return {
                    'success': False,
                    'message': '用户不存在',
                    'data': None
                }
            
            # 检查用户名是否被其他用户使用
            if 'username' in user_data:
                username_user = user_dao.get_by_username(user_data['username'])
                if username_user and username_user.id != user_id:
                    session.close()
                    return {
                        'success': False,
                        'message': '用户名已被其他用户使用',
                        'data': None
                    }
            
            # 检查邮箱是否被其他用户使用
            if 'email' in user_data:
                email_user = user_dao.get_by_email(user_data['email'])
                if email_user and email_user.id != user_id:
                    session.close()
                    return {
                        'success': False,
                        'message': '邮箱已被其他用户使用',
                        'data': None
                    }
            
            # 密码哈希处理
            if 'password' in user_data:
                user_data['password_hash'] = self._hash_password(user_data['password'])
                del user_data['password']
            
            # 更新用户
            user = user_dao.update(user_id, user_data)
            
            session.close()
            
            return {
                'success': True,
                'message': '用户更新成功',
                'data': user.to_dict() if user else None
            }
        except Exception as e:
            logger.error(f"更新用户失败: {e}")
            return {
                'success': False,
                'message': f'更新用户失败: {str(e)}',
                'data': None
            }
    
    def delete_user(self, user_id: int, hard_delete: bool = False) -> Dict[str, Any]:
        """删除用户"""
        try:
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            # 检查用户是否存在
            user = user_dao.get_by_id(user_id)
            if not user:
                session.close()
                return {
                    'success': False,
                    'message': '用户不存在'
                }
            
            # 执行删除
            if hard_delete:
                success = user_dao.hard_delete(user_id)
                message = '用户删除成功' if success else '用户删除失败'
            else:
                success = user_dao.delete(user_id)
                message = '用户禁用成功' if success else '用户禁用失败'
            
            session.close()
            
            return {
                'success': success,
                'message': message
            }
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return {
                'success': False,
                'message': f'删除用户失败: {str(e)}'
            }
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            stats = user_dao.get_statistics()
            
            session.close()
            
            return {
                'success': True,
                'data': stats,
                'message': '获取统计信息成功'
            }
        except Exception as e:
            logger.error(f"获取用户统计失败: {e}")
            return {
                'success': False,
                'message': f'获取用户统计失败: {str(e)}',
                'data': {}
            }
    
    def _validate_user_data(self, user_data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """验证用户数据"""
        if not is_update:
            # 创建时必填字段
            required_fields = ['username', 'email']
            for field in required_fields:
                if field not in user_data or not user_data[field]:
                    return {
                        'valid': False,
                        'message': f'{field} 是必填字段'
                    }
        
        # 用户名验证
        if 'username' in user_data:
            username = user_data['username']
            if len(username) < 3 or len(username) > 50:
                return {
                    'valid': False,
                    'message': '用户名长度必须在3-50个字符之间'
                }
            
            if not re.match(r'^[a-zA-Z0-9_]+$', username):
                return {
                    'valid': False,
                    'message': '用户名只能包含字母、数字和下划线'
                }
        
        # 邮箱验证
        if 'email' in user_data:
            email = user_data['email']
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                return {
                    'valid': False,
                    'message': '邮箱格式不正确'
                }
        
        # 密码验证
        if 'password' in user_data:
            password = user_data['password']
            if len(password) < 6:
                return {
                    'valid': False,
                    'message': '密码长度至少6个字符'
                }
        
        return {
            'valid': True,
            'message': '数据验证通过'
        }
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
