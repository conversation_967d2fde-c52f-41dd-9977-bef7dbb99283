#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据导出工具
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from datetime import datetime
import os
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class ExcelExporter:
    """Excel导出器"""
    
    def __init__(self):
        self.workbook = None
        self.worksheet = None
    
    def export_users(self, users: List[Dict[str, Any]], filename: str = None) -> str:
        """导出用户数据到Excel"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"用户数据_{timestamp}.xlsx"
            
            # 确保导出目录存在
            export_dir = "exports"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            filepath = os.path.join(export_dir, filename)
            
            # 创建工作簿
            self.workbook = Workbook()
            self.worksheet = self.workbook.active
            self.worksheet.title = "用户数据"
            
            # 设置标题
            headers = ['用户ID', '用户名', '邮箱', '状态', '创建时间', '更新时间']
            self._write_headers(headers)
            
            # 写入数据
            for row_idx, user in enumerate(users, start=2):
                self.worksheet.cell(row=row_idx, column=1, value=user.get('id', ''))
                self.worksheet.cell(row=row_idx, column=2, value=user.get('username', ''))
                self.worksheet.cell(row=row_idx, column=3, value=user.get('email', ''))
                self.worksheet.cell(row=row_idx, column=4, value='激活' if user.get('is_active') else '禁用')
                self.worksheet.cell(row=row_idx, column=5, value=user.get('created_at', ''))
                self.worksheet.cell(row=row_idx, column=6, value=user.get('updated_at', ''))
            
            # 调整列宽
            self._auto_adjust_columns()
            
            # 保存文件
            self.workbook.save(filepath)
            
            logger.info(f"用户数据导出成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"导出用户数据失败: {e}")
            raise
    
    def export_series(self, series_list: List[Dict[str, Any]], filename: str = None) -> str:
        """导出系列数据到Excel"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"系列数据_{timestamp}.xlsx"
            
            # 确保导出目录存在
            export_dir = "exports"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            filepath = os.path.join(export_dir, filename)
            
            # 创建工作簿
            self.workbook = Workbook()
            self.worksheet = self.workbook.active
            self.worksheet.title = "系列数据"
            
            # 设置标题
            headers = ['系列ID', '标题', '描述', '价格', '视频数量', '发布状态', '创建时间']
            self._write_headers(headers)
            
            # 写入数据
            for row_idx, series in enumerate(series_list, start=2):
                self.worksheet.cell(row=row_idx, column=1, value=series.get('id', ''))
                self.worksheet.cell(row=row_idx, column=2, value=series.get('title', ''))
                self.worksheet.cell(row=row_idx, column=3, value=series.get('description', ''))
                self.worksheet.cell(row=row_idx, column=4, value=series.get('price', 0))
                self.worksheet.cell(row=row_idx, column=5, value=series.get('video_count', 0))
                self.worksheet.cell(row=row_idx, column=6, value='已发布' if series.get('is_published') else '未发布')
                self.worksheet.cell(row=row_idx, column=7, value=series.get('created_at', ''))
            
            # 调整列宽
            self._auto_adjust_columns()
            
            # 保存文件
            self.workbook.save(filepath)
            
            logger.info(f"系列数据导出成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"导出系列数据失败: {e}")
            raise
    
    def export_orders(self, orders: List[Dict[str, Any]], filename: str = None) -> str:
        """导出订单数据到Excel"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"订单数据_{timestamp}.xlsx"
            
            # 确保导出目录存在
            export_dir = "exports"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            filepath = os.path.join(export_dir, filename)
            
            # 创建工作簿
            self.workbook = Workbook()
            self.worksheet = self.workbook.active
            self.worksheet.title = "订单数据"
            
            # 设置标题
            headers = ['订单ID', '用户名', '用户邮箱', '系列标题', '金额', '状态', '创建时间']
            self._write_headers(headers)
            
            # 写入数据
            for row_idx, order in enumerate(orders, start=2):
                self.worksheet.cell(row=row_idx, column=1, value=order.get('id', ''))
                self.worksheet.cell(row=row_idx, column=2, value=order.get('user_name', ''))
                self.worksheet.cell(row=row_idx, column=3, value=order.get('user_email', ''))
                self.worksheet.cell(row=row_idx, column=4, value=order.get('series_title', ''))
                self.worksheet.cell(row=row_idx, column=5, value=order.get('amount', 0))
                self.worksheet.cell(row=row_idx, column=6, value=order.get('status_display', ''))
                self.worksheet.cell(row=row_idx, column=7, value=order.get('created_at', ''))
            
            # 调整列宽
            self._auto_adjust_columns()
            
            # 保存文件
            self.workbook.save(filepath)
            
            logger.info(f"订单数据导出成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"导出订单数据失败: {e}")
            raise
    
    def export_comprehensive_report(self, data: Dict[str, Any], filename: str = None) -> str:
        """导出综合报表"""
        try:
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"综合报表_{timestamp}.xlsx"
            
            # 确保导出目录存在
            export_dir = "exports"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            filepath = os.path.join(export_dir, filename)
            
            # 创建工作簿
            self.workbook = Workbook()
            
            # 删除默认工作表
            self.workbook.remove(self.workbook.active)
            
            # 创建统计概览工作表
            self._create_overview_sheet(data.get('overview', {}))
            
            # 创建用户数据工作表
            if 'users' in data:
                self._create_users_sheet(data['users'])
            
            # 创建系列数据工作表
            if 'series' in data:
                self._create_series_sheet(data['series'])
            
            # 创建订单数据工作表
            if 'orders' in data:
                self._create_orders_sheet(data['orders'])
            
            # 保存文件
            self.workbook.save(filepath)
            
            logger.info(f"综合报表导出成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"导出综合报表失败: {e}")
            raise
    
    def _write_headers(self, headers: List[str]):
        """写入表头"""
        for col_idx, header in enumerate(headers, start=1):
            cell = self.worksheet.cell(row=1, column=col_idx, value=header)
            
            # 设置样式
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center", vertical="center")
    
    def _auto_adjust_columns(self):
        """自动调整列宽"""
        for column in self.worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            self.worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def _create_overview_sheet(self, overview_data: Dict[str, Any]):
        """创建统计概览工作表"""
        self.worksheet = self.workbook.create_sheet("统计概览")
        
        # 写入统计数据
        stats = [
            ['统计项目', '数值'],
            ['总用户数', overview_data.get('total_users', 0)],
            ['激活用户数', overview_data.get('active_users', 0)],
            ['禁用用户数', overview_data.get('inactive_users', 0)],
            ['总系列数', overview_data.get('total_series', 0)],
            ['已发布系列数', overview_data.get('published_series', 0)],
            ['总视频数', overview_data.get('total_videos', 0)],
            ['总订单数', overview_data.get('total_orders', 0)],
            ['已完成订单数', overview_data.get('completed_orders', 0)],
            ['总收入', f"¥{overview_data.get('total_revenue', 0):.2f}"],
        ]
        
        for row_idx, row_data in enumerate(stats, start=1):
            for col_idx, value in enumerate(row_data, start=1):
                cell = self.worksheet.cell(row=row_idx, column=col_idx, value=value)
                
                if row_idx == 1:  # 表头
                    cell.font = Font(bold=True, color="FFFFFF")
                    cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center", vertical="center")
        
        self._auto_adjust_columns()
    
    def _create_users_sheet(self, users: List[Dict[str, Any]]):
        """创建用户数据工作表"""
        self.worksheet = self.workbook.create_sheet("用户数据")
        
        headers = ['用户ID', '用户名', '邮箱', '状态', '创建时间']
        self._write_headers(headers)
        
        for row_idx, user in enumerate(users, start=2):
            self.worksheet.cell(row=row_idx, column=1, value=user.get('id', ''))
            self.worksheet.cell(row=row_idx, column=2, value=user.get('username', ''))
            self.worksheet.cell(row=row_idx, column=3, value=user.get('email', ''))
            self.worksheet.cell(row=row_idx, column=4, value='激活' if user.get('is_active') else '禁用')
            self.worksheet.cell(row=row_idx, column=5, value=user.get('created_at', ''))
        
        self._auto_adjust_columns()
    
    def _create_series_sheet(self, series_list: List[Dict[str, Any]]):
        """创建系列数据工作表"""
        self.worksheet = self.workbook.create_sheet("系列数据")
        
        headers = ['系列ID', '标题', '价格', '视频数量', '发布状态', '创建时间']
        self._write_headers(headers)
        
        for row_idx, series in enumerate(series_list, start=2):
            self.worksheet.cell(row=row_idx, column=1, value=series.get('id', ''))
            self.worksheet.cell(row=row_idx, column=2, value=series.get('title', ''))
            self.worksheet.cell(row=row_idx, column=3, value=series.get('price', 0))
            self.worksheet.cell(row=row_idx, column=4, value=series.get('video_count', 0))
            self.worksheet.cell(row=row_idx, column=5, value='已发布' if series.get('is_published') else '未发布')
            self.worksheet.cell(row=row_idx, column=6, value=series.get('created_at', ''))
        
        self._auto_adjust_columns()
    
    def _create_orders_sheet(self, orders: List[Dict[str, Any]]):
        """创建订单数据工作表"""
        self.worksheet = self.workbook.create_sheet("订单数据")
        
        headers = ['订单ID', '用户名', '系列标题', '金额', '状态', '创建时间']
        self._write_headers(headers)
        
        for row_idx, order in enumerate(orders, start=2):
            self.worksheet.cell(row=row_idx, column=1, value=order.get('id', ''))
            self.worksheet.cell(row=row_idx, column=2, value=order.get('user_name', ''))
            self.worksheet.cell(row=row_idx, column=3, value=order.get('series_title', ''))
            self.worksheet.cell(row=row_idx, column=4, value=order.get('amount', 0))
            self.worksheet.cell(row=row_idx, column=5, value=order.get('status_display', ''))
            self.worksheet.cell(row=row_idx, column=6, value=order.get('created_at', ''))
        
        self._auto_adjust_columns()

class CSVExporter:
    """CSV导出器"""
    
    def export_to_csv(self, data: List[Dict[str, Any]], filename: str, headers: List[str] = None):
        """导出数据到CSV"""
        try:
            # 确保导出目录存在
            export_dir = "exports"
            if not os.path.exists(export_dir):
                os.makedirs(export_dir)
            
            filepath = os.path.join(export_dir, filename)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 如果指定了表头，重新排序列
            if headers:
                df = df.reindex(columns=headers)
            
            # 导出到CSV
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            logger.info(f"CSV导出成功: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"CSV导出失败: {e}")
            raise
