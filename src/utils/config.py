#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
import configparser
from typing import Dict, Any

# 尝试加载.env文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # 如果没有安装python-dotenv，忽略
    pass

class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化配置
        
        Args:
            config_file: 配置文件路径，默认为项目根目录下的config.ini
        """
        if config_file is None:
            # 获取项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_file = os.path.join(project_root, "config.ini")
        
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
            else:
                # 如果配置文件不存在，创建默认配置
                self.create_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.config['database'] = {
            'host': 'localhost',
            'port': '3306',
            'user': 'root',
            'password': '',
            'database': 'shuimu_course',
            'charset': 'utf8mb4'
        }
        
        self.config['app'] = {
            'title': '水幕课程管理端',
            'version': '1.0.0',
            'window_width': '1200',
            'window_height': '800'
        }
        
        self.config['ui'] = {
            'theme': 'light',
            'font_size': '12',
            'font_family': 'Microsoft YaHei'
        }
        
        self.config['api'] = {
            'base_url': 'http://localhost:8000',
            'timeout': '30',
            'use_api': 'true',
            'retry_count': '3',
            'retry_delay': '1'
        }

        self.config['export'] = {
            'default_path': './exports',
            'date_format': '%Y-%m-%d %H:%M:%S'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, section: str, key: str, fallback: Any = None) -> str:
        """
        获取配置值
        
        Args:
            section: 配置节
            key: 配置键
            fallback: 默认值
            
        Returns:
            配置值
        """
        return self.config.get(section, key, fallback=fallback)
    
    def getint(self, section: str, key: str, fallback: int = 0) -> int:
        """获取整数配置值"""
        return self.config.getint(section, key, fallback=fallback)
    
    def getboolean(self, section: str, key: str, fallback: bool = False) -> bool:
        """获取布尔配置值"""
        return self.config.getboolean(section, key, fallback=fallback)
    
    def set(self, section: str, key: str, value: str):
        """
        设置配置值
        
        Args:
            section: 配置节
            key: 配置键
            value: 配置值
        """
        if not self.config.has_section(section):
            self.config.add_section(section)
        
        self.config.set(section, key, str(value))
        self.save_config()
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return {
            'host': self.get('database', 'host', 'localhost'),
            'port': self.getint('database', 'port', 3306),
            'user': self.get('database', 'user', 'root'),
            'password': self.get('database', 'password', ''),
            'database': self.get('database', 'database', 'shuimu_course'),
            'charset': self.get('database', 'charset', 'utf8mb4')
        }
    
    def get_app_config(self) -> Dict[str, Any]:
        """获取应用配置"""
        return {
            'title': self.get('app', 'title', '水幕课程管理端'),
            'version': self.get('app', 'version', '1.0.0'),
            'window_width': self.getint('app', 'window_width', 1200),
            'window_height': self.getint('app', 'window_height', 800)
        }

    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置（优先使用环境变量）"""
        return {
            'base_url': os.getenv('API_BASE_URL', self.get('api', 'base_url', 'http://localhost:8000')),
            'timeout': int(os.getenv('API_TIMEOUT', self.getint('api', 'timeout', 30))),
            'use_api': os.getenv('USE_API', str(self.getboolean('api', 'use_api', True))).lower() == 'true',
            'retry_count': int(os.getenv('API_RETRY_COUNT', self.getint('api', 'retry_count', 3))),
            'retry_delay': int(os.getenv('API_RETRY_DELAY', self.getint('api', 'retry_delay', 1)))
        }
