#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置管理器
用于保存和恢复应用程序设置
"""

import json
import os
from typing import Dict, Any, List
from pathlib import Path

class SettingsManager:
    """设置管理器"""
    
    def __init__(self, settings_file: str = "settings.json"):
        self.settings_file = Path(settings_file)
        self.settings = {}
        self.load_settings()
    
    def load_settings(self):
        """加载设置"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    self.settings = json.load(f)
            else:
                self.settings = {}
        except Exception as e:
            print(f"加载设置失败: {e}")
            self.settings = {}
    
    def save_settings(self):
        """保存设置"""
        try:
            # 确保目录存在
            self.settings_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存设置失败: {e}")
    
    def get_setting(self, key: str, default: Any = None) -> Any:
        """获取设置值"""
        return self.settings.get(key, default)
    
    def set_setting(self, key: str, value: Any):
        """设置值"""
        self.settings[key] = value
    
    def get_column_widths(self, table_name: str) -> List[int]:
        """获取表格列宽"""
        return self.get_setting(f"column_widths_{table_name}", [])
    
    def set_column_widths(self, table_name: str, widths: List[int]):
        """设置表格列宽"""
        self.set_setting(f"column_widths_{table_name}", widths)
    
    def get_window_geometry(self) -> Dict[str, int]:
        """获取窗口几何信息"""
        return self.get_setting("window_geometry", {})
    
    def set_window_geometry(self, geometry: Dict[str, int]):
        """设置窗口几何信息"""
        self.set_setting("window_geometry", geometry)

# 全局设置管理器实例
settings_manager = SettingsManager()
