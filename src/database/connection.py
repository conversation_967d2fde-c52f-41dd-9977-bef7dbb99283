#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接模块
"""

import pymysql
import pandas as pd
from typing import Dict, Any, Optional, List
from contextlib import contextmanager

class DatabaseConnection:
    """数据库连接管理类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据库连接
        
        Args:
            config: 数据库配置字典
        """
        self.config = config
        self.connection = None
        self._is_connected = False
    
    def connect(self) -> bool:
        """
        连接数据库
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self.connection = pymysql.connect(
                host=self.config['host'],
                port=self.config['port'],
                user=self.config['user'],
                password=self.config['password'],
                database=self.config['database'],
                charset=self.config['charset'],
                autocommit=True
            )
            self._is_connected = True
            print(f"✅ 数据库连接成功: {self.config['host']}:{self.config['port']}")
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            self._is_connected = False
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            try:
                self.connection.close()
                self._is_connected = False
                print("✅ 数据库连接已断开")
            except Exception as e:
                print(f"❌ 断开数据库连接失败: {e}")
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        if not self._is_connected or not self.connection:
            return False
        
        try:
            # 发送ping检查连接状态
            self.connection.ping(reconnect=True)
            return True
        except:
            self._is_connected = False
            return False
    
    @contextmanager
    def get_cursor(self):
        """获取数据库游标的上下文管理器"""
        if not self.is_connected():
            if not self.connect():
                raise Exception("无法连接到数据库")
        
        cursor = self.connection.cursor()
        try:
            yield cursor
        finally:
            cursor.close()
    
    def execute_query(self, query: str, params: tuple = None) -> List[tuple]:
        """
        执行查询语句
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        with self.get_cursor() as cursor:
            cursor.execute(query, params)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """
        执行更新语句
        
        Args:
            query: SQL更新语句
            params: 更新参数
            
        Returns:
            影响的行数
        """
        with self.get_cursor() as cursor:
            return cursor.execute(query, params)
    
    def get_dataframe(self, query: str, params: tuple = None) -> pd.DataFrame:
        """
        执行查询并返回DataFrame
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            pandas DataFrame
        """
        if not self.is_connected():
            if not self.connect():
                raise Exception("无法连接到数据库")
        
        return pd.read_sql(query, self.connection, params=params)
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试数据库连接
        
        Returns:
            测试结果字典
        """
        result = {
            'success': False,
            'message': '',
            'server_info': '',
            'database_name': ''
        }
        
        try:
            if self.connect():
                with self.get_cursor() as cursor:
                    # 获取服务器信息
                    cursor.execute("SELECT VERSION()")
                    server_version = cursor.fetchone()[0]
                    
                    # 获取当前数据库
                    cursor.execute("SELECT DATABASE()")
                    current_db = cursor.fetchone()[0]
                    
                    result.update({
                        'success': True,
                        'message': '连接成功',
                        'server_info': server_version,
                        'database_name': current_db
                    })
            else:
                result['message'] = '连接失败'
                
        except Exception as e:
            result['message'] = f'连接测试失败: {str(e)}'
        
        return result
