#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步状态显示组件
显示数据同步状态和统计信息
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QProgressBar)
from PyQt6.QtCore import QTimer, pyqtSignal
from PyQt6.QtGui import QFont
from cache.sync_manager import sync_manager, SyncStatus
from typing import Dict, Any

class SyncStatusWidget(QWidget):
    """同步状态显示组件"""
    
    # 信号
    refresh_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.setup_timer()
        
        # 注册状态变化回调
        sync_manager.add_status_callback(self.on_sync_status_changed)
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("📊 数据同步状态")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 统计信息
        self.stats_layout = QHBoxLayout()
        layout.addLayout(self.stats_layout)
        
        self.synced_label = QLabel("🟢 已同步: 0")
        self.syncing_label = QLabel("🟡 同步中: 0")
        self.failed_label = QLabel("🔴 同步失败: 0")
        self.queue_label = QLabel("⏳ 队列中: 0")
        
        self.stats_layout.addWidget(self.synced_label)
        self.stats_layout.addWidget(self.syncing_label)
        self.stats_layout.addWidget(self.failed_label)
        self.stats_layout.addWidget(self.queue_label)
        self.stats_layout.addStretch()
        
        # 操作按钮
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)
        
        self.refresh_btn = QPushButton("🔄 刷新状态")
        self.retry_failed_btn = QPushButton("🔁 重试失败")
        self.clear_failed_btn = QPushButton("🗑️ 清除失败")
        
        self.refresh_btn.clicked.connect(self.refresh_status)
        self.retry_failed_btn.clicked.connect(self.retry_failed_tasks)
        self.clear_failed_btn.clicked.connect(self.clear_failed_tasks)
        
        button_layout.addWidget(self.refresh_btn)
        button_layout.addWidget(self.retry_failed_btn)
        button_layout.addWidget(self.clear_failed_btn)
        button_layout.addStretch()
        
        # 失败任务列表
        self.failed_table = QTableWidget()
        self.failed_table.setColumnCount(4)
        self.failed_table.setHorizontalHeaderLabels(['类型', 'ID', '尝试次数', '操作'])
        
        # 设置列宽
        header = self.failed_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        
        layout.addWidget(QLabel("❌ 同步失败的任务:"))
        layout.addWidget(self.failed_table)
        
        # 初始化状态
        self.refresh_status()
    
    def setup_timer(self):
        """设置定时器自动刷新"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_status)
        self.timer.start(5000)  # 每5秒刷新一次
    
    def refresh_status(self):
        """刷新同步状态"""
        try:
            # 获取统计信息
            stats = sync_manager.get_sync_statistics()
            
            # 更新统计标签
            self.synced_label.setText(f"🟢 已同步: {stats.get('synced', 0)}")
            self.syncing_label.setText(f"🟡 同步中: {stats.get('syncing', 0)}")
            self.failed_label.setText(f"🔴 同步失败: {stats.get('failed', 0)}")
            self.queue_label.setText(f"⏳ 队列中: {stats.get('queue_size', 0)}")
            
            # 更新失败任务列表
            self.update_failed_tasks_table()
            
        except Exception as e:
            print(f"❌ 刷新同步状态失败: {e}")
    
    def update_failed_tasks_table(self):
        """更新失败任务表格"""
        try:
            failed_tasks = sync_manager.get_failed_sync_tasks()
            
            self.failed_table.setRowCount(len(failed_tasks))
            
            for row, task in enumerate(failed_tasks):
                # 类型
                type_item = QTableWidgetItem(task.entity_type)
                self.failed_table.setItem(row, 0, type_item)
                
                # ID
                id_item = QTableWidgetItem(str(task.entity_id))
                self.failed_table.setItem(row, 1, id_item)
                
                # 尝试次数
                attempts_item = QTableWidgetItem(f"{task.attempt_count}/{task.max_retries}")
                self.failed_table.setItem(row, 2, attempts_item)
                
                # 操作按钮
                retry_btn = QPushButton("🔁 重试")
                retry_btn.clicked.connect(lambda checked, t=task: self.retry_single_task(t))
                self.failed_table.setCellWidget(row, 3, retry_btn)
                
        except Exception as e:
            print(f"❌ 更新失败任务表格失败: {e}")
    
    def on_sync_status_changed(self, entity_type: str, entity_id: str, status: SyncStatus):
        """同步状态变化回调"""
        print(f"📊 同步状态变化: {entity_type} {entity_id} -> {status.value}")
        
        # 延迟刷新，避免频繁更新
        QTimer.singleShot(1000, self.refresh_status)
        
        # 如果有数据更新，通知父组件刷新
        if status == SyncStatus.SYNCED:
            self.refresh_requested.emit()
    
    def retry_failed_tasks(self):
        """重试所有失败的任务"""
        try:
            failed_tasks = sync_manager.get_failed_sync_tasks()
            if not failed_tasks:
                QMessageBox.information(self, '提示', '没有失败的同步任务')
                return
            
            reply = QMessageBox.question(
                self, '确认', 
                f'确定要重试 {len(failed_tasks)} 个失败的同步任务吗？',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                for task in failed_tasks:
                    sync_manager.retry_failed_task(task)
                
                QMessageBox.information(self, '成功', f'已重新提交 {len(failed_tasks)} 个同步任务')
                self.refresh_status()
                
        except Exception as e:
            QMessageBox.critical(self, '错误', f'重试失败任务时出错: {e}')
    
    def retry_single_task(self, task):
        """重试单个任务"""
        try:
            sync_manager.retry_failed_task(task)
            QMessageBox.information(self, '成功', f'{task.entity_type} {task.entity_id} 已重新提交同步')
            self.refresh_status()
        except Exception as e:
            QMessageBox.critical(self, '错误', f'重试任务时出错: {e}')
    
    def clear_failed_tasks(self):
        """清除失败的任务"""
        try:
            failed_tasks = sync_manager.get_failed_sync_tasks()
            if not failed_tasks:
                QMessageBox.information(self, '提示', '没有失败的同步任务')
                return
            
            reply = QMessageBox.question(
                self, '确认', 
                f'确定要清除 {len(failed_tasks)} 个失败的同步任务吗？\n清除后这些任务将不再重试。',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                sync_manager.clear_failed_tasks()
                QMessageBox.information(self, '成功', '已清除所有失败的同步任务')
                self.refresh_status()
                
        except Exception as e:
            QMessageBox.critical(self, '错误', f'清除失败任务时出错: {e}')
    
    def closeEvent(self, event):
        """关闭事件"""
        if hasattr(self, 'timer'):
            self.timer.stop()
        super().closeEvent(event)
