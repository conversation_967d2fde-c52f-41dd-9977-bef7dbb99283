#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理窗口
"""

import sys
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QComboBox,
                            QLabel, QMessageBox, QDialog, QFormLayout, QCheckBox,
                            QHeaderView, QAbstractItemView, QFrame, QGroupBox,
                            QTextEdit, QSpinBox, QProgressBar)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon
from services.user_service import UserService
from database.models import DatabaseManager
from utils.exporters import ExcelExporter
import logging

logger = logging.getLogger(__name__)

class UserLoadThread(QThread):
    """用户数据加载线程"""
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, user_service, page=1, page_size=20, search=None, is_active=None):
        super().__init__()
        self.user_service = user_service
        self.page = page
        self.page_size = page_size
        self.search = search
        self.is_active = is_active
    
    def run(self):
        try:
            result = self.user_service.get_user_list(
                page=self.page,
                page_size=self.page_size,
                search=self.search,
                is_active=self.is_active
            )
            self.data_loaded.emit(result)
        except Exception as e:
            self.error_occurred.emit(str(e))

class UserEditDialog(QDialog):
    """用户编辑对话框"""
    
    def __init__(self, parent=None, user_data=None):
        super().__init__(parent)
        self.user_data = user_data
        self.init_ui()
        
        if user_data:
            self.load_user_data()
    
    def init_ui(self):
        self.setWindowTitle('编辑用户' if self.user_data else '新增用户')
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout()
        
        # 表单
        form_layout = QFormLayout()
        
        self.username_edit = QLineEdit()
        self.email_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.is_active_check = QCheckBox()
        
        form_layout.addRow('用户名:', self.username_edit)
        form_layout.addRow('邮箱:', self.email_edit)
        form_layout.addRow('密码:', self.password_edit)
        form_layout.addRow('激活状态:', self.is_active_check)
        
        if self.user_data:
            self.password_edit.setPlaceholderText('留空表示不修改密码')
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton('保存')
        self.cancel_btn = QPushButton('取消')
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def load_user_data(self):
        """加载用户数据"""
        self.username_edit.setText(self.user_data.get('username', ''))
        self.email_edit.setText(self.user_data.get('email', ''))
        self.is_active_check.setChecked(self.user_data.get('is_active', True))
    
    def get_form_data(self):
        """获取表单数据"""
        data = {
            'username': self.username_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'is_active': self.is_active_check.isChecked()
        }
        
        # 只有在输入了密码时才包含密码字段
        password = self.password_edit.text().strip()
        if password:
            data['password'] = password
        
        return data

class UserManagementWindow(QWidget):
    """用户管理窗口"""

    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_service = UserService(db_manager)
        self.current_page = 1
        self.page_size = 20
        self.total_pages = 1
        self.load_thread = None

        self.init_ui()
        self.load_users()
    
    def init_ui(self):
        """初始化用户界面"""
        # 不设置窗口标题和几何，因为作为widget嵌入
        
        layout = QVBoxLayout()
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText('搜索用户名或邮箱...')
        self.search_edit.returnPressed.connect(self.search_users)
        
        # 状态筛选
        self.status_combo = QComboBox()
        self.status_combo.addItems(['全部', '激活', '禁用'])
        self.status_combo.currentTextChanged.connect(self.filter_users)
        
        # 按钮
        self.search_btn = QPushButton('搜索')
        self.add_btn = QPushButton('新增用户')
        self.refresh_btn = QPushButton('刷新')
        self.export_btn = QPushButton('导出Excel')
        
        self.search_btn.clicked.connect(self.search_users)
        self.add_btn.clicked.connect(self.add_user)
        self.refresh_btn.clicked.connect(self.refresh_users)
        self.export_btn.clicked.connect(self.export_users)
        
        toolbar_layout.addWidget(QLabel('搜索:'))
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(QLabel('状态:'))
        toolbar_layout.addWidget(self.status_combo)
        toolbar_layout.addWidget(self.search_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.add_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.export_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 用户表格
        self.user_table = QTableWidget()
        self.setup_table()
        layout.addWidget(self.user_table)
        
        # 分页控件
        pagination_layout = QHBoxLayout()
        
        self.prev_btn = QPushButton('上一页')
        self.next_btn = QPushButton('下一页')
        self.page_label = QLabel('第 1 页，共 1 页')
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(['10', '20', '50', '100'])
        self.page_size_combo.setCurrentText('20')
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        
        self.prev_btn.clicked.connect(self.prev_page)
        self.next_btn.clicked.connect(self.next_page)
        
        pagination_layout.addWidget(self.prev_btn)
        pagination_layout.addWidget(self.next_btn)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addStretch()
        pagination_layout.addWidget(QLabel('每页显示:'))
        pagination_layout.addWidget(self.page_size_combo)
        
        layout.addLayout(pagination_layout)
        
        # 加载进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.setLayout(layout)
    
    def setup_table(self):
        """设置表格"""
        headers = ['ID', '用户名', '邮箱', '状态', '创建时间', '操作']
        self.user_table.setColumnCount(len(headers))
        self.user_table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.user_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.user_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.user_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        
        self.user_table.setColumnWidth(0, 60)
        self.user_table.setColumnWidth(3, 80)
        self.user_table.setColumnWidth(4, 150)
        self.user_table.setColumnWidth(5, 200)
    
    def load_users(self):
        """加载用户数据"""
        if self.load_thread and self.load_thread.isRunning():
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        
        # 获取筛选条件
        search = self.search_edit.text().strip() or None
        status_text = self.status_combo.currentText()
        is_active = None if status_text == '全部' else (status_text == '激活')
        
        self.load_thread = UserLoadThread(
            self.user_service, 
            self.current_page, 
            self.page_size, 
            search, 
            is_active
        )
        self.load_thread.data_loaded.connect(self.on_users_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()
    
    def on_users_loaded(self, result):
        """用户数据加载完成"""
        self.progress_bar.setVisible(False)
        
        if result['success']:
            self.populate_table(result['data'])
            self.update_pagination(result['pagination'])
        else:
            QMessageBox.warning(self, '错误', result['message'])
    
    def on_load_error(self, error_msg):
        """加载错误处理"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, '加载失败', f'加载用户数据失败: {error_msg}')
    
    def populate_table(self, users):
        """填充表格数据"""
        self.user_table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            # ID
            self.user_table.setItem(row, 0, QTableWidgetItem(str(user['id'])))
            
            # 用户名
            self.user_table.setItem(row, 1, QTableWidgetItem(user['username']))
            
            # 邮箱
            self.user_table.setItem(row, 2, QTableWidgetItem(user['email']))
            
            # 状态
            status_text = '激活' if user['is_active'] else '禁用'
            status_item = QTableWidgetItem(status_text)
            if user['is_active']:
                status_item.setBackground(Qt.GlobalColor.green)
            else:
                status_item.setBackground(Qt.GlobalColor.red)
            self.user_table.setItem(row, 3, status_item)
            
            # 创建时间
            created_at = user['created_at'][:19] if user['created_at'] else ''
            self.user_table.setItem(row, 4, QTableWidgetItem(created_at))
            
            # 操作按钮
            self.create_action_buttons(row, user)
    
    def create_action_buttons(self, row, user):
        """创建操作按钮"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)
        
        # 查看按钮
        view_btn = QPushButton('查看')
        view_btn.setFixedSize(50, 25)
        view_btn.clicked.connect(lambda: self.view_user(user['id']))
        
        # 编辑按钮
        edit_btn = QPushButton('编辑')
        edit_btn.setFixedSize(50, 25)
        edit_btn.clicked.connect(lambda: self.edit_user(user))
        
        # 删除按钮
        delete_btn = QPushButton('删除')
        delete_btn.setFixedSize(50, 25)
        delete_btn.clicked.connect(lambda: self.delete_user(user['id'], user['username']))
        
        layout.addWidget(view_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)
        
        widget.setLayout(layout)
        self.user_table.setCellWidget(row, 5, widget)
    
    def update_pagination(self, pagination):
        """更新分页信息"""
        self.current_page = pagination['current_page']
        self.total_pages = pagination['total_pages']
        
        self.page_label.setText(f"第 {self.current_page} 页，共 {self.total_pages} 页")
        
        self.prev_btn.setEnabled(pagination['has_prev'])
        self.next_btn.setEnabled(pagination['has_next'])
    
    def search_users(self):
        """搜索用户"""
        self.current_page = 1
        self.load_users()
    
    def filter_users(self):
        """筛选用户"""
        self.current_page = 1
        self.load_users()
    
    def refresh_users(self):
        """刷新用户列表"""
        self.load_users()
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_users()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_users()
    
    def change_page_size(self, size_text):
        """改变每页大小"""
        self.page_size = int(size_text)
        self.current_page = 1
        self.load_users()
    
    def add_user(self):
        """新增用户"""
        dialog = UserEditDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            user_data = dialog.get_form_data()
            result = self.user_service.create_user(user_data)
            
            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_users()
            else:
                QMessageBox.warning(self, '失败', result['message'])
    
    def edit_user(self, user_data):
        """编辑用户"""
        dialog = UserEditDialog(self, user_data)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            form_data = dialog.get_form_data()
            result = self.user_service.update_user(user_data['id'], form_data)
            
            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_users()
            else:
                QMessageBox.warning(self, '失败', result['message'])
    
    def view_user(self, user_id):
        """查看用户详情"""
        result = self.user_service.get_user_detail(user_id)
        
        if result['success']:
            user_data = result['data']
            self.show_user_detail(user_data)
        else:
            QMessageBox.warning(self, '错误', result['message'])
    
    def show_user_detail(self, user_data):
        """显示用户详情"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"用户详情 - {user_data['username']}")
        dialog.setFixedSize(600, 500)
        
        layout = QVBoxLayout()
        
        # 基本信息
        info_group = QGroupBox("基本信息")
        info_layout = QFormLayout()
        
        info_layout.addRow('用户ID:', QLabel(str(user_data['id'])))
        info_layout.addRow('用户名:', QLabel(user_data['username']))
        info_layout.addRow('邮箱:', QLabel(user_data['email']))
        info_layout.addRow('状态:', QLabel('激活' if user_data['is_active'] else '禁用'))
        info_layout.addRow('创建时间:', QLabel(user_data['created_at'][:19] if user_data['created_at'] else ''))
        info_layout.addRow('订单数量:', QLabel(str(user_data['order_count'])))
        info_layout.addRow('总消费:', QLabel(f"¥{user_data['total_spent']:.2f}"))
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 订单历史
        order_group = QGroupBox("订单历史")
        order_layout = QVBoxLayout()
        
        order_text = QTextEdit()
        order_text.setReadOnly(True)
        
        order_info = ""
        for order in user_data['orders']:
            order_info += f"订单ID: {order['id']}\n"
            order_info += f"系列: {order['series_title']}\n"
            order_info += f"金额: ¥{order['amount']:.2f}\n"
            order_info += f"状态: {order['status_display']}\n"
            order_info += f"时间: {order['created_at'][:19]}\n"
            order_info += "-" * 30 + "\n"
        
        order_text.setText(order_info)
        order_layout.addWidget(order_text)
        order_group.setLayout(order_layout)
        layout.addWidget(order_group)
        
        # 关闭按钮
        close_btn = QPushButton('关闭')
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)
        
        dialog.setLayout(layout)
        dialog.exec()
    
    def delete_user(self, user_id, username):
        """删除用户"""
        reply = QMessageBox.question(
            self, 
            '确认删除', 
            f'确定要删除用户 "{username}" 吗？\n\n注意：这将禁用用户账户，不会删除相关数据。',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            result = self.user_service.delete_user(user_id)
            
            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_users()
            else:
                QMessageBox.warning(self, '失败', result['message'])
    
    def export_users(self):
        """导出用户数据"""
        try:
            # 获取所有用户数据
            result = self.user_service.get_user_list(page=1, page_size=10000)  # 获取所有数据

            if not result['success']:
                QMessageBox.warning(self, '导出失败', result['message'])
                return

            users = result['data']
            if not users:
                QMessageBox.information(self, '提示', '没有数据可导出')
                return

            # 导出到Excel
            exporter = ExcelExporter()
            filepath = exporter.export_users(users)

            # 提示成功并询问是否打开文件
            reply = QMessageBox.question(
                self,
                '导出成功',
                f'用户数据已导出到:\n{filepath}\n\n是否打开文件所在目录？',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                import os
                import subprocess
                import platform

                # 打开文件所在目录
                if platform.system() == "Windows":
                    os.startfile(os.path.dirname(filepath))
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", os.path.dirname(filepath)])
                else:  # Linux
                    subprocess.run(["xdg-open", os.path.dirname(filepath)])

        except Exception as e:
            QMessageBox.critical(self, '导出失败', f'导出用户数据失败: {str(e)}')
