#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量导入视频对话框
"""

import re
import os
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QComboBox, QTextEdit, QTableWidget, QTableWidgetItem,
                            QPushButton, QLabel, QMessageBox, QProgressBar,
                            QHeaderView, QAbstractItemView, QGroupBox)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont
from services.course_service import CourseService
from database.models import DatabaseManager
import logging

logger = logging.getLogger(__name__)

class BatchImportThread(QThread):
    """批量导入线程"""
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数
    import_completed = pyqtSignal(dict)  # 导入结果
    error_occurred = pyqtSignal(str)
    
    def __init__(self, course_service, category_id, video_data_list):
        super().__init__()
        self.course_service = course_service
        self.category_id = category_id
        self.video_data_list = video_data_list
    
    def run(self):
        try:
            success_count = 0
            failed_count = 0
            error_details = []
            
            total = len(self.video_data_list)
            
            for i, video_data in enumerate(self.video_data_list):
                try:
                    # 添加分类ID
                    video_data['category_id'] = self.category_id
                    video_data['order_index'] = i + 1

                    # 创建视频
                    result = self.course_service.create_video(video_data)
                    
                    if result['success']:
                        success_count += 1
                    else:
                        failed_count += 1
                        error_details.append(f"第{i+1}行: {result['message']}")
                    
                    # 更新进度
                    self.progress_updated.emit(i + 1, total)
                    
                except Exception as e:
                    failed_count += 1
                    error_details.append(f"第{i+1}行: {str(e)}")
                    logger.error(f"导入视频失败: {e}")
            
            # 发送完成信号
            self.import_completed.emit({
                'success_count': success_count,
                'failed_count': failed_count,
                'error_details': error_details
            })
            
        except Exception as e:
            self.error_occurred.emit(str(e))

class BatchImportDialog(QDialog):
    """批量导入视频对话框"""
    
    def __init__(self, parent=None, course_service=None):
        super().__init__(parent)
        self.course_service = course_service
        self.parsed_videos = []
        self.import_thread = None
        
        self.init_ui()
        self.load_series_list()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('批量导入视频')
        self.setFixedSize(900, 700)
        
        layout = QVBoxLayout()
        
        # 选择区域
        self.create_selection_section(layout)
        
        # 数据输入区域
        self.create_input_section(layout)
        
        # 预览区域
        self.create_preview_section(layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 按钮区域
        self.create_button_section(layout)
        
        self.setLayout(layout)
    
    def create_selection_section(self, parent_layout):
        """创建选择区域"""
        selection_group = QGroupBox("目标选择")
        form_layout = QFormLayout()
        
        # 系列选择
        self.series_combo = QComboBox()
        self.series_combo.currentTextChanged.connect(self.on_series_changed)
        form_layout.addRow('目标系列*:', self.series_combo)
        
        # 分类选择
        self.category_combo = QComboBox()
        form_layout.addRow('目标分类*:', self.category_combo)
        
        selection_group.setLayout(form_layout)
        parent_layout.addWidget(selection_group)
    
    def create_input_section(self, parent_layout):
        """创建数据输入区域"""
        input_group = QGroupBox("批量视频数据")
        input_layout = QVBoxLayout()
        
        # 格式说明
        format_label = QLabel("""
格式说明：每行一个视频，格式为：
* 视频文件名.mp4：视频直链URL；

示例：
* 抖音202569-395747.mp4：https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/抖音202569-395747.mp4；
* 揭秘一个女人最大的隐藏需求.mp4：https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/揭秘一个女人最大的隐藏需求.mp4；
        """)
        format_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-size: 12px;
            }
        """)
        input_layout.addWidget(format_label)
        
        # 文本输入框
        self.input_text = QTextEdit()
        self.input_text.setPlaceholderText("请粘贴批量视频数据...")
        self.input_text.textChanged.connect(self.parse_input_data)
        self.input_text.setMinimumHeight(150)
        input_layout.addWidget(self.input_text)
        
        input_group.setLayout(input_layout)
        parent_layout.addWidget(input_group)
    
    def create_preview_section(self, parent_layout):
        """创建预览区域"""
        preview_group = QGroupBox("解析预览")
        preview_layout = QVBoxLayout()
        
        # 预览表格
        self.preview_table = QTableWidget()
        self.setup_preview_table()
        preview_layout.addWidget(self.preview_table)
        
        # 统计信息
        self.stats_label = QLabel("解析结果：0 个视频")
        self.stats_label.setStyleSheet("font-weight: bold; color: #333;")
        preview_layout.addWidget(self.stats_label)
        
        preview_group.setLayout(preview_layout)
        parent_layout.addWidget(preview_group)
    
    def setup_preview_table(self):
        """设置预览表格"""
        headers = ['序号', '视频标题', '视频URL', '状态']
        self.preview_table.setColumnCount(len(headers))
        self.preview_table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.preview_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setMinimumHeight(200)
        
        # 设置列宽
        header = self.preview_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)
        
        self.preview_table.setColumnWidth(0, 60)
        self.preview_table.setColumnWidth(3, 80)
    
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        
        # 解析按钮
        self.parse_btn = QPushButton('重新解析')
        self.parse_btn.clicked.connect(self.parse_input_data)
        
        # 导入按钮
        self.import_btn = QPushButton('开始导入')
        self.import_btn.clicked.connect(self.start_import)
        self.import_btn.setEnabled(False)
        
        # 取消按钮
        self.cancel_btn = QPushButton('取消')
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.parse_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.import_btn)
        button_layout.addWidget(self.cancel_btn)
        
        parent_layout.addLayout(button_layout)
    
    def load_series_list(self):
        """加载系列列表"""
        try:
            result = self.course_service.get_series_list(page=1, page_size=1000)
            if result['success']:
                self.series_combo.clear()
                self.series_combo.addItem('请选择系列', None)
                
                for series in result['data']:
                    self.series_combo.addItem(series['title'], series['id'])
            else:
                QMessageBox.warning(self, '错误', f"加载系列列表失败: {result['message']}")
        except Exception as e:
            QMessageBox.critical(self, '错误', f"加载系列列表失败: {str(e)}")
    
    def on_series_changed(self):
        """系列选择改变事件"""
        series_id = self.series_combo.currentData()
        self.load_category_list(series_id)
    
    def load_category_list(self, series_id):
        """加载分类列表"""
        self.category_combo.clear()

        if not series_id:
            self.category_combo.addItem('请先选择系列', None)
            return

        try:
            result = self.course_service.get_category_list(page=1, page_size=1000, series_id=series_id)
            if result['success']:
                self.category_combo.addItem('请选择分类', None)

                for category in result['data']:
                    self.category_combo.addItem(f"{category['title']} (¥{category['price']:.2f})", category['id'])

                if not result['data']:
                    self.category_combo.addItem('该系列暂无分类', None)
            else:
                QMessageBox.warning(self, '错误', f"加载分类列表失败: {result['message']}")
        except Exception as e:
            QMessageBox.critical(self, '错误', f"加载分类列表失败: {str(e)}")
    
    def parse_input_data(self):
        """解析输入数据"""
        text = self.input_text.toPlainText().strip()
        if not text:
            self.update_preview([])
            return
        
        lines = text.split('\n')
        parsed_videos = []
        
        # 正则表达式匹配模式
        # 匹配格式：* 文件名：URL；
        pattern = r'^\s*\*\s*([^：]+)：([^；]+)；?\s*$'
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
            
            match = re.match(pattern, line)
            if match:
                filename = match.group(1).strip()
                url = match.group(2).strip()
                
                # 提取视频标题（去掉文件扩展名）
                title = os.path.splitext(filename)[0]
                
                # 基础验证
                status = "✓ 有效"
                if len(title) < 2:
                    status = "✗ 标题过短"
                elif len(title) > 200:
                    status = "✗ 标题过长"
                elif not url.startswith(('http://', 'https://')):
                    status = "✗ URL格式错误"
                
                parsed_videos.append({
                    'line_number': i + 1,
                    'title': title,
                    'video_url': url,
                    'status': status,
                    'is_valid': status == "✓ 有效"
                })
            else:
                # 格式不正确的行
                parsed_videos.append({
                    'line_number': i + 1,
                    'title': f"格式错误: {line[:30]}...",
                    'video_url': '',
                    'status': "✗ 格式错误",
                    'is_valid': False
                })
        
        self.parsed_videos = parsed_videos
        self.update_preview(parsed_videos)
    
    def update_preview(self, videos):
        """更新预览表格"""
        self.preview_table.setRowCount(len(videos))
        
        valid_count = 0
        for row, video in enumerate(videos):
            # 序号
            self.preview_table.setItem(row, 0, QTableWidgetItem(str(video['line_number'])))
            
            # 标题
            title_item = QTableWidgetItem(video['title'])
            if not video['is_valid']:
                title_item.setBackground(Qt.GlobalColor.red)
            self.preview_table.setItem(row, 1, title_item)
            
            # URL
            url_item = QTableWidgetItem(video['video_url'])
            if not video['is_valid']:
                url_item.setBackground(Qt.GlobalColor.red)
            self.preview_table.setItem(row, 2, url_item)
            
            # 状态
            status_item = QTableWidgetItem(video['status'])
            if video['is_valid']:
                status_item.setBackground(Qt.GlobalColor.green)
                valid_count += 1
            else:
                status_item.setBackground(Qt.GlobalColor.red)
            self.preview_table.setItem(row, 3, status_item)
        
        # 更新统计信息
        total_count = len(videos)
        invalid_count = total_count - valid_count
        
        stats_text = f"解析结果：{total_count} 个视频，{valid_count} 个有效，{invalid_count} 个无效"
        self.stats_label.setText(stats_text)
        
        # 更新导入按钮状态
        can_import = (valid_count > 0 and 
                     self.series_combo.currentData() is not None and 
                     self.category_combo.currentData() is not None)
        self.import_btn.setEnabled(can_import)
    
    def start_import(self):
        """开始导入"""
        # 验证选择
        series_id = self.series_combo.currentData()
        category_id = self.category_combo.currentData()

        if not series_id or not category_id:
            QMessageBox.warning(self, '提示', '请选择目标系列和分类')
            return
        
        # 获取有效的视频数据
        valid_videos = [v for v in self.parsed_videos if v['is_valid']]
        
        if not valid_videos:
            QMessageBox.warning(self, '提示', '没有有效的视频数据可导入')
            return
        
        # 确认导入
        reply = QMessageBox.question(
            self, 
            '确认导入', 
            f'确定要导入 {len(valid_videos)} 个视频到选中的分类吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        # 准备导入数据
        import_data = []
        for video in valid_videos:
            import_data.append({
                'title': video['title'],
                'video_url': video['video_url'],
                'description': '',  # 可以后续添加描述解析
                'duration': 0  # 可以后续添加时长解析
            })
        
        # 开始导入
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(import_data))
        self.progress_bar.setValue(0)
        
        # 禁用按钮
        self.import_btn.setEnabled(False)
        self.cancel_btn.setEnabled(False)
        
        # 启动导入线程
        self.import_thread = BatchImportThread(self.course_service, category_id, import_data)
        self.import_thread.progress_updated.connect(self.on_progress_updated)
        self.import_thread.import_completed.connect(self.on_import_completed)
        self.import_thread.error_occurred.connect(self.on_import_error)
        self.import_thread.start()
    
    def on_progress_updated(self, current, total):
        """进度更新"""
        self.progress_bar.setValue(current)
        self.progress_bar.setFormat(f"导入中... {current}/{total}")
    
    def on_import_completed(self, result):
        """导入完成"""
        self.progress_bar.setVisible(False)
        self.import_btn.setEnabled(True)
        self.cancel_btn.setEnabled(True)
        
        success_count = result['success_count']
        failed_count = result['failed_count']
        error_details = result['error_details']
        
        # 显示结果
        if failed_count == 0:
            QMessageBox.information(
                self, 
                '导入成功', 
                f'批量导入完成！\n\n成功导入：{success_count} 个视频'
            )
            self.accept()  # 关闭对话框
        else:
            error_text = '\n'.join(error_details[:10])  # 只显示前10个错误
            if len(error_details) > 10:
                error_text += f'\n... 还有 {len(error_details) - 10} 个错误'
            
            QMessageBox.warning(
                self, 
                '导入完成（有错误）', 
                f'批量导入完成！\n\n成功导入：{success_count} 个视频\n失败：{failed_count} 个视频\n\n错误详情：\n{error_text}'
            )
    
    def on_import_error(self, error_msg):
        """导入错误"""
        self.progress_bar.setVisible(False)
        self.import_btn.setEnabled(True)
        self.cancel_btn.setEnabled(True)
        
        QMessageBox.critical(self, '导入失败', f'批量导入失败：{error_msg}')
