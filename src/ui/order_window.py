#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单管理窗口
"""

import sys
from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                            QTableWidgetItem, QPushButton, QLineEdit, QComboBox,
                            QLabel, QMessageBox, QDialog, QFormLayout, QCheckBox,
                            QHeaderView, QAbstractItemView, QFrame, QGroupBox,
                            QTextEdit, QSpinBox, QProgressBar, QTabWidget,
                            QDateEdit, QGridLayout)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QDate
from PyQt6.QtGui import QFont, QIcon
from services.order_service import OrderService
from database.models import DatabaseManager
from utils.exporters import ExcelExporter
import logging

logger = logging.getLogger(__name__)

class OrderLoadThread(QThread):
    """订单数据加载线程"""
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, order_service, page=1, page_size=20, search=None, status=None):
        super().__init__()
        self.order_service = order_service
        self.page = page
        self.page_size = page_size
        self.search = search
        self.status = status
    
    def run(self):
        try:
            result = self.order_service.get_order_list(
                page=self.page,
                page_size=self.page_size,
                search=self.search,
                status=self.status
            )
            self.data_loaded.emit(result)
        except Exception as e:
            self.error_occurred.emit(str(e))

class StatisticsLoadThread(QThread):
    """统计数据加载线程"""
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, order_service):
        super().__init__()
        self.order_service = order_service
    
    def run(self):
        try:
            result = self.order_service.get_revenue_statistics()
            self.data_loaded.emit(result)
        except Exception as e:
            self.error_occurred.emit(str(e))

class OrderStatusDialog(QDialog):
    """订单状态修改对话框"""
    
    def __init__(self, parent=None, current_status=None):
        super().__init__(parent)
        self.current_status = current_status
        self.init_ui()
    
    def init_ui(self):
        self.setWindowTitle('修改订单状态')
        self.setFixedSize(300, 150)
        
        layout = QVBoxLayout()
        
        # 状态选择
        form_layout = QFormLayout()
        
        self.status_combo = QComboBox()
        self.status_combo.addItem('待支付', 'pending')
        self.status_combo.addItem('已完成', 'completed')
        self.status_combo.addItem('已取消', 'cancelled')
        
        # 设置当前状态
        if self.current_status:
            for i in range(self.status_combo.count()):
                if self.status_combo.itemData(i) == self.current_status:
                    self.status_combo.setCurrentIndex(i)
                    break
        
        form_layout.addRow('订单状态:', self.status_combo)
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton('保存')
        self.cancel_btn = QPushButton('取消')
        
        self.save_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def get_selected_status(self):
        """获取选中的状态"""
        return self.status_combo.currentData()

class OrderManagementWindow(QWidget):
    """订单管理窗口"""
    
    def __init__(self, db_manager: DatabaseManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.order_service = OrderService(db_manager)
        self.current_page = 1
        self.page_size = 20
        self.total_pages = 1
        self.load_thread = None
        self.stats_thread = None
        
        self.init_ui()
        self.load_statistics()
        self.load_orders()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 页面标题
        title = QLabel("💰 订单管理")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                padding: 15px 0;
                color: #333;
            }
        """)
        layout.addWidget(title)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 订单列表标签页
        self.order_tab = QWidget()
        self.setup_order_tab()
        self.tab_widget.addTab(self.order_tab, "订单列表")
        
        # 财务统计标签页
        self.stats_tab = QWidget()
        self.setup_stats_tab()
        self.tab_widget.addTab(self.stats_tab, "财务统计")
        
        # 标签页切换事件
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        layout.addWidget(self.tab_widget)
        
        self.setLayout(layout)
    
    def setup_order_tab(self):
        """设置订单列表标签页"""
        layout = QVBoxLayout()
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText('搜索用户名、邮箱或系列标题...')
        self.search_edit.returnPressed.connect(self.search_orders)
        
        # 状态筛选
        self.status_combo = QComboBox()
        self.status_combo.addItems(['全部状态', '待支付', '已完成', '已取消'])
        self.status_combo.currentTextChanged.connect(self.filter_orders)
        
        # 按钮
        self.search_btn = QPushButton('搜索')
        self.refresh_btn = QPushButton('刷新')
        self.export_btn = QPushButton('导出Excel')
        
        self.search_btn.clicked.connect(self.search_orders)
        self.refresh_btn.clicked.connect(self.refresh_orders)
        self.export_btn.clicked.connect(self.export_orders)
        
        toolbar_layout.addWidget(QLabel('搜索:'))
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(QLabel('状态:'))
        toolbar_layout.addWidget(self.status_combo)
        toolbar_layout.addWidget(self.search_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.export_btn)
        
        layout.addLayout(toolbar_layout)
        
        # 订单表格
        self.order_table = QTableWidget()
        self.setup_order_table()
        layout.addWidget(self.order_table)
        
        # 分页控件
        pagination_layout = QHBoxLayout()
        
        self.prev_btn = QPushButton('上一页')
        self.next_btn = QPushButton('下一页')
        self.page_label = QLabel('第 1 页，共 1 页')
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems(['10', '20', '50', '100'])
        self.page_size_combo.setCurrentText('20')
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        
        self.prev_btn.clicked.connect(self.prev_page)
        self.next_btn.clicked.connect(self.next_page)
        
        pagination_layout.addWidget(self.prev_btn)
        pagination_layout.addWidget(self.next_btn)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addStretch()
        pagination_layout.addWidget(QLabel('每页显示:'))
        pagination_layout.addWidget(self.page_size_combo)
        
        layout.addLayout(pagination_layout)
        
        # 加载进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        self.order_tab.setLayout(layout)
    
    def setup_stats_tab(self):
        """设置财务统计标签页"""
        layout = QVBoxLayout()
        
        # 统计卡片区域
        stats_layout = QGridLayout()
        
        # 创建统计卡片
        self.total_revenue_card = self.create_stat_card("💰", "总收入", "¥0.00")
        self.today_revenue_card = self.create_stat_card("📅", "今日收入", "¥0.00")
        self.month_revenue_card = self.create_stat_card("📊", "本月收入", "¥0.00")
        self.year_revenue_card = self.create_stat_card("📈", "本年收入", "¥0.00")
        
        self.total_orders_card = self.create_stat_card("📋", "总订单数", "0")
        self.completed_orders_card = self.create_stat_card("✅", "已完成", "0")
        self.pending_orders_card = self.create_stat_card("⏳", "待支付", "0")
        self.cancelled_orders_card = self.create_stat_card("❌", "已取消", "0")
        
        # 布局统计卡片
        stats_layout.addWidget(self.total_revenue_card, 0, 0)
        stats_layout.addWidget(self.today_revenue_card, 0, 1)
        stats_layout.addWidget(self.month_revenue_card, 0, 2)
        stats_layout.addWidget(self.year_revenue_card, 0, 3)
        
        stats_layout.addWidget(self.total_orders_card, 1, 0)
        stats_layout.addWidget(self.completed_orders_card, 1, 1)
        stats_layout.addWidget(self.pending_orders_card, 1, 2)
        stats_layout.addWidget(self.cancelled_orders_card, 1, 3)
        
        layout.addLayout(stats_layout)
        
        # 刷新按钮
        refresh_stats_btn = QPushButton('刷新统计数据')
        refresh_stats_btn.clicked.connect(self.load_statistics)
        layout.addWidget(refresh_stats_btn)
        
        layout.addStretch()
        
        self.stats_tab.setLayout(layout)
    
    def create_stat_card(self, icon: str, title: str, value: str) -> QLabel:
        """创建统计卡片"""
        card = QLabel()
        card.setText(f"{icon}\n{title}\n{value}")
        card.setAlignment(Qt.AlignmentFlag.AlignCenter)
        card.setStyleSheet("""
            QLabel {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 15px;
                background-color: #f9f9f9;
                font-size: 14px;
                font-weight: bold;
                min-height: 80px;
                min-width: 120px;
            }
        """)
        return card
    
    def setup_order_table(self):
        """设置订单表格"""
        headers = ['订单ID', '用户名', '用户邮箱', '系列标题', '金额', '状态', '创建时间', '操作']
        self.order_table.setColumnCount(len(headers))
        self.order_table.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.order_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.order_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.order_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)
        
        self.order_table.setColumnWidth(0, 80)
        self.order_table.setColumnWidth(4, 100)
        self.order_table.setColumnWidth(5, 80)
        self.order_table.setColumnWidth(6, 150)
        self.order_table.setColumnWidth(7, 150)

    # ==================== 订单管理方法 ====================

    def load_orders(self):
        """加载订单数据"""
        if self.load_thread and self.load_thread.isRunning():
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # 获取筛选条件
        search = self.search_edit.text().strip() or None
        status_text = self.status_combo.currentText()

        status_map = {
            '全部状态': None,
            '待支付': 'pending',
            '已完成': 'completed',
            '已取消': 'cancelled'
        }
        status = status_map.get(status_text)

        self.load_thread = OrderLoadThread(
            self.order_service,
            self.current_page,
            self.page_size,
            search,
            status
        )
        self.load_thread.data_loaded.connect(self.on_orders_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()

    def on_orders_loaded(self, result):
        """订单数据加载完成"""
        self.progress_bar.setVisible(False)

        if result['success']:
            self.populate_order_table(result['data'])
            self.update_pagination(result['pagination'])
        else:
            QMessageBox.warning(self, '错误', result['message'])

    def populate_order_table(self, orders):
        """填充订单表格数据"""
        self.order_table.setRowCount(len(orders))

        for row, order in enumerate(orders):
            # 订单ID
            self.order_table.setItem(row, 0, QTableWidgetItem(str(order['id'])))

            # 用户名
            self.order_table.setItem(row, 1, QTableWidgetItem(order['user_name'] or ''))

            # 用户邮箱
            self.order_table.setItem(row, 2, QTableWidgetItem(order['user_email'] or ''))

            # 系列标题
            self.order_table.setItem(row, 3, QTableWidgetItem(order['series_title'] or ''))

            # 金额
            self.order_table.setItem(row, 4, QTableWidgetItem(f"¥{order['amount']:.2f}"))

            # 状态
            status_item = QTableWidgetItem(order['status_display'])
            if order['status'] == 'completed':
                status_item.setBackground(Qt.GlobalColor.green)
            elif order['status'] == 'pending':
                status_item.setBackground(Qt.GlobalColor.yellow)
            else:  # cancelled
                status_item.setBackground(Qt.GlobalColor.red)
            self.order_table.setItem(row, 5, status_item)

            # 创建时间
            created_at = order['created_at'][:19] if order['created_at'] else ''
            self.order_table.setItem(row, 6, QTableWidgetItem(created_at))

            # 操作按钮
            self.create_order_action_buttons(row, order)

    def create_order_action_buttons(self, row, order):
        """创建订单操作按钮"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)

        # 查看按钮
        view_btn = QPushButton('查看')
        view_btn.setFixedSize(50, 25)
        view_btn.clicked.connect(lambda: self.view_order(order['id']))

        # 修改状态按钮
        status_btn = QPushButton('状态')
        status_btn.setFixedSize(50, 25)
        status_btn.clicked.connect(lambda: self.change_order_status(order))

        layout.addWidget(view_btn)
        layout.addWidget(status_btn)

        widget.setLayout(layout)
        self.order_table.setCellWidget(row, 7, widget)

    def update_pagination(self, pagination):
        """更新分页信息"""
        self.current_page = pagination['current_page']
        self.total_pages = pagination['total_pages']

        self.page_label.setText(f"第 {self.current_page} 页，共 {self.total_pages} 页")

        self.prev_btn.setEnabled(pagination['has_prev'])
        self.next_btn.setEnabled(pagination['has_next'])

    def search_orders(self):
        """搜索订单"""
        self.current_page = 1
        self.load_orders()

    def filter_orders(self):
        """筛选订单"""
        self.current_page = 1
        self.load_orders()

    def refresh_orders(self):
        """刷新订单列表"""
        self.load_orders()

    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            self.current_page -= 1
            self.load_orders()

    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_orders()

    def change_page_size(self, size_text):
        """改变每页大小"""
        self.page_size = int(size_text)
        self.current_page = 1
        self.load_orders()

    def view_order(self, order_id):
        """查看订单详情"""
        result = self.order_service.get_order_detail(order_id)

        if result['success']:
            order_data = result['data']
            self.show_order_detail(order_data)
        else:
            QMessageBox.warning(self, '错误', result['message'])

    def show_order_detail(self, order_data):
        """显示订单详情"""
        dialog = QDialog(self)
        dialog.setWindowTitle(f"订单详情 - {order_data['id']}")
        dialog.setFixedSize(500, 400)

        layout = QVBoxLayout()

        # 订单信息
        info_group = QGroupBox("订单信息")
        info_layout = QFormLayout()

        info_layout.addRow('订单ID:', QLabel(str(order_data['id'])))
        info_layout.addRow('用户名:', QLabel(order_data['user_name'] or ''))
        info_layout.addRow('用户邮箱:', QLabel(order_data['user_email'] or ''))
        info_layout.addRow('系列标题:', QLabel(order_data['series_title'] or ''))
        info_layout.addRow('订单金额:', QLabel(f"¥{order_data['amount']:.2f}"))
        info_layout.addRow('订单状态:', QLabel(order_data['status_display']))
        info_layout.addRow('创建时间:', QLabel(order_data['created_at'][:19] if order_data['created_at'] else ''))
        info_layout.addRow('更新时间:', QLabel(order_data['updated_at'][:19] if order_data['updated_at'] else ''))

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # 关闭按钮
        close_btn = QPushButton('关闭')
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)

        dialog.setLayout(layout)
        dialog.exec()

    def change_order_status(self, order_data):
        """修改订单状态"""
        dialog = OrderStatusDialog(self, order_data['status'])
        if dialog.exec() == QDialog.DialogCode.Accepted:
            new_status = dialog.get_selected_status()

            if new_status == order_data['status']:
                QMessageBox.information(self, '提示', '订单状态未发生变化')
                return

            result = self.order_service.update_order_status(order_data['id'], new_status)

            if result['success']:
                QMessageBox.information(self, '成功', result['message'])
                self.refresh_orders()
                # 如果在统计页面，也刷新统计数据
                if self.tab_widget.currentIndex() == 1:
                    self.load_statistics()
            else:
                QMessageBox.warning(self, '失败', result['message'])

    def export_orders(self):
        """导出订单数据"""
        try:
            # 获取所有订单数据
            result = self.order_service.get_order_list(page=1, page_size=10000)

            if not result['success']:
                QMessageBox.warning(self, '导出失败', result['message'])
                return

            orders = result['data']
            if not orders:
                QMessageBox.information(self, '提示', '没有数据可导出')
                return

            # 导出到Excel
            exporter = ExcelExporter()
            filepath = exporter.export_orders(orders)

            # 提示成功并询问是否打开文件
            reply = QMessageBox.question(
                self,
                '导出成功',
                f'订单数据已导出到:\n{filepath}\n\n是否打开文件所在目录？',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                import os
                import subprocess
                import platform

                # 打开文件所在目录
                if platform.system() == "Windows":
                    os.startfile(os.path.dirname(filepath))
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", os.path.dirname(filepath)])
                else:  # Linux
                    subprocess.run(["xdg-open", os.path.dirname(filepath)])

        except Exception as e:
            QMessageBox.critical(self, '导出失败', f'导出订单数据失败: {str(e)}')

    # ==================== 统计管理方法 ====================

    def load_statistics(self):
        """加载统计数据"""
        if self.stats_thread and self.stats_thread.isRunning():
            return

        self.stats_thread = StatisticsLoadThread(self.order_service)
        self.stats_thread.data_loaded.connect(self.on_statistics_loaded)
        self.stats_thread.error_occurred.connect(self.on_stats_error)
        self.stats_thread.start()

    def on_statistics_loaded(self, result):
        """统计数据加载完成"""
        if result['success']:
            stats = result['data']
            self.update_statistics_cards(stats)
        else:
            QMessageBox.warning(self, '错误', result['message'])

    def update_statistics_cards(self, stats):
        """更新统计卡片"""
        # 收入统计
        self.total_revenue_card.setText(f"💰\n总收入\n¥{stats['total_revenue']:.2f}")
        self.today_revenue_card.setText(f"📅\n今日收入\n¥{stats['today_revenue']:.2f}")
        self.month_revenue_card.setText(f"📊\n本月收入\n¥{stats['month_revenue']:.2f}")
        self.year_revenue_card.setText(f"📈\n本年收入\n¥{stats['year_revenue']:.2f}")

        # 订单统计
        self.total_orders_card.setText(f"📋\n总订单数\n{stats['total_orders']:,}")
        self.completed_orders_card.setText(f"✅\n已完成\n{stats['completed_orders']:,}")
        self.pending_orders_card.setText(f"⏳\n待支付\n{stats['pending_orders']:,}")
        self.cancelled_orders_card.setText(f"❌\n已取消\n{stats['cancelled_orders']:,}")

    def on_stats_error(self, error_msg):
        """统计数据加载错误"""
        QMessageBox.critical(self, '加载失败', f'加载统计数据失败: {error_msg}')

    # ==================== 通用方法 ====================

    def on_tab_changed(self, index):
        """标签页切换事件"""
        if index == 0:  # 订单列表
            self.load_orders()
        elif index == 1:  # 财务统计
            self.load_statistics()

    def on_load_error(self, error_msg):
        """加载错误处理"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, '加载失败', f'加载订单数据失败: {error_msg}')
