#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的数据库迁移脚本
"""

import pymysql
import sys

def run_migration():
    """执行数据库迁移"""
    try:
        print("开始连接数据库...")
        
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='123456',
            database='shuimu_course',
            charset='utf8mb4'
        )
        
        cursor = connection.cursor()
        print("✅ 数据库连接成功")
        
        # 1. 创建分类表
        print("1. 创建分类表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS `categories` (
          `id` int NOT NULL AUTO_INCREMENT,
          `series_id` int NOT NULL,
          `title` varchar(200) NOT NULL,
          `description` text,
          `price` decimal(10,2) DEFAULT 0.00,
          `order_index` int DEFAULT 0,
          `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `idx_series_id` (`series_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """)
        print("✅ 分类表创建成功")
        
        # 2. 检查并添加category_id字段
        print("2. 检查videos表结构...")
        cursor.execute("SHOW COLUMNS FROM videos LIKE 'category_id'")
        if not cursor.fetchone():
            print("添加category_id字段...")
            cursor.execute("ALTER TABLE `videos` ADD COLUMN `category_id` int DEFAULT NULL")
            cursor.execute("ALTER TABLE `videos` ADD KEY `idx_category_id` (`category_id`)")
            print("✅ category_id字段添加成功")
        else:
            print("✅ category_id字段已存在")
        
        # 3. 为现有系列创建默认分类
        print("3. 为现有系列创建默认分类...")
        cursor.execute("""
        INSERT INTO `categories` (`series_id`, `title`, `description`, `price`, `order_index`)
        SELECT 
            `id` as `series_id`,
            CONCAT(`title`, ' - 默认分类') as `title`,
            '系统自动创建的默认分类' as `description`,
            COALESCE(`price`, 0.00) as `price`,
            1 as `order_index`
        FROM `series`
        WHERE NOT EXISTS (
            SELECT 1 FROM `categories` WHERE `categories`.`series_id` = `series`.`id`
        )
        """)
        affected_rows = cursor.rowcount
        print(f"✅ 创建了 {affected_rows} 个默认分类")
        
        # 4. 将现有视频关联到默认分类
        print("4. 将现有视频关联到默认分类...")
        cursor.execute("""
        UPDATE `videos` v
        JOIN `categories` c ON c.series_id = v.series_id
        SET v.category_id = c.id
        WHERE v.category_id IS NULL
        AND c.title LIKE '%默认分类%'
        """)
        affected_rows = cursor.rowcount
        print(f"✅ 更新了 {affected_rows} 个视频的分类关联")
        
        # 5. 验证数据
        print("5. 验证数据完整性...")
        cursor.execute("""
        SELECT 
            s.title as series_title,
            COUNT(c.id) as category_count,
            SUM(c.price) as total_price,
            COUNT(v.id) as video_count
        FROM series s
        LEFT JOIN categories c ON c.series_id = s.id
        LEFT JOIN videos v ON v.category_id = c.id
        GROUP BY s.id, s.title
        ORDER BY s.id
        """)
        
        results = cursor.fetchall()
        print("数据完整性验证结果:")
        for row in results:
            series_title, category_count, total_price, video_count = row
            print(f"  系列: {series_title}, 分类数: {category_count}, 总价格: ¥{total_price or 0:.2f}, 视频数: {video_count}")
        
        # 提交事务
        connection.commit()
        print("\n🎉 数据库迁移完成！")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        if 'connection' in locals():
            try:
                connection.rollback()
                connection.close()
            except:
                pass
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("水幕课程管理端 - 数据库迁移")
    print("=" * 50)
    
    success = run_migration()
    
    if success:
        print("\n✅ 迁移成功！现在可以启动程序了。")
    else:
        print("\n❌ 迁移失败！请检查错误信息。")
    
    input("\n按回车键退出...")
