# 水幕课程管理端 - 新课程结构使用指南

## 🎯 新课程结构概述

### **三层结构设计**
```
系列 (Series)
├── 分类 (Category) - 有价格 ¥99.00
│   ├── 视频项 (Video)
│   ├── 视频项 (Video)
│   └── ...
├── 分类 (Category) - 有价格 ¥199.00
│   ├── 视频项 (Video)
│   └── ...
└── 系列总价格 = ¥298.00 (动态计算)
```

### **核心特性**
- **分类定价**: 价格设置在分类级别，而不是系列级别
- **动态计算**: 系列价格自动计算为所有分类价格之和
- **灵活管理**: 可以单独管理每个分类的价格和内容
- **批量导入**: 支持批量导入视频到指定分类

## 🔄 数据库迁移

### **执行迁移**
在使用新功能前，需要先执行数据库迁移：

```bash
cd shuimu-admin
python migrate_database.py
```

### **迁移内容**
1. **创建分类表**: 新增 `categories` 表
2. **调整视频表**: 添加 `category_id` 字段
3. **数据迁移**: 为现有系列创建默认分类
4. **关联更新**: 将现有视频关联到默认分类
5. **约束添加**: 添加必要的外键约束

### **迁移安全**
- ✅ 保留所有现有数据
- ✅ 自动创建默认分类
- ✅ 事务保护，失败自动回滚
- ✅ 数据完整性验证

## 📋 功能使用指南

### **1. 系列管理**

#### **查看系列**
- 系列列表显示动态计算的总价格
- 价格 = 所有分类价格之和
- 显示分类数量和视频数量

#### **创建系列**
- 创建系列时不需要设置价格
- 系统会自动创建一个默认分类
- 可以后续添加更多分类

#### **编辑系列**
- 可以修改系列基本信息
- 价格通过分类管理来调整
- 发布状态控制整个系列的可见性

### **2. 分类管理**

#### **查看分类**
- 在课程管理中切换到"分类管理"标签页
- 显示所有分类及其价格
- 可以按系列筛选分类

#### **创建分类**
- 选择所属系列
- 设置分类标题和描述
- **重要**: 设置分类价格
- 设置排序序号

#### **编辑分类**
- 可以修改分类信息和价格
- 价格修改会自动影响系列总价格
- 可以调整分类在系列中的排序

#### **删除分类**
- 只能删除没有视频的分类
- 删除后系列总价格会自动更新

### **3. 视频管理**

#### **查看视频**
- 显示视频所属的分类和系列
- 可以按分类或系列筛选
- 显示分类价格信息

#### **创建视频**
- **必须选择分类**: 视频必须属于某个分类
- 设置视频标题、描述、URL等
- 设置在分类中的排序

#### **批量导入视频**
- 选择目标系列和分类
- 粘贴批量视频数据
- 系统自动解析并导入到指定分类

## 💰 定价策略

### **分类定价原则**
1. **分类是定价单位**: 每个分类有独立的价格
2. **系列价格动态**: 系列总价 = 所有分类价格之和
3. **灵活组合**: 可以创建不同价格的分类组合

### **定价示例**
```
恋爱宝典系列 (总价: ¥398.00)
├── 基础篇 (¥99.00)
│   ├── 如何开始对话.mp4
│   ├── 建立吸引力.mp4
│   └── ...
├── 进阶篇 (¥199.00)
│   ├── 深度沟通技巧.mp4
│   ├── 情感升级.mp4
│   └── ...
└── 高级篇 (¥100.00)
    ├── 长期关系维护.mp4
    └── ...
```

### **价格更新机制**
- 修改分类价格 → 系列总价自动更新
- 添加新分类 → 系列总价增加
- 删除分类 → 系列总价减少
- 实时计算，无需手动更新

## 🚀 操作流程

### **创建完整课程的流程**

#### **第一步：创建系列**
1. 进入课程管理 → 系列管理
2. 点击"新增系列"
3. 填写系列标题和描述
4. 设置发布状态
5. 保存（系统自动创建默认分类）

#### **第二步：管理分类**
1. 切换到"分类管理"标签页
2. 查看系统创建的默认分类
3. 编辑默认分类，设置合适的价格
4. 根据需要添加更多分类
5. 为每个分类设置价格和排序

#### **第三步：添加视频**
1. 切换到"视频管理"标签页
2. 选择"新增视频"或"批量导入"
3. 选择目标分类
4. 添加视频信息
5. 设置视频排序

#### **第四步：验证结果**
1. 返回系列管理查看总价格
2. 确认价格计算正确
3. 测试系列发布状态

### **批量导入视频流程**

#### **准备数据**
```
* 抖音202569-395747.mp4：https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/抖音202569-395747.mp4；
* 揭秘一个女人最大的隐藏需求.mp4：https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/揭秘一个女人最大的隐藏需求.mp4；
```

#### **执行导入**
1. 进入课程管理 → 视频管理
2. 点击"批量导入"按钮
3. 选择目标系列
4. 选择目标分类（显示分类价格）
5. 粘贴批量数据
6. 预览解析结果
7. 确认导入

## 📊 数据统计

### **系列统计**
- 分类数量
- 视频总数
- 总价格（动态计算）
- 总时长

### **分类统计**
- 视频数量
- 分类价格
- 总时长
- 排序位置

### **视频统计**
- 所属分类
- 分类价格
- 视频时长
- 排序位置

## 🔧 技术实现

### **动态价格计算**
```python
# 系列价格 = 所有分类价格之和
total_price = sum(float(category.price or 0) for category in series.categories)
```

### **数据库关系**
```sql
series (1) → (N) categories (1) → (N) videos
```

### **价格更新触发**
- 分类价格修改时
- 分类添加/删除时
- 系列查询时实时计算

## ⚠️ 注意事项

### **迁移前准备**
1. **备份数据库**: 执行迁移前务必备份
2. **停止服务**: 确保没有其他程序在使用数据库
3. **检查权限**: 确保数据库用户有足够权限

### **使用注意**
1. **分类必选**: 创建视频时必须选择分类
2. **价格设置**: 分类价格不能为负数
3. **删除限制**: 有视频的分类不能删除
4. **排序管理**: 注意分类和视频的排序设置

### **性能考虑**
1. **价格计算**: 系列价格实时计算，大量分类时可能影响性能
2. **查询优化**: 使用索引优化分类和视频查询
3. **缓存策略**: 可以考虑缓存系列价格

## 🎉 升级优势

### **业务优势**
- **灵活定价**: 可以为不同难度的内容设置不同价格
- **组合销售**: 支持分类组合的销售策略
- **精细管理**: 更细粒度的内容管理
- **用户体验**: 用户可以选择购买特定分类

### **技术优势**
- **数据结构清晰**: 三层结构逻辑清晰
- **扩展性好**: 易于添加新功能
- **维护性强**: 代码结构清晰，易于维护
- **性能优化**: 合理的数据库设计

## 📞 技术支持

如果在使用过程中遇到问题：
1. 检查数据库迁移是否成功执行
2. 查看系统日志获取详细错误信息
3. 确认数据库连接和权限设置
4. 验证数据完整性和约束关系

新的课程结构为水幕课程管理提供了更强大和灵活的管理能力！
