# 批量导入视频功能使用指南

## 🎯 功能概述

批量导入视频功能允许用户通过特定格式的文本快速导入大量视频信息，大大提高视频管理效率。

## 📍 功能位置

**路径**: 课程管理 → 视频管理标签页 → 批量导入按钮

## 📝 支持的导入格式

### **标准格式**
```
* 视频文件名.mp4：视频直链URL；
```

### **示例数据**
```
* 抖音202569-395747.mp4：https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/抖音202569-395747.mp4；
* 揭秘一个女人最大的隐藏需求.mp4：https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/揭秘一个女人最大的隐藏需求.mp4；
* 如何快速建立吸引力.mp4：https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/如何快速建立吸引力.mp4；
```

## 🔧 解析规则

1. **过滤处理**: 自动过滤行首的星号"*"和空格
2. **标题提取**: 冒号"："前的内容作为原始文件名
3. **URL提取**: 冒号"："后的内容作为视频直链URL
4. **标题生成**: 原始文件名去掉文件扩展名（如.mp4）
5. **格式验证**: 自动验证URL格式和标题长度

## 🖥️ 界面功能

### **批量导入对话框组件**

#### **1. 目标选择区域**
- **系列选择**: 下拉框选择目标系列（必选）
- **分类选择**: 根据选中系列动态加载分类（当前版本暂时简化）

#### **2. 数据输入区域**
- **格式说明**: 详细的格式说明和示例
- **文本输入框**: 多行文本框，支持粘贴大量数据
- **实时解析**: 输入内容时自动解析并预览

#### **3. 预览区域**
- **解析预览表格**: 显示解析后的视频信息
  - 序号：行号
  - 视频标题：去掉扩展名的文件名
  - 视频URL：直链地址
  - 状态：解析状态（有效/错误）
- **统计信息**: 显示解析结果统计

#### **4. 操作按钮**
- **重新解析**: 手动触发重新解析
- **开始导入**: 执行批量导入操作
- **取消**: 关闭对话框

## 📋 使用流程

### **第一步：打开批量导入**
1. 启动水幕课程管理端
2. 点击左侧"📚 课程管理"菜单
3. 切换到"视频管理"标签页
4. 点击"批量导入"按钮

### **第二步：选择目标位置**
1. 在"目标系列"下拉框中选择要导入的系列
2. 系统会自动加载对应的分类选项

### **第三步：输入批量数据**
1. 将批量视频数据粘贴到文本输入框中
2. 系统会自动解析并在预览表格中显示结果
3. 检查解析结果，确认数据格式正确

### **第四步：确认导入**
1. 查看预览表格中的解析结果
2. 确认统计信息（有效视频数量）
3. 点击"开始导入"按钮

### **第五步：查看结果**
1. 系统显示导入进度条
2. 导入完成后显示结果统计
3. 自动刷新视频列表

## ✅ 数据验证规则

### **格式验证**
- **行格式**: 必须符合 `* 文件名：URL；` 格式
- **URL格式**: 必须以 `http://` 或 `https://` 开头
- **标题长度**: 2-200个字符之间

### **内容验证**
- **重复检测**: 检测重复的视频标题
- **字符过滤**: 过滤特殊字符和空格
- **扩展名处理**: 自动去除文件扩展名

## 🔄 错误处理机制

### **解析错误**
- **格式错误**: 不符合规定格式的行会标记为错误
- **URL错误**: 无效的URL格式会被标记
- **标题错误**: 过长或过短的标题会被标记

### **导入错误**
- **数据库错误**: 数据库操作失败的详细提示
- **网络错误**: 网络连接问题的处理
- **事务回滚**: 确保数据一致性

### **错误显示**
- **实时标记**: 预览表格中用颜色标记错误项
- **错误统计**: 显示有效和无效数据的数量
- **详细日志**: 导入完成后显示详细的错误信息

## 📊 性能特点

### **解析性能**
- **实时解析**: 输入时即时解析，无需等待
- **正则匹配**: 高效的正则表达式解析
- **批量处理**: 支持大量数据的快速处理

### **导入性能**
- **异步导入**: 使用线程避免界面卡顿
- **进度显示**: 实时显示导入进度
- **批量插入**: 优化的数据库批量操作

### **内存优化**
- **流式处理**: 避免大量数据占用内存
- **及时释放**: 及时释放不需要的资源

## 🎯 使用技巧

### **数据准备**
1. **格式统一**: 确保所有行都符合标准格式
2. **URL有效**: 确保视频链接可以正常访问
3. **标题清晰**: 使用有意义的文件名作为标题

### **批量操作**
1. **分批导入**: 建议每次导入不超过100个视频
2. **测试先行**: 先用少量数据测试格式是否正确
3. **备份数据**: 导入前备份原始数据

### **错误处理**
1. **逐行检查**: 仔细检查标记为错误的行
2. **格式调整**: 根据错误提示调整数据格式
3. **重新导入**: 修正错误后重新导入

## 🔧 技术实现

### **解析引擎**
- **正则表达式**: `^\s*\*\s*([^：]+)：([^；]+)；?\s*$`
- **文件名处理**: `os.path.splitext(filename)[0]`
- **URL验证**: 基础的URL格式检查

### **数据库操作**
- **事务管理**: 确保数据一致性
- **批量插入**: 优化的批量数据库操作
- **错误回滚**: 失败时自动回滚

### **界面技术**
- **PyQt6**: 现代化的界面框架
- **多线程**: 异步数据处理
- **实时更新**: 动态界面更新

## 📈 扩展功能

### **当前版本特性**
- ✅ 基础批量导入功能
- ✅ 实时数据解析和预览
- ✅ 完整的错误处理机制
- ✅ 进度显示和结果统计

### **未来扩展计划**
- 🔄 支持更多数据格式（CSV、Excel）
- 🔄 视频时长自动检测
- 🔄 视频缩略图自动生成
- 🔄 批量编辑和更新功能

## 🎉 总结

批量导入视频功能为水幕课程管理端提供了强大的数据导入能力，通过简单的文本格式就能快速导入大量视频信息。该功能具有以下优势：

- **操作简单**: 只需复制粘贴文本数据
- **格式灵活**: 支持常见的文本格式
- **验证完整**: 全面的数据验证机制
- **性能优秀**: 高效的解析和导入性能
- **错误友好**: 详细的错误提示和处理

这个功能将大大提高视频管理的效率，特别适合需要批量导入大量视频的场景。
