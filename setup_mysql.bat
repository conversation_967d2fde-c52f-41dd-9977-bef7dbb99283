@echo off
echo 🗄️ 水幕课程管理端 - MySQL数据库初始化
echo ================================================

echo.
echo 📋 请确保MySQL已经安装并启动
echo.

set /p root_password="请输入MySQL root密码: "

echo.
echo 🔄 正在连接MySQL并执行初始化脚本...
echo.

mysql -u root -p%root_password% < setup_database.sql

if %errorlevel% == 0 (
    echo.
    echo ✅ 数据库初始化成功！
    echo.
    echo 📊 创建的内容：
    echo - 数据库: shuimu_course
    echo - 用户: mike (密码: dyj217)
    echo - 表: users, series, videos, orders
    echo - 测试数据: 已插入示例数据
    echo.
    echo 🚀 现在可以启动管理端程序了！
    echo 双击 run.bat 启动程序
) else (
    echo.
    echo ❌ 数据库初始化失败！
    echo 请检查：
    echo 1. MySQL服务是否启动
    echo 2. root密码是否正确
    echo 3. mysql命令是否在PATH中
)

echo.
pause
