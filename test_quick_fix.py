#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复验证脚本
测试API路径和字段修复是否生效
"""

import sys
import os
import requests

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from api.client import APIClient, SeriesAPIClient, CategoryAPIClient, VideoAPIClient

def test_admin_api_paths():
    """测试admin API路径"""
    print("🧪 测试admin API路径...")
    
    base_url = "http://localhost:8000"
    
    # 直接测试API路径
    test_paths = [
        "/api/admin/series",
        "/api/admin/categories", 
        "/api/admin/videos"
    ]
    
    for path in test_paths:
        try:
            url = base_url + path + "?page=1&page_size=3"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {path} -> 200 OK, 数据类型: {type(data)}")
                if isinstance(data, list):
                    print(f"   数组长度: {len(data)}")
                elif isinstance(data, dict):
                    print(f"   字典键: {list(data.keys())}")
            else:
                print(f"❌ {path} -> {response.status_code}: {response.text[:100]}")
                
        except Exception as e:
            print(f"💥 {path} -> 异常: {e}")

def test_api_clients():
    """测试API客户端"""
    print("\n🧪 测试API客户端...")
    
    try:
        api_client = APIClient("http://localhost:8000")
        
        # 测试系列API
        series_client = SeriesAPIClient(api_client)
        series_result = series_client.get_series(page=1, page_size=3)
        print(f"📚 系列API: success={series_result.get('success')}, count={len(series_result.get('data', []))}")
        
        # 测试分类API
        category_client = CategoryAPIClient(api_client)
        category_result = category_client.get_categories(page=1, page_size=3)
        print(f"📂 分类API: success={category_result.get('success')}, count={len(category_result.get('data', []))}")
        
        # 检查分类数据是否有video_count字段
        if category_result.get('success') and category_result.get('data'):
            first_category = category_result['data'][0]
            has_video_count = 'video_count' in first_category
            print(f"   分类数据包含video_count: {has_video_count}")
            if not has_video_count:
                print(f"   分类数据字段: {list(first_category.keys())}")
        
        # 测试视频API
        video_client = VideoAPIClient(api_client)
        video_result = video_client.get_videos(page=1, page_size=3)
        print(f"📹 视频API: success={video_result.get('success')}, count={len(video_result.get('data', []))}")
        
        # 统计成功率
        success_count = sum([
            series_result.get('success', False),
            category_result.get('success', False),
            video_result.get('success', False)
        ])
        
        print(f"\n📊 API成功率: {success_count}/3")
        
        if success_count >= 2:
            print("🎉 API修复基本成功！")
            return True
        else:
            print("❌ API修复失败")
            return False
            
    except Exception as e:
        print(f"❌ API客户端测试异常: {e}")
        return False

def test_data_format():
    """测试数据格式"""
    print("\n🧪 测试数据格式处理...")
    
    try:
        # 直接请求API查看原始数据格式
        response = requests.get("http://localhost:8000/api/admin/categories?page=1&page_size=2", timeout=5)
        
        if response.status_code == 200:
            raw_data = response.json()
            print(f"📊 原始数据类型: {type(raw_data)}")
            
            if isinstance(raw_data, list) and len(raw_data) > 0:
                first_item = raw_data[0]
                print(f"📊 第一项数据字段: {list(first_item.keys())}")
                
                # 检查关键字段
                required_fields = ['id', 'title', 'series_id']
                missing_fields = [field for field in required_fields if field not in first_item]
                
                if missing_fields:
                    print(f"⚠️ 缺少字段: {missing_fields}")
                else:
                    print("✅ 基本字段完整")
                
                # 检查video_count字段
                if 'video_count' not in first_item:
                    print("⚠️ 缺少video_count字段，但已在界面代码中处理")
                else:
                    print("✅ 包含video_count字段")
            
            return True
        else:
            print(f"❌ 数据格式测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 数据格式测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始快速修复验证...")
    print("=" * 50)
    
    # 测试admin API路径
    test_admin_api_paths()
    
    # 测试API客户端
    api_ok = test_api_clients()
    
    # 测试数据格式
    format_ok = test_data_format()
    
    print("=" * 50)
    print("🎯 快速修复验证结果:")
    print(f"  API客户端: {'✅ 正常' if api_ok else '❌ 异常'}")
    print(f"  数据格式: {'✅ 正常' if format_ok else '❌ 异常'}")
    
    if api_ok and format_ok:
        print("\n🎉 修复验证通过！")
        print("✅ API路径已修复为 /api/admin/*")
        print("✅ video_count字段缺失已处理")
        print("✅ 数据格式转换正常工作")
    else:
        print("\n⚠️ 部分修复可能需要进一步调整")

if __name__ == "__main__":
    main()
