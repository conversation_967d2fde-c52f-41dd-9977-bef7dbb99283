# 管理端环境配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置

# ========== API服务配置 ==========
API_BASE_URL=http://localhost:8000
API_TIMEOUT=30
USE_API=true
API_RETRY_COUNT=3
API_RETRY_DELAY=1

# ========== 数据库配置 ==========
DB_HOST=localhost
DB_PORT=3306
DB_USER=mike
DB_PASSWORD=dyj217
DB_NAME=shuimu_course
DB_CHARSET=utf8mb4

# ========== 应用配置 ==========
APP_TITLE=水幕课程管理端
APP_VERSION=1.0.0
WINDOW_WIDTH=1200
WINDOW_HEIGHT=800

# ========== 界面配置 ==========
UI_THEME=light
UI_FONT_SIZE=12
UI_FONT_FAMILY=Microsoft YaHei

# ========== 缓存配置 ==========
CACHE_FILE=./data/cache.json
CACHE_EXPIRE_HOURS=24
AUTO_SYNC_INTERVAL=300
ENABLE_OFFLINE_MODE=true

# ========== 导出配置 ==========
EXPORT_DEFAULT_PATH=./exports
EXPORT_DATE_FORMAT=%Y-%m-%d %H:%M:%S

# ========== 开发配置 ==========
DEBUG=false
LOG_LEVEL=INFO
