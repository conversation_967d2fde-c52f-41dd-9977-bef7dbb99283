# Cursor开发指令文档 - 水幕视频课程App（v5-升级版）

> **项目说明**：基于 [01-项目PRD_视频课程App_v5.md] 开发的付费视频课程学习平台  
> **技术栈**：Android Kotlin + Jetpack Compose + Clean Architecture + AndroidX Media3  
> **核心特色**：网盘直链 + 强制本地缓存 + 离线播放 + UI组件100%还原

---

## 🎯 核心开发原则与AI指令遵循规范

**1. 核心原则：严格保真原型 (High-Fidelity to Prototype)**
所有UI组件和功能模块的开发，必须 **100% 忠实于** `UI Prototype` 文件夹中提供的设计原型、数据结构和交互逻辑。**严禁任何形式的简化、省略或想当然的实现**。任何偏离原型的实现都将被视为任务失败，需要返工重做。

**2. 指令执行要求：禁止偷工减料**
每一条指令都经过精心设计，包含必须完成的所有细节。执行时必须完整实现所有要求，**禁止只完成部分功能或表面工作**。完成度低于95%的交付物将被驳回。

**3. 强制自我验证机制**
对于关键指令，特别是UI和业务逻辑实现，会包含"强制自我验证"步骤。AI在完成编码后，**必须**对照验证清单逐项检查，并明确汇报每项的完成情况。

---

## 🤖 AI 增强执行协议 (AI Enhanced Execution Protocol) - v2.0

为彻底解决AI在执行任务时出现的"自由发挥"和"简化实现"问题，确保最终交付物与原型100%一致，兹引入以下**v2.0版增强执行协议**。从即刻起，AI在处理每一条开发指令时，必须严格遵循此协议。

### 核心思想：原型驱动的闭环验证系统

将模糊的"视觉原型"转化为AI必须严格遵守的、可逐项验证的"工程指令清单"。

### 协议流程：

1.  **原型量化分解 (Quantitative Prototype Decomposition):**
    *   在开发任何一个UI界面前，AI的首要任务**不是写代码**，而是**分析原型，并输出一份具体的、可量化的"UI规范清单"**。
    *   **清单示例**：
        *   `-` **用户头像:** 形状(圆形), 尺寸(64dp x 64dp), 与屏幕顶部距离(24dp)
        *   `-` **用户昵称:** 字体大小(18sp), 颜色(#111827), 与头像右侧距离(16dp)

2.  **基于清单编码 (Coding Based on the Checklist):**
    *   AI在实现Compose UI代码时，其唯一的依据就是上一步生成的这份清单。所有代码中的参数都必须与清单中的值严格对应。

3.  **代码-原型双向验证 (Code-to-Prototype Bidirectional Verification):**
    *   编码完成后，AI必须**回到第一步的清单，为其中的每一项，都附上对应的实现代码片段作为证据**。
    *   **交付物示例**:
        *   `-` **用户头像:** `✅ 已实现`
            *   `-` 尺寸：`64dp` x `64dp` -> `证据: Modifier.size(64.dp)`
        *   `-` **用户昵称:** `✅ 已实现`
            *   `-` 字体大小：`18sp` -> `证据: fontSize = 18.sp`
    *   此过程强制AI自证其清，将视觉还原度从主观感受问题，变为客观的、可审计的工程问题。

**违规处理：**
任何未遵循此协议的交付物都将被视为无效，AI必须无条件返工，直到协议的所有步骤都得到完整、严格的执行。

---

## 📊 项目进度总览

| 指令 | 任务名称 | 状态 | 开始时间 | 完成时间 |
| ---- | -------- | ---- | -------- | -------- |
| 0 | UI组件提取和架构设计 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 1 | Mock Server环境搭建 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 2 | 创建Android项目结构 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 3 | 配置基础依赖和构建脚本 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 4 | 设置Hilt依赖注入 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 5 | 配置Clean Architecture包结构 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 6 | 设置基础Compose主题 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 7 | 集成UI组件系统 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 8 | 创建数据模型类 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 9 | 配置Room数据库 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 10 | 实现网络API接口 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 11 | 创建Repository实现 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 12 | 配置DataStore存储 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 13 | 创建导航架构 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 14 | 实现首页课程展示 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 15 | 开发用户登录页面 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 16 | 创建个人中心页面 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 17 | 实现支付购买界面 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 18 | 开发缓存管理页面 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 19 | 配置AndroidX Media3播放器 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 20 | 实现视频播放界面 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 21 | 开发进度记录功能 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 22 | 实现播放控制器 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 23 | 完善缓存管理逻辑 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 24 | 开发分享收益页面 | ✅ 已完成 | 2024-07-30 | 2024-07-30 |
| 25 | 实现分享排行功能 | ✅ 已完成 | 2024-07-30 | 2024-07-30 |
| 26 | 创建分享素材页面 | ✅ 已完成 | 2024-07-30 | 2024-07-30 |
| 27 | 开发个人中心 - 基础布局和信息展示 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 28 | 开发我的订单页面 | ✅ 已完成 | 2024-07-29 | 2024-07-29 |
| 29 | 实现单元测试 | ⏳ 待开始 | | |
| 30 | 实现集成测试 | ⏳ 待开始 | | |
| 31 | 实现UI测试 | ⏳ 待开始 | | |

### 🤖 Cursor AI 状态更新指令

**重要**：每完成一个指令后，Cursor AI 必须执行以下操作：

1. **更新进度总览表格**：获取当前时间，将对应指令的状态从 `⏳ 待开始` 改为 `✅ 已完成`，并填写开始时间和完成时间
2. **更新指令详情状态**：修改具体指令中的**状态**、**开始时间**、**完成时间**字段
3. **🔄 执行质量检查**：确保所有验收标准都已满足
4. **📋 生成完成报告**：汇报完成的具体内容和交付物
5. **✅ 更新完成状态**：更新项目进度总览和指令详情中的状态
6. **继续下一个指令**：无需等待用户响应，直接执行下一个指令

**状态标记说明**：
- ⏳ 待开始：尚未开始执行
- 🔄 进行中：正在执行中
- ⏸️ 等待确认：已完成执行，等待用户确认
- ✅ 已完成：已成功完成并通过质量检查和用户确认
- ❌ 失败：执行失败，需要重新执行
- ⏸️ 暂停：暂停执行，等待条件满足

---

## 📋 指令索引

### 前期准备（指令0-2）
- **指令0**：UI组件提取和架构设计
- **指令1**：Mock Server环境搭建
- **指令2**：创建Android项目结构

### 项目初始化（指令3-7）
- **指令3**：配置基础依赖和构建脚本
- **指令4**：设置Hilt依赖注入
- **指令5**：配置Clean Architecture包结构
- **指令6**：设置基础Compose主题
- **指令7**：集成UI组件系统

### 数据层开发（指令8-12）
- **指令8**：创建数据模型类
- **指令9**：配置Room数据库
- **指令10**：实现网络API接口
- **指令11**：创建Repository实现
- **指令12**：配置DataStore存储

### UI层开发（指令13-18）
- **指令13**：创建导航架构
- **指令14**：实现首页课程展示
- **指令15**：开发用户登录页面
- **指令16**：创建个人中心页面
- **指令17**：实现支付购买界面
- **指令18**：开发缓存管理页面

### 视频播放系统（指令19-23）
- **指令19**：配置AndroidX Media3播放器
- **指令20**：实现视频播放界面
- **指令21**：开发进度记录功能
- **指令22**：实现播放控制器
- **指令23**：完善缓存管理逻辑

### 分享分成系统（指令24-26）
- **指令24**：开发分享收益页面
- **指令25**：实现分享排行功能
- **指令26**：创建分享素材页面

### 个人中心开发（指令27-28）
- **指令27**：开发个人中心 - 基础布局和信息展示
- **指令28**：开发我的订单页面

### 应用质量保障（指令29-31）
- **指令29**：实现单元测试
- **指令30**：实现集成测试
- **指令31**：实现UI测试

---

## 🎨 前期准备

### 指令0：UI组件提取和架构设计

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
**前置任务：原型分析**
在开始任务前，必须首先详细分析 `UI Prototype` 文件夹中的以下文件，并简要总结出关键设计规范：
1. `UI Prototype/data/component-templates-with-styles.js`：分析组件模板结构。
2. `UI Prototype/data/badge-styles-dual.css`：分析徽章系统的具体样式。
3. `UI Prototype/data/app-data.js` 和 `user-state.js`：分析组件的状态定义。

---

**核心任务：提取UI组件并创建文件结构**

基于以上原型分析，提取核心UI组件，并创建对应的Kotlin文件：

1. 创建组件目录结构（路径：`app/src/main/java/com/shuimu/course/presentation/ui/components/`），包含 **26个核心UI组件** 和 **1个非UI的组件注册表**：
├── base/               # 基础组件（5个）
│   ├── ShuimuButton.kt
│   ├── ShuimuCard.kt  
│   ├── ShuimuTextField.kt
│   ├── WatchCountBadge.kt      # 观看次数徽章组件
│   └── VideoProgressBar.kt     # 视频观看进度条组件
├── display/            # 展示组件（4个）
│   ├── VideoItem.kt            # 视频项组件（4种使用场景，每个场景都分两种状态：已购买和未购买）
│   ├── CategoryCard.kt         # 分类卡片（非系列头部）
│   ├── SearchItem.kt
│   └── SeriesCard.kt           # 系列卡片组件
├── navigation/         # 导航组件（2个）
│   ├── TopAppBar.kt
│   └── BottomNavigationBar.kt
├── dialog/            # 弹窗组件（4个）
│   ├── ShareModal.kt
│   ├── PaymentModal.kt
│   ├── PurchaseModal.kt
│   └── SearchModal.kt
├── video/             # 视频组件（2个）
│   ├── VideoInfoPanel.kt
│   └── PlaylistPanel.kt
├── user/              # 用户组件（4个）
│   ├── UserProfileHeader.kt
│   ├── UserAvatar.kt
│   ├── MenuItem.kt
│   └── SettingItem.kt
├── share/             # 分享组件（3个）
│   ├── ShareButton.kt
│   ├── EarningsCard.kt
│   └── RankingItem.kt
├── state/             # 状态组件（2个）
│   ├── LoadingIndicator.kt
│   └── ErrorMessage.kt
└── ComponentRegistry.kt # 组件注册表 (非UI组件，用于管理)

2. 关键组件说明：
   - **WatchCountBadge**：显示视频观看次数（×N格式）
   - **VideoProgressBar**：显示视频观看进度和百分比
   - **VideoItem**：支持4种使用场景的视频项组件，每个场景都分两种状态：已购买和未购买
   - **SeriesCard**：系列卡片

3. 主色调规范：
   - 主色：绿色系渐变
   - 强调色：适配的蓝色
   - 背景色：浅灰白色系
   - 文字色：深灰色系（参考原型中的颜色）

**强制自我验证**：
完成文件创建后，请对照以下问题进行检查并确认：
- 目录结构和所有文件（26个UI组件 + 1个注册表）是否已完整创建，无一遗漏？
- 主色调规范是否严格遵循了原型分析得出的结论？
- 对于 `WatchCountBadge` 和 `VideoProgressBar` 等关键组件，其核心功能职责是否已在设计上明确区分？
```

**验收标准**：
- [x] **前置任务完成**：已完成对指定原型文件的分析。
- [x] **目录结构完整性**：严格按照要求创建了包含 `base`, `display`, `navigation`, `dialog`, `video`, `user`, `share`, `state` 的目录结构。
- [x] **组件文件完整性**：26个UI组件和1个注册表的 `.kt` 文件已全部创建在对应目录下，无任何遗漏。
- [x] **组件注册表**：`ComponentRegistry.kt` 文件已创建。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

### 指令1：Mock Server环境搭建

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
搭建完整的Mock Server环境用于前端开发：

1. 创建Mock Server项目结构：
mock_server/
├── src/
│   ├── main.py              # FastAPI主入口
│   ├── models/              # 数据模型
│   │   ├── user.py
│   │   ├── series.py        # 系列模型
│   │   ├── category.py      # 分类模型
│   │   ├── video.py         # 视频模型
│   │   └── payment.py
│   ├── api/                 # API端点
│   │   ├── auth.py          # 用户认证
│   │   ├── series.py        # 系列管理
│   │   ├── categories.py    # 分类管理
│   │   ├── videos.py        # 视频管理
│   │   ├── payments.py      # 支付管理
│   │   ├── analytics.py     # 数据统计
│   │   └── users.py         # 用户管理
│   ├── data/                # 模拟数据
│   │   ├── users.json
│   │   ├── series.json
│   │   ├── categories.json
│   │   ├── videos.json
│   │   └── purchases.json
│   └── utils/               # 工具函数

2. 核心API接口实现（共14个）：
   - POST /api/auth/login - 用户登录
   - GET /api/series - 获取系列列表（包含分类和视频项）
   - GET /api/series/{id}/categories - 获取系列下的分类
   - GET /api/categories/{id}/videos - 获取分类下的视频
   - GET /api/videos/{id} - 获取单个视频详情
   - POST /api/payments/create - 创建支付订单
   - GET /api/user/profile - 获取用户信息
   - GET /api/user/purchases - 获取购买记录
   - PUT /api/videos/{id}/progress - 更新播放进度
   - PUT /api/videos/{id}/watch-count - 更新观看次数
   - GET /api/share/earnings - 获取分享收益
   - POST /api/share/create - 创建分享链接
   - GET /api/share/ranking - 获取分享排行榜
   - GET /api/share/materials - 获取分享素材

3. 数据结构层级关系：
   - **系列（Series）** → **分类（Category）** → **视频项（Video）**
   - 系列/分类具有购买状态：免费、已购买、未购买
   - 视频项包含：播放图标 + 视频标题
     - **已购买状态展示**：缓存状态显示 + 播放进度条 + 进度百分比 + 观看次数徽章（×N格式）
     - **未购买状态展示**：锁定图标（🔒）

4. 用户状态设计：
   - **免费用户**：可观看免费内容
   - **已购买用户**：可观看已购买的系列/分类
   - **未购买用户**：无法观看付费内容
   - 注意：没有其他任何用户等级概念，禁止出现任何用户等级概念
```

**验收标准**：
- [x] **环境运行**：Mock Server可成功启动，运行在`localhost:8000`。
- [x] **API完整性**：清单中的14个核心API接口已全部实现，且可正常响应。
- [x] **数据结构正确性**：API返回的数据结构严格遵循"系列→分类→视频"的层级关系。
- [x] **用户状态逻辑**：用户状态（免费/已购买/未购买）的逻辑已在API中正确实现。

---

### 指令2：创建Android项目结构

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
创建Android项目，项目名称为"水幕视频课程App"，包名为com.shuimu.course，最低SDK版本为API 26（Android 8.0），目标SDK为最新版本。使用Kotlin语言，启用ViewBinding和DataBinding。

创建基础目录结构：
app/src/main/java/com/shuimu/course/
├── data/           # 数据层
├── domain/         # 业务层  
├── presentation/   # 表现层
├── di/            # 依赖注入
├── utils/         # 工具类
└── MainActivity.kt

同时创建对应的资源文件目录，并设置基础的strings.xml、colors.xml、themes.xml文件。

重要：确保网络配置支持连接Mock Server（localhost:8000）。
```

**验收标准**：
- [x] **项目创建成功**：项目名称、包名、SDK版本均符合要求。
- [x] **目录结构规范**：严格按照要求创建了`data`, `domain`, `presentation`, `di`, `utils`目录。
- [x] **网络配置可用**：应用已配置可连接到`localhost:8000`的Mock Server。
- [x] **基础资源文件**：`strings.xml`, `colors.xml`, `themes.xml`已创建。

---

## 🚀 项目初始化

### 指令3：配置基础依赖和构建脚本

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
配置项目的build.gradle.kts文件，添加以下核心依赖的最新稳定版本：

1. Jetpack Compose BOM及相关组件
2. AndroidX Media3 ExoPlayer
3. Hilt依赖注入
4. Room数据库
5. Retrofit网络请求
6. Kotlin Coroutines和Flow
7. Navigation Compose
8. DataStore

特别配置：
- 网络安全配置允许HTTP连接（用于Mock Server），添加`usesCleartextTraffic`权限。
- 配置Retrofit baseUrl为Mock Server地址。
```

**验收标准**：
- [x] **编译通过**：项目可成功编译，所有依赖已正确添加到`build.gradle.kts`。
- [x] **网络连接配置**：已添加`usesCleartextTraffic`权限，Retrofit的`baseUrl`已指向Mock Server。
- [x] **依赖版本**：所有核心依赖均使用了最新的稳定版本。

---

### 指令4：设置Hilt依赖注入

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
配置Hilt依赖注入框架：

1. 创建Application类并添加`@HiltAndroidApp`注解。
2. 配置Hilt模块结构：
   - `NetworkModule`：提供网络相关依赖（Retrofit, OkHttp）。
   - `DatabaseModule`：提供数据库相关依赖（Room, Dao）。
   - `RepositoryModule`：提供仓库层依赖。
   - `ViewModelModule`：提供ViewModel依赖。
3. 为`MainActivity`添加`@AndroidEntryPoint`注解。

**依赖注入最佳实践（编码规范）**：
- **单例模式**: 对数据库、网络服务、仓库等重量级或需要全局共享的实例，统一使用`@Singleton`注解。
- **作用域**: 根据生命周期需要，合理使用`@ActivityScoped`等作用域注解。
- **可测试性**: 模块设计应便于在测试环境中提供Mock依赖进行替换。
```

**验收标准**：
- [x] **Hilt应用启动**：Application类已配置`@HiltAndroidApp`，应用可正常启动。
- [x] **模块完整性**：`NetworkModule`, `DatabaseModule`, `RepositoryModule`, `ViewModelModule`已创建。
- [x] **入口点配置**：`MainActivity`已添加`@AndroidEntryPoint`注解。

---

### 指令5：配置Clean Architecture包结构

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
在指令2创建的基础上，进一步完善Clean Architecture包结构：

app/src/main/java/com/shuimu/course/
├── data/
│   ├── local/              # 本地数据源
│   │   ├── dao/           # Room DAO
│   │   ├── entities/      # Room实体
│   │   └── database/      # 数据库配置
│   ├── remote/            # 远程数据源  
│   │   ├── api/          # API接口
│   │   ├── dto/          # 数据传输对象
│   │   └── interceptors/ # 网络拦截器
│   ├── repository/        # 仓库实现
│   └── datastore/        # DataStore配置
├── domain/
│   ├── model/            # 领域模型
│   ├── repository/       # 仓库接口
│   └── usecase/         # 业务用例
├── presentation/
│   ├── ui/              # UI组件
│   │   ├── components/  # 可复用组件 (在指令0已创建)
│   │   ├── screens/     # 页面组件
│   │   └── theme/       # 主题配置
│   ├── viewmodel/       # ViewModel
│   └── navigation/      # 导航配置
├── di/                  # 依赖注入模块 (在指令4已创建)
└── utils/               # 工具类
```

**验收标准**：
- [x] **包结构完整**：严格按照图示创建了`data`, `domain`, `presentation`层下的所有子包。
- [x] **职责分离**：目录结构清晰，体现了数据、业务、表现层的分离。
- [x] **依赖关系正确**：符合`presentation` -> `domain` <- `data`的依赖倒置原则。

---

### 指令6：设置基础Compose主题

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
基于UI原型设计配置Compose主题系统 (`presentation/ui/theme/`目录下)：

1. **颜色系统 (Color.kt)**：
   - 主色：绿色系渐变 (#10B981, #059669)
   - 强调色：蓝色系 (#3B82F6, #2563EB)  
   - 背景色：浅灰白色系 (#F9FAFB, #FFFFFF)
   - 文字色：深灰色系 (#111827, #374151, #6B7280)
   - 错误色/成功色

2. **字体规范 (Type.kt)**：
   - 标题、正文、说明文字的字号和字重。

3. **形状系统 (Shape.kt)**：
   - 组件的圆角半径。

4. **主题配置 (Theme.kt)**：
   - 整合颜色、字体、形状，并支持浅色/深色模式。
```

**验收标准**：
- [x] **颜色配置**：`Color.kt`中定义的颜色与原型一致。
- [x] **字体配置**：`Type.kt`中定义的字体规范符合要求。
- [x] **形状配置**：`Shape.kt`中定义的圆角规范符合要求。
- [x] **主题可用**：`Theme.kt`配置完成，应用可在Compose预览中正确应用主题。

---

### 指令7：集成UI组件系统

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
**前置任务：原型复核**
在开始编码前，再次复核 `UI Prototype` 文件夹中的设计，确保你对每个组件的视觉样式和交互行为有精确的理解。

---

**核心任务：实现UI组件**

基于指令0创建的组件文件，实现以下26个核心UI组件。**必须严格遵循原型，禁止任何简化。**

1.  **基础组件 (5个)**：
    *   `ShuimuButton`：支持原型中定义的所有样式。
    *   `ShuimuCard`：统一的卡片容器。
    *   `ShuimuTextField`：标准输入框。
    *   `WatchCountBadge`：见下方详细规范。
    *   `VideoProgressBar`：见下方详细规范。

2.  **弹窗组件 (4个)**：
    *   `ShareModal`, `PaymentModal`, `PurchaseModal`
    *   `SearchModal`：必须包含搜索历史和下拉建议功能。

3.  **展示组件 (4个)**：
    *   `CategoryCard`, `SearchItem`, `SeriesCard`
    *   `VideoItem`：**（重点实现，复杂度高）** 必须严格实现以下所有8种变体，不可遗漏或简化：
        *   **场景1-首页列表**: 已购买状态 (显示进度条+徽章) vs 未购买状态 (显示锁定图标🔒)
        *   **场景2-系列详情**: 已购买状态 vs 未购买状态
        *   **场景3-搜索结果**: 已购买状态 vs 未购买状态
        *   **场景4-播放列表**: 已购买状态 vs 未购买状态
4.  **...以及原型中定义的其他13个组件**，补完至26个。

---
#### **重点组件详细规范**

*   **`WatchCountBadge` (观看次数徽章)**
    *   **目标**：实现 `ORIGINAL` 和 `MIXED` 两套徽章体系，可动态切换。
    *   **体系一: `ORIGINAL` (钻石体系)**
        *   Level 0-2: 无图标
        *   Level 3-5: 1个 `💎`
        *   Level 6-9: 2个 `💎`
        *   Level 10+: 1个 `👑` + 1个 `👧`
    *   **体系二: `MIXED` (混合体系)**
        *   Level 0: 无图标
        *   Level 1: 1个 `⭐` (海星)
        *   Level 2: 2个 `⭐` (海星)
        *   Level 3: 1个 `✨` (闪耀)
        *   Level 4: 2个 `✨` (闪耀)
        *   Level 5: 1个 `💎` (钻石)
        *   Level 6: 2个 `💎` (钻石)
        *   Level 7: 1个 `💎` (粉钻)
        *   Level 8: 2个 `💎` (粉钻)
        *   Level 9: 1个 `👑` (皇冠)
        *   Level 10+: 1个 `👑` + 1个 `👧`
    *   **动画要求**: 必须100%复现CSS中定义的所有动画效果（生长、闪烁、旋转、渐变等）。

*   **`VideoProgressBar` (视频进度条)**
    *   **目标**: 以视觉方式展示视频进度。
    *   **核心要求**:
        *   必须同时显示图形进度条和 "xx%" 的百分比文本。
        *   进度变化时必须有平滑的过渡动画。
        *   支持激活闪光动画效果。
        *   支持 `4dp` 和 `6dp` 两种高度。

---
#### **执行策略与技术要点**

为确保100%还原原型，AI在执行时必须遵循以下策略：
1.  **唯一的真相来源**: 所有视觉参数（颜色、尺寸、间距、圆角）和动画细节（时长、延迟、缓动曲线）的 **唯一、最终、不可置疑的参考标准** 是 `UI Prototype/data/` 目录下的 `.css` 和 `.js` 文件。禁止任何形式的简化或主观臆断。
2.  **动画转换策略**:
    *   CSS 的 `@keyframes` 循环动画，应使用 Jetpack Compose 的 `InfiniteTransition` 配合 `infiniteRepeatable` 和 `keyframes` 来精确复现。
    *   CSS 的 `transition` 属性，应使用 `animate*AsState` 系列函数配合 `tween` 或 `spring` 来实现。缓动曲线 (Cubic Bezier) 的参数必须完全一致。
3.  **图标实现方式**:
    *   所有徽章图标 (`⭐`, `💎`, `👑`, `👧` 等) 均为标准的 **Unicode 字符 (Emoji)**，必须使用 `Text` 组件实现，而不是作为图片 (`Image`) 资源。
4.  **组件化结构**:
    *   在实现 `WatchCountBadge` 等复杂组件时，鼓励采用嵌套的、单一职责的Composable函数。例如，一个基础的`BadgeContainer`，其上层再通过`BoxWithConstraints`来叠加独立的`IconOverlay`组件，以分别处理图标的定位和动画。

**强制自我验证**：
完成编码后，请对照以下问题进行检查并确认：
- 是否总共实现了指令中要求的 **26个** 核心UI组件？
- `VideoItem` 的全部8种变体（4种场景 x 2种状态）是否都已完整实现，并且视觉样式与原型100%匹配？
- `WatchCountBadge` 的两种徽章体系是否已根据规范完整实现，包括正确的图标、数量和动画？
- `VideoProgressBar` 是否同时显示了进度条和百分比文本？
- `SearchModal` 是否包含了搜索历史和下拉建议功能？
- 所有组件的样式（颜色、间距、字体）是否严格遵循了指令6中定义的主题规范？
```

**验收标准**：

- [x] **组件完整性**：指令中要求的26个核心UI组件已全部实现，基础功能完整。
- [x] **VideoItem高保真实行**：`VideoItem`的8种变体全部实现，状态和场景区分明确，样式严格符合原型。
- [x] **关键组件细节**：`WatchCountBadge`, `VideoProgressBar`, `SearchModal`的细节要求已满足。
- [x] **样式一致性**：所有组件样式符合原型设计和主题规范。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

### 指令8：用户个人资料数据结构

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
基于PRD需求创建完整的数据模型类：

1. **领域模型 (`domain/model/`)**: `User`, `Series`, `Category`, `Video`, `Purchase`, `PlayProgress`, `CacheInfo`
2. **Room实体 (`data/local/entities/`)**: `UserEntity`, `SeriesEntity`, `CategoryEntity`, `VideoEntity`, `PurchaseEntity`, `PlayProgressEntity`, `CacheInfoEntity`
3. **网络DTO (`data/remote/dto/`)**: `UserDto`, `SeriesDto`, `CategoryDto`, `VideoDto`, `PaymentDto`
4. **数据转换器**: 实现`Entity` ↔ `Domain Model`以及`DTO` → `Domain Model`的转换逻辑。

+ **架构最佳实践**：
+ - **DTO隔离原则**: DTO（数据传输对象）仅在`data/remote`层使用，严禁将其传递到`domain`层或`presentation`层。必须在`Repository`实现中将其转换为`Domain Model`，以实现与后端API的解耦。
```

**验收标准**：
- [x] **模型完整性**：所有指定的领域模型、Room实体和网络DTO均已创建。
- [x] **结构正确性**：模型类的属性和结构与PRD及Mock Server接口定义一致。
- [x] **转换器实现**：数据转换逻辑已实现，确保数据在各层之间正确流转。

---

### 指令9：配置Room数据库

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
配置Room数据库和DAO接口：

1. **数据库表设计**:
   - `users`, `series`, `categories`, `videos`, `purchases`, `play_progress`, `cache_info`
2. **DAO接口设计**:
   - 为上述每个表创建对应的DAO接口 (`UserDao`, `SeriesDao`等)。
3. **关系映射**:
   - `Series` (1:N) `Categories`
   - `Categories` (1:N) `Videos`
   - `Users` (N:M) `Series`/`Categories` (通过`Purchase`表)
   - `Videos` (1:1) `PlayProgress`
   - `Videos` (1:1) `CacheInfo`
4. **数据库版本管理**:
   - 定义初始版本和迁移策略。
```

**验收标准**：
- [x] **表结构正确**：所有表和字段均已在Room实体中正确定义。
- [x] **DAO接口完整**：所有DAO接口已创建，并包含基本的CRUD操作。
- [x] **关系映射实现**：已使用`@Relation`等注解正确实现表之间的关系。
- [x] **数据库初始化**：数据库实例可通过Hilt提供，并成功创建。

---

### 指令10：实现网络API接口

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
基于Mock Server，使用Retrofit实现网络API接口：

1. **API接口定义 (`data/remote/api/`)**:
   - `AuthApi`, `SeriesApi`, `VideoApi`, `PaymentApi`, `UserApi`
   - 在接口中定义指令1中列出的所有API端点。
2. **网络配置**:
   - Retrofit实例配置（连接Mock Server `localhost:8000`）。
   - OkHttp拦截器配置（日志、错误处理等）。
3. **错误处理**:
   - 建立统一的网络请求错误处理机制。
```

**验收标准**：
- [x] **接口定义完整**：所有API接口和端点均已在Retrofit接口中定义。
- [x] **网络连接成功**：应用可通过Retrofit成功调用Mock Server的接口。
- [x] **错误处理机制**：已建立基础的网络错误处理逻辑。

---

### 指令11：创建Repository实现

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
实现Repository层，统一数据访问接口：

1. **Repository接口 (`domain/repository/`)**:
   - `UserRepository`, `SeriesRepository`, `VideoRepository`, `PaymentRepository`, `CacheRepository`
2. **Repository实现 (`data/repository/`)**:
   - 创建上述接口的实现类，如`UserRepositoryImpl`。
   - 在实现类中，整合本地数据源（DAO）和远程数据源（API）。
3. **数据同步策略**:
   - 实现基本的数据同步逻辑（如网络优先）。
4. **响应式数据流**:
   - 所有数据出口必须统一使用Kotlin Flow作为返回类型，以支持响应式数据流。
   - **用户状态**：`UserRepository`必须提供一个`userState: Flow<User?>`来响应式地追踪用户的登录状态。
```

**验收标准**：
- [x] **接口与实现分离**：`domain`层只包含接口，`data`层包含实现，架构清晰。
- [x] **数据源整合**：Repository实现类已正确调用DAO和API。
- [x] **响应式返回**：接口统一使用Flow返回数据。

---

### 指令12：配置DataStore存储

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
配置DataStore进行用户设置和应用状态存储：

1. **存储内容**:
   - **用户偏好**: 登录Token、视频播放设置、缓存设置、主题设置。
   - **应用状态**: 首次启动标记、搜索历史。
2. **DataStore配置**:
   - 根据存储数据类型，选择使用Proto DataStore或Preferences DataStore。
   - 提供DataStore实例。
```

**验收标准**：
- [x] **DataStore配置正确**：DataStore实例可通过Hilt注入。
- [x] **数据读写正常**：可成功向DataStore写入和读取用户偏好设置。
- [x] **数据持久化**：应用重启后，存储的设置能够保持。

---

## 🎨 UI层开发

### 指令13：创建导航架构

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
基于Navigation Compose创建应用导航架构：

1. **导航图设计**:
   - 定义主导航（首页、搜索、我的）、详情导航、用户导航等。
2. **导航目标定义 (Screens)**:
   - `HomeScreen`, `SeriesDetailScreen`, `VideoPlayerScreen`, `LoginScreen`, `ProfileScreen`等。
3. **导航参数和深链接**:
   - 实现页面间的参数传递（如系列ID、视频ID）。
   - 配置基础的深链接处理。
```

**验收标准**：
- [x] **导航图建立**：`NavHost`和`NavController`已配置，应用内主要页面可通过导航跳转。
- [x] **参数传递正常**：页面间传递ID等参数的功能可正常工作。
- [x] **返回栈管理**：导航返回逻辑正确，符合用户预期。

---

### 指令14：实现首页课程展示

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
实现首页课程展示界面 (`HomeScreen`)。**必须严格遵循原型，禁止简化。**

1. **布局结构**:
   - `TopAppBar` (含搜索图标)。
   - `LazyColumn`展示系列列表。
   - 使用指令7中创建的`SeriesCard`组件。
2. **系列卡片状态**:
   - 根据数据动态展示系列的免费、已购买、未购买状态。
3. **交互功能**:
   - 点击系列卡片导航至系列详情页。
   - 点击搜索图标打开搜索弹窗。
   - 下拉刷新。

**强制自我验证**:
- `SeriesCard`的"免费"、"已购买"、"未购买"三种状态是否都已根据数据正确显示，且视觉样式与原型一致？
- 点击系列卡片和搜索图标的导航功能是否正常？
- 下拉刷新功能是否能触发数据重新加载？
```

**验收标准**：
- [x] **布局高保真**：首页布局与原型设计100%一致。
- [x] **状态展示正确**：系列卡片的三种状态（免费/已购买/未购买）显示正确。
- [x] **交互功能完整**：页面跳转、搜索、下拉刷新功能全部实现。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

### 指令15：开发用户登录页面

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
实现用户登录功能页面 (`LoginScreen`)。**必须严格遵循原型，禁止简化。**

1. **布局结构**:
   - 应用Logo、手机号/验证码输入框、获取验证码按钮、登录按钮、协议链接。
2. **登录流程**:
   - 实现手机号格式验证、60秒倒计时、调用登录API、保存用户信息等逻辑。

**强制自我验证**:
- 登录页面的所有UI元素是否都已实现，且布局与原型一致？
- 获取验证码按钮的60秒倒计时功能是否正常？
- 表单输入验证（如手机号格式）是否已实现？
- 登录成功后，是否会导航到正确页面并保存用户状态？
```

**验收标准**：
- [x] **布局高保真**：登录页面布局与原型设计100%一致。
- [x] **登录流程完整**：获取验证码、登录、状态保存的整个流程可正常工作。
- [x] **表单验证有效**：输入验证逻辑符合要求。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

### 指令16：创建个人中心页面

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
实现个人中心页面 (`ProfileScreen`)。**必须严格遵循原型，禁止简化。**

1. **布局结构**:
   - 用户信息区（头像、昵称）。
   - 功能菜单列表（购买记录、缓存管理、设置、退出登录等）。
2. **交互功能**:
   - 点击菜单项导航到对应页面。
   - 实现退出登录逻辑，包括确认弹窗和清理用户状态。

**强制自我验证**:
- 个人中心的用户信息和功能菜单列表是否都已按原型布局实现？
- 点击每一个菜单项是否都能正确导航？
- 退出登录功能是否包含确认弹窗，并能成功清除用户数据？
```

**验收标准**：
- [x] **布局高保真**：个人中心页面布局与原型设计100%一致。
- [x] **功能完整**：菜单导航和退出登录功能全部实现。
- [x] **数据展示正确**：能正确显示当前登录用户的头像和昵称。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

### 指令17：实现支付购买界面

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
实现课程购买和支付功能 (`PaymentScreen`)。**必须严格遵循原型，禁止简化。**

1. **布局结构**:
   - 课程信息区、支付方式选择、价格明细、确认支付按钮。
2. **支付流程**:
   - 调用Mock Server创建支付订单。
   - 模拟调用第三方支付SDK。
   - 处理支付成功/失败的结果，并更新UI和用户购买状态。

**强制自我验证**:
- 支付页面的课程信息、价格、支付方式等UI元素是否都已实现？
- "创建订单 -> 模拟支付 -> 处理结果"的整个流程是否能走通？
- 支付成功后，课程的购买状态是否会更新？
```

**验收标准**：
- [x] **布局高保真**：支付页面布局与原型设计100%一致。
- [x] **支付流程完整**：与Mock Server对接的支付流程可完整运行。
- [x] **状态管理完善**：能正确处理支付中、成功、失败等状态。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

### 指令18：开发缓存管理页面

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
实现视频缓存管理功能 (`CacheManagerScreen`)。**必须严格遵循原型，禁止简化。**

1. **布局结构**:
   - 缓存概览（总空间、已用空间）。
   - 缓存设置区。
   - 已缓存视频列表（按系列分组）。
   - 操作按钮（全部删除等）。
2. **功能实现**:
   - 展示所有已缓存的视频。
   - 实现单个/批量删除缓存的功能。
   - 实时更新存储空间信息。
3. **响应式状态管理**:
   - 必须实现并监听一个`cacheStatus: Flow<Map<String, CacheInfo>>`数据流，用于响应式地驱动UI更新，准确反映每个视频的缓存状态（已缓存、缓存中、未缓存、失败）。

**强制自我验证**:
- 缓存概览信息是否能正确计算并显示？
- 已缓存视频是否按系列正确分组显示？
```

**验收标准**：
- [x] **布局高保真**：缓存管理页面布局与原型设计100%一致。
- [x] **功能完整**：缓存列表展示、空间统计、删除功能均已实现。
- [x] **状态实时**：缓存状态和存储信息能够实时更新。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

## 📱 视频播放系统

### 指令19：配置AndroidX Media3播放器

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
配置AndroidX Media3 ExoPlayer播放器作为项目核心播放引擎。

**核心原则：强制本地缓存，先缓存后播放**
- 为保证极致的离线体验和播放流畅度，本项目严格遵循"先缓存后播放"原则。
- **播放器 (ExoPlayer) 只能播放本地已完整缓存的视频文件**，严禁直接播放在线流媒体URL。

1. **ExoPlayer配置**:
   - 初始化基础播放器实例。
   - 管理音频焦点和播放器生命周期。
2. **媒体源管理**:
   - 支持从**本地文件**加载媒体。
3. **缓存集成**:
   - **此指令不处理下载逻辑**，仅确保播放器能与Media3的缓存模块集成，并在播放时**优先且仅**读取本地缓存。下载任务的管理将在指令23中实现。
```

**验收标准**：
- [x] **播放器可运行**：ExoPlayer实例可被创建并在应用中用于播放。
- [x] **支持本地播放**：可成功播放本地视频文件。
- [x] **缓存读取机制**：播放器已配置优先从缓存中读取数据。

---

### 指令20：实现视频播放界面

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
实现完整的视频播放界面 (`VideoPlayerScreen`)。**必须严格遵循原型，禁止简化。**

1. **布局结构**:
   - 视频播放区 (`PlayerView`)。
   - 播放控制栏（播放按钮、进度条、时间）。
   - 视频信息区（标题、观看次数）。
   - 播放列表区。
2. **播放控制**:
   - 实现播放/暂停、进度拖拽、全屏切换。
   - 实现手势控制音量和亮度。
3. **响应式状态管理**:
   - 必须实现并监听一个`playerState: Flow<PlayerState>`数据流，用于响应式地管理播放器状态（播放、暂停、缓冲、错误等），并驱动UI更新。

**强制自我验证**:
- 播放器界面的布局是否与原型高度一致，并能适配横竖屏？
- 播放、暂停、拖动进度条、全屏功能是否正常工作？
```

**验收标准**：
- [x] **布局高保真**：播放界面布局与原型100%一致，适配横竖屏。
- [x] **核心控制功能**：播放/暂停、进度控制、全屏功能完整。
- [x] **手势操作流畅**：音量、亮度手势控制已实现且体验良好。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

### 指令21：开发进度记录功能

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
实现视频播放进度记录和恢复功能。

1. **进度记录**:
   - 播放时，定时（如每5秒）将当前播放位置保存到本地Room数据库。
   - 播放完成时，更新完成状态。
   - 调用API将进度同步到服务器。
2. **进度恢复**:
   - 打开视频时，从数据库读取上次播放位置并跳转。
3. **进度展示**:
   - 在视频列表项（`VideoItem`）中，使用`VideoProgressBar`显示播放进度。

**强制自我验证**:
- 观看一个视频中途退出后，再次进入是否能从上次的位置继续播放？
- 播放进度是否能正确保存到本地数据库并同步到服务器？
- 在课程列表页，已观看视频的进度条是否能正确显示？
```

**验收标准**：
- [x] **进度保存准确**：播放进度能被准确、及时地保存。
- [x] **进度恢复无误**：能成功恢复到上次播放的位置。
- [x] **数据同步正常**：本地与服务器的进度数据同步功能已实现。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

### 指令22：实现播放控制器

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
基于`PlayerView`实现自定义的播放控制器UI。**必须严格遵循原型，禁止简化。**

1. **控制组件**:
   - 实现播放/暂停、快进/快退、上一集/下一集按钮。
   - 实现播放速度、清晰度选择菜单。
2. **手势控制**:
   - 双击快进/快退。
   - 长按倍速播放。

**强制自我验证**:
- 所有控制按钮（播放、快进、下一集、速度等）是否都已实现并能正常工作？
- 双击快进/快退15秒的手势是否已实现？
- 播放器控制器的UI样式是否与原型100%一致？
```

**验收标准**：
- [x] **控制器功能完整**：所有指定的控制按钮和菜单功能均已实现。
- [x] **手势控制有效**：双击、长按等手势操作已实现。
- [x] **UI高保真**：自定义控制器的UI与原型设计100%一致。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

### 指令23：完善缓存管理逻辑

**状态**：✅ 已完成
**开始时间**：2024-07-29
**完成时间**：2024-07-29

```prompt
完善视频缓存管理的核心逻辑，实现"先缓存后播放"。

1. **缓存下载逻辑**:
   - 用户点击未缓存的视频时，**不立即播放**，而是**必须启动后台下载任务**，并向UI层暴露下载进度。
   - **下载完成后**，更新视频的本地缓存状态，此时视频才变为可播放。
   - 支持在"设置"中提供"仅在WiFi下自动缓存已购买课程"的选项。

2. **离线播放逻辑**:
   - 播放时，**必须先检查视频是否已完整缓存**。
   - 如果已缓存，则获取本地文件路径，交给播放器（指令19）进行播放。
   - 如果未缓存，则UI应明确提示用户"正在缓存中"或"请先下载"，并显示下载按钮/进度。
3. **响应式进度更新**:
   - 缓存下载过程必须通过`downloadProgress: Flow<Int>`数据流对外暴露下载进度。UI层通过监听此Flow来实时更新进度条显示。

**强制自我验证**:
- 点击一个未缓存的视频时，是否会先开始下载而不是直接播放在线URL？
- 一个视频完整缓存后，断开网络是否仍然可以播放？
- 下载进度是否能通过响应式Flow实时显示在UI上？
```

**验收标准**：
- [x] **强制缓存实现**："先缓存后播放"的逻辑已正确实现。
- [x] **离线播放可用**：已缓存的视频在离线状态下可正常播放。
- [x] **下载管理完善**：支持下载任务管理和进度显示。
- [x] **自我验证通过**：已完成强制自我验证并确认所有项均符合要求。

---

## 🎁 分享分成系统

### 指令24：开发分享收益页面

**状态**：✅ 已完成  
**开始时间**：2024-07-30  
**完成时间**：2024-07-30  

```prompt
实现分享收益功能页面。**必须严格遵循原型，禁止简化。**

1. **布局结构**:
   - 收益概览区（总收益、本月收益）。
   - 收益明细列表。
   - 提现按钮。
2. **功能实现**:
   - 调用API获取并展示收益数据。
   - 实现课程分享功能，生成带用ID的分享链接。
   - 实现提现逻辑。
3. **收益计算**:
   - 确保前端展示的收益与PRD中定义的30%分成比例逻辑一致。
```

**验收标准**：
- [x] **布局高保真**：页面布局与原型设计100%一致。
- [x] **数据展示准确**：收益数据能从API获取并正确展示。
- [x] **分享功能可用**：能成功生成个人分享链接。
- [x] **收益逻辑一致**：前端展示逻辑与30%分成规则吻合。

---

### 指令25：实现分享排行功能

**状态**：✅ 已完成  
**开始时间**：2024-07-30  
**完成时间**：2024-07-30  

```prompt
实现分享排行榜功能页面。**必须严格遵循原型，禁止简化。**

1. **布局结构**:
   - 榜单切换（月榜/总榜）。
   - 排行榜列表（排名、用户、金额）。
   - 当前用户排名高亮显示。
2. **功能实现**:
   - 调用API获取排行榜数据。
   - 实现榜单切换逻辑。
```

**验收标准**：
- [x] **布局高保真**：排行榜页面布局与原型设计100%一致。
- [x] **数据展示准确**：能正确获取并展示排行榜数据。
- [x] **交互功能完整**：榜单切换、当前用户高亮等功能已实现。

---

### 指令26：创建分享素材页面

**状态**：✅ 已完成  
**开始时间**：2024-07-30  
**完成时间**：2024-07-30  

```prompt
实现分享素材管理页面。**必须严格遵循原型，禁止简化。**

1. **布局结构**:
   - 素材分类（海报、文案）。
   - 素材预览区。
   - 操作区（保存到相册、复制文案）。
2. **功能实现**:
   - 调用API获取分享素材。
   - 在海报素材上动态嵌入用户的个人分享码。
   - 实现保存图片和复制文本的功能。
```

**验收标准**：
- [x] **布局高保真**：分享素材页面布局与原型设计100%一致。
- [x] **素材加载正常**：能从API获取并展示各类素材。
- [x] **个性化功能实现**：能将用户的分享码动态添加到海报上。

---

## 📋 项目交付

### 最终验收标准

- [ ] 所有31个指令全部完成
- [ ] 应用功能完整，符合PRD需求
- [ ] UI界面美观，100%还原原型设计
- [ ] 视频播放系统稳定，缓存机制正确
- [ ] 分享分成系统完整，收益计算准确
- [ ] 代码质量良好，架构设计合理
- [ ] 测试覆盖全面，无重大bug

### 交付物清单

- [ ] 完整的Android应用源码
- [ ] Mock Server环境和数据
- [ ] 应用安装包（APK）
- [ ] 技术文档和部署说明
- [ ] 测试报告（包含单元测试、集成测试、UI测试的结果和覆盖率）和已知问题说明 

---

## 🧐 应用质量保障

### 指令29：实现单元测试

**状态**：⏳ 待开始  
**开始时间**：  
**完成时间**：  

```prompt
**目标**：为核心的业务逻辑组件编写单元测试，确保其功能的正确性和稳定性。

**核心概念**：单元测试专注于测试最小的可测试单元（如单个函数或类），在完全隔离的环境中进行，不依赖于Android框架、网络或数据库。

**任务要求**：
1.  **测试目标**：
    *   为 `domain` 层的所有 `UseCase` 编写单元测试。
    *   为 `presentation` 层的所有 `ViewModel` 编写单元测试，重点测试业务逻辑和状态转换。
    *   为 `data` 层的 `Repository` 实现编写单元测试（使用Mock的DAO和API）。
2.  **测试框架**：使用 `JUnit4` 或 `JUnit5` 和 `Mockito-Kotlin` 或 `MockK` 进行测试。
3.  **代码覆盖率**：核心业务逻辑的测试覆盖率目标为 **70%** 以上。

**验收标准**：
- [ ] **测试覆盖**：所有 `UseCase` 和 `ViewModel` 均有对应的测试类。
- [ ] **测试通过**：所有单元测试用例均可成功运行并通过。
- [ ] **逻辑验证**：测试用例覆盖了正常的业务流程、边界条件和潜在的错误情况。
```

---

### 指令30：实现集成测试

**状态**：⏳ 待开始  
**开始时间**：  
**完成时间**：  

```prompt
**目标**：验证应用内不同组件（特别是数据层）之间的交互是否按预期工作。

**核心概念**：集成测试用于检查多个单元协同工作时的正确性，例如数据库访问、网络请求与本地存储的联动。

**任务要求**：
1.  **测试场景**：
    *   **数据库集成测试**：测试所有 `Room DAO` 接口的增、删、改、查功能是否正确。使用内存数据库（`inMemoryDatabaseBuilder`）进行测试。
    *   **数据流集成测试**：测试从"调用API -> 数据存入Room -> 从Room读取并展示"的完整数据流动链路。
2.  **测试框架**：使用 `AndroidX Test` 库和 `Robolectric`（如果需要）。

**验收标准**：
- [ ] **DAO测试**：所有DAO接口的关键方法都已通过测试验证。
- [ ] **数据链路测试**：获取课程列表并存入数据库的完整流程可以成功走通。
- [ ] **测试环境隔离**：测试使用独立的内存数据库，不影响开发数据库。
```

---

### 指令31：实现UI测试

**状态**：⏳ 待开始  
**开始时间**：  
**完成时间**：  

```prompt
**目标**：通过模拟用户操作，验证关键用户界面的行为和功能的正确性。

**核心概念**：UI测试（或端到端测试）从用户的视角检查应用，确保从UI交互到后端逻辑的整个流程是通畅的。

**任务要求**：
1.  **测试框架**：使用 `Compose Test` 框架 (`createComposeRule`)。
2.  **测试关键流程**：
    *   **登录流程**：测试输入手机号/验证码、点击登录、验证登录后跳转的完整流程。
    *   **首页浏览流程**：测试首页课程列表是否正确显示，点击课程卡片是否能导航到详情页。
    *   **视频播放流程**：测试从点击视频到进入播放页面的流程是否通畅。
    *   **（可选）视觉回归测试**：对核心界面（如首页`SeriesCard`）进行截图测试，确保UI与原型视觉上100%一致。

**验收标准**：
- [ ] **测试用例实现**：已为上述至少3个关键流程编写UI测试用例。
- [ ] **测试通过**：所有UI测试用例均可在模拟器或真机上成功运行。
- [ ] **断言正确**：测试用例中包含了对UI状态（如某个组件是否存在、文本是否正确）的断言。
```

---

## 📋 项目交付
