import requests
import json

base_url = "https://api.shuimu.us.kg/api"

print("=== 缓存下载管理系统 API 测试 ===")

# 测试1: 获取缓存配置
print("1. 测试缓存配置 API")
try:
    response = requests.get(f"{base_url}/cache/config")
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"配置: {response.json()}")
    else:
        print(f"错误: {response.text}")
except Exception as e:
    print(f"请求失败: {e}")

print()

# 测试2: 同步缓存状态
print("2. 测试缓存状态同步 API")
try:
    data = {
        "videoId": "test_video_001",
        "isCached": True,
        "localPath": "/storage/emulated/0/VideoCache/test_video_001.mp4"
    }
    response = requests.post(f"{base_url}/cache/state", json=data)
    print(f"状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"结果: {response.json()}")
    else:
        print(f"错误: {response.text}")
except Exception as e:
    print(f"请求失败: {e}")

print()
print("=== 测试完成 ===") 