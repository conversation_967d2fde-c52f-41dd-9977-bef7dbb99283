{"environments": {"development": {"name": "开发环境", "host": "0.0.0.0", "port": 8000, "reload": true, "workers": 1, "log_level": "info", "tunnel_type": "cloudflare_temp", "public_url": "https://api.shuimu.us.kg", "features": {"hot_reload": true, "debug_mode": true, "cors_allow_all": true}}, "production": {"name": "Cloudflare生产环境", "host": "0.0.0.0", "port": 8000, "reload": false, "workers": 4, "log_level": "warning", "tunnel_type": "cloudflare_workers", "public_url": "https://shuimu-api.your-domain.workers.dev", "features": {"hot_reload": false, "debug_mode": false, "cors_allow_origins": ["https://your-app-domain.com"]}}}, "cache_api": {"endpoints": ["/api/cache/config", "/api/cache/state", "/api/cache/states", "/api/cache/batch-sync"], "max_concurrent_downloads": 5, "supported_formats": ["mp4", "m3u8"]}, "cloudflare": {"workers_script": "mock_server/src/main.py", "deployment_commands": ["wrangler publish", "cloudflare tunnel --url http://localhost:8000"]}}