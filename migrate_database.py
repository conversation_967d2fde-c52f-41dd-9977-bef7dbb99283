#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本
执行课程结构调整：系列 → 分类 → 视频项
"""

import sys
import os
from src.config.config import Config
from src.database.connection import DatabaseConnection
import pymysql
import logging
sys.path.append(os.path.dirname(os.path.abspath(__file__)))



# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def execute_migration():
    """执行数据库迁移"""
    try:
        # 加载配置
        config = Config()
        db_config = config.get_database_config()
        
        # 创建数据库连接
        connection = pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['user'],
            password=db_config['password'],
            database=db_config['database'],
            charset=db_config['charset']
        )
        
        cursor = connection.cursor()
        
        logger.info("开始执行数据库迁移...")
        
        # 1. 创建分类表
        logger.info("1. 创建分类表...")
        create_categories_table = """
        CREATE TABLE IF NOT EXISTS `categories` (
          `id` int NOT NULL AUTO_INCREMENT,
          `series_id` int NOT NULL COMMENT '所属系列ID',
          `title` varchar(200) NOT NULL COMMENT '分类标题',
          `description` text COMMENT '分类描述',
          `price` decimal(10,2) DEFAULT '0.00' COMMENT '分类价格',
          `order_index` int DEFAULT '0' COMMENT '排序序号',
          `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
          PRIMARY KEY (`id`),
          KEY `idx_series_id` (`series_id`),
          KEY `idx_order_index` (`order_index`),
          CONSTRAINT `fk_categories_series` FOREIGN KEY (`series_id`) REFERENCES `series` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程分类表'
        """
        cursor.execute(create_categories_table)
        logger.info("分类表创建成功")
        
        # 2. 检查videos表是否已有category_id字段
        logger.info("2. 检查videos表结构...")
        cursor.execute("SHOW COLUMNS FROM videos LIKE 'category_id'")
        if not cursor.fetchone():
            logger.info("为videos表添加category_id字段...")
            cursor.execute("ALTER TABLE `videos` ADD COLUMN `category_id` int DEFAULT NULL COMMENT '所属分类ID' AFTER `series_id`")
            cursor.execute("ALTER TABLE `videos` ADD KEY `idx_category_id` (`category_id`)")
            logger.info("category_id字段添加成功")
        else:
            logger.info("category_id字段已存在")
        
        # 3. 为现有系列创建默认分类
        logger.info("3. 为现有系列创建默认分类...")
        cursor.execute("""
        INSERT INTO `categories` (`series_id`, `title`, `description`, `price`, `order_index`)
        SELECT 
            `id` as `series_id`,
            CONCAT(`title`, ' - 默认分类') as `title`,
            '系统自动创建的默认分类' as `description`,
            COALESCE(`price`, 0.00) as `price`,
            1 as `order_index`
        FROM `series`
        WHERE NOT EXISTS (
            SELECT 1 FROM `categories` WHERE `categories`.`series_id` = `series`.`id`
        )
        """)
        affected_rows = cursor.rowcount
        logger.info(f"创建了 {affected_rows} 个默认分类")
        
        # 4. 将现有视频关联到默认分类
        logger.info("4. 将现有视频关联到默认分类...")
        cursor.execute("""
        UPDATE `videos` v
        JOIN `categories` c ON c.series_id = v.series_id
        SET v.category_id = c.id
        WHERE v.category_id IS NULL
        AND c.title LIKE '%默认分类%'
        """)
        affected_rows = cursor.rowcount
        logger.info(f"更新了 {affected_rows} 个视频的分类关联")
        
        # 5. 添加外键约束（如果不存在）
        logger.info("5. 添加外键约束...")
        try:
            cursor.execute("ALTER TABLE `videos` ADD CONSTRAINT `fk_videos_category` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL")
            logger.info("外键约束添加成功")
        except pymysql.Error as e:
            if "Duplicate key name" in str(e) or "already exists" in str(e):
                logger.info("外键约束已存在")
            else:
                logger.warning(f"添加外键约束失败: {e}")
        
        # 6. 验证数据完整性
        logger.info("6. 验证数据完整性...")
        cursor.execute("""
        SELECT 
            s.title as series_title,
            COUNT(c.id) as category_count,
            SUM(c.price) as total_price,
            COUNT(v.id) as video_count
        FROM series s
        LEFT JOIN categories c ON c.series_id = s.id
        LEFT JOIN videos v ON v.category_id = c.id
        GROUP BY s.id, s.title
        ORDER BY s.id
        """)
        
        results = cursor.fetchall()
        logger.info("数据完整性验证结果:")
        for row in results:
            series_title, category_count, total_price, video_count = row
            logger.info(f"  系列: {series_title}, 分类数: {category_count}, 总价格: ¥{total_price or 0:.2f}, 视频数: {video_count}")
        
        # 提交事务
        connection.commit()
        logger.info("数据库迁移完成！")
        
        # 关闭连接
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        if 'connection' in locals():
            connection.rollback()
            connection.close()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("水幕课程管理端 - 数据库迁移工具")
    print("=" * 60)
    print()
    print("本工具将执行以下操作:")
    print("1. 创建分类表 (categories)")
    print("2. 为视频表添加分类关联字段")
    print("3. 为现有系列创建默认分类")
    print("4. 将现有视频关联到默认分类")
    print("5. 添加必要的外键约束")
    print("6. 验证数据完整性")
    print()
    
    # 确认执行
    confirm = input("确定要执行数据库迁移吗？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("迁移已取消")
        return
    
    print()
    print("开始执行迁移...")
    
    # 执行迁移
    success = execute_migration()
    
    print()
    if success:
        print("✅ 数据库迁移成功完成！")
        print()
        print("现在可以启动管理端程序，新的课程结构已生效:")
        print("  系列 → 分类 → 视频项")
        print("  分类可以单独定价，系列价格为所有分类价格之和")
    else:
        print("❌ 数据库迁移失败！")
        print("请检查错误日志并修复问题后重试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
