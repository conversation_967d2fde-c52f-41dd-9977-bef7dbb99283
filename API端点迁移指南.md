# API端点架构迁移指南

## 📋 迁移概述
- **迁移时间**: 2025-06-29
- **版本**: v1.0 → v2.0
- **目标**: 统一API端点架构，按数据归属分类

## 🎯 迁移目标

### 解决的问题
1. **端点设计混乱** - 同一数据有多个更新端点
2. **权限控制不清晰** - App端和管理端使用不同端点更新相同数据
3. **功能重复** - 同一业务逻辑在多个端点中重复实现

### 迁移后的优势
1. **逻辑清晰** - 按数据归属分类，易于理解和维护
2. **权限统一** - 同一数据的权限控制逻辑统一
3. **减少重复** - 避免功能重复，降低维护成本
4. **扩展性好** - 新增数据类型时容易决定归属

## 📊 端点变更对照表

### 用户数据端点变更

| 功能 | 旧端点 (App) | 旧端点 (管理端) | 新端点 (统一) |
|------|-------------|----------------|---------------|
| 用户进度 | `PUT /api/videos/{id}/progress` | `PUT /api/admin/users/{user_id}/progress/{video_id}` | `PUT /api/users/{user_id}/progress/{video_id}` |
| 用户缓存 | `PUT /api/videos/{id}/cache` | `PUT /api/admin/users/{user_id}/cache/{video_id}` | `PUT /api/users/{user_id}/cache/{video_id}` |
| 用户设置 | `PUT /api/user/settings` | `PUT /api/admin/users/{user_id}/settings` | `PUT /api/users/{user_id}/settings` |
| 用户收藏 | - | - | `PUT /api/users/{user_id}/favorites` |

### 内容管理端点变更

| 功能 | 旧端点 (管理端) | 新端点 (管理端) | 说明 |
|------|----------------|----------------|------|
| 系列更新 | `PUT /api/series/{id}` | `PUT /api/admin/series/{id}` | 使用管理端专用端点 |
| 分类更新 | `PUT /api/categories/{id}` | `PUT /api/admin/categories/{id}` | 使用管理端专用端点 |
| 视频更新 | `PUT /api/videos/{id}` | `PUT /api/admin/videos/{id}` | 使用管理端专用端点 |

## 🔧 权限控制变更

### 新的权限验证机制

#### 请求头要求
```http
# App用户请求
X-User-Id: user_001
Content-Type: application/json

# 管理员请求
X-User-Id: admin_001
X-Is-Admin: true
Content-Type: application/json
```

#### 权限验证逻辑
```python
def verify_user_data_permission(current_user_id: str, target_user_id: str, is_admin: bool = False):
    """验证用户数据访问权限"""
    if is_admin:
        return True  # 管理员可以操作任何用户数据
    if current_user_id == target_user_id:
        return True  # 用户可以操作自己的数据
    raise HTTPException(status_code=403, detail="Permission denied")
```

## 📱 App端迁移步骤

### 1. 更新API调用
```kotlin
// 旧代码
videoApi.updateVideoProgress(videoId, progress)
videoApi.updateVideoCacheStatus(videoId, isCached)

// 新代码
userDataApi.updateUserProgress(userId, videoId, progress)
userDataApi.updateUserCache(userId, videoId, cacheData)
```

### 2. 更新请求头
```kotlin
// 添加用户ID头
private val headers = mapOf(
    "X-User-Id" to currentUserId,
    "Content-Type" to "application/json"
)
```

### 3. 更新数据模型
```kotlin
// 新的请求数据模型
data class UserProgressUpdateRequest(
    val position: Int? = null,
    val progress: Float? = null,
    val watchCount: Int? = null
)
```

## 🛠️ 管理端迁移步骤

### 1. 更新API客户端
```python
# 旧代码
self.client.put(f'/api/videos/{video_id}', json=video_data)
self.client.put(f'/api/admin/users/{user_id}/progress/{video_id}', data=progress_data)

# 新代码
self.client.put(f'/api/admin/videos/{video_id}', json=video_data)
self.client.put(f'/api/users/{user_id}/progress/{video_id}', data=progress_data)
```

### 2. 更新请求头
```python
# 添加管理员权限头
self.session.headers.update({
    'X-User-Id': 'admin_001',
    'X-Is-Admin': 'true',
    'Content-Type': 'application/json'
})
```

## 🧪 测试验证

### 1. 运行测试脚本
```bash
python test_new_api_architecture.py
```

### 2. 验证项目
- [ ] 用户进度更新功能
- [ ] 用户缓存状态更新
- [ ] 用户设置更新
- [ ] 管理端内容更新
- [ ] 权限控制验证
- [ ] API文档可访问性

### 3. 性能测试
- [ ] 响应时间测试
- [ ] 并发请求测试
- [ ] 权限验证性能

## 🚨 注意事项

### 向后兼容性
- 旧端点暂时保留，标记为废弃
- 客户端逐步迁移到新端点
- 完成迁移后移除旧端点

### 数据一致性
- 确保新旧端点操作相同的数据源
- 验证数据格式兼容性
- 测试数据同步正确性

### 错误处理
- 统一错误响应格式
- 完善权限错误提示
- 添加详细的日志记录

## 📅 迁移时间表

### 第一阶段 (已完成)
- [x] 创建统一用户数据端点
- [x] 实现权限控制逻辑
- [x] 更新管理端API客户端
- [x] 创建测试脚本

### 第二阶段 (进行中)
- [ ] App端API调用迁移
- [ ] 全面测试验证
- [ ] 性能优化

### 第三阶段 (计划中)
- [ ] 移除旧端点
- [ ] 更新API文档
- [ ] 部署到生产环境

## 🔗 相关文档
- [API端点架构设计文档.md](./API端点架构设计文档.md)
- [测试脚本](./test_new_api_architecture.py)
- [FastAPI文档](http://localhost:8000/docs)

## 📞 支持联系
如有问题，请联系开发团队或查看相关文档。

---
**最后更新**: 2025-06-29  
**版本**: v1.0
