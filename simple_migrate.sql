-- 简单迁移脚本
USE shuimu_course;

-- 1. 创建分类表
CREATE TABLE IF NOT EXISTS categories (
  id int NOT NULL AUTO_INCREMENT,
  series_id int NOT NULL,
  title varchar(200) NOT NULL,
  description text,
  price decimal(10,2) DEFAULT 0.00,
  order_index int DEFAULT 0,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_series_id (series_id)
);

-- 2. 添加category_id字段到videos表
ALTER TABLE videos ADD COLUMN category_id int DEFAULT NULL;
ALTER TABLE videos ADD KEY idx_category_id (category_id);

-- 3. 为现有系列创建默认分类
INSERT INTO categories (series_id, title, description, price, order_index)
SELECT 
    id as series_id,
    CONCAT(title, ' - 默认分类') as title,
    '系统自动创建的默认分类' as description,
    COALESCE(price, 0.00) as price,
    1 as order_index
FROM series
WHERE NOT EXISTS (
    SELECT 1 FROM categories WHERE categories.series_id = series.id
);

-- 4. 将现有视频关联到默认分类
UPDATE videos v
JOIN categories c ON c.series_id = v.series_id
SET v.category_id = c.id
WHERE v.category_id IS NULL
AND c.title LIKE '%默认分类%';
