#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将Python程序打包成exe文件
"""

import os
import sys
import shutil
import subprocess

def build_exe():
    """打包成exe文件"""
    print("🚀 开始打包水幕课程管理端...")
    
    # 检查PyInstaller是否安装
    try:
        import PyInstaller
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyInstaller"])
    
    # 打包命令
    build_cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个文件
        "--windowed",                   # 不显示控制台窗口
        "--name=水幕课程管理端",          # 程序名称
        "--icon=src/resources/icons/app.ico",  # 图标文件（如果有的话）
        "--add-data=config.ini;.",      # 包含配置文件
        "--distpath=dist",              # 输出目录
        "--workpath=build",             # 临时文件目录
        "--specpath=build",             # spec文件目录
        "src/main.py"                   # 主程序文件
    ]
    
    try:
        # 执行打包命令
        print("📦 正在打包...")
        result = subprocess.run(build_cmd, check=True, capture_output=True, text=True)
        
        print("✅ 打包成功！")
        print(f"📁 输出文件: dist/水幕课程管理端.exe")
        
        # 复制配置文件到输出目录
        if os.path.exists("config.ini"):
            shutil.copy2("config.ini", "dist/")
            print("📋 配置文件已复制到输出目录")
        
        # 创建使用说明
        create_readme()
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False
    
    return True

def create_readme():
    """创建使用说明文件"""
    readme_content = """# 水幕课程管理端使用说明

## 运行程序
双击 "水幕课程管理端.exe" 即可运行程序。

## 首次使用
1. 程序启动后，需要配置数据库连接
2. 编辑 config.ini 文件，填入正确的数据库信息：
   - host: 数据库服务器地址
   - port: 数据库端口（通常是3306）
   - user: 数据库用户名
   - password: 数据库密码
   - database: 数据库名称

## 配置示例
```ini
[database]
host = your-server.com
port = 3306
user = your-username
password = your-password
database = shuimu_course
```

## 注意事项
- 确保数据库服务器允许远程连接
- 建议使用VPN或安全连接访问生产数据库
- 定期备份重要数据

## 技术支持
如有问题，请联系技术支持。
"""
    
    with open("dist/使用说明.txt", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("📖 使用说明已创建")

def clean_build():
    """清理构建文件"""
    print("🧹 清理构建文件...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️ 已删除: {dir_name}")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "clean":
        clean_build()
        return
    
    # 切换到脚本所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 清理旧的构建文件
    clean_build()
    
    # 开始打包
    if build_exe():
        print("\n🎉 打包完成！")
        print("📁 可执行文件位置: dist/水幕课程管理端.exe")
        print("📋 配置文件位置: dist/config.ini")
        print("📖 使用说明位置: dist/使用说明.txt")
    else:
        print("\n❌ 打包失败！")

if __name__ == "__main__":
    main()
