@echo off
echo 正在修复数据库结构...

echo 添加category_id字段...
mysql -h localhost -u root -p123456 -e "USE shuimu_course; ALTER TABLE videos ADD COLUMN category_id int DEFAULT NULL;"

echo 创建索引...
mysql -h localhost -u root -p123456 -e "USE shuimu_course; ALTER TABLE videos ADD KEY idx_category_id (category_id);"

echo 为现有系列创建默认分类...
mysql -h localhost -u root -p123456 -e "USE shuimu_course; INSERT INTO categories (series_id, title, description, price, order_index) SELECT id as series_id, CONCAT(title, ' - 默认分类') as title, '系统自动创建的默认分类' as description, COALESCE(price, 0.00) as price, 1 as order_index FROM series WHERE NOT EXISTS (SELECT 1 FROM categories WHERE categories.series_id = series.id);"

echo 将现有视频关联到默认分类...
mysql -h localhost -u root -p123456 -e "USE shuimu_course; UPDATE videos v JOIN categories c ON c.series_id = v.series_id SET v.category_id = c.id WHERE v.category_id IS NULL AND c.title LIKE '%默认分类%';"

echo 检查结果...
mysql -h localhost -u root -p123456 -e "USE shuimu_course; SELECT COUNT(*) as category_count FROM categories;"
mysql -h localhost -u root -p123456 -e "USE shuimu_course; SELECT COUNT(*) as video_with_category FROM videos WHERE category_id IS NOT NULL;"

echo 修复完成！
pause
