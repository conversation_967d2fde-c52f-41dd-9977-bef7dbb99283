# PaymentSlideUpSheet 组件使用指南

## 组件概述

`PaymentSlideUpSheet` 是一个可复用的底部弹出支付组件，支持动画、拖拽关闭、加载状态等功能。

**组件位置：** `app/src/main/java/com/shuimu/course/presentation/ui/components/dialog/PaymentModal.kt`

## 组件特性

- ✅ 平滑的滑入/滑出动画
- ✅ 支持拖拽关闭手势
- ✅ 加载状态支持（支付过程中禁用拖拽）
- ✅ 自定义高度和提示文本
- ✅ 完全事件透传（不阻塞背景交互）
- ✅ Material 3 设计风格

## 基本用法

### 1. 导入组件

```kotlin
import com.shuimu.course.presentation.ui.components.dialog.PaymentSlideUpSheet
import com.shuimu.course.domain.model.PurchaseInfo
```

### 2. 状态管理

```kotlin
// PurchaseInfo 数据类已在 domain.model 包中定义，可直接使用

@Composable
fun YourScreen() {
    // 支付弹窗状态
    var purchaseInfo by remember { mutableStateOf<PurchaseInfo?>(null) }
    
    // 支付ViewModel（可注入）
    val paymentViewModel: PaymentViewModel = hiltViewModel()
    val paymentState by paymentViewModel.paymentState.collectAsState()
    
    // 你的页面内容...
}
```

### 3. 组件使用

```kotlin
@Composable
fun YourScreen() {
    Box(modifier = Modifier.fillMaxSize()) {
        // 你的主要内容
        YourMainContent()
        
        // 支付弹窗 - 放在Box底部
        purchaseInfo?.let { info ->
            PaymentSlideUpSheet(
                visible = true,
                courseTitle = info.name,
                price = info.price.removePrefix("¥"),
                onPay = {
                    paymentViewModel.processPayment(
                        itemId = info.id,
                        itemType = info.type,
                        amount = info.price.removePrefix("¥").toFloatOrNull() ?: 0f
                    )
                },
                onDismiss = { 
                    if (!paymentState.isLoading) {
                        purchaseInfo = null 
                    }
                },
                loading = paymentState.isLoading,
                enableDragToClose = !paymentState.isLoading,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}
```

## 在搜索结果页面中的使用示例

```kotlin
@Composable
fun SearchResultScreen(
    navController: NavController
) {
    var purchaseInfo by remember { mutableStateOf<PurchaseInfo?>(null) }
    val paymentViewModel: PaymentViewModel = hiltViewModel()
    val paymentState by paymentViewModel.paymentState.collectAsState()
    
    Box(modifier = Modifier.fillMaxSize()) {
        LazyColumn {
            items(searchResults) { item ->
                SearchResultItem(
                    title = item.title,
                    price = item.price,
                    isPurchased = item.isPurchased,
                    isFree = item.isFree,
                    onClick = {
                        // 点击未购买项目时触发支付弹窗
                        if (!item.isPurchased && !item.isFree) {
                            // 根据项目类型生成正确的标题
                            val displayName = when (item.type) {
                                "series" -> item.title
                                "category", "video" -> {
                                    // 分类和视频显示：系列名·分类名
                                    if (item.seriesTitle != null) {
                                        "${item.seriesTitle}·${item.title}"
                                    } else {
                                        item.title
                                    }
                                }
                                else -> item.title
                            }
                            
                            purchaseInfo = PurchaseInfo(
                                id = item.id,
                                name = displayName,
                                price = "¥${item.price}",
                                type = item.type
                            )
                        } else {
                            // 已购买或免费的，直接进入
                            navController.navigate("${Routes.COURSE_DETAIL}/${item.id}")
                        }
                    }
                )
            }
        }
        
        // 复用相同的支付弹窗组件
        purchaseInfo?.let { info ->
            PaymentSlideUpSheet(
                visible = true,
                courseTitle = info.name,
                price = info.price.removePrefix("¥"),
                onPay = {
                    paymentViewModel.processPayment(
                        itemId = info.id,
                        itemType = info.type,
                        amount = info.price.removePrefix("¥").toFloatOrNull() ?: 0f
                    )
                },
                onDismiss = { 
                    if (!paymentState.isLoading) {
                        purchaseInfo = null 
                    }
                },
                loading = paymentState.isLoading,
                enableDragToClose = !paymentState.isLoading,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}
```

## 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `visible` | Boolean | ✅ | - | 是否显示弹窗 |
| `courseTitle` | String | ✅ | - | 课程标题 |
| `price` | String | ✅ | - | 价格（不含¥符号） |
| `onPay` | () -> Unit | ✅ | - | 支付按钮点击回调 |
| `onDismiss` | () -> Unit | ✅ | - | 关闭弹窗回调 |
| `modifier` | Modifier | ❌ | Modifier | 修饰符 |
| `rewardHint` | String | ❌ | "购买后可享受30%分享分成" | 奖励提示文本 |
| `loading` | Boolean | ❌ | false | 是否显示加载状态 |
| `sheetHeight` | Dp | ❌ | 320.dp | 弹窗高度 |
| `enableDragToClose` | Boolean | ❌ | true | 是否启用拖拽关闭 |

## 最佳实践

### 1. 状态管理
- 使用 `purchaseInfo` 控制弹窗显示/隐藏
- 支付加载时禁用拖拽关闭：`enableDragToClose = !paymentState.isLoading`

### 2. 布局要求
- 必须放在 `Box` 容器中
- 使用 `Modifier.align(Alignment.BottomCenter)` 定位
- 为主内容添加底部内边距避免遮挡

### 3. 事件处理
```kotlin
// 正确的触发逻辑
if (!item.isPurchased && !item.isFree) {
    // 显示支付弹窗
    purchaseInfo = PurchaseInfo(...)
} else {
    // 隐藏支付弹窗，执行其他操作
    purchaseInfo = null
    // 进入课程或其他逻辑
}
```

### 4. 支付结果处理
配合 `PaymentResultDialog` 处理支付结果：

```kotlin
// 监听支付结果
val paymentResult = when {
    paymentState.paymentResult != null -> PaymentResult(success = true, ...)
    paymentState.error != null -> PaymentResult(success = false, ...)
    else -> null
}

paymentResult?.let { result ->
    PaymentResultDialog(
        result = result,
        onDismiss = { 
            paymentViewModel.clearPaymentState()
            purchaseInfo = null
        }
        // 其他回调...
    )
}
```

## 组件依赖

使用此组件需要确保项目中包含：

1. **PaymentViewModel**: 处理支付逻辑
2. **PaymentPrimaryButton**: 支付按钮组件
3. **主题颜色**: `DangerRed`, `TextSecondaryLight`
4. **Material 3**: 基础 UI 组件

## 向后兼容

项目中保留了 `PaymentBottomSheet` 作为向后兼容的包装器，但建议新代码直接使用 `PaymentSlideUpSheet`。 