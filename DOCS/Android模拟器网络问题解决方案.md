# Android 模拟器网络问题解决方案

## 问题描述

在开发过程中遇到 Android 模拟器无法访问 HTTPS API 的问题，具体表现为：

1. **IP 直连 HTTPS 失败**：`GET https://************/api/series` 报错 `SSLHandshakeException: SSLV3_ALERT_HANDSHAKE_FAILURE`
2. **域名解析失败**：改用域名后出现 `UnknownHostException: Unable to resolve host "api.shuimu.us.kg"`
3. **浏览器正常，App 异常**：PC 浏览器能正常访问 `https://api.shuimu.us.kg/api/series`，但 Android App 无法连接

## 问题分析

### 根本原因
1. **TLS 证书不匹配**：直接用 IP 访问 HTTPS 时，Cloudflare 的证书只匹配域名，导致 TLS 握手失败
2. **模拟器 DNS 解析异常**：Android AVD 默认使用 *******/*******，在某些网络环境下无法解析 `.us.kg` 域名

### 技术细节
- Cloudflare 使用 SNI (Server Name Indication) 技术，要求客户端在 TLS 握手时发送正确的域名
- Android 模拟器的网络堆栈与宿主机隔离，DNS 配置可能不同步
- 部分企业网络或地区 ISP 可能阻断特定 DNS 服务器或域名解析

## 解决方案

### 最终成功方案：代码层自定义 DNS
在 `NetworkModule.kt` 中实现自定义 DNS 解析器：

```kotlin
@Provides
@Singleton
fun provideCustomDns(): Dns = object : Dns {
    override fun lookup(hostname: String): List<java.net.InetAddress> {
        return if (hostname == HOST_NAME) {
            // 直接返回已知 IP，避免系统 DNS 解析失败
            listOf(java.net.InetAddress.getByName(SERVER_IP))
        } else {
            // 其他域名仍走系统 DNS
            Dns.SYSTEM.lookup(hostname)
        }
    }
}

// 在 OkHttpClient 中应用
fun provideOkHttpClient(
    loggingInterceptor: HttpLoggingInterceptor,
    hostInterceptor: Interceptor,
    hostnameVerifier: HostnameVerifier,
    customDns: Dns
): OkHttpClient {
    return OkHttpClient.Builder()
        .addInterceptor(hostInterceptor)
        .addInterceptor(loggingInterceptor)
        .hostnameVerifier(hostnameVerifier)
        .dns(customDns)  // 关键：注入自定义 DNS
        .build()
}
```

### 配置要点
```kotlin
private const val BASE_URL = "https://api.shuimu.us.kg/api/"  // 使用域名
private const val HOST_NAME = "api.shuimu.us.kg"
private const val SERVER_IP = "************"
```

## 替代方案（系统层）

### 方案一：修改 AVD DNS 配置
编辑 `C:\Users\<USER>\.android\avd\设备名.avd\config.ini`：
```ini
dns.server=***************,*******
```

### 方案二：启动时指定 DNS
```powershell
"C:\...\emulator" -avd 设备名 -dns-server ***************,*******
```

### 方案三：Host 文件映射
在宿主机 `C:\Windows\System32\drivers\etc\hosts` 添加：
```
************  api.shuimu.us.kg
```

## 优势对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| 自定义 DNS | 代码可控、跨设备一致、无需系统权限 | 需要已知 IP | 生产环境推荐 |
| 修改 AVD 配置 | 系统级解决、对代码无侵入 | 每个 AVD 需单独配置 | 开发测试 |
| Host 文件 | 全局生效、配置简单 | 需要管理员权限、影响全系统 | 临时调试 |

## 验证步骤

1. **编译检查**：确保代码无语法错误
2. **冷启动**：AVD Manager → Cold Boot Now（清除网络缓存）
3. **日志观察**：查看 OkHttp 日志确认请求成功
4. **功能测试**：验证 App 能正常获取和展示数据

## 经验总结

### 关键技巧
1. **优先域名**：HTTPS 服务应始终使用域名访问，避免证书不匹配
2. **DNS 兜底**：在网络模块实现 DNS 降级策略，提高连接成功率
3. **日志分析**：通过 OkHttp 日志准确定位网络问题类型
4. **分层测试**：先验证浏览器，再排查 App 层问题

### 最佳实践
- 生产环境使用自定义 DNS 方案，保证稳定性
- 开发环境可结合多种方案，快速定位问题
- 网络配置变更后务必执行冷启动，避免缓存干扰
- 预留多个 DNS 服务器，增强容灾能力

### 预防措施
- 在 CI/CD 中增加网络连通性测试
- 为不同网络环境准备备用域名或 IP
- 考虑实现智能 DNS 切换逻辑
- 定期验证第三方 DNS 服务的可用性

---

**文档创建时间**：2025-06-25  
**解决方案状态**：✅ 已验证有效  
**维护责任人**：开发团队 