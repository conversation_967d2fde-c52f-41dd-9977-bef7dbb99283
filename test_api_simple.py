#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单API连接测试
"""

import requests
import json

def test_api_connection():
    """测试API连接"""
    base_url = "http://localhost:8000"
    
    print("🔍 测试API连接...")
    print(f"服务端地址: {base_url}")
    
    try:
        # 测试基础连接
        response = requests.get(f"{base_url}/api/series", timeout=5)
        print(f"✅ 连接成功，状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"📊 获取到 {len(data)} 个系列")
            
            # 显示前3个系列
            for i, series in enumerate(data[:3]):
                print(f"  {i+1}. {series.get('title', 'N/A')} (ID: {series.get('id', 'N/A')})")
        
        # 测试分类API
        print("\n🔍 测试分类API...")
        response = requests.get(f"{base_url}/api/categories", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"📊 获取到 {len(data)} 个分类")
            
            # 显示前3个分类
            for i, category in enumerate(data[:3]):
                print(f"  {i+1}. {category.get('title', 'N/A')} (系列: {category.get('seriesId', 'N/A')})")
        
        # 测试视频API
        print("\n🔍 测试视频API...")
        response = requests.get(f"{base_url}/api/videos", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"📊 获取到 {len(data)} 个视频")
            
            # 显示前3个视频
            for i, video in enumerate(data[:3]):
                print(f"  {i+1}. {video.get('title', 'N/A')} (分类: {video.get('categoryId', 'N/A')})")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：无法连接到服务端")
        print("请确保服务端正在运行在 http://localhost:8000")
        return False
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
        return False
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False

if __name__ == "__main__":
    test_api_connection()
