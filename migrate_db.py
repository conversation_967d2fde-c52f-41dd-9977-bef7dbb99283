#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本
将数据库表结构从整数ID改为字符串ID
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database.models import DatabaseManager, Base, Series, Category, Video
from cache.global_data_manager import global_data_manager

def drop_and_recreate_tables():
    """删除并重新创建表"""
    print("🔄 重新创建数据库表...")
    
    try:
        db_manager = DatabaseManager()
        engine = db_manager.engine
        
        # 删除所有表
        print("🗑️ 删除现有表...")
        Base.metadata.drop_all(engine)
        print("✅ 现有表已删除")
        
        # 重新创建表
        print("🏗️ 创建新表结构...")
        Base.metadata.create_all(engine)
        print("✅ 新表结构已创建")
        
        return True
        
    except Exception as e:
        print(f"❌ 表重建失败: {e}")
        return False

def sync_data_from_server():
    """从服务端同步数据到数据库"""
    print("🌐 从服务端同步数据到数据库...")
    
    try:
        # 确保内存数据已加载
        if not global_data_manager.is_data_loaded():
            print("📦 加载服务端数据到内存...")
            success = global_data_manager.load_all_data_once()
            if not success:
                print("❌ 服务端数据加载失败")
                return False
        
        # 获取内存中的数据
        summary = global_data_manager.get_data_summary()
        print(f"📊 内存数据统计: {summary}")
        
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # 同步系列数据
        series_result = global_data_manager.get_series_list(page=1, page_size=1000)
        if series_result.get('success'):
            series_count = 0
            for series_data in series_result['data']:
                try:
                    # 创建系列记录
                    series = Series(
                        id=series_data['id'],
                        title=series_data.get('title', ''),
                        description=series_data.get('description', ''),
                        price=series_data.get('price', 0),
                        is_published=series_data.get('is_published', False)
                    )
                    session.add(series)
                    series_count += 1
                    print(f"✅ 系列 {series_data['id']} 已添加")
                except Exception as e:
                    print(f"⚠️ 系列 {series_data.get('id')} 同步失败: {e}")
            
            print(f"✅ 同步了 {series_count} 个系列")
        
        # 同步分类数据
        category_result = global_data_manager.get_category_list(page=1, page_size=1000)
        if category_result.get('success'):
            category_count = 0
            for category_data in category_result['data']:
                try:
                    # 创建分类记录
                    category = Category(
                        id=category_data['id'],
                        series_id=category_data.get('series_id', ''),
                        title=category_data.get('title', ''),
                        description=category_data.get('description', ''),
                        price=category_data.get('price', 0),
                        order_index=category_data.get('order_index', 0)
                    )
                    session.add(category)
                    category_count += 1
                    print(f"✅ 分类 {category_data['id']} 已添加")
                except Exception as e:
                    print(f"⚠️ 分类 {category_data.get('id')} 同步失败: {e}")
            
            print(f"✅ 同步了 {category_count} 个分类")
        
        # 同步视频数据
        video_result = global_data_manager.get_video_list(page=1, page_size=1000)
        if video_result.get('success'):
            video_count = 0
            for video_data in video_result['data']:
                try:
                    # 创建视频记录
                    video = Video(
                        id=video_data['id'],
                        category_id=video_data.get('category_id', ''),
                        title=video_data.get('title', ''),
                        description=video_data.get('description', ''),
                        video_url=video_data.get('video_url', ''),
                        duration=video_data.get('duration', 0),
                        order_index=video_data.get('order_index', 0)
                    )
                    session.add(video)
                    video_count += 1
                    print(f"✅ 视频 {video_data['id']} 已添加")
                except Exception as e:
                    print(f"⚠️ 视频 {video_data.get('id')} 同步失败: {e}")
            
            print(f"✅ 同步了 {video_count} 个视频")
        
        # 提交所有更改
        session.commit()
        session.close()
        
        print("✅ 数据同步完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据同步失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_migration():
    """验证迁移结果"""
    print("🔍 验证迁移结果...")
    
    try:
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        # 检查数据数量
        series_count = session.query(Series).count()
        category_count = session.query(Category).count()
        video_count = session.query(Video).count()
        
        print(f"📊 数据库数据统计:")
        print(f"   系列: {series_count}")
        print(f"   分类: {category_count}")
        print(f"   视频: {video_count}")
        
        # 检查ID类型
        if series_count > 0:
            first_series = session.query(Series).first()
            print(f"📊 系列ID示例: {repr(first_series.id)} ({type(first_series.id)})")
        
        if category_count > 0:
            first_category = session.query(Category).first()
            print(f"📊 分类ID示例: {repr(first_category.id)} ({type(first_category.id)})")
        
        if video_count > 0:
            first_video = session.query(Video).first()
            print(f"📊 视频ID示例: {repr(first_video.id)} ({type(first_video.id)})")
        
        session.close()
        
        # 对比内存数据
        summary = global_data_manager.get_data_summary()
        print(f"📊 内存数据统计: {summary}")
        
        # 检查一致性
        memory_series = summary['series_count']
        memory_categories = summary['category_count']
        memory_videos = summary['video_count']
        
        if series_count == memory_series and category_count == memory_categories and video_count == memory_videos:
            print("✅ 数据库与内存数据完全一致")
            return True
        else:
            print("⚠️ 数据库与内存数据不一致")
            print(f"   数据库: 系列={series_count}, 分类={category_count}, 视频={video_count}")
            print(f"   内存: 系列={memory_series}, 分类={memory_categories}, 视频={memory_videos}")
            return False
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主迁移函数"""
    print("🚀 开始数据库迁移...")
    print("🎯 目标：将数据库表结构从整数ID改为字符串ID")
    print("=" * 80)
    
    # 1. 重建表结构
    if not drop_and_recreate_tables():
        print("❌ 表重建失败，迁移终止")
        return False
    
    # 2. 从服务端同步数据
    if not sync_data_from_server():
        print("❌ 数据同步失败，迁移终止")
        return False
    
    # 3. 验证迁移结果
    if not verify_migration():
        print("⚠️ 迁移验证失败，请检查数据")
        return False
    
    print("\n" + "=" * 80)
    print("🎉 数据库迁移完成！")
    print("\n✅ 迁移结果:")
    print("   - 数据库表结构已更新为字符串ID")
    print("   - 所有数据已从服务端重新同步")
    print("   - 数据库与服务端数据保持一致")
    print("\n🔧 现在可以正常使用系列更新功能了！")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ 迁移失败，请检查错误信息并重试")
            sys.exit(1)
    except Exception as e:
        print(f"\n❌ 迁移过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
