#!/usr/bin/env python3
"""
测试服务器状态和端点可用性
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_server_basic():
    """测试服务器基本连接"""
    print("🔍 测试服务器基本连接...")
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"💥 服务器连接失败: {e}")
        return False

def test_api_docs():
    """测试API文档"""
    print("\n🔍 测试API文档...")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        if response.status_code == 200:
            print("✅ API文档可访问")
            return True
        else:
            print(f"❌ API文档访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"💥 API文档访问异常: {e}")
        return False

def test_openapi_spec():
    """测试OpenAPI规范"""
    print("\n🔍 测试OpenAPI规范...")
    try:
        response = requests.get(f"{BASE_URL}/openapi.json", timeout=5)
        if response.status_code == 200:
            openapi_spec = response.json()
            paths = openapi_spec.get("paths", {})
            
            print(f"✅ OpenAPI规范可访问，包含 {len(paths)} 个端点")
            
            # 检查新的用户数据端点是否存在
            user_data_endpoints = [path for path in paths.keys() if "/users/" in path]
            if user_data_endpoints:
                print(f"✅ 发现用户数据端点: {len(user_data_endpoints)} 个")
                for endpoint in user_data_endpoints:
                    print(f"   - {endpoint}")
            else:
                print("❌ 未发现用户数据端点")
            
            # 检查admin端点
            admin_endpoints = [path for path in paths.keys() if "/admin/" in path]
            if admin_endpoints:
                print(f"✅ 发现管理端端点: {len(admin_endpoints)} 个")
                for endpoint in admin_endpoints[:5]:  # 只显示前5个
                    print(f"   - {endpoint}")
                if len(admin_endpoints) > 5:
                    print(f"   ... 还有 {len(admin_endpoints) - 5} 个")
            else:
                print("❌ 未发现管理端端点")
                
            return True
        else:
            print(f"❌ OpenAPI规范访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"💥 OpenAPI规范访问异常: {e}")
        return False

def test_existing_endpoints():
    """测试现有端点"""
    print("\n🔍 测试现有端点...")
    
    existing_endpoints = [
        "/api/series",
        "/api/categories", 
        "/api/videos",
        "/api/admin/series",
        "/api/admin/categories",
        "/api/admin/videos"
    ]
    
    for endpoint in existing_endpoints:
        try:
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - 正常")
            elif response.status_code == 404:
                print(f"❌ {endpoint} - 404 Not Found")
            else:
                print(f"⚠️ {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"💥 {endpoint} - 异常: {e}")

def test_new_user_endpoints():
    """测试新的用户数据端点"""
    print("\n🔍 测试新的用户数据端点...")
    
    new_endpoints = [
        "/api/users/user_001/progress/video_001",
        "/api/users/user_001/settings",
        "/api/users/user_001/cache/video_001",
        "/api/users/user_001/favorites"
    ]
    
    headers = {
        "X-User-Id": "user_001",
        "Content-Type": "application/json"
    }
    
    for endpoint in new_endpoints:
        try:
            # 尝试GET请求（虽然这些是PUT端点，但可以检查路由是否存在）
            response = requests.get(f"{BASE_URL}{endpoint}", headers=headers, timeout=5)
            if response.status_code == 405:  # Method Not Allowed 说明路由存在但方法不对
                print(f"✅ {endpoint} - 路由存在（405 Method Not Allowed）")
            elif response.status_code == 404:
                print(f"❌ {endpoint} - 404 Not Found")
            else:
                print(f"⚠️ {endpoint} - {response.status_code}")
        except Exception as e:
            print(f"💥 {endpoint} - 异常: {e}")

def main():
    """主测试函数"""
    print("🚀 开始测试服务器状态")
    print("=" * 60)
    
    # 基本连接测试
    if not test_server_basic():
        print("\n❌ 服务器连接失败，请检查服务器是否启动")
        print("启动命令: cd mock_server && uvicorn src.main:app --reload")
        return
    
    # API文档测试
    test_api_docs()
    
    # OpenAPI规范测试
    test_openapi_spec()
    
    # 现有端点测试
    test_existing_endpoints()
    
    # 新端点测试
    test_new_user_endpoints()
    
    print("\n" + "=" * 60)
    print("🎉 服务器状态测试完成")
    
    print("\n💡 如果新端点返回404，请尝试：")
    print("1. 重启服务器: cd mock_server && uvicorn src.main:app --reload")
    print("2. 检查导入错误: python -c 'from mock_server.src.api import user_data'")
    print("3. 查看服务器日志确认是否有错误")

if __name__ == "__main__":
    main()
