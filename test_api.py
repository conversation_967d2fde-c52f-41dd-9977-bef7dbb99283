#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API连接
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.api.client import api_client, category_api, series_api

def test_api_connection():
    """测试API连接"""
    print("测试API连接...")
    
    # 测试基础连接
    if api_client.test_connection():
        print("✅ API连接成功")
    else:
        print("❌ API连接失败")
        return False
    
    # 测试分类API
    print("\n测试分类API...")
    try:
        result = category_api.get_categories(page=1, page_size=5)
        if result.get('success'):
            print(f"✅ 分类API测试成功，获取到 {len(result.get('data', []))} 个分类")
            for category in result.get('data', [])[:3]:
                print(f"  - {category.get('title', 'N/A')}")
        else:
            print(f"❌ 分类API测试失败: {result.get('message', 'Unknown error')}")
    except Exception as e:
        print(f"❌ 分类API测试异常: {e}")
    
    # 测试系列API
    print("\n测试系列API...")
    try:
        result = series_api.get_series(page=1, page_size=5)
        if result.get('success'):
            print(f"✅ 系列API测试成功，获取到 {len(result.get('data', []))} 个系列")
            for series in result.get('data', [])[:3]:
                print(f"  - {series.get('title', 'N/A')}")
        else:
            print(f"❌ 系列API测试失败: {result.get('message', 'Unknown error')}")
    except Exception as e:
        print(f"❌ 系列API测试异常: {e}")
    
    return True

if __name__ == "__main__":
    test_api_connection()
