# 服务器重启指南

## 🚨 问题诊断

测试显示新的API端点返回404错误，这通常是因为：
1. **服务器未重启** - 新的路由需要重启服务器才能生效
2. **导入错误** - 新模块可能有语法错误导致导入失败
3. **路由冲突** - 新路由可能与现有路由冲突

## 🔧 解决步骤

### 第一步：停止当前服务器
如果服务器正在运行，请先停止：
```bash
# 在运行服务器的终端中按 Ctrl+C 停止服务器
```

### 第二步：检查模块导入
在重启前，先检查新模块是否有错误：
```bash
cd mock_server
python -c "from src.api import user_data; print('✅ user_data模块导入成功')"
```

如果有错误，会显示具体的错误信息。

### 第三步：重启服务器
```bash
cd mock_server
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

### 第四步：验证服务器状态
运行状态检查脚本：
```bash
python test_server_status.py
```

### 第五步：测试新架构
如果状态检查通过，运行完整测试：
```bash
python test_new_api_architecture.py
```

## 🔍 故障排除

### 如果导入失败
检查以下文件是否存在语法错误：
- `mock_server/src/api/user_data.py`
- `mock_server/src/main.py`

### 如果端点仍然404
1. **检查OpenAPI文档**：访问 http://localhost:8000/docs
2. **查看端点列表**：确认新端点是否在文档中
3. **检查路由注册**：确认 `user_data.router` 已正确注册

### 如果权限错误
确保请求头包含：
```http
X-User-Id: user_001
X-Is-Admin: true  # 仅管理员请求
Content-Type: application/json
```

## 📋 验证清单

重启后请验证以下项目：

### 基础连接
- [ ] 服务器根路径可访问: http://localhost:8000/
- [ ] API文档可访问: http://localhost:8000/docs
- [ ] OpenAPI规范可访问: http://localhost:8000/openapi.json

### 现有端点
- [ ] `/api/series` - 系列列表
- [ ] `/api/categories` - 分类列表  
- [ ] `/api/videos` - 视频列表
- [ ] `/api/admin/series` - 管理端系列
- [ ] `/api/admin/categories` - 管理端分类
- [ ] `/api/admin/videos` - 管理端视频

### 新增端点
- [ ] `PUT /api/users/{user_id}/progress/{video_id}` - 用户进度
- [ ] `PUT /api/users/{user_id}/settings` - 用户设置
- [ ] `PUT /api/users/{user_id}/cache/{video_id}` - 用户缓存
- [ ] `PUT /api/users/{user_id}/favorites` - 用户收藏

### 权限控制
- [ ] App用户只能操作自己的数据
- [ ] 管理员可以操作任何用户的数据
- [ ] 无权限用户被正确拒绝

## 🚀 快速重启命令

```bash
# 一键重启和测试
cd mock_server && uvicorn src.main:app --reload &
sleep 5
python ../test_server_status.py
python ../test_new_api_architecture.py
```

## 📞 如需帮助

如果问题仍然存在，请：
1. 查看服务器启动日志中的错误信息
2. 检查 `mock_server/src/api/user_data.py` 文件是否完整
3. 确认所有依赖的数据文件存在

---

**重要提醒**：新的API端点架构需要服务器重启才能生效！
