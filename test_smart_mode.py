#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能模式测试脚本
测试：本地有数据用本地，没数据用API
"""

import sys
import os
import time

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from database.models import DatabaseManager
from services.course_service import CourseService
from services.user_service import UserService
from utils.config import Config

def test_smart_mode():
    """测试智能模式"""
    print("🧪 测试智能模式：本地有数据用本地，没数据用API")
    print("=" * 60)
    
    try:
        # 初始化配置和数据库
        config = Config()
        db_config = config.get_database_config()
        db_manager = DatabaseManager(db_config)
        
        # 检查API配置
        try:
            from config.api_config import api_config
            api_enabled = api_config.use_api
            print(f"📡 API配置: {api_enabled}")
        except:
            api_enabled = False
            print("📡 API配置: False (配置加载失败)")
        
        # 创建服务（智能模式）
        course_service = CourseService(db_manager, use_api=api_enabled, use_cache=True)
        user_service = UserService(db_manager, use_cache=True)
        
        print(f"📊 CourseService: API模式={course_service.use_api}, 缓存模式={course_service.use_cache}")
        
        # 测试数据初始化
        print("\n🚀 开始智能数据初始化...")
        start_time = time.time()
        
        init_result = course_service.initialize_data(user_service)
        init_time = time.time() - start_time
        
        print(f"📈 初始化结果: {init_result}, 耗时: {init_time:.2f}秒")
        
        if init_result:
            # 测试数据获取
            print("\n📊 测试数据获取...")
            
            # 测试视频数据
            video_result = course_service.get_video_list(page=1, page_size=5)
            print(f"📹 视频数据: success={video_result.get('success')}, "
                  f"count={len(video_result.get('data', []))}")
            
            # 测试系列数据
            series_result = course_service.get_series_list(page=1, page_size=5)
            print(f"📚 系列数据: success={series_result.get('success')}, "
                  f"count={len(series_result.get('data', []))}")
            
            # 测试分类数据
            category_result = course_service.get_category_list(page=1, page_size=5)
            print(f"📂 分类数据: success={category_result.get('success')}, "
                  f"count={len(category_result.get('data', []))}")
            
            # 测试用户数据
            user_result = user_service.get_user_list(page=1, page_size=5)
            print(f"👥 用户数据: success={user_result.get('success')}, "
                  f"count={len(user_result.get('data', []))}")
            
            # 分析数据来源
            total_records = sum([
                len(video_result.get('data', [])),
                len(series_result.get('data', [])),
                len(category_result.get('data', [])),
                len(user_result.get('data', []))
            ])
            
            print(f"\n📊 总数据量: {total_records} 条")
            
            if total_records == 0:
                print("💡 分析: 本地数据库为空")
                if api_enabled:
                    print("🔄 建议: 系统应该从API获取数据")
                    print("❓ 可能原因: API连接失败或API无数据")
                else:
                    print("⚠️ 说明: API模式未启用，无法从服务端获取数据")
            else:
                print("💡 分析: 本地数据库有数据，系统使用本地数据")
            
            # 统计成功率
            success_count = sum([
                video_result.get('success', False),
                series_result.get('success', False),
                category_result.get('success', False),
                user_result.get('success', False)
            ])
            
            print(f"📊 数据获取成功率: {success_count}/4")
            
            if success_count >= 3:
                print("🎉 智能模式测试通过！")
                return True
            else:
                print("⚠️ 部分功能正常")
                return True
        else:
            print("❌ 数据初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 智能模式测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始智能模式测试...")
    
    # 测试智能模式
    smart_ok = test_smart_mode()
    
    print("=" * 60)
    print("🎯 测试结果总结:")
    print(f"  智能模式: {'✅ 正常' if smart_ok else '❌ 异常'}")
    
    if smart_ok:
        print("\n🎉 智能模式测试通过！")
        print("\n✅ 智能逻辑:")
        print("  1. 优先检查本地缓存")
        print("  2. 缓存无效时检查本地数据库")
        print("  3. 本地无数据时从API获取")
        print("  4. 所有方式失败时使用空数据")
        
        print("\n💡 现在的行为:")
        print("  - 如果本地有数据 → 使用本地数据")
        print("  - 如果本地没有数据 → 从服务端API获取")
        print("  - API失败时 → 显示空数据，不会崩溃")
    else:
        print("\n⚠️ 智能模式需要进一步调试")

if __name__ == "__main__":
    main()
