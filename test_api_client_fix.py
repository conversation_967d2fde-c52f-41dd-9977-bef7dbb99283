#!/usr/bin/env python3
"""
测试API客户端修复
验证PUT和POST请求参数是否正确
"""

import sys
import os

# 添加shuimu-admin到Python路径
sys.path.insert(0, os.path.join(os.getcwd(), 'shuimu-admin'))

def test_api_client_import():
    """测试API客户端导入"""
    print("🧪 测试API客户端导入...")
    
    try:
        from src.api.client import APIClient, SeriesAPIClient, CategoryAPIClient, VideoAPIClient, AdminUserAPIClient
        print("✅ API客户端导入成功")
        return True
    except Exception as e:
        print(f"❌ API客户端导入失败: {e}")
        return False

def test_api_client_methods():
    """测试API客户端方法"""
    print("\n🧪 测试API客户端方法...")
    
    try:
        from src.api.client import APIClient
        
        # 创建客户端实例
        client = APIClient()
        
        # 检查方法是否存在
        methods_to_check = ['get', 'post', 'put', 'delete']
        for method in methods_to_check:
            if hasattr(client, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法不存在")
                return False
        
        return True
    except Exception as e:
        print(f"❌ API客户端方法测试失败: {e}")
        return False

def test_series_api_client():
    """测试系列API客户端"""
    print("\n🧪 测试系列API客户端...")
    
    try:
        from src.api.client import SeriesAPIClient
        
        # 创建客户端实例
        client = SeriesAPIClient()
        
        # 检查关键方法
        methods_to_check = ['update_series', 'create_series']
        for method in methods_to_check:
            if hasattr(client, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法不存在")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 系列API客户端测试失败: {e}")
        return False

def test_method_signatures():
    """测试方法签名"""
    print("\n🧪 测试方法签名...")
    
    try:
        from src.api.client import APIClient
        import inspect
        
        client = APIClient()
        
        # 检查put方法签名
        put_signature = inspect.signature(client.put)
        put_params = list(put_signature.parameters.keys())
        
        if 'endpoint' in put_params and 'data' in put_params:
            print("✅ put方法签名正确: endpoint, data")
        else:
            print(f"❌ put方法签名错误: {put_params}")
            return False
        
        # 检查post方法签名
        post_signature = inspect.signature(client.post)
        post_params = list(post_signature.parameters.keys())
        
        if 'endpoint' in post_params and 'data' in post_params:
            print("✅ post方法签名正确: endpoint, data")
        else:
            print(f"❌ post方法签名错误: {post_params}")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 方法签名测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试API客户端修复")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 检查shuimu-admin目录是否存在
    admin_dir = os.path.join(current_dir, 'shuimu-admin')
    if os.path.exists(admin_dir):
        print("✅ shuimu-admin目录存在")
    else:
        print("❌ shuimu-admin目录不存在")
        return
    
    # 执行测试
    tests = [
        test_api_client_import,
        test_api_client_methods,
        test_series_api_client,
        test_method_signatures
    ]
    
    success_count = 0
    for test_func in tests:
        if test_func():
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"🎉 测试完成: {success_count}/{len(tests)} 通过")
    
    if success_count == len(tests):
        print("✅ 所有API客户端测试通过！")
        print("\n💡 修复总结:")
        print("1. ✅ 修复了PUT请求的json参数问题")
        print("2. ✅ 修复了POST请求的json参数问题")
        print("3. ✅ 所有API客户端方法现在使用正确的data参数")
        print("\n🚀 现在管理端应该能正常更新服务器数据了")
    else:
        print("❌ 部分API客户端测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
